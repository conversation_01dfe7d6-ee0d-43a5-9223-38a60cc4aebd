{
    "clangd.arguments": [
        "--background-index",
        "--header-insertion=never",
        "--completion-style=detailed",
        "--compile-commands-dir=${workspaceFolder}/out",
        "--query-driver=**"
    ],
    "clangd.fallbackFlags": [
        "-std=gnu++17",
        "-I${workspaceFolder}/include"
    ],
    "cmake.sourceDirectory": "/home/<USER>/project/m5pro/third_party/iceoryx/iceoryx_meta",
}