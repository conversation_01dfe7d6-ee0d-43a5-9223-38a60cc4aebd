defines = -DLOG_TAG_STR=idvr.ccu
include_dirs = -I../applications/idvr/idvr.ccu -I../applications/idvr/idvr.ccu/mcu.msg.handler -I../applications/idvr/idvr.ccu/sms.recieve.handler -I../applications/idvr/include/idvr -I../applications/protocol/algo -I../applications/vendor/8838_sdk4/include -I../foundation/base/core/filelog -I../foundation/base/core/mystd/include -I../foundation/base/service/imu/libimu -I../foundation/communication/ipcAgent/include -I../foundation/communication/libflow/include -I../foundation/communication/libflow/flowWrap -I../foundation/communication/message -I../foundation/communication/property -I../third_party/nlohmann_json -I../third_party/msgpack-c/include -I../applications/protocol/prot.jtt -I../applications/protocol/prot.jtt/mm -I../applications/protocol/prot.jtt/base -I../applications/protocol/prot.jtt/subiao -I../applications/protocol/prot.jtt/utils -I../applications/protocol/prot.jtt/transporter -I../applications/idvr/include/idvr -I../applications/protocol/algo -I../foundation/base/core/filelog -I../foundation/base/core/mystd/include -I../foundation/communication/socketcmd/include -I../foundation/communication/ipcAgent/include -I../foundation/communication/libflow/include -I../foundation/communication/libflow/flowWrap -I../foundation/communication/message -I../foundation/communication/property -I../third_party/nlohmann_json -I../third_party/msgpack-c/include -I../applications/protocol/prot.anncr.sanfeng -I../applications/vendor/8838_sdk4/include -I../foundation/base/core/filelog -I../foundation/base/core/mystd/include -I../foundation/communication/socketcmd/include -I../foundation/communication/ipcAgent/include -I../foundation/communication/libflow/include -I../foundation/communication/libflow/flowWrap -I../foundation/communication/message -I../foundation/communication/property -I../third_party/nlohmann_json -I../third_party/msgpack-c/include -I../applications/protocol/prot.middle.ware -I../applications/protocol/prot.middle.ware/base -I../applications/protocol/prot.middle.ware/utils -I../applications/protocol/prot.middle.ware/xunjian -I../applications/protocol/prot.middle.ware/INSP -I../applications/protocol/prot.jtt808-1078 -I../applications/protocol/prot.jtt808-1078/area -I../applications/protocol/prot.jtt808-1078/faceid -I../applications/protocol/prot.jtt808-1078/utils -I../applications/protocol/prot.jtt808-1078/base -I../applications/protocol/prot.jtt808-1078/chengwei -I../applications/protocol/prot.jtt808-1078/chongqing -I../applications/protocol/prot.jtt808-1078/chengdu.bus -I../applications/protocol/prot.jtt808-1078/guangdong -I../applications/protocol/prot.jtt808-1078/henan.telecom -I../applications/protocol/prot.jtt808-1078/hualan.tec -I../applications/protocol/prot.jtt808-1078/huoyun -I../applications/protocol/prot.jtt808-1078/jiangsu.puhuo -I../applications/protocol/prot.jtt808-1078/lvcheng -I../applications/protocol/prot.jtt808-1078/nanjing -I../applications/protocol/prot.jtt808-1078/ningbo.zhatu -I../applications/protocol/prot.jtt808-1078/roadnet.mngr -I../applications/protocol/prot.jtt808-1078/sichuan -I../applications/protocol/prot.jtt808-1078/shengpinganfang -I../applications/protocol/prot.jtt808-1078/ttx -I../applications/protocol/prot.jtt808-1078/wuxi.tianyi -I../applications/protocol/prot.jtt808-1078/zhoushan -I../applications/protocol/prot.jtt808-1078/ztc -I../applications/protocol/prot.jtt808-1078/zf.bsd -I../applications/protocol/prot.jtt808-1078/xiashi -I../applications/protocol/prot.jtt808-1078/sanyi -I../applications/protocol/prot.jtt808-1078/xiangbiao -I../applications/protocol/prot.jtt808-1078/io.service -I../applications/idvr/include/idvr -I../applications/protocol/algo -I../applications/protocol/road.net/libroadnet -I../applications/vendor/8838_sdk4/include -I../foundation/base/core/filelog -I../foundation/base/service/imu/libimu -I../foundation/base/core/mystd/include -I../foundation/communication/socketcmd/include -I../foundation/communication/ipcAgent/include -I../foundation/communication/libflow/include -I../foundation/communication/libflow/flowWrap -I../foundation/communication/message -I../foundation/communication/property -I../foundation/multimedia/libsrc/libfallocate -I../foundation/multimedia/libsrc/libstrmts -I../third_party/nlohmann_json -I../third_party/msgpack-c/include -I../third_party/SQLiteCpp/include -I../third_party/SQLiteCpp/sqlite3 -I../third_party/freetype2/freetype-2.11.1/include -I../applications/protocol/road.net/libroadnet -I../foundation/base/core/mystd -I../foundation/base/core/mystd/include -I../foundation/base/core/filelog -I../foundation/base/service/tts/libtts -I../foundation/communication/property -I../foundation/base/service/imu/libimu -I../foundation/communication/ipcAgent/include -I../foundation/communication/socketcmd/include -I../foundation/communication/socketcmd/include/socketcmd -I../foundation/communication/message -I../foundation/communication/ringbuf -I../foundation/communication/libflow/flowWrap -I../third_party/msgpack-c/include -I../foundation/communication/libflow/include -I../third_party/curl/include -I../third_party/msgpack-c/include -I../third_party/liboss/include -I../third_party/liboss/include/alibabacloud -I../third_party/liboss/include/alibabacloud/oss -I../third_party/SQLiteCpp/include -I../third_party/sqlite/include -I../foundation/base/service/libphone -I../foundation/communication/libevservice/export -I../foundation/communication/fileprop -I../foundation/multimedia/libsrc/libfallocate
cflags = -nostdlib -fno-common -fno-builtin -fno-strict-aliasing -funwind-tables -fsigned-char -DCROSS_PLAT_32 -O3 -fPIE
cflags_cc = -Wno-psabi -Wno-unused-parameter -nostdlib -fno-common -fno-builtin -fno-strict-aliasing -funwind-tables -DCROSS_PLAT_32 -O3 -fpermissive -fPIE
target_output_name = idvr.ccu

build obj/applications/idvr/idvr.ccu/mcu.msg.handler/idvr.ccu.mcu.msg.handler.o: cxx ../applications/idvr/idvr.ccu/mcu.msg.handler/mcu.msg.handler.cpp
build obj/applications/idvr/idvr.ccu/sms.recieve.handler/idvr.ccu.sms.recieve.handler.o: cxx ../applications/idvr/idvr.ccu/sms.recieve.handler/sms.recieve.handler.cpp
build obj/applications/idvr/idvr.ccu/idvr.ccu.main.o: cxx ../applications/idvr/idvr.ccu/main.cpp
build obj/applications/idvr/idvr.ccu/idvr.ccu.manager.o: cxx ../applications/idvr/idvr.ccu/manager.cpp
build obj/applications/idvr/idvr.ccu/idvr.ccu.controller.o: cxx ../applications/idvr/idvr.ccu/controller.cpp
build obj/applications/idvr/idvr.ccu/idvr.ccu.service.o: cxx ../applications/idvr/idvr.ccu/service.cpp
build obj/applications/idvr/idvr.ccu/idvr.ccu.service.jtt808-1078.o: cxx ../applications/idvr/idvr.ccu/service.jtt808-1078.cpp
build obj/applications/idvr/idvr.ccu/idvr.ccu.service.private.o: cxx ../applications/idvr/idvr.ccu/service.private.cpp

build ./bin/idvr.ccu: link obj/applications/idvr/idvr.ccu/mcu.msg.handler/idvr.ccu.mcu.msg.handler.o obj/applications/idvr/idvr.ccu/sms.recieve.handler/idvr.ccu.sms.recieve.handler.o obj/applications/idvr/idvr.ccu/idvr.ccu.main.o obj/applications/idvr/idvr.ccu/idvr.ccu.manager.o obj/applications/idvr/idvr.ccu/idvr.ccu.controller.o obj/applications/idvr/idvr.ccu/idvr.ccu.service.o obj/applications/idvr/idvr.ccu/idvr.ccu.service.jtt808-1078.o obj/applications/idvr/idvr.ccu/idvr.ccu.service.private.o ./libalgo.so ./libprot.jtt.so ./libprot.anncr.sanfeng.so ./libprot.middle.ware.so ./libprot.jtt808-1078.so ./libroadnet.so ./libmystd.so ./libfilelog.so ./libtts.so ./libimu.so ./libipcAgent.so ./libsocketcmd.so ./libmessage.so ./libringbuf.so ./libflowWrap.so ./libproperty.so ./libs/libcurl_static.a ./libmsgpackc.so ./libs/liboss_static.a ./libSQLiteCpp.so ./libphone.so ./libevservice.so ./libfileprop.so ./libfallocate.so ./libflow.so ./libs/libcrypto_static.a ./libs/libssl_static.a ./libs/libzlib_static.a ./libsqlite.so
  ldflags = -L/home/<USER>/project/m5pro/applications/vendor/8838_sdk4/lib/share//opencv_9.1.0 -lopencv_imgcodecs -lopencv_imgproc -lopencv_core -ldl -L/home/<USER>/project/m5pro/out -Wl,-rpath-link=/home/<USER>/project/m5pro/out -ldl -lc -rdynamic -lpthread -pie
  libs =
  output_extension = 
  output_dir = .
