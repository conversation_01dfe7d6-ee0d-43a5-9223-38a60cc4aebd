defines = -DLOG_TAG_STR=expand
include_dirs = -I../applications/idvr/expand -I../applications/protocol/prot.jtt808-1078/io.service -I../applications/protocol/algo -I../applications/idvr/expand/prot -I../applications/idvr/include/idvr -I../third_party/rapidjson-1.1.0 -I../third_party/nlohmann_json -I../third_party/msgpack-c/include -I../foundation/communication/libflow/include -I../foundation/communication/libflow/flowWrap -I../foundation/communication/message -I../foundation/communication/property -I../applications/protocol/algo -I../applications/vendor/8838_sdk4/include -I../foundation/base/core/filelog -I../foundation/base/core/mystd/include -I../foundation/base/service/imu/libimu -I../foundation/communication/ipcAgent/include -I../foundation/communication/libflow/include -I../foundation/communication/libflow/flowWrap -I../foundation/communication/message -I../foundation/communication/property -I../third_party/nlohmann_json -I../third_party/msgpack-c/include -I../foundation/base/core/mystd -I../foundation/base/core/mystd/include -I../foundation/base/core/filelog -I../foundation/base/service/tts/libtts -I../foundation/communication/property -I../foundation/base/core/nmea_decode/include -I../foundation/communication/ipcAgent/include -I../foundation/communication/socketcmd/include -I../foundation/communication/socketcmd/include/socketcmd -I../foundation/communication/ringbuf -I../foundation/communication/libflow/flowWrap -I../third_party/msgpack-c/include -I../foundation/communication/libflow/include -I../foundation/communication/message -I../third_party/msgpack-c/include
cflags = -nostdlib -fno-common -fno-builtin -fno-strict-aliasing -funwind-tables -fsigned-char -DCROSS_PLAT_32 -O3 -fPIE
cflags_cc = -Wno-unused-parameter -Wno-psabi -std=gnu++2a -nostdlib -fno-common -fno-builtin -fno-strict-aliasing -funwind-tables -DCROSS_PLAT_32 -O3 -fpermissive -fPIE
target_output_name = expand

build obj/applications/idvr/expand/expand.main.o: cxx ../applications/idvr/expand/main.cpp
build obj/applications/idvr/expand/expand.cmdline.o: cxx ../applications/idvr/expand/cmdline.cpp
build obj/applications/idvr/expand/expand.devicehub.o: cxx ../applications/idvr/expand/devicehub.cpp
build obj/applications/idvr/expand/expand.expand.o: cxx ../applications/idvr/expand/expand.cpp
build obj/applications/idvr/expand/expand.transporter.o: cxx ../applications/idvr/expand/transporter.cpp
build obj/applications/idvr/expand/expand.transporter.slot.o: cxx ../applications/idvr/expand/transporter.slot.cpp
build obj/applications/idvr/expand/expand.worklooper.o: cxx ../applications/idvr/expand/worklooper.cpp
build obj/applications/idvr/expand/prot/expand.algoOutputCanProt.o: cxx ../applications/idvr/expand/prot/algoOutputCanProt.cpp
build obj/applications/idvr/expand/prot/expand.alertor.o: cxx ../applications/idvr/expand/prot/alertor.cpp
build obj/applications/idvr/expand/prot/expand.displed.o: cxx ../applications/idvr/expand/prot/displed.cpp
build obj/applications/idvr/expand/prot/expand.speedled.o: cxx ../applications/idvr/expand/prot/speedled.cpp
build obj/applications/idvr/expand/prot/expand.tahsensor.o: cxx ../applications/idvr/expand/prot/tahsensor.cpp
build obj/applications/idvr/expand/prot/expand.tpms.haoyue.o: cxx ../applications/idvr/expand/prot/tpms.haoyue.cpp
build obj/applications/idvr/expand/prot/expand.weightSensor.o: cxx ../applications/idvr/expand/prot/weightSensor.cpp
build obj/applications/idvr/expand/prot/expand.f9kGPS.o: cxx ../applications/idvr/expand/prot/f9kGPS.cpp
build obj/applications/idvr/expand/prot/expand.zfRadar.o: cxx ../applications/idvr/expand/prot/zfRadar.cpp
build obj/applications/idvr/expand/prot/expand.f9kDisp.o: cxx ../applications/idvr/expand/prot/f9kDisp.cpp
build obj/applications/idvr/expand/prot/expand.f9kGpsNovatelMsg.o: cxx ../applications/idvr/expand/prot/f9kGpsNovatelMsg.cpp
build obj/applications/idvr/expand/prot/expand.Display.xunjian.o: cxx ../applications/idvr/expand/prot/Display.xunjian.cpp
build obj/applications/idvr/expand/prot/expand.canDisplay.o: cxx ../applications/idvr/expand/prot/canDisplay.cpp
build obj/applications/idvr/expand/prot/expand.FuelMeter.ChangRun.o: cxx ../applications/idvr/expand/prot/FuelMeter.ChangRun.cpp
build obj/applications/idvr/expand/prot/expand.FuelMeter.FuTai.o: cxx ../applications/idvr/expand/prot/FuelMeter.FuTai.cpp
build obj/applications/idvr/expand/prot/expand.LiquidMeter.FuTai.o: cxx ../applications/idvr/expand/prot/LiquidMeter.FuTai.cpp
build obj/applications/idvr/expand/prot/expand.WeightSensor.FuTai.o: cxx ../applications/idvr/expand/prot/WeightSensor.FuTai.cpp
build obj/applications/idvr/expand/prot/expand.AnKaiCanProt.o: cxx ../applications/idvr/expand/prot/AnKaiCanProt.cpp
build obj/applications/idvr/expand/prot/expand.passenger.flow.meter.o: cxx ../applications/idvr/expand/prot/passenger.flow.meter.cpp
build obj/applications/idvr/expand/prot/expand.bsdAlert.XiaMenCiBei.o: cxx ../applications/idvr/expand/prot/bsdAlert.XiaMenCiBei.cpp
build obj/applications/idvr/expand/prot/expand.SuZhouJinLong.o: cxx ../applications/idvr/expand/prot/SuZhouJinLong.cpp
build obj/applications/idvr/expand/prot/expand.AlinkAlertor.RongSheng.o: cxx ../applications/idvr/expand/prot/AlinkAlertor.RongSheng.cpp
build obj/applications/idvr/expand/prot/expand.LongAnAlertor.RongSheng.o: cxx ../applications/idvr/expand/prot/LongAnAlertor.RongSheng.cpp
build obj/applications/idvr/expand/prot/expand.BsdCanInfo.RongSheng.o: cxx ../applications/idvr/expand/prot/BsdCanInfo.RongSheng.cpp
build obj/applications/idvr/expand/prot/expand.AlgoCanDisplay.o: cxx ../applications/idvr/expand/prot/AlgoCanDisplay.cpp
build obj/applications/idvr/expand/prot/expand.NormalCan1Out.o: cxx ../applications/idvr/expand/prot/NormalCan1Out.cpp
build obj/applications/idvr/expand/prot/expand.XiaMenShuoQi.CanProt.o: cxx ../applications/idvr/expand/prot/XiaMenShuoQi.CanProt.cpp

build ./bin/expand: link obj/applications/idvr/expand/expand.main.o obj/applications/idvr/expand/expand.cmdline.o obj/applications/idvr/expand/expand.devicehub.o obj/applications/idvr/expand/expand.expand.o obj/applications/idvr/expand/expand.transporter.o obj/applications/idvr/expand/expand.transporter.slot.o obj/applications/idvr/expand/expand.worklooper.o obj/applications/idvr/expand/prot/expand.algoOutputCanProt.o obj/applications/idvr/expand/prot/expand.alertor.o obj/applications/idvr/expand/prot/expand.displed.o obj/applications/idvr/expand/prot/expand.speedled.o obj/applications/idvr/expand/prot/expand.tahsensor.o obj/applications/idvr/expand/prot/expand.tpms.haoyue.o obj/applications/idvr/expand/prot/expand.weightSensor.o obj/applications/idvr/expand/prot/expand.f9kGPS.o obj/applications/idvr/expand/prot/expand.zfRadar.o obj/applications/idvr/expand/prot/expand.f9kDisp.o obj/applications/idvr/expand/prot/expand.f9kGpsNovatelMsg.o obj/applications/idvr/expand/prot/expand.Display.xunjian.o obj/applications/idvr/expand/prot/expand.canDisplay.o obj/applications/idvr/expand/prot/expand.FuelMeter.ChangRun.o obj/applications/idvr/expand/prot/expand.FuelMeter.FuTai.o obj/applications/idvr/expand/prot/expand.LiquidMeter.FuTai.o obj/applications/idvr/expand/prot/expand.WeightSensor.FuTai.o obj/applications/idvr/expand/prot/expand.AnKaiCanProt.o obj/applications/idvr/expand/prot/expand.passenger.flow.meter.o obj/applications/idvr/expand/prot/expand.bsdAlert.XiaMenCiBei.o obj/applications/idvr/expand/prot/expand.SuZhouJinLong.o obj/applications/idvr/expand/prot/expand.AlinkAlertor.RongSheng.o obj/applications/idvr/expand/prot/expand.LongAnAlertor.RongSheng.o obj/applications/idvr/expand/prot/expand.BsdCanInfo.RongSheng.o obj/applications/idvr/expand/prot/expand.AlgoCanDisplay.o obj/applications/idvr/expand/prot/expand.NormalCan1Out.o obj/applications/idvr/expand/prot/expand.XiaMenShuoQi.CanProt.o ./libalgo.so ./libmystd.so ./libfilelog.so ./libtts.so ./libnmea_decode.so ./libipcAgent.so ./libsocketcmd.so ./libringbuf.so ./libflowWrap.so ./libproperty.so ./libmessage.so ./libmsgpackc.so ./libflow.so
  ldflags = -L/home/<USER>/project/m5pro/applications/vendor/8838_sdk4/lib/share//opencv_9.1.0 -lopencv_imgcodecs -lopencv_imgproc -lopencv_core -ldl -pthread -L/home/<USER>/project/m5pro/out -Wl,-rpath-link=/home/<USER>/project/m5pro/out -ldl -lc -rdynamic -lpthread -pie
  libs =
  output_extension = 
  output_dir = .
