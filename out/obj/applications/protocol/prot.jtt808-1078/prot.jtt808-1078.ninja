defines =
include_dirs = -I../applications/protocol/prot.jtt808-1078 -I../applications/protocol/prot.jtt808-1078/area -I../applications/protocol/prot.jtt808-1078/faceid -I../applications/protocol/prot.jtt808-1078/utils -I../applications/protocol/prot.jtt808-1078/base -I../applications/protocol/prot.jtt808-1078/chengwei -I../applications/protocol/prot.jtt808-1078/chongqing -I../applications/protocol/prot.jtt808-1078/chengdu.bus -I../applications/protocol/prot.jtt808-1078/guangdong -I../applications/protocol/prot.jtt808-1078/henan.telecom -I../applications/protocol/prot.jtt808-1078/hualan.tec -I../applications/protocol/prot.jtt808-1078/huoyun -I../applications/protocol/prot.jtt808-1078/jiangsu.puhuo -I../applications/protocol/prot.jtt808-1078/lvcheng -I../applications/protocol/prot.jtt808-1078/nanjing -I../applications/protocol/prot.jtt808-1078/ningbo.zhatu -I../applications/protocol/prot.jtt808-1078/roadnet.mngr -I../applications/protocol/prot.jtt808-1078/sichuan -I../applications/protocol/prot.jtt808-1078/shengpinganfang -I../applications/protocol/prot.jtt808-1078/ttx -I../applications/protocol/prot.jtt808-1078/wuxi.tianyi -I../applications/protocol/prot.jtt808-1078/zhoushan -I../applications/protocol/prot.jtt808-1078/ztc -I../applications/protocol/prot.jtt808-1078/zf.bsd -I../applications/protocol/prot.jtt808-1078/xiashi -I../applications/protocol/prot.jtt808-1078/sanyi -I../applications/protocol/prot.jtt808-1078/xiangbiao -I../applications/protocol/prot.jtt808-1078/io.service -I../applications/idvr/include/idvr -I../applications/protocol/algo -I../applications/protocol/road.net/libroadnet -I../applications/vendor/8838_sdk4/include -I../foundation/base/core/filelog -I../foundation/base/service/imu/libimu -I../foundation/base/core/mystd/include -I../foundation/communication/socketcmd/include -I../foundation/communication/ipcAgent/include -I../foundation/communication/libflow/include -I../foundation/communication/libflow/flowWrap -I../foundation/communication/message -I../foundation/communication/property -I../foundation/multimedia/libsrc/libfallocate -I../foundation/multimedia/libsrc/libstrmts -I../third_party/nlohmann_json -I../third_party/msgpack-c/include -I../third_party/SQLiteCpp/include -I../third_party/SQLiteCpp/sqlite3 -I../third_party/freetype2/freetype-2.11.1/include -I../foundation/base/core/mystd -I../foundation/base/core/mystd/include -I../foundation/base/core/filelog -I../foundation/base/service/tts/libtts -I../foundation/communication/property -I../foundation/base/service/imu/libimu -I../foundation/base/core/json-c-util -I../foundation/communication/ipcAgent/include -I../foundation/communication/socketcmd/include -I../foundation/communication/socketcmd/include/socketcmd -I../foundation/communication/message -I../foundation/communication/ringbuf -I../foundation/communication/libflow/flowWrap -I../third_party/msgpack-c/include -I../foundation/communication/libflow/include -I../foundation/communication/fileprop -I../foundation/multimedia/libsrc/libstrmts -I../foundation/multimedia/libsrc/libfallocate -I../third_party/curl/include -I../third_party/liboss/include -I../third_party/liboss/include/alibabacloud -I../third_party/liboss/include/alibabacloud/oss -I../third_party/freetype2/build/include/freetype2
cflags = -nostdlib -fno-common -fno-builtin -fno-strict-aliasing -funwind-tables -fsigned-char -DCROSS_PLAT_32 -O3 -fPIC
cflags_cc = -Wno-psabi -Wno-unused-parameter -std=c++2a -nostdlib -fno-common -fno-builtin -fno-strict-aliasing -funwind-tables -DCROSS_PLAT_32 -O3 -fpermissive -fPIC
target_output_name = libprot.jtt808-1078

build obj/applications/protocol/prot.jtt808-1078/area/libprot.jtt808-1078.prot.jtt808-1078.area.o: cxx ../applications/protocol/prot.jtt808-1078/area/prot.jtt808-1078.area.cpp
build obj/applications/protocol/prot.jtt808-1078/faceid/libprot.jtt808-1078.face_recog.o: cxx ../applications/protocol/prot.jtt808-1078/faceid/face_recog.cpp
build obj/applications/protocol/prot.jtt808-1078/faceid/libprot.jtt808-1078.face_recog_v2.o: cxx ../applications/protocol/prot.jtt808-1078/faceid/face_recog_v2.cpp
build obj/applications/protocol/prot.jtt808-1078/io.service/libprot.jtt808-1078.expand.receiver.o: cxx ../applications/protocol/prot.jtt808-1078/io.service/expand.receiver.cpp
build obj/applications/protocol/prot.jtt808-1078/io.service/libprot.jtt808-1078.in.service.o: cxx ../applications/protocol/prot.jtt808-1078/io.service/in.service.cpp
build obj/applications/protocol/prot.jtt808-1078/io.service/libprot.jtt808-1078.out.service.o: cxx ../applications/protocol/prot.jtt808-1078/io.service/out.service.cpp
build obj/applications/protocol/prot.jtt808-1078/io.service/libprot.jtt808-1078.service.data.o: cxx ../applications/protocol/prot.jtt808-1078/io.service/service.data.cpp
build obj/applications/protocol/prot.jtt808-1078/roadnet.mngr/libprot.jtt808-1078.roadnetManager.o: cxx ../applications/protocol/prot.jtt808-1078/roadnet.mngr/roadnetManager.cpp
build obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.rec.alarm.manager.o: cxx ../applications/protocol/prot.jtt808-1078/utils/rec.alarm.manager.cpp
build obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.car.status.cache.o: cxx ../applications/protocol/prot.jtt808-1078/utils/car.status.cache.cpp
build obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.location.db.helper.o: cxx ../applications/protocol/prot.jtt808-1078/utils/location.db.helper.cpp
build obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.sqlite.data.base.o: cxx ../applications/protocol/prot.jtt808-1078/utils/sqlite.data.base.cpp
build obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.prot.config.helper.o: cxx ../applications/protocol/prot.jtt808-1078/utils/prot.config.helper.cpp
build obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.database.schema.o: cxx ../applications/protocol/prot.jtt808-1078/utils/database.schema.cpp
build obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.database.helper.o: cxx ../applications/protocol/prot.jtt808-1078/utils/database.helper.cpp
build obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.wakeup.manager.o: cxx ../applications/protocol/prot.jtt808-1078/utils/wakeup.manager.cpp
build obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.media.cmd.manager.o: cxx ../applications/protocol/prot.jtt808-1078/utils/media.cmd.manager.cpp
build obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.freetype_overlay.o: cxx ../applications/protocol/prot.jtt808-1078/utils/freetype_overlay.cpp
build obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.RecTsSegExport.o: cxx ../applications/protocol/prot.jtt808-1078/utils/RecTsSegExport.cpp
build obj/applications/protocol/prot.jtt808-1078/base/libprot.jtt808-1078.prot.jtt808-1078.data.o: cxx ../applications/protocol/prot.jtt808-1078/base/prot.jtt808-1078.data.cpp
build obj/applications/protocol/prot.jtt808-1078/base/libprot.jtt808-1078.prot.jtt808-1078.msg.o: cxx ../applications/protocol/prot.jtt808-1078/base/prot.jtt808-1078.msg.cpp
build obj/applications/protocol/prot.jtt808-1078/base/libprot.jtt808-1078.prot.jtt808-base.o: cxx ../applications/protocol/prot.jtt808-1078/base/prot.jtt808-base.cpp
build obj/applications/protocol/prot.jtt808-1078/base/libprot.jtt808-1078.prot.jtt808-1078.client.o: cxx ../applications/protocol/prot.jtt808-1078/base/prot.jtt808-1078.client.cpp
build obj/applications/protocol/prot.jtt808-1078/base/libprot.jtt808-1078.prot.jtt808-1078.tcpclient.o: cxx ../applications/protocol/prot.jtt808-1078/base/prot.jtt808-1078.tcpclient.cpp
build obj/applications/protocol/prot.jtt808-1078/base/libprot.jtt808-1078.prot.jtt808-1078.upload.o: cxx ../applications/protocol/prot.jtt808-1078/base/prot.jtt808-1078.upload.cpp
build obj/applications/protocol/prot.jtt808-1078/base/libprot.jtt808-1078.prot.jtt808-1078.ftp.o: cxx ../applications/protocol/prot.jtt808-1078/base/prot.jtt808-1078.ftp.cpp
build obj/applications/protocol/prot.jtt808-1078/base/libprot.jtt808-1078.oss.upload.o: cxx ../applications/protocol/prot.jtt808-1078/base/oss.upload.cpp
build obj/applications/protocol/prot.jtt808-1078/base/libprot.jtt808-1078.al.oss.upload.o: cxx ../applications/protocol/prot.jtt808-1078/base/al.oss.upload.cpp
build obj/applications/protocol/prot.jtt808-1078/chengdu.bus/libprot.jtt808-1078.prot.jtt808-1078_chengdu.bus.o: cxx ../applications/protocol/prot.jtt808-1078/chengdu.bus/prot.jtt808-1078_chengdu.bus.cpp
build obj/applications/protocol/prot.jtt808-1078/chongqing/libprot.jtt808-1078.prot.jtt808-1078_chongqing.o: cxx ../applications/protocol/prot.jtt808-1078/chongqing/prot.jtt808-1078_chongqing.cpp
build obj/applications/protocol/prot.jtt808-1078/chengwei/libprot.jtt808-1078.prot.jtt808-1078_chengwei.o: cxx ../applications/protocol/prot.jtt808-1078/chengwei/prot.jtt808-1078_chengwei.cpp
build obj/applications/protocol/prot.jtt808-1078/guangdong/libprot.jtt808-1078.prot.jtt808-1078_guangdong.o: cxx ../applications/protocol/prot.jtt808-1078/guangdong/prot.jtt808-1078_guangdong.cpp
build obj/applications/protocol/prot.jtt808-1078/henan.telecom/libprot.jtt808-1078.prot.jtt808-1078_henan_telecom.o: cxx ../applications/protocol/prot.jtt808-1078/henan.telecom/prot.jtt808-1078_henan_telecom.cpp
build obj/applications/protocol/prot.jtt808-1078/hualan.tec/libprot.jtt808-1078.prot.jtt808-1078_hualanTec.o: cxx ../applications/protocol/prot.jtt808-1078/hualan.tec/prot.jtt808-1078_hualanTec.cpp
build obj/applications/protocol/prot.jtt808-1078/huoyun/libprot.jtt808-1078.prot.jtt808-1078_huoyun.o: cxx ../applications/protocol/prot.jtt808-1078/huoyun/prot.jtt808-1078_huoyun.cpp
build obj/applications/protocol/prot.jtt808-1078/jiangsu.puhuo/libprot.jtt808-1078.prot.jtt808-1078_jiangsuPuHuo.o: cxx ../applications/protocol/prot.jtt808-1078/jiangsu.puhuo/prot.jtt808-1078_jiangsuPuHuo.cpp
build obj/applications/protocol/prot.jtt808-1078/nanjing/libprot.jtt808-1078.prot.jtt808-1078_nanjing_download.o: cxx ../applications/protocol/prot.jtt808-1078/nanjing/prot.jtt808-1078_nanjing_download.cpp
build obj/applications/protocol/prot.jtt808-1078/nanjing/libprot.jtt808-1078.prot.jtt808-1078_nanjing.o: cxx ../applications/protocol/prot.jtt808-1078/nanjing/prot.jtt808-1078_nanjing.cpp
build obj/applications/protocol/prot.jtt808-1078/ningbo.zhatu/libprot.jtt808-1078.prot.jtt808-1078_ningbozhatu.o: cxx ../applications/protocol/prot.jtt808-1078/ningbo.zhatu/prot.jtt808-1078_ningbozhatu.cpp
build obj/applications/protocol/prot.jtt808-1078/sichuan/libprot.jtt808-1078.prot.jtt808-1078_sichuan.o: cxx ../applications/protocol/prot.jtt808-1078/sichuan/prot.jtt808-1078_sichuan.cpp
build obj/applications/protocol/prot.jtt808-1078/shengpinganfang/libprot.jtt808-1078.prot.jtt808-1078_shengpinganfang.o: cxx ../applications/protocol/prot.jtt808-1078/shengpinganfang/prot.jtt808-1078_shengpinganfang.cpp
build obj/applications/protocol/prot.jtt808-1078/ttx/libprot.jtt808-1078.prot.jtt808-1078_ttx.o: cxx ../applications/protocol/prot.jtt808-1078/ttx/prot.jtt808-1078_ttx.cpp
build obj/applications/protocol/prot.jtt808-1078/ztc/libprot.jtt808-1078.prot.jtt808-1078_ZTC_MiniEye.o: cxx ../applications/protocol/prot.jtt808-1078/ztc/prot.jtt808-1078_ZTC_MiniEye.cpp
build obj/applications/protocol/prot.jtt808-1078/zhoushan/libprot.jtt808-1078.prot.jtt808-1078_zhoushan.o: cxx ../applications/protocol/prot.jtt808-1078/zhoushan/prot.jtt808-1078_zhoushan.cpp
build obj/applications/protocol/prot.jtt808-1078/zf.bsd/libprot.jtt808-1078.prot.jtt808-1078_zf_bsd.o: cxx ../applications/protocol/prot.jtt808-1078/zf.bsd/prot.jtt808-1078_zf_bsd.cpp
build obj/applications/protocol/prot.jtt808-1078/xiashi/libprot.jtt808-1078.prot.jtt808-1078_xiashi.o: cxx ../applications/protocol/prot.jtt808-1078/xiashi/prot.jtt808-1078_xiashi.cpp
build obj/applications/protocol/prot.jtt808-1078/udas/libprot.jtt808-1078.prot.jtt808-1078_udas.o: cxx ../applications/protocol/prot.jtt808-1078/udas/prot.jtt808-1078_udas.cpp
build obj/applications/protocol/prot.jtt808-1078/sanyi/libprot.jtt808-1078.prot.jtt808-1078_sanyi.o: cxx ../applications/protocol/prot.jtt808-1078/sanyi/prot.jtt808-1078_sanyi.cpp
build obj/applications/protocol/prot.jtt808-1078/xiangbiao/libprot.jtt808-1078.prot.jtt808-1078_xiangbiao.o: cxx ../applications/protocol/prot.jtt808-1078/xiangbiao/prot.jtt808-1078_xiangbiao.cpp
build obj/applications/protocol/prot.jtt808-1078/crosswalk.decel/libprot.jtt808-1078.prot.jtt808-1078_crosswalkDecel.o: cxx ../applications/protocol/prot.jtt808-1078/crosswalk.decel/prot.jtt808-1078_crosswalkDecel.cpp

build ./libprot.jtt808-1078.so: solink obj/applications/protocol/prot.jtt808-1078/area/libprot.jtt808-1078.prot.jtt808-1078.area.o obj/applications/protocol/prot.jtt808-1078/faceid/libprot.jtt808-1078.face_recog.o obj/applications/protocol/prot.jtt808-1078/faceid/libprot.jtt808-1078.face_recog_v2.o obj/applications/protocol/prot.jtt808-1078/io.service/libprot.jtt808-1078.expand.receiver.o obj/applications/protocol/prot.jtt808-1078/io.service/libprot.jtt808-1078.in.service.o obj/applications/protocol/prot.jtt808-1078/io.service/libprot.jtt808-1078.out.service.o obj/applications/protocol/prot.jtt808-1078/io.service/libprot.jtt808-1078.service.data.o obj/applications/protocol/prot.jtt808-1078/roadnet.mngr/libprot.jtt808-1078.roadnetManager.o obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.rec.alarm.manager.o obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.car.status.cache.o obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.location.db.helper.o obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.sqlite.data.base.o obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.prot.config.helper.o obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.database.schema.o obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.database.helper.o obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.wakeup.manager.o obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.media.cmd.manager.o obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.freetype_overlay.o obj/applications/protocol/prot.jtt808-1078/utils/libprot.jtt808-1078.RecTsSegExport.o obj/applications/protocol/prot.jtt808-1078/base/libprot.jtt808-1078.prot.jtt808-1078.data.o obj/applications/protocol/prot.jtt808-1078/base/libprot.jtt808-1078.prot.jtt808-1078.msg.o obj/applications/protocol/prot.jtt808-1078/base/libprot.jtt808-1078.prot.jtt808-base.o obj/applications/protocol/prot.jtt808-1078/base/libprot.jtt808-1078.prot.jtt808-1078.client.o obj/applications/protocol/prot.jtt808-1078/base/libprot.jtt808-1078.prot.jtt808-1078.tcpclient.o obj/applications/protocol/prot.jtt808-1078/base/libprot.jtt808-1078.prot.jtt808-1078.upload.o obj/applications/protocol/prot.jtt808-1078/base/libprot.jtt808-1078.prot.jtt808-1078.ftp.o obj/applications/protocol/prot.jtt808-1078/base/libprot.jtt808-1078.oss.upload.o obj/applications/protocol/prot.jtt808-1078/base/libprot.jtt808-1078.al.oss.upload.o obj/applications/protocol/prot.jtt808-1078/chengdu.bus/libprot.jtt808-1078.prot.jtt808-1078_chengdu.bus.o obj/applications/protocol/prot.jtt808-1078/chongqing/libprot.jtt808-1078.prot.jtt808-1078_chongqing.o obj/applications/protocol/prot.jtt808-1078/chengwei/libprot.jtt808-1078.prot.jtt808-1078_chengwei.o obj/applications/protocol/prot.jtt808-1078/guangdong/libprot.jtt808-1078.prot.jtt808-1078_guangdong.o obj/applications/protocol/prot.jtt808-1078/henan.telecom/libprot.jtt808-1078.prot.jtt808-1078_henan_telecom.o obj/applications/protocol/prot.jtt808-1078/hualan.tec/libprot.jtt808-1078.prot.jtt808-1078_hualanTec.o obj/applications/protocol/prot.jtt808-1078/huoyun/libprot.jtt808-1078.prot.jtt808-1078_huoyun.o obj/applications/protocol/prot.jtt808-1078/jiangsu.puhuo/libprot.jtt808-1078.prot.jtt808-1078_jiangsuPuHuo.o obj/applications/protocol/prot.jtt808-1078/nanjing/libprot.jtt808-1078.prot.jtt808-1078_nanjing_download.o obj/applications/protocol/prot.jtt808-1078/nanjing/libprot.jtt808-1078.prot.jtt808-1078_nanjing.o obj/applications/protocol/prot.jtt808-1078/ningbo.zhatu/libprot.jtt808-1078.prot.jtt808-1078_ningbozhatu.o obj/applications/protocol/prot.jtt808-1078/sichuan/libprot.jtt808-1078.prot.jtt808-1078_sichuan.o obj/applications/protocol/prot.jtt808-1078/shengpinganfang/libprot.jtt808-1078.prot.jtt808-1078_shengpinganfang.o obj/applications/protocol/prot.jtt808-1078/ttx/libprot.jtt808-1078.prot.jtt808-1078_ttx.o obj/applications/protocol/prot.jtt808-1078/ztc/libprot.jtt808-1078.prot.jtt808-1078_ZTC_MiniEye.o obj/applications/protocol/prot.jtt808-1078/zhoushan/libprot.jtt808-1078.prot.jtt808-1078_zhoushan.o obj/applications/protocol/prot.jtt808-1078/zf.bsd/libprot.jtt808-1078.prot.jtt808-1078_zf_bsd.o obj/applications/protocol/prot.jtt808-1078/xiashi/libprot.jtt808-1078.prot.jtt808-1078_xiashi.o obj/applications/protocol/prot.jtt808-1078/udas/libprot.jtt808-1078.prot.jtt808-1078_udas.o obj/applications/protocol/prot.jtt808-1078/sanyi/libprot.jtt808-1078.prot.jtt808-1078_sanyi.o obj/applications/protocol/prot.jtt808-1078/xiangbiao/libprot.jtt808-1078.prot.jtt808-1078_xiangbiao.o obj/applications/protocol/prot.jtt808-1078/crosswalk.decel/libprot.jtt808-1078.prot.jtt808-1078_crosswalkDecel.o ./libmystd.so ./libfilelog.so ./libtts.so ./libimu.so ./libjsonUtil.so ./libipcAgent.so ./libsocketcmd.so ./libmessage.so ./libringbuf.so ./libflowWrap.so ./libfileprop.so ./libproperty.so ./libstrmts.so ./libfallocate.so ./libs/libcurl_static.a ./libs/liboss_static.a ./libfreetype2.so ./libflow.so ./libs/libcrypto_static.a ./libs/libssl_static.a ./libs/libzlib_static.a
  ldflags = -L/home/<USER>/project/m5pro/out -Wl,-rpath-link=/home/<USER>/project/m5pro/out -ldl -lc -rdynamic -lpthread
  libs =
  output_extension = .so
  output_dir = .
