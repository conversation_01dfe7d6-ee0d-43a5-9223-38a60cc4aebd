defines =
include_dirs = -I../applications/protocol/prot.anncr.sanfeng -I../applications/vendor/8838_sdk4/include -I../foundation/base/core/filelog -I../foundation/base/core/mystd/include -I../foundation/communication/socketcmd/include -I../foundation/communication/ipcAgent/include -I../foundation/communication/libflow/include -I../foundation/communication/libflow/flowWrap -I../foundation/communication/message -I../foundation/communication/property -I../third_party/nlohmann_json -I../third_party/msgpack-c/include -I../applications/protocol/prot.jtt808-1078 -I../applications/protocol/prot.jtt808-1078/area -I../applications/protocol/prot.jtt808-1078/faceid -I../applications/protocol/prot.jtt808-1078/utils -I../applications/protocol/prot.jtt808-1078/base -I../applications/protocol/prot.jtt808-1078/chengwei -I../applications/protocol/prot.jtt808-1078/chongqing -I../applications/protocol/prot.jtt808-1078/chengdu.bus -I../applications/protocol/prot.jtt808-1078/guangdong -I../applications/protocol/prot.jtt808-1078/henan.telecom -I../applications/protocol/prot.jtt808-1078/hualan.tec -I../applications/protocol/prot.jtt808-1078/huoyun -I../applications/protocol/prot.jtt808-1078/jiangsu.puhuo -I../applications/protocol/prot.jtt808-1078/lvcheng -I../applications/protocol/prot.jtt808-1078/nanjing -I../applications/protocol/prot.jtt808-1078/ningbo.zhatu -I../applications/protocol/prot.jtt808-1078/roadnet.mngr -I../applications/protocol/prot.jtt808-1078/sichuan -I../applications/protocol/prot.jtt808-1078/shengpinganfang -I../applications/protocol/prot.jtt808-1078/ttx -I../applications/protocol/prot.jtt808-1078/wuxi.tianyi -I../applications/protocol/prot.jtt808-1078/zhoushan -I../applications/protocol/prot.jtt808-1078/ztc -I../applications/protocol/prot.jtt808-1078/zf.bsd -I../applications/protocol/prot.jtt808-1078/xiashi -I../applications/protocol/prot.jtt808-1078/sanyi -I../applications/protocol/prot.jtt808-1078/xiangbiao -I../applications/protocol/prot.jtt808-1078/io.service -I../applications/idvr/include/idvr -I../applications/protocol/algo -I../applications/protocol/road.net/libroadnet -I../applications/vendor/8838_sdk4/include -I../foundation/base/core/filelog -I../foundation/base/service/imu/libimu -I../foundation/base/core/mystd/include -I../foundation/communication/socketcmd/include -I../foundation/communication/ipcAgent/include -I../foundation/communication/libflow/include -I../foundation/communication/libflow/flowWrap -I../foundation/communication/message -I../foundation/communication/property -I../foundation/multimedia/libsrc/libfallocate -I../foundation/multimedia/libsrc/libstrmts -I../third_party/nlohmann_json -I../third_party/msgpack-c/include -I../third_party/SQLiteCpp/include -I../third_party/SQLiteCpp/sqlite3 -I../third_party/freetype2/freetype-2.11.1/include -I../foundation/base/core/mystd -I../foundation/base/core/mystd/include -I../foundation/base/core/filelog -I../foundation/base/service/tts/libtts -I../foundation/communication/property -I../foundation/communication/ipcAgent/include -I../foundation/communication/socketcmd/include -I../foundation/communication/socketcmd/include/socketcmd -I../foundation/communication/message -I../foundation/communication/ringbuf -I../foundation/communication/libflow/flowWrap -I../third_party/msgpack-c/include -I../foundation/communication/libflow/include
cflags = -nostdlib -fno-common -fno-builtin -fno-strict-aliasing -funwind-tables -fsigned-char -DCROSS_PLAT_32 -O3 -fPIC
cflags_cc = -Wno-psabi -Wno-unused-parameter -nostdlib -fno-common -fno-builtin -fno-strict-aliasing -funwind-tables -DCROSS_PLAT_32 -O3 -fpermissive -fPIC
target_output_name = libprot.anncr.sanfeng

build obj/applications/protocol/prot.anncr.sanfeng/libprot.anncr.sanfeng.prot.anncr.sanfeng.o: cxx ../applications/protocol/prot.anncr.sanfeng/prot.anncr.sanfeng.cpp

build ./libprot.anncr.sanfeng.so: solink obj/applications/protocol/prot.anncr.sanfeng/libprot.anncr.sanfeng.prot.anncr.sanfeng.o ./libprot.jtt808-1078.so ./libmystd.so ./libfilelog.so ./libtts.so ./libipcAgent.so ./libsocketcmd.so ./libmessage.so ./libringbuf.so ./libflowWrap.so ./libproperty.so ./libflow.so
  ldflags = -L/home/<USER>/project/m5pro/out -Wl,-rpath-link=/home/<USER>/project/m5pro/out -ldl -lc -rdynamic -lpthread
  libs =
  output_extension = .so
  output_dir = .
