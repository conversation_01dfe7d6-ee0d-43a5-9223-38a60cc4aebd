
#include "mystd.h"
#include "VdeChMnger.h"
#include "libvdec.h"

class VdecWrap
    : public my::Singleton<VdecWrap>
    , public my::thread
{
    friend class my::Singleton<VdecWrap>;
    private:
        VdecWrap() {
        }
    public:
        virtual ~VdecWrap() {
            stop();
        }
        bool init() {
            std::lock_guard<std::mutex> _lock_(mMtx);
            if (!mbInit) {
                //logd("sysModuleInit+\n");
                mbInit = sysModuleInit();
                //logd("sysModuleInit-\n");
            }
            mpVdecMngr = VdeChMnger::getInstance();
            return mpVdecMngr->init() && !start();
        }

        int32_t open(const VDEC_OPEN_PARAM_T & param) {
            //logd("init+%p\n", mpVdecMngr);
            std::lock_guard<std::mutex> _lock_(mMtx);
            if (mpVdecMngr != nullptr) {
                //logd("init+%p\n", mpVdecMngr);
                int32_t bgn = 0;
                int32_t i = bgn;
                for (; i < bgn + MAX_VDEC_CHN; i++) {
                    auto it = mChns.find(i);
                    if (it == mChns.end()) {
                        break;
                    } else {
                        //skip
                    }
                }
                if (i < bgn + MAX_VDEC_CHN) {
                    vdecMode_t vm = (param.type == VDEC_TYPE_H264) ? E_VDEC_MODE_H264 : E_VDEC_MODE_H265;
                    //logd("init+");
                    if (mpVdecMngr->init(i, vm, param.wIn, param.hIn, param.wOut, param.hOut)) {
                        if (mpVdecMngr->setDepth(i, 4) &&
                            mpVdecMngr->enable(i)) {
                            int32_t fd = mpVdecMngr->getfd(i);
                            mChns[i] = std::pair<int32_t, VDEC_OPEN_PARAM_T>(fd, param);
                            //logd("init -%d\n", i);
                            return i;
                        } else {
                            mpVdecMngr->uninit(i);
                        }
                    }
                }
            }
            return -1;
        }

        int32_t decode(int32_t decoder, const VDEC_DATA_T & in, VDEC_DATA_T * out = nullptr) {
            if ((decoder < 0) || (decoder >= MAX_VDEC_CHN + 2) || (mpVdecMngr == nullptr)) {
                return -1;
            }
            int32_t fd = -1;
            VDEC_OPEN_PARAM_T param;
            do {
                std::lock_guard<std::mutex> _lock_(mMtx);
                auto it = mChns.find(decoder);
                if (it != mChns.end()) {
                    fd = it->second.first;
                    param = it->second.second;
                } else {
                    return -2;
                }
            } while (0);
            int32_t tmoutms = 30;
            //logd("putstrm 1!");
            mpVdecMngr->freestrm(decoder);
            //logd("putstrm 2!");
            int32_t ret = mpVdecMngr->putstrm(decoder, in.data, in.length, in.pts, tmoutms);
            if (ret < 0) {
                loge("putstrm fail! %d", decoder);
                return ret;
            } else {
                //logd("putstrm succ!");
            }
            if (param.dataCb != nullptr) {
                return ret > 0;
            } else if (out) {
                int32_t fdTbl[1];
                bool status[1];
                fdTbl[0] = fd;
                int32_t ret = select(fdTbl, sizeof(fdTbl) / sizeof(fdTbl[0]), status, 1000);
                for (int i = 0; i < sizeof(fdTbl) / sizeof(fdTbl[0]); i++) {
                    if (status[i]) {
                        stream_t s;
                        mpVdecMngr->getstrm(decoder, s);
                        out->data           = (const char*)s.YUVStrm.data;
                        out->length         = s.YUVStrm.len;
                        out->pts            = s.YUVStrm.pts;
                        out->width          = s.YUVStrm.w;
                        out->height         = s.YUVStrm.h;
                        out->phy.phyYAddr   = s.YUVStrm.phyYAddr;
                        out->phy.phyUVAddr  = s.YUVStrm.phyUVAddr;
                        out->phy.dataLen    = s.YUVStrm.len;
                        //logd("coder %d!", it->first);
                        return 1;
                    }
                }
            }
            return -3;
        }
        bool decode_clear(int32_t decoder) {
            if ((decoder < 0) ||
                (decoder >= MAX_VDEC_CHN)) {
                return false;
            }
            std::lock_guard<std::mutex> _lock_(mMtx);
            auto it = mChns.find(decoder);
            if ((it != mChns.end()) && ((it->second.second.dataCb == nullptr))) {
                return mpVdecMngr->freestrm(it->first);
            }
            return false;
        }
        int32_t close(int32_t decoder) {
            if ((decoder < 0) || (decoder >= MAX_VDEC_CHN)) {
                return false;
            }
            std::lock_guard<std::mutex> _lock_(mMtx);
            auto it = mChns.find(decoder);
            if (it != mChns.end()) {
                if (!mpVdecMngr->disable(it->first)) {
                    return false;
                }
                if (mpVdecMngr->uninit(it->first)) {
                    mChns.erase(it);
                } else {
                    return false;
                }
            }
            return true;
        }
    private:
        virtual void run() {
            while(!exiting()) {
                int32_t fdTbl[MAX_VDEC_CHN] = {0};
                bool status[MAX_VDEC_CHN] = {false};
                std::map<int32_t, int32_t> fd2decoder;
                int32_t count = 0;
                do {
                    std::lock_guard<std::mutex> _lock_(mMtx);
                    count = mChns.size();
                } while(0);

                if (count) {
                    std::lock_guard<std::mutex> _lock_(mMtx);
                    count = 0;
                    for (auto & r : mChns) {
                        if (r.second.second.dataCb) {
                            fdTbl[count++] = r.second.first;
                        }
                        fd2decoder[r.second.first] = r.first;
                        if (count >= MAX_VDEC_CHN) {
                            break;
                        }
                    }
                }
                if (count) {
                    logd("count %d", count);
                    int32_t ret = select(fdTbl, count, status, 20);
                    for (int i = 0; i < sizeof(fdTbl) / sizeof(fdTbl[0]); i++) {
                        int32_t decoder = fd2decoder[fdTbl[i]];
                        if (status[i]) {
                            logd("decoder %d", decoder);
                            stream_t s;
                            mpVdecMngr->getstrm(decoder, s);
                            VDEC_DATA_T out;
                            out.data = (const char*)s.YUVStrm.data;
                            out.length = s.YUVStrm.len;
                            out.pts = s.YUVStrm.pts;
                            out.width = s.YUVStrm.w;
                            out.height = s.YUVStrm.h;
                            {
                                logd("decoder %d", decoder);
                                std::lock_guard<std::mutex> _lock_(mMtx);
                                if (mChns[decoder].second.dataCb) {
                                    logd("decoder %d", decoder);
                                    mChns[decoder].second.dataCb->onVdecData(decoder, out);
                                    logd("decoder %d", decoder);
                                    mpVdecMngr->freestrm(decoder);
                                    logd("decoder %d", decoder);
                                }
                            }
                        }
                    }
                } else {
                    usleep(1000000);
                }
            }
        }
    private:
        std::mutex  mMtx;
        bool  mbInit = false;
        VdeChMnger * mpVdecMngr = nullptr;
        std::map<int32_t, std::pair<int32_t, VDEC_OPEN_PARAM_T>> mChns;
};

SINGLETON_STATIC_INSTANCE(VdecWrap);

bool vdec_init()
{
    VdecWrap & vw = VdecWrap::getInstance();
    return vw.init();
}
int32_t vdec_open(const VDEC_OPEN_PARAM_T & param)
{
    VdecWrap & vw = VdecWrap::getInstance();
    return vw.open(param);
}
int32_t vdec_decode(int32_t decoder, const VDEC_DATA_T & in, VDEC_DATA_T * out)
{
    VdecWrap & vw = VdecWrap::getInstance();
    return vw.decode(decoder, in, out);
}
int32_t vdec_decode_clear(int32_t decoder)
{
    VdecWrap & vw = VdecWrap::getInstance();
    return vw.decode_clear(decoder);
}
int32_t vdec_close(int32_t decoder)
{
    VdecWrap & vw = VdecWrap::getInstance();
    return vw.close(decoder);
}