#ifndef __LIB_VDEC_H__
#define __LIB_VDEC_H__
#include <functional>
#define MAX_VDEC_CHN 4
typedef enum
{
    VDEC_TYPE_H264,
    VDEC_TYPE_H265
} VDEC_TYPE_E;

typedef struct {
    uint64_t    phyYAddr = 0;
    uint64_t    phyUVAddr = 0;
    uint32_t    dataLen = 0;
} PHY_MEMO_T;

typedef struct
{
    const char * data   = nullptr;
    int32_t      length = 0;
    int32_t      width  = 0;
    int32_t      height = 0;
    int32_t      index  = 0;
    uint64_t     pts    = 0;
    PHY_MEMO_T   phy;
} VDEC_DATA_T;

class IVdecDataCallback
{
    public:
        virtual int32_t onVdecData(int32_t decoder, const VDEC_DATA_T & data) = 0;
};
typedef struct
{
    VDEC_TYPE_E type = VDEC_TYPE_H264;
    int32_t wIn = 1280;
    int32_t hIn = 720;
    int32_t wOut = 1280;
    int32_t hOut = 720;
    IVdecDataCallback * dataCb = nullptr;
} VDEC_OPEN_PARAM_T;



bool vdec_init();

/*
    打开一个解码器
    返回解码器值>=0 成功, < 0失败
*/
int32_t vdec_open(const VDEC_OPEN_PARAM_T & param);

/*
    视频解码
    如果对应的vdec_open函数参数param中的回调如果为null，
    则vdec_decode结果从最后一个参数out输出，否则由回调输出；
    必须和vdec_decode_clear配对调用；
*/
int32_t vdec_decode(int32_t decoder, const VDEC_DATA_T & in, VDEC_DATA_T * out = nullptr);
int32_t vdec_decode_clear(int32_t decoder);

/*
    关闭视频解码器
*/
int32_t vdec_close(int32_t decoder);

#endif
