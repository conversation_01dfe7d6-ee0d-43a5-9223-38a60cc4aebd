#include "SensorMnger.h"

SensorMnger::SensorMnger()
{
    for (uint8_t i = 0; i < E_SNR_PAD_ID_MAX; i ++) {
        mSensor[i] = nullptr;
    }
}

SensorMnger::~SensorMnger()
{

}

bool SensorMnger::init(snrPadId_t snrPadId)
{
    if (! (E_SNR_PAD_ID_0 <= snrPadId && snrPadId < E_SNR_PAD_ID_MAX)) {
        return false;
    }

    if (mSensor[snrPadId] == nullptr) {
        mSensor[snrPadId] = new Sensor(snrPadId);

        if (mSensor[snrPadId]->init() == false) {
            loge("init error !");
            delete mSensor[snrPadId];
            mSensor[snrPadId] = nullptr;
            return false;
        }
    }

    return true;
}

bool SensorMnger::uninit(snrPadId_t snrPadId)
{
    if (! (E_SNR_PAD_ID_0 <= snrPadId && snrPadId < E_SNR_PAD_ID_MAX)) {
        return false;
    }

    if (mSensor[snrPadId] != nullptr) {
        mSensor[snrPadId]->disable();
    }

    delete mSensor[snrPadId];
    mSensor[snrPadId] = nullptr;

    return true;
}

bool SensorMnger::setPlane(snrPadId_t snrPadId, snrPlaneId_t planeId, uint16_t reslw, uint16_t reslh, uint8_t fps)
{
    if (! (E_SNR_PAD_ID_0 <= snrPadId && snrPadId < E_SNR_PAD_ID_MAX)) {
        return false;
    }

    if (mSensor[snrPadId] == nullptr) {
        return false;
    }

    return mSensor[snrPadId]->setPlane(planeId, reslw, reslh, fps);
}

bool SensorMnger::disable(snrPadId_t snrPadId)
{
    if (! (E_SNR_PAD_ID_0 <= snrPadId && snrPadId < E_SNR_PAD_ID_MAX)) {
        return false;
    }

    if (mSensor[snrPadId] == nullptr) {
        return false;
    }

    return mSensor[snrPadId]->disable();
}

bool SensorMnger::enable(snrPadId_t snrPadId)
{
    if (! (E_SNR_PAD_ID_0 <= snrPadId && snrPadId < E_SNR_PAD_ID_MAX)) {
        return false;
    }

    if (mSensor[snrPadId] == nullptr) {
        return false;
    }

    return mSensor[snrPadId]->enable();
}

bool SensorMnger::dump(snrPadId_t snrPadId)
{
    if (! (E_SNR_PAD_ID_0 <= snrPadId && snrPadId < E_SNR_PAD_ID_MAX)) {
        return false;
    }

    if (mSensor[snrPadId] == nullptr) {
        return false;
    }

    return mSensor[snrPadId]->dump();
}

SensorMnger *SensorMnger::getInstance()
{
    static std::mutex mutex;
    static SensorMnger *_instance = nullptr;

    if (_instance == nullptr) {
        std::lock_guard<std::mutex> lock(mutex);

        if (_instance == nullptr) {
            _instance = new SensorMnger();
        }
    }
    return _instance;
}
