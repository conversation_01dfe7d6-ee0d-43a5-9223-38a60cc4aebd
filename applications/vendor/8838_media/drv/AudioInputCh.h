#ifndef __AUDIO_INPUT_CH_H__
#define __AUDIO_INPUT_CH_H__

#include "defcomm.h"

#define AUDIO_INPORT_DEPTH_MAX_NUM  4

class AudioInputCh
{
public:
    AudioInputCh(audioInputDevId_t audioInputDevId, audioInputChnId_t audioInputChnId);
    ~AudioInputCh();

    bool init();
    bool uninit();

    bool enable();
    bool disable();

    bool setVolume(audioVolumeLevel_t audioVolumeLevel);

    int32_t getfd();
    bool getstrm(stream_t& stream, int32_t tmoutms = 100);
    bool freestrm();

private:
    bool setGain();
    bool setDepth(uint8_t depth);
    bool clrDepth();

private:
    audioInputDevId_t           mAuDevId;
    audioInputChnId_t           mAuChnId;

    bool                        mIsEnabled;
    bool                        mIsSetGain;

    uint8_t                     mDepth;
    bool                        mIsSetDepth;

    audioVolumeLevel_t          mVolumeLevel;

    MI_AUDIO_Frame_t            mAudioFrame;
    MI_AUDIO_AecFrame_t         mAecFrame;

    int32_t                     mFd;

    bool                        mIsNewStrm;

    uint32_t                    mFrameNum;
};

#endif
