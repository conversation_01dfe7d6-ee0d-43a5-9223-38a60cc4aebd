#include "SclOutport.h"

#define __SCL_OUTPORT_LOGD__(fmt, args...)    logd("sclDevId[%d] sclChnId[%u] sclOutportId[%d] " fmt, mSclDevId, mSclChnId, mSclOutportId, ## args)
#define __SCL_OUTPORT_LOGE__(fmt, args...)    loge("sclDevId[%d] sclChnId[%u] sclOutportId[%d] " fmt, mSclDevId, mSclChnId, mSclOutportId, ## args)

SclOutport::SclOutport(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId)
    : mFd(-1)
    , mSclDevId(sclDevId)
    , mSclChnId(sclChnId)
    , mSclOutportId(sclOutportId)
    , mIsEnabled(false)
    , mDepth(0)
    , mHandleIdx(0)
    , mFrameNum(0)
    , mPhyBufIdx(0)
    , mPhyFrameNum(0)
    , mIsPhyInited(false)
    , mIsSetDepth(false)
    , mReslW(0)
    , mReslH(0)
{
    for (uint8_t i = 0; i < SCL_OUTPORT_DEPTH_MAX_NUM; i ++) {
        mHandle[i] = 0;
    }

    for (uint8_t i = 0; i < SCL_PHY_BUF_MAX_NUM; i ++) {
        mPhyBufAddr[i] = 0;
    }
}

SclOutport::~SclOutport()
{
    uninit();
}

bool SclOutport::init(uint16_t inWidth, uint16_t inHeight, uint16_t outWidth, uint16_t outHeight)
{
    MI_S32 ret = -1;

    MI_SCL_OutPortParam_t miSclOutputParam;
    MI_SYS_WindowRect_t miOrigPortCrop;
    MI_SYS_WindowSize_t miOrigPortSize;

    inWidth = ALIGN_UP(inWidth, 2);
    inHeight = ALIGN_UP(inHeight, 2);

    outWidth = ALIGN_UP(outWidth, 2);
    outHeight = ALIGN_UP(outHeight, 2);

    __SCL_OUTPORT_LOGD__("in[%u x %d] out[%u x %u]", inWidth, inHeight, outWidth, outHeight);

    memset(&miSclOutputParam, 0, sizeof(MI_SCL_OutPortParam_t));
    memset(&miOrigPortCrop, 0, sizeof(MI_SYS_WindowRect_t));
    memset(&miOrigPortSize, 0, sizeof(MI_SYS_WindowSize_t));

    miOrigPortCrop.u16X = 0;
    miOrigPortCrop.u16Y = 0;
    miOrigPortCrop.u16Width = inWidth;
    miOrigPortCrop.u16Height = inHeight;

    miOrigPortSize.u16Width = inWidth;
    miOrigPortSize.u16Height = inHeight;

    memcpy(&miSclOutputParam.stSCLOutCropRect, &miOrigPortCrop, sizeof(MI_SYS_WindowRect_t));
    memcpy(&miSclOutputParam.stSCLOutputSize, &miOrigPortSize, sizeof(MI_SYS_WindowSize_t));
    miSclOutputParam.ePixelFormat = E_MI_SYS_PIXEL_FRAME_YUV_SEMIPLANAR_420;// YYU420_NV12
    miSclOutputParam.bMirror = 0;
    miSclOutputParam.bFlip = 0;

    miSclOutputParam.stSCLOutputSize.u16Width = outWidth;
    miSclOutputParam.stSCLOutputSize.u16Height = outHeight;

    if (! (E_SCL_DEV_ID_0 <= mSclDevId && mSclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (! (E_SCL_OUTPORT_ID_0 <= mSclOutportId && mSclOutportId < E_SCL_OUTPORT_ID_MAX)) {
        return false;
    }

    ret = MI_SCL_SetOutputPortParam(mSclDevId, mSclChnId, mSclOutportId, &miSclOutputParam);
    if (ret != MI_SUCCESS) {
        __SCL_OUTPORT_LOGE__("MI_SCL_SetOutputPortParam error ! err:%x", ret);
        return false;
    }

    mReslW = outWidth;
    mReslH = outHeight;

    return true;
}

bool SclOutport::uninit()
{
    disable();
    phyfree();

    return true;
}

bool SclOutport::setSize(uint16_t outWidth, uint16_t outHeight)
{
    MI_S32 ret = -1;
    MI_SCL_OutPortParam_t miSclOutputParam;

    bool isEnable = mIsEnabled;

    outWidth = ALIGN_UP(outWidth, 2);
    outHeight = ALIGN_UP(outHeight, 2);

    __SCL_OUTPORT_LOGD__("out[%u x %u]", outWidth, outHeight);

    if (mIsPhyInited == true) {
        __SCL_OUTPORT_LOGE__("not support change size because physical buffer has been initialized !");
        return false;
    }

    if (disable() == false) {
        __SCL_OUTPORT_LOGE__("disable error !");
        return false;
    }

    memset(&miSclOutputParam, 0, sizeof(MI_SCL_OutPortParam_t));

    if (! (E_SCL_DEV_ID_0 <= mSclDevId && mSclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (! (E_SCL_OUTPORT_ID_0 <= mSclOutportId && mSclOutportId < E_SCL_OUTPORT_ID_MAX)) {
        return false;
    }

    ret = MI_SCL_GetOutputPortParam(mSclDevId, mSclChnId, mSclOutportId, &miSclOutputParam);
    if (ret != MI_SUCCESS) {
        __SCL_OUTPORT_LOGE__("MI_SCL_GetOutputPortParam error ! err:%x", ret);
        return false;
    }

    miSclOutputParam.stSCLOutputSize.u16Width = outWidth;
    miSclOutputParam.stSCLOutputSize.u16Height = outHeight;

    ret = MI_SCL_SetOutputPortParam(mSclDevId, mSclChnId, mSclOutportId, &miSclOutputParam);
    if (ret != MI_SUCCESS) {
        __SCL_OUTPORT_LOGE__("MI_SCL_SetOutputPortParam error ! err:%x", ret);
        return false;
    }
    mReslW = outWidth;
    mReslH = outHeight;

    if (isEnable == true) {
        if (enable() == false) {
            __SCL_OUTPORT_LOGE__("enable error !");
            return false;
        }
    }

    return true;
}

bool SclOutport::setCrop(uint16_t x, uint16_t y, uint16_t w, uint16_t h)
{
    MI_S32 ret = -1;

    MI_SCL_OutPortParam_t miSclOutputParam;
    MI_SYS_WindowRect_t miOrigPortCrop;

    __SCL_OUTPORT_LOGD__("crop[%u, %u, %u, %u]", x, y, w, h);

    bool isEnable = mIsEnabled;

    if (mIsPhyInited == true) {
        __SCL_OUTPORT_LOGE__("not support change size because physical buffer has been initialized !");
        return false;
    }

    if (mSclDevId != E_SCL_DEV_ID_2) {

        disable();

        w = ALIGN_UP(w, 2);
        h = ALIGN_UP(h, 2);

        memset(&miSclOutputParam, 0, sizeof(MI_SCL_OutPortParam_t));
        memset(&miOrigPortCrop, 0, sizeof(MI_SYS_WindowRect_t));

        if (! (E_SCL_DEV_ID_0 <= mSclDevId && mSclDevId < E_SCL_DEV_ID_MAX)) {
            return false;
        }

        if (! (E_SCL_OUTPORT_ID_0 <= mSclOutportId && mSclOutportId < E_SCL_OUTPORT_ID_MAX)) {
            return false;
        }

        ret = MI_SCL_GetOutputPortParam(mSclDevId, mSclChnId, mSclOutportId, &miSclOutputParam);
        if (ret != MI_SUCCESS) {
            __SCL_OUTPORT_LOGE__("MI_SCL_GetOutputPortParam error ! err:%x", ret);
            return false;
        }

        miOrigPortCrop.u16X = x;
        miOrigPortCrop.u16Y = y;
        miOrigPortCrop.u16Width = w;
        miOrigPortCrop.u16Height = h;

        memcpy(&miSclOutputParam.stSCLOutCropRect, &miOrigPortCrop, sizeof(MI_SYS_WindowRect_t));
        miSclOutputParam.stSCLOutputSize.u16Width = w;
        miSclOutputParam.stSCLOutputSize.u16Height = h;

        ret = MI_SCL_SetOutputPortParam(mSclDevId, mSclChnId, mSclOutportId, &miSclOutputParam);
        if (ret != MI_SUCCESS) {
            __SCL_OUTPORT_LOGE__("MI_SCL_SetOutputPortParam error ! err:%x", ret);
            return false;
        }

        mReslW = w;
        mReslH = h;

        if (isEnable == true) {
            if (enable() == false) {
                __SCL_OUTPORT_LOGE__("enable error !");
                return false;
            }
        }
    } else {
        __SCL_OUTPORT_LOGE__("sclDevId[%d] not support !", mSclDevId);
        return false;
    }

    return true;
}

bool SclOutport::setMirror(bool isMirror)
{
    MI_S32 ret = -1;

    MI_SCL_OutPortParam_t miSclOutputParam;

    __SCL_OUTPORT_LOGD__("isMirror[%d]", isMirror);

    bool isEnable = mIsEnabled;

    disable();

    memset(&miSclOutputParam, 0, sizeof(MI_SCL_OutPortParam_t));

    if (! (E_SCL_DEV_ID_0 <= mSclDevId && mSclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (! (E_SCL_OUTPORT_ID_0 <= mSclOutportId && mSclOutportId < E_SCL_OUTPORT_ID_MAX)) {
        return false;
    }

    ret = MI_SCL_GetOutputPortParam(mSclDevId, mSclChnId, mSclOutportId, &miSclOutputParam);
    if (ret != MI_SUCCESS) {
        __SCL_OUTPORT_LOGE__("MI_SCL_GetOutputPortParam error ! err:%x", ret);
        return false;
    }

    miSclOutputParam.bMirror = isMirror;

    ret = MI_SCL_SetOutputPortParam(mSclDevId, mSclChnId, mSclOutportId, &miSclOutputParam);
    if (ret != MI_SUCCESS) {
        __SCL_OUTPORT_LOGE__("MI_SCL_SetOutputPortParam error ! err:%x", ret);
        return false;
    }

    if (isEnable == true) {
        if (enable() == false) {
            __SCL_OUTPORT_LOGE__("enable error !");
            return false;
        }
    }

    return true;
}

bool SclOutport::setFlip(bool isFlip)
{
    MI_S32 ret = -1;

    MI_SCL_OutPortParam_t miSclOutputParam;

    __SCL_OUTPORT_LOGD__("isFlip[%d]", isFlip);

    bool isEnable = mIsEnabled;

    disable();

    memset(&miSclOutputParam, 0, sizeof(MI_SCL_OutPortParam_t));

    if (! (E_SCL_DEV_ID_0 <= mSclDevId && mSclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (! (E_SCL_OUTPORT_ID_0 <= mSclOutportId && mSclOutportId < E_SCL_OUTPORT_ID_MAX)) {
        return false;
    }

    ret = MI_SCL_GetOutputPortParam(mSclDevId, mSclChnId, mSclOutportId, &miSclOutputParam);
    if (ret != MI_SUCCESS) {
        __SCL_OUTPORT_LOGE__("MI_SCL_GetOutputPortParam error ! err:%x", ret);
        return false;
    }

    miSclOutputParam.bFlip = isFlip;

    ret = MI_SCL_SetOutputPortParam(mSclDevId, mSclChnId, mSclOutportId, &miSclOutputParam);
    if (ret != MI_SUCCESS) {
        __SCL_OUTPORT_LOGE__("MI_SCL_SetOutputPortParam error ! err:%x", ret);
        return false;
    }

    if (isEnable == true) {
        if (enable() == false) {
            __SCL_OUTPORT_LOGE__("enable error !");
            return false;
        }
    }

    return true;
}

bool SclOutport::setDepth(uint8_t depth)
{
    MI_S32 ret = -1;
    MI_SYS_ChnPort_t miSysChnPort;

    if (depth <= 0) {
        return false;
    }

    if (mIsSetDepth == false) {
        mDepth = MIN(depth, SCL_OUTPORT_DEPTH_MAX_NUM);

        __SCL_OUTPORT_LOGD__("depth[%u]", mDepth);

        memset(&miSysChnPort, 0, sizeof(MI_SYS_ChnPort_t));
        miSysChnPort.eModId = E_MI_MODULE_ID_SCL;
        miSysChnPort.u32DevId = mSclDevId;
        miSysChnPort.u32ChnId = mSclChnId;
        miSysChnPort.u32PortId = mSclOutportId;

        ret = MI_SYS_SetChnOutputPortDepth(0, &miSysChnPort, mDepth, mDepth + 3);
        if (ret != MI_SUCCESS) {
            __SCL_OUTPORT_LOGE__("MI_SYS_SetChnOutputPortDepth error ! err:%x", ret);
            return false;
        }
        mIsSetDepth = true;
    }

    return true;
}

bool SclOutport::clrDepth()
{
    MI_S32 ret = -1;
    MI_SYS_ChnPort_t miSysChnPort;

    if (! (0 < mDepth && mDepth <= SCL_OUTPORT_DEPTH_MAX_NUM)) {
        return false;
    }

    if (mIsSetDepth == true) {

        __SCL_OUTPORT_LOGD__("clr depth");

        memset(&miSysChnPort, 0, sizeof(MI_SYS_ChnPort_t));
        miSysChnPort.eModId = E_MI_MODULE_ID_SCL;
        miSysChnPort.u32DevId = mSclDevId;
        miSysChnPort.u32ChnId = mSclChnId;
        miSysChnPort.u32PortId = mSclOutportId;

        ret = MI_SYS_SetChnOutputPortDepth(0, &miSysChnPort, 0, mDepth + 3);
        if (ret != MI_SUCCESS) {
            __SCL_OUTPORT_LOGE__("MI_SYS_SetChnOutputPortDepth error ! err:%x", ret);
            return false;
        }

        mIsSetDepth = false;
    }

    return true;
}

bool SclOutport::disable()
{
    MI_S32 ret = -1;

    freestrm_();
    clrDepth();

    if (mFd > 0) {
        if (sysCloseSclChnFd(mFd) == false) {
            __SCL_OUTPORT_LOGE__("sysCloseSclChnFd error !");
            // return false;
        }
        mFd = -1;
    }

    if (mIsEnabled == true) {

        __SCL_OUTPORT_LOGD__("disable outport");

        if (! (E_SCL_DEV_ID_0 <= mSclDevId && mSclDevId < E_SCL_DEV_ID_MAX)) {
            return false;
        }

        if (! (E_SCL_OUTPORT_ID_0 <= mSclOutportId && mSclOutportId < E_SCL_OUTPORT_ID_MAX)) {
            return false;
        }

        ret = MI_SCL_DisableOutputPort(mSclDevId, mSclChnId, mSclOutportId);
        if (ret != MI_SUCCESS) {
            __SCL_OUTPORT_LOGE__("MI_SCL_DisableOutputPort error ! err:%x", ret);
            return false;
        }

        mIsEnabled = false;
    }

    return true;
}

bool SclOutport::enable()
{
    MI_S32 ret = -1;

    setDepth(mDepth);

    if (mIsEnabled == false) {

        __SCL_OUTPORT_LOGD__("enable outport");

        if (! (E_SCL_DEV_ID_0 <= mSclDevId && mSclDevId < E_SCL_DEV_ID_MAX)) {
            return false;
        }

        if (! (E_SCL_OUTPORT_ID_0 <= mSclOutportId && mSclOutportId < E_SCL_OUTPORT_ID_MAX)) {
            return false;
        }

        ret = MI_SCL_EnableOutputPort(mSclDevId, mSclChnId, mSclOutportId);
        if (ret != MI_SUCCESS) {
            __SCL_OUTPORT_LOGE__("MI_SCL_EnableOutputPort error ! err:%x", ret);
            return false;
        }

        mIsEnabled = true;
    }

    return true;
}

int32_t SclOutport::getfd()
{
    if (! (0 < mDepth && mDepth <= SCL_OUTPORT_DEPTH_MAX_NUM)) {
        return -1;
    }

    if (mFd < 0) {
        mFd = sysGetSclChnFd(mSclDevId, mSclChnId, mSclOutportId);
        if (mFd < 0) {
            __SCL_OUTPORT_LOGE__("sysGetSclChnFd error !");
            return -1;
        }
    }

    return mFd;
}

bool SclOutport::phylloc()
{
    MI_S32 ret = -1;
    uint32_t bufSize = 0;

    if (mIsPhyInited == false &&
        mReslW > 0 &&
        mReslH > 0
    ) {

        bufSize = ALIGN_UP((int32_t) (mReslW * mReslH * 1.5), 4096);

        for (uint8_t i = 0; i < SCL_PHY_BUF_MAX_NUM; i ++) {
            ret = MI_SYS_MMA_Alloc(0, (MI_U8 *) "mma_heap_name0", bufSize, &mPhyBufAddr[i]);
            if (ret != MI_SUCCESS) {
                __SCL_OUTPORT_LOGE__("MI_SYS_MMA_Alloc error ! err:%x", ret);
                return false;
            }
        }
        mIsPhyInited = true;
    }

    return mIsPhyInited;
}

bool SclOutport::phyfree()
{
    MI_S32 ret = -1;

    if (mIsPhyInited == true) {
        for (uint8_t i = 0; i < SCL_PHY_BUF_MAX_NUM; i ++) {
            if (mPhyBufAddr[i] != 0) {
                ret = MI_SYS_MMA_Free(0, mPhyBufAddr[i]);
                if (ret != MI_SUCCESS) {
                    __SCL_OUTPORT_LOGE__("MI_SYS_MMA_Alloc error ! err:%x", ret);
                    // return false;
                } else {
                    mPhyBufAddr[i] = 0;
                }
            }
        }
        mIsPhyInited = false;
    }

    return true;
}

bool SclOutport::freestrm_()
{
    MI_S32 ret = -1;
    if (! (0 < mDepth && mDepth <= SCL_OUTPORT_DEPTH_MAX_NUM)) {
        return false;
    }

    for (uint8_t i = 0; i < mDepth; i ++) {
        if (mHandle[i] != 0) {
            ret = MI_SYS_ChnOutputPortPutBuf(mHandle[i]);
            if (ret != MI_SUCCESS) {
                __SCL_OUTPORT_LOGE__("MI_SYS_ChnOutputPortPutBuf error ! err:%x", ret);
                // return false;
            }
            mHandle[i] = 0;
        }
    }
    mHandleIdx = 0;

    return true;
}

bool SclOutport::freestrm()
{
    if (mDepth > 0 && mHandleIdx == (mDepth - 1)) {
        return freestrm_();
    }

    return true;
}

bool SclOutport::getstrm(stream_t& stream, bool mempa)
{
    MI_S32 ret = -1;

    MI_PHY yaddr = 0;
    MI_S32 ysize = 0;

    MI_PHY phyAddr = 0;

    MI_SYS_ChnPort_t miChnPort;

    if (! (0 < mDepth && mDepth <= SCL_OUTPORT_DEPTH_MAX_NUM)) {
        return false;
    }

    /**
     * @brief 
     * 不再memset，节约资源
     */
    memset(&miChnPort, 0, sizeof(MI_SYS_ChnPort_t));
    memset(&mBufInfo, 0, sizeof(MI_SYS_BufInfo_t));

    miChnPort.eModId = E_MI_MODULE_ID_SCL;
    miChnPort.u32DevId = mSclDevId;
    miChnPort.u32ChnId = mSclChnId;
    miChnPort.u32PortId = mSclOutportId;

    mHandleIdx = mFrameNum % mDepth;
    // __SCL_OUTPORT_LOGD__("mHandleIdx[%u] mFrameNum[%llu] mDepth[%u]", mHandleIdx, mFrameNum, mDepth);

    ret = MI_SYS_ChnOutputPortGetBuf(&miChnPort, &mBufInfo, &mHandle[mHandleIdx]);
    if (ret != MI_SUCCESS) {
        __SCL_OUTPORT_LOGE__("MI_SYS_ChnOutputPortGetBuf error ! err:%x", ret);
        return false;
    }
    mFrameNum ++;

    yaddr = mBufInfo.stFrameData.phyAddr[0];
    ysize = mBufInfo.stFrameData.phyAddr[1] - mBufInfo.stFrameData.phyAddr[0];

    stream.strmType = E_STRM_TYPE_YUV;

    stream.YUVStrm.data = mBufInfo.stFrameData.pVirAddr[0];
    stream.YUVStrm.len = mBufInfo.stFrameData.u16Height * mBufInfo.stFrameData.u32Stride[0] +
        mBufInfo.stFrameData.u16Height * mBufInfo.stFrameData.u32Stride[1] / 2;
    stream.YUVStrm.w = mBufInfo.stFrameData.u16Width;
    stream.YUVStrm.h = mBufInfo.stFrameData.u16Height;
    stream.YUVStrm.pts = my::getTimestampUs(); // mBufInfo.u64Pts == 0 ? my::getTimestampUs() : mBufInfo.u64Pts;
    stream.YUVStrm.frameNum = mFrameNum;

    if (mempa == true) {

        if (phylloc() == true) {
            mPhyBufIdx = mPhyFrameNum % SCL_PHY_BUF_MAX_NUM;
            phyAddr = mPhyBufAddr[mPhyBufIdx];
            if (phyAddr != 0) {
                ret = MI_SYS_MemcpyPa(0, phyAddr, yaddr, stream.YUVStrm.len);
                if (ret == MI_SUCCESS) {
                    stream.YUVStrm.phyYAddr = phyAddr;
                    stream.YUVStrm.phyUVAddr = phyAddr + ysize;

                    mPhyFrameNum ++;
                } else {
                    __SCL_OUTPORT_LOGE__("MI_SYS_MemcpyPa error ! err:%x", ret);
                }
            } else {
                __SCL_OUTPORT_LOGE__("mPhyBufIdx[%u] phyAddr[0] !!!", mPhyBufIdx);
            }
        } else {
            __SCL_OUTPORT_LOGE__("phylloc error !");
        }
    } else {
        stream.YUVStrm.phyYAddr = yaddr;
        stream.YUVStrm.phyUVAddr = yaddr + ysize;
    }

    return true;
}
