#include "VdeCh.h"

#define __VDEC_LOGD__(fmt, args...)    logd("VDECH[%u] MODE[%d] " fmt, mVdeChnId, mMode, ## args)
#define __VDEC_LOGE__(fmt, args...)    loge("VDECH[%u] MODE[%d] " fmt, mVdeChnId, mMode, ## args)

#define VDEC_CH_MAX_NUM     32

VdeCh::VdeCh(uint8_t vdeChId)
    : mVdeChnId(vdeChId)
    , mMode(E_VDEC_MODE_UNKOWN)
    , mIsChCreated(false)
    , mIsEnabled(false)
    , mDepth(0)
    , mIsSetDepth(false)
    , mIsBound(false)
    , mFd(-1)
    , mHandleIdx(0)
    , mFrameNum(0)
    , mInReslW(0)
    , mInReslH(0)
    , mOutReslW(0)
    , mOutReslH(0)
    , mPhyBufIdx(0)
    , mPhyFrameNum(0)
    , mIsPhyInited(false)
{
    for (uint8_t i = 0; i < VDEC_OUTPORT_DEPTH_MAX_NUM; i ++) {
        mHandle[i] = 0;
    }

    for (uint8_t i = 0; i < VDEC_PHY_BUF_MAX_NUM; i ++) {
        mPhyBufAddr[i] = 0;
    }
}

VdeCh::~VdeCh()
{
    uninit();
}

bool VdeCh::init(vdecMode_t vdecMode, uint16_t inWidth, uint16_t inHeight, uint16_t outWidth, uint16_t outHeight)
{
    MI_S32 ret = -1;
    MI_VDEC_DEV vdecDevId = VDEC_DEV_ID;

    MI_VDEC_ChnAttr_t miVdecChnAttr;
    MI_VDEC_OutputPortAttr_t miVdecOutputPortAttr;

    __VDEC_LOGD__("vdecMode[%d] in[%ux%u] out[%ux%u]", vdecMode, inWidth, inHeight, outWidth, outHeight);

    memset(&miVdecChnAttr, 0, sizeof(MI_VDEC_ChnAttr_t));
    memset(&miVdecOutputPortAttr, 0, sizeof(MI_VDEC_OutputPortAttr_t));

    if (vdecMode == E_VDEC_MODE_H264) {
        miVdecChnAttr.eCodecType = E_MI_VDEC_CODEC_TYPE_H264;
    } else if (vdecMode == E_VDEC_MODE_H265) {
        miVdecChnAttr.eCodecType = E_MI_VDEC_CODEC_TYPE_H265;
    } else if (vdecMode == E_VDEC_MODE_JPEG) {
        miVdecChnAttr.eCodecType = E_MI_VDEC_CODEC_TYPE_JPEG;
    } else {
        __VDEC_LOGE__("unkown vdecMode: %d !", vdecMode);
        return false;
    }

#if 0 //vdec -> disp旋转需要用到，但是目前未调通
    if (vdecMode == E_VDEC_MODE_H264 ||
        vdecMode == E_VDEC_MODE_H265
    ) {
        MI_VDEC_OutbufLayoutMode_e eBufTileMode = E_MI_VDEC_OUTBUF_LAYOUT_TILE;
        ret = MI_VDEC_SetOutputPortLayoutMode(vdecDevId, eBufTileMode);
        if (ret != MI_SUCCESS) {
            __VDEC_LOGE__("MI_VDEC_SetOutputPortLayoutMode error ! err:%x", ret);
            return false;
        }

        //Tiramisu TILE输出buffer对齐要求为128x64
        outWidth = outWidth / 128 * 128 + (outWidth % 128 ? 128 : 0);
        outHeight = outHeight / 64 * 64 + (outHeight % 64 ? 64 : 0);
    }
#endif

    miVdecChnAttr.u32PicWidth = inWidth;
    miVdecChnAttr.u32PicHeight = inHeight;

    miVdecChnAttr.eVideoMode = E_MI_VDEC_VIDEO_MODE_FRAME; //按帧发送

    if ((miVdecChnAttr.u32PicWidth * miVdecChnAttr.u32PicHeight) > (1920 * 1080)) {
        miVdecChnAttr.u32BufSize = 2 * 1024 * 1024;
    } else if ((miVdecChnAttr.u32PicWidth * miVdecChnAttr.u32PicHeight) > (720 * 576)) {
        miVdecChnAttr.u32BufSize = 1024 * 1024;
    } else {
        miVdecChnAttr.u32BufSize = 512 * 1024;
    }

    miVdecChnAttr.eDpbBufMode = E_MI_VDEC_DPB_MODE_INPLACE_ONE_BUF;
    miVdecChnAttr.stVdecVideoAttr.u32RefFrameNum = 1;
    miVdecChnAttr.u32Priority = 0;
    miVdecChnAttr.stVdecVideoAttr.stErrHandlePolicy.bUseCusPolicy = FALSE;

    if (! (0 <= mVdeChnId && mVdeChnId < VDEC_CH_MAX_NUM)) {
        return false;
    }

    if (mIsChCreated == false) {
        __VDEC_LOGD__("create channel");

        ret = MI_VDEC_CreateChn(vdecDevId, mVdeChnId, &miVdecChnAttr);
        if (ret != MI_SUCCESS) {
            __VDEC_LOGE__("MI_VDEC_CreateChn error ! err:%x", ret);
            return false;
        }
        mIsChCreated = true;

        mInReslW = inWidth;
        mInReslH = inHeight;

        mOutReslW = outWidth;
        mOutReslH = outHeight;

        mMode = vdecMode;
    }

    miVdecOutputPortAttr.u16Width = outWidth;
    miVdecOutputPortAttr.u16Height = outHeight;
    ret = MI_VDEC_SetOutputPortAttr(vdecDevId, mVdeChnId, &miVdecOutputPortAttr);
    if (ret != MI_SUCCESS) {
        __VDEC_LOGE__("MI_VDEC_SetOutputPortAttr error ! err:%x", ret);
        return false;
    }

    return true;
}

bool VdeCh::uninit()
{
    MI_S32 ret = -1;
    MI_VDEC_DEV vdecDevId = VDEC_DEV_ID;

    if (unBind() == false) {
        __VDEC_LOGE__("unBind error !");
        return false;
    }

    if (disable() == false) {
        __VDEC_LOGE__("disable error !");
        return false;
    }

    if (mIsChCreated == true) {
        __VDEC_LOGD__("destroy channel !");

        if (! (0 <= vdecDevId && vdecDevId < VDEC_CH_MAX_NUM)) {
            return false;
        }

        ret = MI_VDEC_DestroyChn(vdecDevId, mVdeChnId);
        if (ret != MI_SUCCESS) {
            __VDEC_LOGE__("MI_VDEC_DestroyChn error ! err:%x", ret);
            return false;
        }
        mIsChCreated = false;

        mInReslW = 0;
        mInReslH = 0;

        mOutReslW = 0;
        mOutReslH = 0;
    }

    return true;
}

bool VdeCh::disable()
{
    MI_S32 ret = -1;
    MI_VDEC_DEV vdecDevId = VDEC_DEV_ID;

    freestrm_();
    clrDepth();

    if (mFd > 0) {
        if (sysCloseVdeChnFd(mFd) == false) {
            __VDEC_LOGE__("sysCloseVdeChnFd error !");
            // return false;
        }
        mFd = -1;
    }

    if (mIsEnabled == true) {
        __VDEC_LOGD__("disable");

        if (! (0 <= mVdeChnId && mVdeChnId < VDEC_CH_MAX_NUM)) {
            return false;
        }

        ret = MI_VDEC_StopChn(vdecDevId, mVdeChnId);
        if (ret != MI_SUCCESS) {
            __VDEC_LOGE__("MI_VDEC_StopChn error ! err:%x", ret);
            return false;
        }
        mIsEnabled = false;
    }

    return true;
}

bool VdeCh::enable()
{
    MI_S32 ret = -1;
    MI_VDEC_DEV vdecDevId = VDEC_DEV_ID;

    setDepth(mDepth);

    if (mIsEnabled == false) {
        __VDEC_LOGD__("enable");

        if (! (0 <= mVdeChnId && mVdeChnId < VDEC_CH_MAX_NUM)) {
            return false;
        }

        ret = MI_VDEC_StartChn(vdecDevId, mVdeChnId);
        if (ret != MI_SUCCESS) {
            __VDEC_LOGE__("MI_VDEC_StartChn error ! err:%x", ret);
            return false;
        }
        mIsEnabled = true;

        //官方文档描述不支持该接口
        // ret = MI_VDEC_ResetChn(vdecDevId, mVdeChnId);
        // if (ret != MI_SUCCESS) {
        //     __VDEC_LOGE__("MI_VDEC_ResetChn error ! err:%x", ret);
        //     return false;
        // }
    }

    return true;
}

bool VdeCh::bind(MI_ModuleId_e moduleId, uint8_t srcDevId, uint8_t srcChnId, uint8_t srcOutportId, uint8_t inFps, uint8_t outFps)
{
    MI_S32 ret = -1;
    MI_VDEC_DEV vdecDevId = VDEC_DEV_ID;

    if (unBind() == false) {
        __VDEC_LOGE__("unBind error !");
        return false;
    }

    if (mIsBound == false) {
        __VDEC_LOGD__("bind");

        if (! (0 <= mVdeChnId && mVdeChnId < VDEC_CH_MAX_NUM)) {
            return false;
        }

        memset(&mBindInfo, 0, sizeof(sysBindInfo_t));

        mBindInfo.stSrcChnPort.eModId = moduleId;
        mBindInfo.stSrcChnPort.u32DevId = srcDevId;
        mBindInfo.stSrcChnPort.u32ChnId = srcChnId;
        mBindInfo.stSrcChnPort.u32PortId = srcOutportId;

        mBindInfo.stDstChnPort.eModId = E_MI_MODULE_ID_VDEC;
        mBindInfo.stDstChnPort.u32DevId = vdecDevId;
        mBindInfo.stDstChnPort.u32ChnId = mVdeChnId;
        mBindInfo.stDstChnPort.u32PortId = 0;

        mBindInfo.u32SrcFrmrate = inFps;
        mBindInfo.u32DstFrmrate = outFps;
        mBindInfo.eBindType = E_MI_SYS_BIND_TYPE_FRAME_BASE;

        if (sysBind(&mBindInfo) == false) {
            return false;
        }

        mIsBound = true;
    }

    return true;
}

bool VdeCh::unBind()
{
    if (mIsBound == true) {
        __VDEC_LOGD__("unBind ...");

        if (sysUnBind(&mBindInfo) == false) {
            return false;
        }
        memset(&mBindInfo, 0, sizeof(sysBindInfo_t));

        mIsBound = false;
    }

    return true;
}

bool VdeCh::clrDepth()
{
    MI_S32 ret = -1;

    MI_VDEC_DEV vdecDevId = VDEC_DEV_ID;

    MI_SYS_ChnPort_t miSysChnPort;

    if (! (0 < mDepth && mDepth <= VDEC_OUTPORT_DEPTH_MAX_NUM)) {
        return false;
    }

    if (mIsSetDepth == true) {

        __VDEC_LOGD__("clr depth");

        memset(&miSysChnPort, 0, sizeof(MI_SYS_ChnPort_t));
        miSysChnPort.eModId = E_MI_MODULE_ID_VDEC;
        miSysChnPort.u32DevId = vdecDevId;
        miSysChnPort.u32ChnId = mVdeChnId;
        miSysChnPort.u32PortId = 0;

        ret = MI_SYS_SetChnOutputPortDepth(0, &miSysChnPort, 0, mDepth + 3);
        if (ret != MI_SUCCESS) {
            __VDEC_LOGE__("MI_SYS_SetChnOutputPortDepth error ! err:%x", ret);
            return false;
        }

        mIsSetDepth = false;
    }

    return true;
}

bool VdeCh::setDepth(uint8_t depth)
{
    MI_S32 ret = -1;

    MI_VDEC_DEV vdecDevId = VDEC_DEV_ID;

    MI_SYS_ChnPort_t miSysChnPort;

    bool isEnable = mIsEnabled;

    if (depth <= 0) {
        return false;
    }

    if (mIsSetDepth == false) {
        mDepth = MIN(depth, VDEC_OUTPORT_DEPTH_MAX_NUM);

        __VDEC_LOGD__("depth[%u]", mDepth);

        memset(&miSysChnPort, 0, sizeof(MI_SYS_ChnPort_t));
        miSysChnPort.eModId = E_MI_MODULE_ID_VDEC;
        miSysChnPort.u32DevId = vdecDevId;
        miSysChnPort.u32ChnId = mVdeChnId;
        miSysChnPort.u32PortId = 0;

        ret = MI_SYS_SetChnOutputPortDepth(0, &miSysChnPort, mDepth, mDepth + 3);
        if (ret != MI_SUCCESS) {
            __VDEC_LOGE__("MI_SYS_SetChnOutputPortDepth error ! err:%x", ret);
            return false;
        }
        mIsSetDepth = true;
    }

    return true;
}

int32_t VdeCh::putstrm(const char *data, int32_t len, uint64_t pts, int32_t tmoutms)
{
    MI_S32 ret = -1;
    MI_VDEC_DEV vdecDevId = VDEC_DEV_ID;

    MI_VDEC_VideoStream_t miVideoStream;

    /**
     * @brief 
     * 不再memset，节约资源
     */
    // memset(&miVideoStream, 0, sizeof(MI_VDEC_VideoStream_t));
    miVideoStream.pu8Addr = (MI_U8 *) data;
    miVideoStream.u32Len = len;
    miVideoStream.u64PTS = pts;
    miVideoStream.bEndOfFrame = TRUE;
    miVideoStream.bEndOfStream = FALSE;

    if (! (0 <= mVdeChnId && mVdeChnId < VDEC_CH_MAX_NUM)) {
        return -1;
    }

    ret = MI_VDEC_SendStream(vdecDevId, mVdeChnId, &miVideoStream, tmoutms);
    if (ret != MI_SUCCESS) {
        if (ret == MI_ERR_VDEC_BUF_FULL) {
            __VDEC_LOGE__("MI_VDEC_SendStream buff full ! err:%x", ret);
            return 0;
        } else {
            __VDEC_LOGE__("MI_VDEC_SendStream error ! err:%x", ret);
            return -1;
        }
    }

    return len;
}

int32_t VdeCh::getfd()
{
    MI_VDEC_DEV vdecDevId = VDEC_DEV_ID;

    if (! (0 < mDepth && mDepth <= VDEC_OUTPORT_DEPTH_MAX_NUM)) {
        return -1;
    }

    if (mFd < 0) {
        mFd = sysGetVdeChnFd(vdecDevId, mVdeChnId);
        if (mFd < 0) {
            __VDEC_LOGE__("sysGetVdeChnFd error !");
            return -1;
        }
    }

    return mFd;
}

bool VdeCh::phylloc()
{
    MI_S32 ret = -1;
    uint32_t bufSize = 0;

    if (mIsPhyInited == false &&
        mOutReslW > 0 &&
        mOutReslH > 0
    ) {

        bufSize = ALIGN_UP((int32_t) (mOutReslW * mOutReslH * 1.5), 4096);

        for (uint8_t i = 0; i < VDEC_PHY_BUF_MAX_NUM; i ++) {
            ret = MI_SYS_MMA_Alloc(0, (MI_U8 *) "mma_heap_name0", bufSize, &mPhyBufAddr[i]);
            if (ret != MI_SUCCESS || !mPhyBufAddr[i]) {
                __VDEC_LOGE__("MI_SYS_MMA_Alloc error ! err:%x, phy : %p", ret, mPhyBufAddr[i]);
                return false;
            } else {
                __VDEC_LOGD__("%d : %p", i, mPhyBufAddr[i]);
            }
        }
        mIsPhyInited = true;
    }

    return mIsPhyInited;
}

bool VdeCh::phyfree()
{
    MI_S32 ret = -1;

    if (mIsPhyInited == true) {
        for (uint8_t i = 0; i < VDEC_PHY_BUF_MAX_NUM; i ++) {
            if (mPhyBufAddr[i] != 0) {
                ret = MI_SYS_MMA_Free(0, mPhyBufAddr[i]);
                if (ret != MI_SUCCESS) {
                    __VDEC_LOGE__("MI_SYS_MMA_Alloc error ! err:%x", ret);
                    // return false;
                } else {
                    mPhyBufAddr[i] = 0;
                }
            }
        }
        mIsPhyInited = false;
    }

    return true;
}

bool VdeCh::freestrm_()
{
    MI_S32 ret = -1;
    if (! (0 < mDepth && mDepth <= VDEC_OUTPORT_DEPTH_MAX_NUM)) {
        return false;
    }

    for (uint8_t i = 0; i < mDepth; i ++) {
        if (mHandle[i] != 0) {
            ret = MI_SYS_ChnOutputPortPutBuf(mHandle[i]);
            if (ret != MI_SUCCESS) {
                __VDEC_LOGE__("MI_SYS_ChnOutputPortPutBuf error ! err:%x", ret);
                // return false;
            }
            mHandle[i] = 0;
        }
    }
    mHandleIdx = 0;

    return true;
}

bool VdeCh::freestrm()
{
    if (mDepth > 0 && mHandleIdx == (mDepth - 1)) {
        return freestrm_();
    }

    return true;
}

bool VdeCh::getstrm(stream_t& stream, bool mempa)
{
    MI_S32 ret = -1;

    MI_VDEC_DEV vdecDevId = VDEC_DEV_ID;

    MI_PHY yaddr = 0;
    MI_S32 ysize = 0;

    MI_SYS_ChnPort_t miChnPort;

    if (! (0 < mDepth && mDepth <= VDEC_OUTPORT_DEPTH_MAX_NUM)) {
        return false;
    }

    /**
     * @brief 
     * 不再memset，节约资源
     */
    // memset(&miChnPort, 0, sizeof(MI_SYS_ChnPort_t));
    // memset(&mBufInfo, 0, sizeof(MI_SYS_BufInfo_t));

    miChnPort.eModId = E_MI_MODULE_ID_VDEC;
    miChnPort.u32DevId = vdecDevId;
    miChnPort.u32ChnId = mVdeChnId;
    miChnPort.u32PortId = 0;

    mHandleIdx = mFrameNum % mDepth;
    // __VDEC_LOGD__("mHandleIdx[%u] mFrameNum[%llu] mDepth[%u]", mHandleIdx, mFrameNum, mDepth);

    ret = MI_SYS_ChnOutputPortGetBuf(&miChnPort, &mBufInfo, &mHandle[mHandleIdx]);
    if (ret != MI_SUCCESS) {
        __VDEC_LOGE__("MI_SYS_ChnOutputPortGetBuf error ! err:%x", ret);
        return false;
    }
    mFrameNum ++;

    yaddr = mBufInfo.stFrameData.phyAddr[0];
    ysize = mBufInfo.stFrameData.phyAddr[1] - mBufInfo.stFrameData.phyAddr[0];

    stream.strmType = E_STRM_TYPE_YUV;
    stream.YUVStrm.phyYAddr = yaddr;
    stream.YUVStrm.phyUVAddr = yaddr + ysize;
    stream.YUVStrm.data = mBufInfo.stFrameData.pVirAddr[0];
    stream.YUVStrm.len = mBufInfo.stFrameData.u16Height * mBufInfo.stFrameData.u32Stride[0] +
        mBufInfo.stFrameData.u16Height * mBufInfo.stFrameData.u32Stride[1] / 2;
    stream.YUVStrm.w = mBufInfo.stFrameData.u16Width;
    stream.YUVStrm.h = mBufInfo.stFrameData.u16Height;
    stream.YUVStrm.pts = my::getTimestampUs(); // mBufInfo.u64Pts == 0 ? my::getTimestampUs() : mBufInfo.u64Pts;
    stream.YUVStrm.frameNum = mFrameNum;

    if (mempa == true) {

        if (phylloc() == true) {
            mPhyBufIdx = mPhyFrameNum % VDEC_PHY_BUF_MAX_NUM;
            MI_PHY phyAddr = mPhyBufAddr[mPhyBufIdx];
            if (phyAddr != 0) {
                ret = MI_SYS_MemcpyPa(0, phyAddr, yaddr, stream.YUVStrm.len);
                if (ret == MI_SUCCESS) {
                    stream.YUVStrm.phyYAddr = phyAddr;
                    stream.YUVStrm.phyUVAddr = phyAddr + ysize;

                    mPhyFrameNum ++;
                } else {
                    __VDEC_LOGE__("MI_SYS_MemcpyPa error ! err:%x", ret);
                }
            } else {
                __VDEC_LOGE__("mPhyBufIdx[%u] phyAddr[0] !!! %d", mPhyBufIdx, mVdeChnId);
                for (int i = 0; i < VDEC_PHY_BUF_MAX_NUM; i++) {
                    __VDEC_LOGE__("%d : %p", i, mPhyBufAddr[i]);
                }
            }
        } else {
            __VDEC_LOGE__("phylloc error !");
        }
    } else {
        stream.YUVStrm.phyYAddr = yaddr;
        stream.YUVStrm.phyUVAddr = yaddr + ysize;
    }

    return true;
}

uint16_t VdeCh::getInReslW()
{
    return mInReslW;
}

uint16_t VdeCh::getInReslH()
{
    return mInReslH;
}

uint16_t VdeCh::getOutReslW()
{
    return mOutReslW;
}

uint16_t VdeCh::getOutReslH()
{
    return mOutReslH;
}
