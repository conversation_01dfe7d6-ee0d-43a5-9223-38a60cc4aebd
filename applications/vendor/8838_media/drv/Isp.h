#ifndef __ISP_H__
#define __ISP_H__

#include "defcomm.h"

#include "IspCh.h"

class Isp
{
public:
    Isp(ispDevId_t ispDevId);
    ~Isp();

    bool init();
    bool uninit();

    //-----------------------

    bool initCh(uint8_t ispChnId);
    bool uninitCh(uint8_t ispChnId);

    bool setChRot(uint8_t ispChnId, IspRot_t rot);
    bool setChMirror(uint8_t ispChnId, bool isMirror);

    bool disableCh(uint8_t ispChnId);
    bool enableCh(uint8_t ispChnId);

    bool bind(uint8_t ispChnId, MI_ModuleId_e moduleId, MI_SYS_BindType_e bindType, uint8_t srcDevId, uint8_t srcChnId, uint8_t srcOutportId, uint8_t inFps, uint8_t outFps);

    //-----------------------

    bool initOutport(uint8_t ispChnId, ispOutportId_t ispOutportId, uint16_t inWidth, uint16_t inHeight, uint16_t outWidth, uint16_t outHeight);
    bool uninitOutport(uint8_t ispChnId, ispOutportId_t ispOutportId);

    bool setDepth(uint8_t ispChnId, ispOutportId_t ispOutportId, uint8_t depth);

    bool disableOutport(uint8_t ispChnId, ispOutportId_t ispOutportId);
    bool enableOutport(uint8_t ispChnId, ispOutportId_t ispOutportId);

    int32_t getfd(uint8_t ispChnId, ispOutportId_t ispOutportId);
    bool getstrm(uint8_t ispChnId, ispOutportId_t ispOutportId, stream_t& stream);
    bool freestrm(uint8_t ispChnId, ispOutportId_t ispOutportId);

private:

private:

    ispDevId_t              mIspDevId;

    IspCh                   *mIspCh[ISP_CHN_MAX_NUM];

    bool                    mIsInited;

};

#endif
