#ifndef __DISP_H__
#define __DISP_H__

#include "defcomm.h"
#include "DispLayer.h"
#include "DispInPort.h"

class Disp
{
public:
    Disp(dispDevId_t dispDevId);
    ~Disp();

    bool init();
    bool uninit();

    bool disable();
    bool enable();

    bool setIntface(dispIntface_t intf);

private:

private:
    bool                    mIsEnabled;
    dispDevId_t             mDispDevId;
};

#endif