#ifndef __DISP_LAYER_H__
#define __DISP_LAYER_H__

#include "defcomm.h"
#include "DispInPort.h"

class DispLayer
{
public:
    DispLayer(dispLayerId_t dispLayerId);
    ~DispLayer();

    bool init();
    bool uninit();

    bool disable();
    bool enable();

    bool bindDev(dispDevId_t dispDevId, uint16_t w, uint16_t h);
    bool unBindDev();

    //-----------------------------------------------------------

    bool initInPort(dispInPortId_t inPortId);
    bool uninitInPort(dispInPortId_t inPortId);

    bool setInPortWin(dispInPortId_t inPortId, uint16_t x, uint16_t y, uint16_t w, uint16_t h);

    bool inPortBind(dispInPortId_t inPortId, MI_ModuleId_e moduleId, uint8_t srcDevId, uint8_t srcChnId, uint8_t srcOutportId, dispDevId_t dispDevId, uint8_t inFps, uint8_t outFps);
    bool inPortUnBind(dispInPortId_t inPortId);

    bool inPortDisable(dispInPortId_t inPortId);
    bool inPortEnable(dispInPortId_t inPortId);

private:

private:
    bool                    mIsEnabled;
    bool                    mIsBound;

    dispDevId_t             mDispDevId;
    dispLayerId_t           mLayerId;

    DispInPort              *mDispInPort[E_DISP_INPORT_ID_MAX];
    bool                    mIsInPortEnable[E_DISP_INPORT_ID_MAX];
};

#endif
