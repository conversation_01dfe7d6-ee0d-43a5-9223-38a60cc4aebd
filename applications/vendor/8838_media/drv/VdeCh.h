#ifndef __VDEC_CH_H__
#define __VDEC_CH_H__

#include "defcomm.h"

#define VDEC_DEV_ID                 0

#define VDEC_OUTPORT_DEPTH_MAX_NUM  6
#define VDEC_PHY_BUF_MAX_NUM        6
class VdeCh
{
public:
    VdeCh(uint8_t vdeChId);
    ~VdeCh();

    bool init(vdecMode_t vdecMode, uint16_t inWidth, uint16_t inHeight, uint16_t outWidth, uint16_t outHeight);
    bool uninit();

    bool disable();
    bool enable();

    bool bind(MI_ModuleId_e moduleId, uint8_t srcDevId, uint8_t srcChnId, uint8_t srcOutportId, uint8_t inFps, uint8_t outFps);
    bool unBind();

    bool setDepth(uint8_t depth);

    int32_t putstrm(const char *data, int32_t len, uint64_t pts, int32_t tmoutms = 20);

    int32_t getfd();
    bool freestrm();
    bool getstrm(stream_t& stream , bool mempa = true);

    uint16_t getInReslW();
    uint16_t getInReslH();

    uint16_t getOutReslW();
    uint16_t getOutReslH();

private:
    bool freestrm_();
    bool clrDepth();

    bool phylloc();
    bool phyfree();

private:

    uint8_t                 mVdeChnId;
    vdecMode_t              mMode;

    bool                    mIsChCreated;
    bool                    mIsEnabled;

    uint8_t                 mDepth;
    bool                    mIsSetDepth;

    sysBindInfo_t           mBindInfo;
    bool                    mIsBound;

    int32_t                 mFd;
    MI_SYS_BUF_HANDLE       mHandle[VDEC_OUTPORT_DEPTH_MAX_NUM];
    uint8_t                 mHandleIdx;
    uint32_t                mFrameNum;

    MI_PHY                      mPhyBufAddr[VDEC_PHY_BUF_MAX_NUM];
    uint8_t                     mPhyBufIdx;
    uint8_t                     mPhyFrameNum;

    bool                        mIsPhyInited;

    MI_SYS_BufInfo_t        mBufInfo;

    uint16_t                mInReslW;
    uint16_t                mInReslH;

    uint16_t                mOutReslW;
    uint16_t                mOutReslH;
};

#endif
