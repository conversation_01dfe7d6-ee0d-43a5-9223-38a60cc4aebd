#include "AudioOutputMnger.h"

AudioOutputMnger::AudioOutputMnger()
{
    for (uint8_t i = 0; i < E_AUDIO_OUTPUT_DEV_ID_MAX; i ++) {
        mAudioOutputDev[i] = nullptr;
    }
}

AudioOutputMnger::~AudioOutputMnger()
{
    uninit();
}

bool AudioOutputMnger::init()
{
    return true;
}

bool AudioOutputMnger::uninit()
{
    for (uint8_t i = 0; i < E_AUDIO_OUTPUT_DEV_ID_MAX; i ++) {
        uninitDev((audioOutputDevId_t) i);
    }

    return true;
}

//------------------------------------------------------

bool AudioOutputMnger::initDev(audioOutputDevId_t audioOutputDevId)
{
    if (! (E_AUDIO_OUTPUT_DEV_ID_0 <= audioOutputDevId && audioOutputDevId < E_AUDIO_OUTPUT_DEV_ID_MAX)) {
        return false;
    }

    if (mAudioOutputDev[audioOutputDevId] == nullptr) {
        mAudioOutputDev[audioOutputDevId] = new AudioOutputDev(audioOutputDevId);
        if (mAudioOutputDev[audioOutputDevId]->init() == false) {
            loge("init error !");
            delete mAudioOutputDev[audioOutputDevId];
            mAudioOutputDev[audioOutputDevId]= nullptr;
        }
    }

    return true;
}

bool AudioOutputMnger::uninitDev(audioOutputDevId_t audioOutputDevId)
{
    if (! (E_AUDIO_OUTPUT_DEV_ID_0 <= audioOutputDevId && audioOutputDevId < E_AUDIO_OUTPUT_DEV_ID_MAX)) {
        return false;
    }

    if (mAudioOutputDev[audioOutputDevId] != nullptr) {
        mAudioOutputDev[audioOutputDevId]->uninit();
    }

    delete mAudioOutputDev[audioOutputDevId];
    mAudioOutputDev[audioOutputDevId] = nullptr;

    return true;
}

bool AudioOutputMnger::disableDev(audioOutputDevId_t audioOutputDevId)
{
    if (! (E_AUDIO_OUTPUT_DEV_ID_0 <= audioOutputDevId && audioOutputDevId < E_AUDIO_OUTPUT_DEV_ID_MAX)) {
        return false;
    }

    if (mAudioOutputDev[audioOutputDevId] == nullptr) {
        return false;
    }

    return mAudioOutputDev[audioOutputDevId]->disable();
}

bool AudioOutputMnger::enableDev(audioOutputDevId_t audioOutputDevId)
{
    if (! (E_AUDIO_OUTPUT_DEV_ID_0 <= audioOutputDevId && audioOutputDevId < E_AUDIO_OUTPUT_DEV_ID_MAX)) {
        return false;
    }

    if (mAudioOutputDev[audioOutputDevId] == nullptr) {
        return false;
    }

    return mAudioOutputDev[audioOutputDevId]->enable();
}

bool AudioOutputMnger::setSampleRate(audioOutputDevId_t audioOutputDevId, audioSampleRate_t sampleRate)
{
    if (! (E_AUDIO_OUTPUT_DEV_ID_0 <= audioOutputDevId && audioOutputDevId < E_AUDIO_OUTPUT_DEV_ID_MAX)) {
        return false;
    }

    if (mAudioOutputDev[audioOutputDevId] == nullptr) {
        return false;
    }

    return mAudioOutputDev[audioOutputDevId]->setSampleRate(sampleRate);
}

//--------------------------------------------------------------

bool AudioOutputMnger::initCh(audioOutputDevId_t audioOutputDevId, audioOutputChnId_t audioOutputChnId)
{
    if (! (E_AUDIO_OUTPUT_DEV_ID_0 <= audioOutputDevId && audioOutputDevId < E_AUDIO_OUTPUT_DEV_ID_MAX)) {
        return false;
    }

    if (mAudioOutputDev[audioOutputDevId] == nullptr) {
        return false;
    }

    return mAudioOutputDev[audioOutputDevId]->initCh(audioOutputChnId);
}

bool AudioOutputMnger::uninitCh(audioOutputDevId_t audioOutputDevId, audioOutputChnId_t audioOutputChnId)
{
    if (! (E_AUDIO_OUTPUT_DEV_ID_0 <= audioOutputDevId && audioOutputDevId < E_AUDIO_OUTPUT_DEV_ID_MAX)) {
        return false;
    }

    if (mAudioOutputDev[audioOutputDevId] == nullptr) {
        return false;
    }

    return mAudioOutputDev[audioOutputDevId]->uninitCh(audioOutputChnId);
}

bool AudioOutputMnger::disableCh(audioOutputDevId_t audioOutputDevId, audioOutputChnId_t audioOutputChnId)
{
    if (! (E_AUDIO_OUTPUT_DEV_ID_0 <= audioOutputDevId && audioOutputDevId < E_AUDIO_OUTPUT_DEV_ID_MAX)) {
        return false;
    }

    if (mAudioOutputDev[audioOutputDevId] == nullptr) {
        return false;
    }

    return mAudioOutputDev[audioOutputDevId]->disableCh(audioOutputChnId);
}

bool AudioOutputMnger::enableCh(audioOutputDevId_t audioOutputDevId, audioOutputChnId_t audioOutputChnId)
{
    if (! (E_AUDIO_OUTPUT_DEV_ID_0 <= audioOutputDevId && audioOutputDevId < E_AUDIO_OUTPUT_DEV_ID_MAX)) {
        return false;
    }

    if (mAudioOutputDev[audioOutputDevId] == nullptr) {
        return false;
    }

    return mAudioOutputDev[audioOutputDevId]->enableCh(audioOutputChnId);
}

bool AudioOutputMnger::setVolume(audioOutputDevId_t audioOutputDevId, audioOutputChnId_t audioOutputChnId, audioVolumeLevel_t audioVolumeLevel)
{
    if (! (E_AUDIO_OUTPUT_DEV_ID_0 <= audioOutputDevId && audioOutputDevId < E_AUDIO_OUTPUT_DEV_ID_MAX)) {
        return false;
    }

    if (mAudioOutputDev[audioOutputDevId] == nullptr) {
        return false;
    }

    return mAudioOutputDev[audioOutputDevId]->setVolume(audioOutputChnId, audioVolumeLevel);
}

bool AudioOutputMnger::reset(audioOutputDevId_t audioOutputDevId, audioOutputChnId_t audioOutputChnId)
{
    if (! (E_AUDIO_OUTPUT_DEV_ID_0 <= audioOutputDevId && audioOutputDevId < E_AUDIO_OUTPUT_DEV_ID_MAX)) {
        return false;
    }

    if (mAudioOutputDev[audioOutputDevId] == nullptr) {
        return false;
    }

    return mAudioOutputDev[audioOutputDevId]->reset(audioOutputChnId);
}

bool AudioOutputMnger::pause(audioOutputDevId_t audioOutputDevId, audioOutputChnId_t audioOutputChnId)
{
    if (! (E_AUDIO_OUTPUT_DEV_ID_0 <= audioOutputDevId && audioOutputDevId < E_AUDIO_OUTPUT_DEV_ID_MAX)) {
        return false;
    }

    if (mAudioOutputDev[audioOutputDevId] == nullptr) {
        return false;
    }

    return mAudioOutputDev[audioOutputDevId]->pause(audioOutputChnId);
}

bool AudioOutputMnger::resume(audioOutputDevId_t audioOutputDevId, audioOutputChnId_t audioOutputChnId)
{
    if (! (E_AUDIO_OUTPUT_DEV_ID_0 <= audioOutputDevId && audioOutputDevId < E_AUDIO_OUTPUT_DEV_ID_MAX)) {
        return false;
    }

    if (mAudioOutputDev[audioOutputDevId] == nullptr) {
        return false;
    }

    return mAudioOutputDev[audioOutputDevId]->resume(audioOutputChnId);
}

bool AudioOutputMnger::putstrm(audioOutputDevId_t audioOutputDevId, audioOutputChnId_t audioOutputChnId, const char *pcm, int32_t len)
{
    if (! (E_AUDIO_OUTPUT_DEV_ID_0 <= audioOutputDevId && audioOutputDevId < E_AUDIO_OUTPUT_DEV_ID_MAX)) {
        return false;
    }

    if (mAudioOutputDev[audioOutputDevId] == nullptr) {
        return false;
    }

    return mAudioOutputDev[audioOutputDevId]->putstrm(audioOutputChnId, pcm, len);
}
//-----------------------------------------------------------

AudioOutputMnger *AudioOutputMnger::getInstance()
{
    static std::mutex mutex;
    static AudioOutputMnger *_instance = nullptr;

    if (_instance == nullptr) {
        std::lock_guard<std::mutex> lock(mutex);

        if (_instance == nullptr) {
            _instance = new AudioOutputMnger();
        }
    }
    return _instance;
}
