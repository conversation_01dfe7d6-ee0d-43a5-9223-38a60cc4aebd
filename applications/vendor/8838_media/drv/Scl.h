#ifndef __SCL_H__
#define __SCL_H__

#include "defcomm.h"

#include "SclCh.h"

class Scl
{
public:
    Scl(sclDevId_t sclDevId, uint32_t hwSclMask);
    ~Scl();

    bool init();
    bool uninit();

    //-----------------------

    bool initCh(uint8_t sclChnId);
    bool uninitCh(uint8_t sclChnId);

    bool setChRot(uint8_t sclChnId, SclRot_t rot);
    bool setChCrop(uint8_t sclChnId, uint16_t x, uint16_t y, uint16_t w, uint16_t h);

    bool disableCh(uint8_t sclChnId);
    bool enableCh(uint8_t sclChnId);

    bool bind(uint8_t sclChnId, MI_ModuleId_e moduleId, MI_SYS_BindType_e bindType, uint8_t srcDevId, uint8_t srcChnId, uint8_t srcOutportId, uint8_t inFps, uint8_t outFps);

    //-----------------------

    bool initOutport(uint8_t sclChnId, sclOutportId_t sclOutportId, uint16_t inWidth, uint16_t inHeight, uint16_t outWidth, uint16_t outHeight);
    bool uninitOutport(uint8_t sclChnId, sclOutportId_t sclOutportId);

    bool setOutportSize(uint8_t sclChnId, sclOutportId_t sclOutportId, uint16_t w, uint16_t h);
    bool setOutportCrop(uint8_t sclChnId, sclOutportId_t sclOutportId, uint16_t x, uint16_t y, uint16_t w, uint16_t h);

    bool setOutportMirror(uint8_t sclChnId, sclOutportId_t sclOutportId, bool isMirror);
    bool setOutportFlip(uint8_t sclChnId, sclOutportId_t sclOutportId, bool isFlip);

    bool setDepth(uint8_t sclChnId, sclOutportId_t sclOutportId, uint8_t depth);

    bool disableOutport(uint8_t sclChnId, sclOutportId_t sclOutportId);
    bool enableOutport(uint8_t sclChnId, sclOutportId_t sclOutportId);

    int32_t getfd(uint8_t sclChnId, sclOutportId_t sclOutportId);
    bool getstrm(uint8_t sclChnId, sclOutportId_t sclOutportId, stream_t& stream, bool mempa = false);
    bool freestrm(uint8_t sclChnId, sclOutportId_t sclOutportId);

private:

private:

    sclDevId_t              mSclDevId;
    uint32_t                mHwSclMask;

    SclCh                   *mSclCh[SCL_CHN_MAX_NUM];

    bool                    mIsInited;

};

#endif
