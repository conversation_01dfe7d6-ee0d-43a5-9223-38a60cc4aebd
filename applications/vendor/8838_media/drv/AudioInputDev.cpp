#include "AudioInputDev.h"

#define __AUDIO_INPUT_DEV_LOGD__(fmt, args...)    logd("AIDEV[%d] " fmt, mAuDevId, ## args)
#define __AUDIO_INPUT_DEV_LOGE__(fmt, args...)    loge("AIDEV[%d] " fmt, mAuDevId, ## args)

AudioInputDev::AudioInputDev(audioInputDevId_t audioInputDevId)
    : mAuDevId(audioInputDevId)
    , mIsEnabled(false)
{
    for (uint8_t i = 0; i < E_AUDIO_INPUT_CHN_ID_MAX; i ++) {
        mAudioInputCh[i] = nullptr;
    }
}

AudioInputDev::~AudioInputDev()
{
    uninit();
}

bool AudioInputDev::init()
{
    /**
     * @brief 
     * samplerate: AI 仅支持 8/16/32/48kHz
     */

    MI_S32 ret = -1;
    MI_AUDIO_Attr_t miAiSetAttr;

    memset(&miAiSetAttr, 0, sizeof(MI_AUDIO_Attr_t));
    miAiSetAttr.eBitwidth = E_MI_AUDIO_BIT_WIDTH_16;
    miAiSetAttr.eSamplerate = (MI_AUDIO_SampleRate_e) E_AUDIO_SAMPLE_RATE_8000;
    miAiSetAttr.eSoundmode = E_MI_AUDIO_SOUND_MODE_MONO;
    miAiSetAttr.eWorkmode = E_MI_AUDIO_MODE_I2S_MASTER;
    miAiSetAttr.u32ChnCnt = 4;  // 该值与AiDevId有关，详见AI_API文档
    miAiSetAttr.u32PtNumPerFrm = 512; // for aec
    miAiSetAttr.WorkModeSetting.stI2sConfig.bSyncClock = TRUE;
    miAiSetAttr.WorkModeSetting.stI2sConfig.eFmt = E_MI_AUDIO_I2S_FMT_I2S_MSB;
    miAiSetAttr.WorkModeSetting.stI2sConfig.eMclk = E_MI_AUDIO_I2S_MCLK_0;
    miAiSetAttr.WorkModeSetting.stI2sConfig.u32TdmSlots = 0;
	miAiSetAttr.WorkModeSetting.stI2sConfig.eI2sBitWidth = E_MI_AUDIO_BIT_WIDTH_32;

    if (! (E_AUDIO_INPUT_DEV_ID_0 <= mAuDevId && mAuDevId < E_AUDIO_INPUT_DEV_ID_MAX)) {
        return false;
    }

    ret = MI_AI_SetPubAttr(mAuDevId, &miAiSetAttr);
    if (ret != MI_SUCCESS) {
        __AUDIO_INPUT_DEV_LOGE__("MI_AI_SetPubAttr error ! err:%x", ret);
        return false;
    }

    return true;
}

bool AudioInputDev::uninit()
{
    for (uint8_t i = 0; i < E_AUDIO_INPUT_CHN_ID_MAX; i ++) {
        uninitCh((audioInputChnId_t) i);
    }

    return disable();
}

bool AudioInputDev::disable()
{
    MI_S32 ret = -1;

    if (mIsEnabled == true) {

        __AUDIO_INPUT_DEV_LOGD__("disable");

        if (! (E_AUDIO_INPUT_DEV_ID_0 <= mAuDevId && mAuDevId < E_AUDIO_INPUT_DEV_ID_MAX)) {
            return false;
        }

        ret = MI_AI_Disable(mAuDevId);
        if (ret != MI_SUCCESS) {
            __AUDIO_INPUT_DEV_LOGE__("MI_AI_Disable error ! err:%x", ret);
            return false;
        }

        mIsEnabled = false;
    }

    return true;
}

bool AudioInputDev::enable()
{
    MI_S32 ret = -1;

    if (mIsEnabled == false) {

        __AUDIO_INPUT_DEV_LOGD__("enable");

        if (! (E_AUDIO_INPUT_DEV_ID_0 <= mAuDevId && mAuDevId < E_AUDIO_INPUT_DEV_ID_MAX)) {
            return false;
        }

        ret = MI_AI_Enable(mAuDevId);
        if (ret != MI_SUCCESS) {
            __AUDIO_INPUT_DEV_LOGE__("MI_AI_Enable error ! err:%x", ret);
            return false;
        }

        mIsEnabled = true;
    }

    return true;
}

bool AudioInputDev::setSampleRate(audioSampleRate_t sampleRate)
{
    MI_S32 ret = -1;
    MI_AUDIO_Attr_t miAiSetAttr;

    bool isEnable = mIsEnabled;

    __AUDIO_INPUT_DEV_LOGD__("sampleRate[%d]", sampleRate);

    /**
     * @brief 
     * samplerate: AI 仅支持 8/16/32/48kHz
     */
    if ((E_AUDIO_SAMPLE_RATE_8000 != sampleRate) &&
        (E_AUDIO_SAMPLE_RATE_16000 != sampleRate) &&
        (E_AUDIO_SAMPLE_RATE_32000 != sampleRate) &&
        (E_AUDIO_SAMPLE_RATE_48000 != sampleRate)) {
        return false;
    }

    if (disable() == false) {
        __AUDIO_INPUT_DEV_LOGE__("disable error !");
        return false;
    }

    if (! (E_AUDIO_INPUT_DEV_ID_0 <= mAuDevId && mAuDevId < E_AUDIO_INPUT_DEV_ID_MAX)) {
        return false;
    }

    memset(&miAiSetAttr, 0, sizeof(MI_AUDIO_Attr_t));
    ret = MI_AI_GetPubAttr(mAuDevId, &miAiSetAttr);
    if (ret != MI_SUCCESS) {
        __AUDIO_INPUT_DEV_LOGE__("MI_AI_GetPubAttr error ! err:%x", ret);
        return false;
    }

    miAiSetAttr.eSamplerate = (MI_AUDIO_SampleRate_e) sampleRate;

    ret = MI_AI_SetPubAttr(mAuDevId, &miAiSetAttr);
    if (ret != MI_SUCCESS) {
        __AUDIO_INPUT_DEV_LOGE__("MI_AI_SetPubAttr error ! err:%x", ret);
        return false;
    }

    if (isEnable == true) {
        if (enable() == false) {
            __AUDIO_INPUT_DEV_LOGE__("enable error !");
            return false;
        }
    }

    return true;
}

//--------------------------------------------------------------

bool AudioInputDev::initCh(audioInputChnId_t audioInputChnId)
{
    if (! (E_AUDIO_INPUT_CHN_ID_0 <= audioInputChnId && audioInputChnId < E_AUDIO_INPUT_CHN_ID_MAX)) {
        return false;
    }

    if (mAudioInputCh[audioInputChnId] == nullptr) {
        mAudioInputCh[audioInputChnId] = new AudioInputCh(mAuDevId, audioInputChnId);
        if (mAudioInputCh[audioInputChnId]->init() == false) {
            loge("init error !");
            delete mAudioInputCh[audioInputChnId];
            mAudioInputCh[audioInputChnId]= nullptr;
        }
    }

    return true;
}

bool AudioInputDev::uninitCh(audioInputChnId_t audioInputChnId)
{
    if (! (E_AUDIO_INPUT_CHN_ID_0 <= audioInputChnId && audioInputChnId < E_AUDIO_INPUT_CHN_ID_MAX)) {
        return false;
    }

    if (mAudioInputCh[audioInputChnId] != nullptr) {
        mAudioInputCh[audioInputChnId]->uninit();
    }

    delete mAudioInputCh[audioInputChnId];
    mAudioInputCh[audioInputChnId]= nullptr;

    return true;
}

bool AudioInputDev::disableCh(audioInputChnId_t audioInputChnId)
{
    if (! (E_AUDIO_INPUT_CHN_ID_0 <= audioInputChnId && audioInputChnId < E_AUDIO_INPUT_CHN_ID_MAX)) {
        return false;
    }

    if (mAudioInputCh[audioInputChnId] == nullptr) {
        return false;
    }

    return mAudioInputCh[audioInputChnId]->disable();
}

bool AudioInputDev::enableCh(audioInputChnId_t audioInputChnId)
{
    if (! (E_AUDIO_INPUT_CHN_ID_0 <= audioInputChnId && audioInputChnId < E_AUDIO_INPUT_CHN_ID_MAX)) {
        return false;
    }

    if (mAudioInputCh[audioInputChnId] == nullptr) {
        return false;
    }

    return mAudioInputCh[audioInputChnId]->enable();
}

bool AudioInputDev::setVolume(audioInputChnId_t audioInputChnId, audioVolumeLevel_t audioVolumeLevel)
{
    if (! (E_AUDIO_INPUT_CHN_ID_0 <= audioInputChnId && audioInputChnId < E_AUDIO_INPUT_CHN_ID_MAX)) {
        return false;
    }

    if (mAudioInputCh[audioInputChnId] == nullptr) {
        return false;
    }

    return mAudioInputCh[audioInputChnId]->setVolume(audioVolumeLevel);
}

int32_t AudioInputDev::getfd(audioInputChnId_t audioInputChnId)
{
    if (! (E_AUDIO_INPUT_CHN_ID_0 <= audioInputChnId && audioInputChnId < E_AUDIO_INPUT_CHN_ID_MAX)) {
        return -1;
    }

    if (mAudioInputCh[audioInputChnId] == nullptr) {
        return -1;
    }

    return mAudioInputCh[audioInputChnId]->getfd();
}

bool AudioInputDev::getstrm(audioInputChnId_t audioInputChnId, stream_t& stream, int32_t tmoutms)
{
    if (! (E_AUDIO_INPUT_CHN_ID_0 <= audioInputChnId && audioInputChnId < E_AUDIO_INPUT_CHN_ID_MAX)) {
        return false;
    }

    if (mAudioInputCh[audioInputChnId] == nullptr) {
        return false;
    }

    return mAudioInputCh[audioInputChnId]->getstrm(stream, tmoutms);
}

bool AudioInputDev::freestrm(audioInputChnId_t audioInputChnId)
{
    if (! (E_AUDIO_INPUT_CHN_ID_0 <= audioInputChnId && audioInputChnId < E_AUDIO_INPUT_CHN_ID_MAX)) {
        return false;
    }

    if (mAudioInputCh[audioInputChnId] == nullptr) {
        return false;
    }

    return mAudioInputCh[audioInputChnId]->freestrm();
}
