#ifndef __DISP_IN_PORT_H__
#define __DISP_IN_PORT_H__

#include "defcomm.h"

class DispInPort
{
public:
    DispInPort(dispInPortId_t inPortId, dispLayerId_t layerId);
    ~DispInPort();

    bool init();
    bool uninit();

    bool bind(MI_ModuleId_e moduleId, uint8_t srcDevId, uint8_t srcChnId, uint8_t srcOutportId, dispDevId_t dispDevId, uint8_t inFps, uint8_t outFps);
    bool unBind();

    bool setRect(uint16_t x, uint16_t y, uint16_t w, uint16_t h);

    bool disable();
    bool enable();

    bool isEnable();

private:

private:

    bool                    mIsEnabled;
    bool                    mIsBound;

    sysBindInfo_t           mBindInfo;

    dispLayerId_t           mLayerId;
    dispInPortId_t          mDispInPortId;
};

#endif