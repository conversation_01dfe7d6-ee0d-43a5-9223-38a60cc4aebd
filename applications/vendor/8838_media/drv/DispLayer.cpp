#include "DispLayer.h"

#define __DISP_LAYER_LOGD__(fmt, args...)    logd("layerId[%d] " fmt, mLayerId, ## args)
#define __DISP_LAYER_LOGE__(fmt, args...)    loge("layerId[%d] " fmt, mLayerId, ## args)

DispLayer::DispLayer(dispLayerId_t layerId)
    : mIsEnabled(false)
    , mIsBound(false)
    , mDispDevId(E_DISP_DEV_ID_UNKOWN)
    , mLayerId(layerId)
{
    for (uint8_t i = 0; i < E_DISP_INPORT_ID_MAX; i ++) {
        mDispInPort[i] = nullptr;
        mIsInPortEnable[i] = false;
    }
}

DispLayer::~DispLayer()
{
    uninit();
}

bool DispLayer::init()
{
    return true;
}

bool DispLayer::uninit()
{
    for (uint8_t i = 0; i < E_DISP_INPORT_ID_MAX; i ++) {
        uninitInPort((dispInPortId_t) i);
    }

    if (disable() == false) {
        __DISP_LAYER_LOGE__("disable error !");
        return false;
    }

    if (unBindDev() == false) {
        __DISP_LAYER_LOGE__("unBindDev error !");
        return false;
    }

    return true;
}

bool DispLayer::disable()
{
    MI_S32 ret = -1;

    if (! (E_DISP_LAYER_ID_0 <= mLayerId && mLayerId < E_DISP_LAYER_ID_MAX)) {
        return false;
    }

    for (uint8_t i = 0; i < E_DISP_INPORT_ID_MAX; i ++) {
        mIsInPortEnable[i] = false;
        if (mDispInPort[i] != nullptr) {
            mIsInPortEnable[i] = mDispInPort[i]->isEnable();
            mDispInPort[i]->disable();
        }
    }

    if (mIsEnabled == true) {
        __DISP_LAYER_LOGD__("disable");

        ret = MI_DISP_DisableVideoLayer(mLayerId);
        if (ret != MI_SUCCESS) {
            __DISP_LAYER_LOGE__("MI_DISP_DisableVideoLayer error ! err:%x", ret);
            return false;
        }

        mIsEnabled = false;
    }

    return true;
}

bool DispLayer::enable()
{
    MI_S32 ret = -1;

    if (! (E_DISP_LAYER_ID_0 <= mLayerId && mLayerId < E_DISP_LAYER_ID_MAX)) {
        return false;
    }

    if (mIsEnabled == false) {
        __DISP_LAYER_LOGD__("enable");

        ret = MI_DISP_EnableVideoLayer(mLayerId);
        if (ret != MI_SUCCESS) {
            __DISP_LAYER_LOGE__("MI_DISP_EnableVideoLayer error ! err:%x", ret);
            return false;
        }

        mIsEnabled = true;
    }

    for (uint8_t i = 0; i < E_DISP_INPORT_ID_MAX; i ++) {
        if (mIsInPortEnable[i] && mDispInPort[i] != nullptr) {
            mDispInPort[i]->enable();
        }
    }

    return true;
}

bool DispLayer::bindDev(dispDevId_t dispDevId, uint16_t w, uint16_t h)
{
    MI_S32 ret = -1;
    MI_DISP_VideoLayerAttr_t miDispLayerAttr;

    bool isEnable = mIsEnabled;

    if (disable() == false) {
        __DISP_LAYER_LOGE__("disable error !");
        return false;
    }

    if (unBindDev() == false) {
        __DISP_LAYER_LOGE__("unBindDev");
        return false;
    }

    if (mIsBound == false) {
        __DISP_LAYER_LOGD__("DISPDEV[%d] w[%u] h[%u]", dispDevId, w, h);

        if (! (E_DISP_DEV_ID_0 <= dispDevId && dispDevId < E_DISP_DEV_ID_MAX)) {
            return false;
        }

        if (! (E_DISP_LAYER_ID_0 <= mLayerId && mLayerId < E_DISP_LAYER_ID_MAX)) {
            return false;
        }

        ret = MI_DISP_BindVideoLayer(mLayerId, dispDevId);
        if (ret != MI_SUCCESS) {
            __DISP_LAYER_LOGE__("MI_DISP_BindVideoLayer error ! err:%x", ret);
            return false;
        }
        mIsBound = true;
        mDispDevId = dispDevId;

        memset(&miDispLayerAttr, 0, sizeof(MI_DISP_VideoLayerAttr_t));
        miDispLayerAttr.stVidLayerDispWin.u16X = 0;
        miDispLayerAttr.stVidLayerDispWin.u16Y = 0;
        miDispLayerAttr.stVidLayerDispWin.u16Width = w;
        miDispLayerAttr.stVidLayerDispWin.u16Height = h;
        miDispLayerAttr.stVidLayerSize.u16Width = w;
        miDispLayerAttr.stVidLayerSize.u16Height = h;

        ret = MI_DISP_SetVideoLayerAttr(mLayerId, &miDispLayerAttr);
        if (ret != MI_SUCCESS) {
            __DISP_LAYER_LOGE__("MI_DISP_SetVideoLayerAttr error ! err:%x", ret);
            return false;
        }
    }

    if (isEnable == true) {
        if (enable() == false) {
            __DISP_LAYER_LOGE__("enable error !");
            return false;
        }
    }

    return true;
}

bool DispLayer::unBindDev()
{
    MI_S32 ret = -1;

    if (disable() == false) {
        __DISP_LAYER_LOGE__("disable error !");
        return false;
    }

    if (mIsBound == true) {

        __DISP_LAYER_LOGD__("unbind DISPDEV[%d]", mDispDevId);

        if (! (E_DISP_DEV_ID_0 <= mDispDevId && mDispDevId < E_DISP_DEV_ID_MAX)) {
            return false;
        }

        if (! (E_DISP_LAYER_ID_0 <= mLayerId && mLayerId < E_DISP_LAYER_ID_MAX)) {
            return false;
        }

        ret = MI_DISP_UnBindVideoLayer(mLayerId, mDispDevId);
        if (ret != MI_SUCCESS) {
            __DISP_LAYER_LOGE__("MI_DISP_UnBindVideoLayer error ! err:%x", ret);
            return false;
        }
        mIsBound = false;
        mDispDevId = E_DISP_DEV_ID_UNKOWN;
    }

    return true;
}
//--------------------------------------------------------------

bool DispLayer::initInPort(dispInPortId_t inPortId)
{
    if (! (E_DISP_INPORT_ID_0 <= inPortId && inPortId < E_DISP_INPORT_ID_MAX)) {
        return false;
    }

    if (mDispInPort[inPortId] == nullptr) {
        mDispInPort[inPortId] = new DispInPort(inPortId, mLayerId);

        if (mDispInPort[inPortId]->init() == false) {
            __DISP_LAYER_LOGE__("init error !");
            delete mDispInPort[inPortId];
            mDispInPort[inPortId] = nullptr;
            return false;
        }
    }

    return true;
}

bool DispLayer::uninitInPort(dispInPortId_t inPortId)
{
    if (! (E_DISP_INPORT_ID_0 <= inPortId && inPortId < E_DISP_INPORT_ID_MAX)) {
        return false;
    }

    if (mDispInPort[inPortId] != nullptr) {
        mDispInPort[inPortId]->uninit();
    }

    delete mDispInPort[inPortId];
    mDispInPort[inPortId] = nullptr;

    return true;
}

bool DispLayer::setInPortWin(dispInPortId_t inPortId, uint16_t x, uint16_t y, uint16_t w, uint16_t h)
{
    if (! (E_DISP_INPORT_ID_0 <= inPortId && inPortId < E_DISP_INPORT_ID_MAX)) {
        return false;
    }

    if (mDispInPort[inPortId] == nullptr) {
        return false;
    }

    return mDispInPort[inPortId]->setRect(x, y, w, h);
}

bool DispLayer::inPortBind(dispInPortId_t inPortId, 
    MI_ModuleId_e moduleId, uint8_t srcDevId, uint8_t srcChnId, 
    uint8_t srcOutportId, dispDevId_t dispDevId, uint8_t inFps, uint8_t outFps)
{
    if (! (E_DISP_INPORT_ID_0 <= inPortId && inPortId < E_DISP_INPORT_ID_MAX)) {
        return false;
    }

    if (mDispInPort[inPortId] == nullptr) {
        return false;
    }

    return mDispInPort[inPortId]->bind(moduleId, srcDevId, srcChnId, srcOutportId, dispDevId, inFps, outFps);
}

bool DispLayer::inPortUnBind(dispInPortId_t inPortId)
{
    if (! (E_DISP_INPORT_ID_0 <= inPortId && inPortId < E_DISP_INPORT_ID_MAX)) {
        return false;
    }

    if (mDispInPort[inPortId] == nullptr) {
        return false;
    }

    return mDispInPort[inPortId]->unBind();
}

bool DispLayer::inPortDisable(dispInPortId_t inPortId)
{
    if (! (E_DISP_INPORT_ID_0 <= inPortId && inPortId < E_DISP_INPORT_ID_MAX)) {
        return false;
    }

    if (mDispInPort[inPortId] == nullptr) {
        return false;
    }

    return mDispInPort[inPortId]->disable();
}

bool DispLayer::inPortEnable(dispInPortId_t inPortId)
{
    if (! (E_DISP_INPORT_ID_0 <= inPortId && inPortId < E_DISP_INPORT_ID_MAX)) {
        return false;
    }

    if (mDispInPort[inPortId] == nullptr) {
        return false;
    }

    return mDispInPort[inPortId]->enable();
}
