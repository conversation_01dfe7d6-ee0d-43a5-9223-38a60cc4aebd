#include "SclMnger.h"

SclMnger::SclMnger()
{
    for (uint8_t i = 0; i < E_SCL_DEV_ID_MAX; i ++) {
        mScl[i] = nullptr;
    }
}

SclMnger::~SclMnger()
{
    for (uint8_t i = 0; i < E_SCL_DEV_ID_MAX; i ++) {
        uninit((sclDevId_t) i);
    }
}

bool SclMnger::init(sclDevId_t sclDevId, uint32_t hwSclMask)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] == nullptr) {
        mScl[sclDevId] = new Scl(sclDevId, hwSclMask);

        if (mScl[sclDevId]->init() == false) {
            loge("init error !");
            delete mScl[sclDevId];
            mScl[sclDevId] = nullptr;
            return false;
        }
    }

    return true;
}

bool SclMnger::uninit(sclDevId_t sclDevId)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] != nullptr) {
        mScl[sclDevId]->uninit();
    }

    delete mScl[sclDevId];
    mScl[sclDevId] = nullptr;

    return true;
}

//-----------------------

bool SclMnger::initCh(sclDevId_t sclDevId, uint8_t sclChnId)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] == nullptr) {
        return false;
    }

    return mScl[sclDevId]->initCh(sclChnId);
}

bool SclMnger::uninitCh(sclDevId_t sclDevId, uint8_t sclChnId)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] == nullptr) {
        return false;
    }

    return mScl[sclDevId]->uninitCh(sclChnId);
}

bool SclMnger::setChRot(sclDevId_t sclDevId, uint8_t sclChnId, SclRot_t rot)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] == nullptr) {
        return false;
    }

    return mScl[sclDevId]->setChRot(sclChnId, rot);
}

bool SclMnger::setChCrop(sclDevId_t sclDevId, uint8_t sclChnId, uint16_t x, uint16_t y, uint16_t w, uint16_t h)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] == nullptr) {
        return false;
    }

    return mScl[sclDevId]->setChCrop(sclChnId, x, y, w, h);
}

bool SclMnger::disableCh(sclDevId_t sclDevId, uint8_t sclChnId)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] == nullptr) {
        return false;
    }

    return mScl[sclDevId]->disableCh(sclChnId);
}

bool SclMnger::enableCh(sclDevId_t sclDevId, uint8_t sclChnId)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] == nullptr) {
        return false;
    }

    return mScl[sclDevId]->enableCh(sclChnId);
}

bool SclMnger::bind(sclDevId_t sclDevId, uint8_t sclChnId, 
    MI_ModuleId_e moduleId, MI_SYS_BindType_e bindType, uint8_t srcDevId, 
    uint8_t srcChnId, uint8_t srcOutportId, uint8_t inFps, uint8_t outFps)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] == nullptr) {
        return false;
    }

    return mScl[sclDevId]->bind(sclChnId, moduleId, bindType, srcDevId, srcChnId, srcOutportId, inFps, outFps);
}
//-----------------------

bool SclMnger::initOutport(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId, uint16_t inWidth, uint16_t inHeight, uint16_t outWidth, uint16_t outHeight)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] == nullptr) {
        return false;
    }

    return mScl[sclDevId]->initOutport(sclChnId, sclOutportId, inWidth, inHeight, outWidth, outHeight);
}

bool SclMnger::uninitOutport(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] == nullptr) {
        return false;
    }

    return mScl[sclDevId]->uninitOutport(sclChnId, sclOutportId);
}

bool SclMnger::setOutportSize(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId, uint16_t w, uint16_t h)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] == nullptr) {
        return false;
    }

    return mScl[sclDevId]->setOutportSize(sclChnId, sclOutportId, w, h);
}

bool SclMnger::setOutportCrop(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId, uint16_t x, uint16_t y, uint16_t w, uint16_t h)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] == nullptr) {
        return false;
    }

    return mScl[sclDevId]->setOutportCrop(sclChnId, sclOutportId, x, y, w, h);
}

bool SclMnger::setOutportMirror(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId, bool isMirror)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] == nullptr) {
        return false;
    }

    return mScl[sclDevId]->setOutportMirror(sclChnId, sclOutportId, isMirror);
}

bool SclMnger::setOutportFlip(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId, bool isFlip)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] == nullptr) {
        return false;
    }

    return mScl[sclDevId]->setOutportFlip(sclChnId, sclOutportId, isFlip);
}

bool SclMnger::setDepth(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId, uint8_t depth)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] == nullptr) {
        return false;
    }

    return mScl[sclDevId]->setDepth(sclChnId, sclOutportId, depth);
}

bool SclMnger::disableOutport(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] == nullptr) {
        return false;
    }

    return mScl[sclDevId]->disableOutport(sclChnId, sclOutportId);
}

bool SclMnger::enableOutport(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] == nullptr) {
        return false;
    }

    return mScl[sclDevId]->enableOutport(sclChnId, sclOutportId);
}

int32_t SclMnger::getfd(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return -1;
    }

    if (mScl[sclDevId] == nullptr) {
        return -1;
    }

    return mScl[sclDevId]->getfd(sclChnId, sclOutportId);
}

bool SclMnger::getstrm(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId, stream_t& stream, bool mempa)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] == nullptr) {
        return false;
    }

    return mScl[sclDevId]->getstrm(sclChnId, sclOutportId, stream, mempa);
}

bool SclMnger::freestrm(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId)
{
    if (! (E_SCL_DEV_ID_0 <= sclDevId && sclDevId < E_SCL_DEV_ID_MAX)) {
        return false;
    }

    if (mScl[sclDevId] == nullptr) {
        return false;
    }

    return mScl[sclDevId]->freestrm(sclChnId, sclOutportId);
}

SclMnger *SclMnger::getInstance()
{
    static std::mutex mutex;
    static SclMnger *_instance = nullptr;

    if (_instance == nullptr) {
        std::lock_guard<std::mutex> lock(mutex);

        if (_instance == nullptr) {
            _instance = new SclMnger();
        }
    }
    return _instance;
}
