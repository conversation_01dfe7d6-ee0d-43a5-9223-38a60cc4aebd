#include "DispMnger.h"

DispMnger::DispMnger()
{
    for (uint8_t i = 0; i < E_DISP_DEV_ID_MAX; i ++) {
        mDisp[i] = nullptr;
    }

    for (uint8_t i = 0; i < E_DISP_LAYER_ID_MAX; i ++) {
        mDispLayer[i] = nullptr;
    }
}

DispMnger::~DispMnger()
{

}

bool DispMnger::init()
{
    return true;
}

bool DispMnger::uninit()
{
    for (uint8_t i = 0; i < E_DISP_LAYER_ID_MAX; i ++) {
        uninitLayer((dispLayerId_t) i);
    }

    for (uint8_t i = 0; i < E_DISP_DEV_ID_MAX; i ++) {
        uninitDev((dispDevId_t) i);
    }

    return true;
}

bool DispMnger::initDev(dispDevId_t dispDevId, dispIntface_t intf)
{
    if (! (E_DISP_DEV_ID_0 <= dispDevId && dispDevId < E_DISP_DEV_ID_MAX)) {
        return false;
    }

    if (mDisp[dispDevId] == nullptr) {
        mDisp[dispDevId] = new Disp(dispDevId);

        if (mDisp[dispDevId]->init() == false) {
            loge("init error !");
            delete mDisp[dispDevId];
            mDisp[dispDevId] = nullptr;
            return false;
        }

        if (mDisp[dispDevId]->setIntface(intf) == false) {
            loge("setIntface error !");
            delete mDisp[dispDevId];
            mDisp[dispDevId] = nullptr;
            return false;
        }
    }

    return true;
}

bool DispMnger::uninitDev(dispDevId_t dispDevId)
{
    if (! (E_DISP_DEV_ID_0 <= dispDevId && dispDevId < E_DISP_DEV_ID_MAX)) {
        return false;
    }

    if (mDisp[dispDevId] != nullptr) {
        mDisp[dispDevId]->uninit();
    }

    delete mDisp[dispDevId];
    mDisp[dispDevId] = nullptr;

    return true;
}

bool DispMnger::disableDev(dispDevId_t dispDevId)
{
    if (! (E_DISP_DEV_ID_0 <= dispDevId && dispDevId < E_DISP_DEV_ID_MAX)) {
        return false;
    }

    if (mDisp[dispDevId] == nullptr) {
        return false;
    }

    return mDisp[dispDevId]->disable();
}

bool DispMnger::enableDev(dispDevId_t dispDevId)
{
    if (! (E_DISP_DEV_ID_0 <= dispDevId && dispDevId < E_DISP_DEV_ID_MAX)) {
        return false;
    }

    if (mDisp[dispDevId] == nullptr) {
        return false;
    }

    return mDisp[dispDevId]->enable();
}

//-----------------------------------------------------

bool DispMnger::initLayer(dispLayerId_t dispLayerId)
{
    if (! (E_DISP_LAYER_ID_0 <= dispLayerId && dispLayerId < E_DISP_LAYER_ID_MAX)) {
        return false;
    }

    if (mDispLayer[dispLayerId] == nullptr) {
        mDispLayer[dispLayerId] = new DispLayer(dispLayerId);

        if (mDispLayer[dispLayerId]->init() == false) {
            loge("init error !");
            delete mDispLayer[dispLayerId];
            mDispLayer[dispLayerId] = nullptr;
            return false;
        }
    }

    return true;
}

bool DispMnger::uninitLayer(dispLayerId_t dispLayerId)
{
    if (! (E_DISP_LAYER_ID_0 <= dispLayerId && dispLayerId < E_DISP_LAYER_ID_MAX)) {
        return false;
    }

    if (mDispLayer[dispLayerId] != nullptr) {
        mDispLayer[dispLayerId]->uninit();
    }

    delete mDispLayer[dispLayerId];
    mDispLayer[dispLayerId] = nullptr;

    return true;
}

bool DispMnger::disableLayer(dispLayerId_t dispLayerId)
{
    if (! (E_DISP_LAYER_ID_0 <= dispLayerId && dispLayerId < E_DISP_LAYER_ID_MAX)) {
        return false;
    }

    if (mDispLayer[dispLayerId] == nullptr) {
        return false;
    }

    return mDispLayer[dispLayerId]->disable();
}

bool DispMnger::enableLayer(dispLayerId_t dispLayerId)
{
    if (! (E_DISP_LAYER_ID_0 <= dispLayerId && dispLayerId < E_DISP_LAYER_ID_MAX)) {
        return false;
    }

    if (mDispLayer[dispLayerId] == nullptr) {
        return false;
    }

    return mDispLayer[dispLayerId]->enable();
}

bool DispMnger::layerBindDev(dispLayerId_t dispLayerId, dispDevId_t dispDevId, uint16_t w, uint16_t h)
{
    if (! (E_DISP_LAYER_ID_0 <= dispLayerId && dispLayerId < E_DISP_LAYER_ID_MAX)) {
        return false;
    }

    if (mDispLayer[dispLayerId] == nullptr) {
        return false;
    }

    return mDispLayer[dispLayerId]->bindDev(dispDevId, w, h);
}

bool DispMnger::layerUnBindDev(dispLayerId_t dispLayerId)
{
    if (! (E_DISP_LAYER_ID_0 <= dispLayerId && dispLayerId < E_DISP_LAYER_ID_MAX)) {
        return false;
    }

    if (mDispLayer[dispLayerId] == nullptr) {
        return false;
    }

    return mDispLayer[dispLayerId]->unBindDev();
}
//--------------------------------------------------------------

bool DispMnger::initInPort(dispLayerId_t dispLayerId, dispInPortId_t inPortId)
{
    if (! (E_DISP_LAYER_ID_0 <= dispLayerId && dispLayerId < E_DISP_LAYER_ID_MAX)) {
        return false;
    }

    if (mDispLayer[dispLayerId] == nullptr) {
        return false;
    }

    return mDispLayer[dispLayerId]->initInPort(inPortId);
}

bool DispMnger::uninitInPort(dispLayerId_t dispLayerId, dispInPortId_t inPortId)
{
    if (! (E_DISP_LAYER_ID_0 <= dispLayerId && dispLayerId < E_DISP_LAYER_ID_MAX)) {
        return false;
    }

    if (mDispLayer[dispLayerId] == nullptr) {
        return false;
    }

    return mDispLayer[dispLayerId]->uninitInPort(inPortId);
}

bool DispMnger::setInPortWin(dispLayerId_t dispLayerId, dispInPortId_t inPortId, uint16_t x, uint16_t y, uint16_t w, uint16_t h)
{
    if (! (E_DISP_LAYER_ID_0 <= dispLayerId && dispLayerId < E_DISP_LAYER_ID_MAX)) {
        return false;
    }

    if (mDispLayer[dispLayerId] == nullptr) {
        return false;
    }

    return mDispLayer[dispLayerId]->setInPortWin(inPortId, x, y, w, h);
}

bool DispMnger::inPortBind(dispLayerId_t dispLayerId, dispInPortId_t inPortId, 
    MI_ModuleId_e moduleId, uint8_t srcDevId, uint8_t srcChnId, 
    uint8_t srcOutportId, dispDevId_t dispDevId, uint8_t inFps, uint8_t outFps)
{
    if (! (E_DISP_LAYER_ID_0 <= dispLayerId && dispLayerId < E_DISP_LAYER_ID_MAX)) {
        return false;
    }

    if (mDispLayer[dispLayerId] == nullptr) {
        return false;
    }

    return mDispLayer[dispLayerId]->inPortBind(inPortId, moduleId, srcDevId, srcChnId, srcOutportId, dispDevId, inFps, outFps);
}

bool DispMnger::inPortUnBind(dispLayerId_t dispLayerId, dispInPortId_t inPortId)
{
    if (! (E_DISP_LAYER_ID_0 <= dispLayerId && dispLayerId < E_DISP_LAYER_ID_MAX)) {
        return false;
    }

    if (mDispLayer[dispLayerId] == nullptr) {
        return false;
    }

    return mDispLayer[dispLayerId]->inPortUnBind(inPortId);
}

bool DispMnger::inPortDisable(dispLayerId_t dispLayerId, dispInPortId_t inPortId)
{
    if (! (E_DISP_LAYER_ID_0 <= dispLayerId && dispLayerId < E_DISP_LAYER_ID_MAX)) {
        return false;
    }

    if (mDispLayer[dispLayerId] == nullptr) {
        return false;
    }

    return mDispLayer[dispLayerId]->inPortDisable(inPortId);
}

bool DispMnger::inPortEnable(dispLayerId_t dispLayerId, dispInPortId_t inPortId)
{
    if (! (E_DISP_LAYER_ID_0 <= dispLayerId && dispLayerId < E_DISP_LAYER_ID_MAX)) {
        return false;
    }

    if (mDispLayer[dispLayerId] == nullptr) {
        return false;
    }

    return mDispLayer[dispLayerId]->inPortEnable(inPortId);
}

//-----------------------------------------------------------

DispMnger *DispMnger::getInstance()
{
    static std::mutex mutex;
    static DispMnger *_instance = nullptr;

    if (_instance == nullptr) {
        std::lock_guard<std::mutex> lock(mutex);

        if (_instance == nullptr) {
            _instance = new DispMnger();
        }
    }
    return _instance;
}
