#include "IspOutport.h"

#define __ISP_OUTPORT_LOGD__(fmt, args...)    logd("ispDevId[%d] ispChnId[%u] ispOutportId[%d] " fmt, mIspDevId, mIspChnId, mIspOutportId, ## args)
#define __ISP_OUTPORT_LOGE__(fmt, args...)    loge("ispDevId[%d] ispChnId[%u] ispOutportId[%d] " fmt, mIspDevId, mIspChnId, mIspOutportId, ## args)

IspOutport::IspOutport(ispDevId_t ispDevId, uint8_t ispChnId, ispOutportId_t ispOutportId)
    : mFd(-1)
    , mIspDevId(ispDevId)
    , mIspChnId(ispChnId)
    , mIspOutportId(ispOutportId)
    , mIsEnabled(false)
    , mDepth(0)
    , mHandleIdx(0)
    , mFrameNum(0)
    , mIsSetDepth(false)
{
    for (uint8_t i = 0; i < ISP_OUTPORT_DEPTH_MAX_NUM; i ++) {
        mHandle[i] = 0;
    }
}

IspOutport::~IspOutport()
{
    uninit();
}

bool IspOutport::init(uint16_t inWidth, uint16_t inHeight, uint16_t outWidth, uint16_t outHeight)
{
    MI_S32 ret = -1;

    MI_ISP_OutPortParam_t miIspOutputParam;
    MI_SYS_WindowRect_t miOrigPortCrop;

    inWidth = ALIGN_UP(inWidth, 2);
    inHeight = ALIGN_UP(inHeight, 2);

    outWidth = ALIGN_UP(outWidth, 2);
    outHeight = ALIGN_UP(outHeight, 2);

    __ISP_OUTPORT_LOGD__("in[%u x %d] out[%u x %u]", inWidth, inHeight, outWidth, outHeight);

    memset(&miIspOutputParam, 0, sizeof(MI_ISP_OutPortParam_t));
    memset(&miOrigPortCrop, 0, sizeof(MI_SYS_WindowRect_t));

    miOrigPortCrop.u16X = 0;
    miOrigPortCrop.u16Y = 0;
    miOrigPortCrop.u16Width = inWidth;
    miOrigPortCrop.u16Height = inHeight;

    memcpy(&miIspOutputParam.stCropRect, &miOrigPortCrop, sizeof(MI_SYS_WindowRect_t));
    miIspOutputParam.ePixelFormat = E_MI_SYS_PIXEL_FRAME_YUV_SEMIPLANAR_420;// YYU420_NV12

    if (! (E_ISP_DEV_ID_0 <= mIspDevId && mIspDevId < E_ISP_DEV_ID_MAX)) {
        return false;
    }

    if (! (E_ISP_OUTPORT_ID_0 <= mIspOutportId && mIspOutportId < E_ISP_OUTPORT_ID_MAX)) {
        return false;
    }

    ret = MI_ISP_SetOutputPortParam(mIspDevId, mIspChnId, mIspOutportId, &miIspOutputParam);
    if (ret != MI_SUCCESS) {
        __ISP_OUTPORT_LOGE__("MI_ISP_SetOutputPortParam error ! err:%x", ret);
        return false;
    }

    return true;
}

bool IspOutport::uninit()
{
    return disable();
}

bool IspOutport::setDepth(uint8_t depth)
{
    MI_S32 ret = -1;
    MI_SYS_ChnPort_t miSysChnPort;

    if (depth <= 0) {
        return false;
    }

    if (mIsSetDepth == false) {
        mDepth = MIN(depth, ISP_OUTPORT_DEPTH_MAX_NUM);

        __ISP_OUTPORT_LOGD__("depth[%u]", mDepth);

        memset(&miSysChnPort, 0, sizeof(MI_SYS_ChnPort_t));
        miSysChnPort.eModId = E_MI_MODULE_ID_ISP;
        miSysChnPort.u32DevId = mIspDevId;
        miSysChnPort.u32ChnId = mIspChnId;
        miSysChnPort.u32PortId = mIspOutportId;

        ret = MI_SYS_SetChnOutputPortDepth(0, &miSysChnPort, mDepth, mDepth + 3);
        if (ret != MI_SUCCESS) {
            __ISP_OUTPORT_LOGE__("MI_SYS_SetChnOutputPortDepth error ! err:%x", ret);
            return false;
        }
        mIsSetDepth = true;
    }

    return true;
}

bool IspOutport::clrDepth()
{
    MI_S32 ret = -1;
    MI_SYS_ChnPort_t miSysChnPort;

    if (! (0 < mDepth && mDepth <= ISP_OUTPORT_DEPTH_MAX_NUM)) {
        return false;
    }

    if (mIsSetDepth == true) {

        __ISP_OUTPORT_LOGD__("clr depth");

        memset(&miSysChnPort, 0, sizeof(MI_SYS_ChnPort_t));
        miSysChnPort.eModId = E_MI_MODULE_ID_ISP;
        miSysChnPort.u32DevId = mIspDevId;
        miSysChnPort.u32ChnId = mIspChnId;
        miSysChnPort.u32PortId = mIspOutportId;

        ret = MI_SYS_SetChnOutputPortDepth(0, &miSysChnPort, 0, mDepth + 3);
        if (ret != MI_SUCCESS) {
            __ISP_OUTPORT_LOGE__("MI_SYS_SetChnOutputPortDepth error ! err:%x", ret);
            return false;
        }

        mIsSetDepth = false;
    }

    return true;
}

bool IspOutport::disable()
{
    MI_S32 ret = -1;

    freestrm_();
    clrDepth();

    if (mFd > 0) {
        if (sysCloseIspChnFd(mFd) == false) {
            __ISP_OUTPORT_LOGE__("sysCloseIspChnFd error !");
            // return false;
        }
        mFd = -1;
    }

    if (mIsEnabled == true) {

        __ISP_OUTPORT_LOGD__("disable outport");

        if (! (E_ISP_DEV_ID_0 <= mIspDevId && mIspDevId < E_ISP_DEV_ID_MAX)) {
            return false;
        }

        if (! (E_ISP_OUTPORT_ID_0 <= mIspOutportId && mIspOutportId < E_ISP_OUTPORT_ID_MAX)) {
            return false;
        }

        ret = MI_ISP_DisableOutputPort(mIspDevId, mIspChnId, mIspOutportId);
        if (ret != MI_SUCCESS) {
            __ISP_OUTPORT_LOGE__("MI_ISP_DisableOutputPort error ! err:%x", ret);
            return false;
        }

        mIsEnabled = false;
    }

    return true;
}

bool IspOutport::enable()
{
    MI_S32 ret = -1;

    setDepth(mDepth);

    if (mIsEnabled == false) {

        __ISP_OUTPORT_LOGD__("enable outport");

        if (! (E_ISP_DEV_ID_0 <= mIspDevId && mIspDevId < E_ISP_DEV_ID_MAX)) {
            return false;
        }

        if (! (E_ISP_OUTPORT_ID_0 <= mIspOutportId && mIspOutportId < E_ISP_OUTPORT_ID_MAX)) {
            return false;
        }

        ret = MI_ISP_EnableOutputPort(mIspDevId, mIspChnId, mIspOutportId);
        if (ret != MI_SUCCESS) {
            __ISP_OUTPORT_LOGE__("MI_ISP_EnableOutputPort error ! err:%x", ret);
            return false;
        }

        mIsEnabled = true;
    }

    return true;
}

int32_t IspOutport::getfd()
{
    if (! (0 < mDepth && mDepth <= ISP_OUTPORT_DEPTH_MAX_NUM)) {
        return -1;
    }

    if (mFd < 0) {
        mFd = sysGetIspChnFd(mIspDevId, mIspChnId, mIspOutportId);
        if (mFd < 0) {
            __ISP_OUTPORT_LOGE__("sysGetIspChnFd error !");
            return -1;
        }
    }

    return mFd;
}

bool IspOutport::freestrm_()
{
    MI_S32 ret = -1;
    if (! (0 < mDepth && mDepth <= ISP_OUTPORT_DEPTH_MAX_NUM)) {
        return false;
    }

    for (uint8_t i = 0; i < mDepth; i ++) {
        if (mHandle[i] != 0) {
            ret = MI_SYS_ChnOutputPortPutBuf(mHandle[i]);
            if (ret != MI_SUCCESS) {
                __ISP_OUTPORT_LOGE__("MI_SYS_ChnOutputPortPutBuf error ! err:%x", ret);
                // return false;
            }
            mHandle[i] = 0;
        }
    }
    mHandleIdx = 0;

    return true;
}

bool IspOutport::freestrm()
{
    if (mDepth > 0 && mHandleIdx == (mDepth - 1)) {
        return freestrm_();
    }

    return true;
}

bool IspOutport::getstrm(stream_t& stream)
{
    MI_S32 ret = -1;

    MI_PHY yaddr = 0;
    MI_S32 ysize = 0;

    MI_SYS_ChnPort_t miChnPort;

    if (! (0 < mDepth && mDepth <= ISP_OUTPORT_DEPTH_MAX_NUM)) {
        return false;
    }

    /**
     * @brief 
     * 不再memset，节约资源
     */
    // memset(&miChnPort, 0, sizeof(MI_SYS_ChnPort_t));
    // memset(&mBufInfo, 0, sizeof(MI_SYS_BufInfo_t));

    miChnPort.eModId = E_MI_MODULE_ID_ISP;
    miChnPort.u32DevId = mIspDevId;
    miChnPort.u32ChnId = mIspChnId;
    miChnPort.u32PortId = mIspOutportId;

    mHandleIdx = mFrameNum % mDepth;
    // __ISP_OUTPORT_LOGD__("mHandleIdx[%u] mFrameNum[%llu] mDepth[%u]", mHandleIdx, mFrameNum, mDepth);

    ret = MI_SYS_ChnOutputPortGetBuf(&miChnPort, &mBufInfo, &mHandle[mHandleIdx]);
    if (ret != MI_SUCCESS) {
        __ISP_OUTPORT_LOGE__("MI_SYS_ChnOutputPortGetBuf error ! err:%x", ret);
        return false;
    }
    mFrameNum ++;

    yaddr = mBufInfo.stFrameData.phyAddr[0];
    ysize = mBufInfo.stFrameData.phyAddr[1] - mBufInfo.stFrameData.phyAddr[0];

    stream.strmType = E_STRM_TYPE_YUV;
    stream.YUVStrm.phyYAddr = yaddr;
    stream.YUVStrm.phyUVAddr = yaddr + ysize;
    stream.YUVStrm.data = mBufInfo.stFrameData.pVirAddr[0];
    stream.YUVStrm.len = mBufInfo.stFrameData.u16Height * mBufInfo.stFrameData.u32Stride[0] +
        mBufInfo.stFrameData.u16Height * mBufInfo.stFrameData.u32Stride[1] / 2;
    stream.YUVStrm.w = mBufInfo.stFrameData.u16Width;
    stream.YUVStrm.h = mBufInfo.stFrameData.u16Height;
    stream.YUVStrm.pts = my::getTimestampUs(); // mBufInfo.u64Pts == 0 ? my::getTimestampUs() : mBufInfo.u64Pts;
    stream.YUVStrm.frameNum = mFrameNum;

    return true;
}
