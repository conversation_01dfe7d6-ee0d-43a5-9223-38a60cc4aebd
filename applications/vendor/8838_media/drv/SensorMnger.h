#ifndef __SENSOR_MNGER_H__
#define __SENSOR_MNGER_H__

#include "defcomm.h"

#include "Sensor.h"

class SensorMnger
{
private:
    SensorMnger();
    ~SensorMnger();

public:
    bool init(snrPadId_t snrPadId);
    bool uninit(snrPadId_t snrPadId);

    bool setPlane(snrPadId_t snrPadId, snrPlaneId_t planeId, uint16_t reslw, uint16_t reslh, uint8_t fps);

    bool disable(snrPadId_t snrPadId);
    bool enable(snrPadId_t snrPadId);

    bool dump(snrPadId_t snrPadId);

    static SensorMnger *getInstance();

private:

private:

    Sensor              *mSensor[E_SNR_PAD_ID_MAX];
};

#endif
