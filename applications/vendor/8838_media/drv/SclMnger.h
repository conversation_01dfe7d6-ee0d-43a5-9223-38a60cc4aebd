#ifndef __SCL_MNGER_H__
#define __SCL_MNGER_H__

#include "defcomm.h"

#include "Scl.h"

class SclMnger
{
private:
    SclMnger();
    ~SclMnger();

public:

    bool init(sclDevId_t sclDevId, uint32_t hwSclMask);
    bool uninit(sclDevId_t sclDevId);

    //-----------------------

    bool initCh(sclDevId_t sclDevId, uint8_t sclChnId);
    bool uninitCh(sclDevId_t sclDevId, uint8_t sclChnId);

    bool setChRot(sclDevId_t sclDevId, uint8_t sclChnId, SclRot_t rot);
    bool setChCrop(sclDevId_t sclDevId, uint8_t sclChnId, uint16_t x, uint16_t y, uint16_t w, uint16_t h);

    bool disableCh(sclDevId_t sclDevId, uint8_t sclChnId);
    bool enableCh(sclDevId_t sclDevId, uint8_t sclChnId);

    bool bind(sclDevId_t sclDevId, uint8_t sclChnId, 
        MI_ModuleId_e moduleId, MI_SYS_BindType_e bindType, uint8_t srcDevId, 
        uint8_t srcChnId, uint8_t srcOutportId, uint8_t inFps, uint8_t outFps);

    //-----------------------

    bool initOutport(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId, uint16_t inWidth, uint16_t inHeight, uint16_t outWidth, uint16_t outHeight);
    bool uninitOutport(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId);

    bool setOutportSize(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId, uint16_t w, uint16_t h);
    bool setOutportCrop(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId, uint16_t x, uint16_t y, uint16_t w, uint16_t h);

    bool setOutportMirror(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId, bool isMirror);
    bool setOutportFlip(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId, bool isFlip);

    bool setDepth(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId, uint8_t depth);

    bool disableOutport(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId);
    bool enableOutport(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId);

    int32_t getfd(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId);
    bool getstrm(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId, stream_t& stream, bool mempa = false);
    bool freestrm(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId);

    static SclMnger *getInstance();

private:

private:

    Scl                 *mScl[E_SCL_DEV_ID_MAX];
};

#endif
