#include "Isp.h"

#define __ISP_DEV_LOGD__(fmt, args...)    logd("ISPDEV[%d] " fmt, mIspDevId, ## args)
#define __ISP_DEV_LOGE__(fmt, args...)    loge("ISPDEV[%d] " fmt, mIspDevId, ## args)

Isp::Isp(ispDevId_t ispDevId)
    : mIspDevId(ispDevId)
    , mIsInited(false)
{
    for (uint8_t i = 0; i < ISP_CHN_MAX_NUM; i ++) {
        mIspCh[i] = nullptr;
    }
}

Isp::~Isp()
{
    uninit();
}

bool Isp::init()
{
    MI_S32 ret = -1;
    MI_ISP_DevAttr_t miCreateDevAttr;

    if (mIsInited == false) {

        __ISP_DEV_LOGD__("create device");

        if (! (E_ISP_DEV_ID_0 <= mIspDevId && mIspDevId < E_ISP_DEV_ID_MAX)) {
            return false;
        }

        memset(&miCreateDevAttr, 0, sizeof(MI_ISP_DevAttr_t));
        miCreateDevAttr.u32DevStitchMask = mIspDevId == E_ISP_DEV_ID_0 ? 
            E_MI_ISP_DEVICEMASK_ID0 : E_MI_ISP_DEVICEMASK_ID1;

        ret = MI_ISP_CreateDevice(mIspDevId, &miCreateDevAttr);
        if (ret != MI_SUCCESS) {
            __ISP_DEV_LOGE__("MI_ISP_CreateDevice error ! err:%x", ret);
            return false;
        }

        mIsInited = true;
    }

    return true;
}

bool Isp::uninit()
{
    MI_S32 ret = -1;

    for (uint8_t i = 0; i < ISP_CHN_MAX_NUM; i ++) {
        uninitCh(i);
    }

    if (mIsInited == true) {
        __ISP_DEV_LOGD__("destroy device");

        if (! (E_ISP_DEV_ID_0 <= mIspDevId && mIspDevId < E_ISP_DEV_ID_MAX)) {
            return false;
        }

        ret = MI_ISP_DestoryDevice(mIspDevId);
        if (ret != MI_SUCCESS) {
            __ISP_DEV_LOGE__("MI_ISP_DestoryDevice error ! err:%x", ret);
            return false;
        }

        mIsInited = false;
    }

    return true;
}

//---------------------------

bool Isp::initCh(uint8_t ispChnId)
{
    if (! (0 <= ispChnId && ispChnId < ISP_CHN_MAX_NUM)) {
        return false;
    }

    if (mIspCh[ispChnId] == nullptr) {
        mIspCh[ispChnId] = new IspCh(mIspDevId, ispChnId);

        if (mIspCh[ispChnId]->init() == false) {
            __ISP_DEV_LOGE__("init error !");
            delete mIspCh[ispChnId];
            mIspCh[ispChnId] = nullptr;
            return false;
        }
    }

    return true;
}

bool Isp::uninitCh(uint8_t ispChnId)
{
    if (! (0 <= ispChnId && ispChnId < ISP_CHN_MAX_NUM)) {
        return false;
    }

    if (mIspCh[ispChnId] != nullptr) {
        mIspCh[ispChnId]->uninit();
    }

    delete mIspCh[ispChnId];
    mIspCh[ispChnId] = nullptr;

    return true;
}

bool Isp::disableCh(uint8_t ispChnId)
{
    if (! (0 <= ispChnId && ispChnId < ISP_CHN_MAX_NUM)) {
        return false;
    }

    if (mIspCh[ispChnId] == nullptr) {
        return false;
    }

    return mIspCh[ispChnId]->disable();
}

bool Isp::enableCh(uint8_t ispChnId)
{
    if (! (0 <= ispChnId && ispChnId < ISP_CHN_MAX_NUM)) {
        return false;
    }

    if (mIspCh[ispChnId] == nullptr) {
        return false;
    }

    return mIspCh[ispChnId]->enable();
}

bool Isp::setChRot(uint8_t ispChnId, IspRot_t rot)
{
    if (! (0 <= ispChnId && ispChnId < ISP_CHN_MAX_NUM)) {
        return false;
    }

    if (mIspCh[ispChnId] == nullptr) {
        return false;
    }

    return mIspCh[ispChnId]->setRot(rot);
}

bool Isp::setChMirror(uint8_t ispChnId, bool isMirror)
{
    if (! (0 <= ispChnId && ispChnId < ISP_CHN_MAX_NUM)) {
        return false;
    }

    if (mIspCh[ispChnId] == nullptr) {
        return false;
    }

    return mIspCh[ispChnId]->setMirror(isMirror);
}

bool Isp::bind(uint8_t ispChnId, MI_ModuleId_e moduleId, MI_SYS_BindType_e bindType, uint8_t srcDevId, uint8_t srcChnId, uint8_t srcOutportId, uint8_t inFps, uint8_t outFps)
{
    if (! (0 <= ispChnId && ispChnId < ISP_CHN_MAX_NUM)) {
        return false;
    }

    if (mIspCh[ispChnId] == nullptr) {
        return false;
    }

    return mIspCh[ispChnId]->bind(moduleId, bindType, srcDevId, srcChnId, srcOutportId, inFps, outFps);
}
//-------------------------------------------

bool Isp::initOutport(uint8_t ispChnId, ispOutportId_t ispOutportId, uint16_t inWidth, uint16_t inHeight, uint16_t outWidth, uint16_t outHeight)
{
    if (! (0 <= ispChnId && ispChnId < ISP_CHN_MAX_NUM)) {
        return false;
    }

    if (mIspCh[ispChnId] == nullptr) {
        return false;
    }

    return mIspCh[ispChnId]->initOutport(ispOutportId, inWidth, inHeight, outWidth, outHeight);
}

bool Isp::uninitOutport(uint8_t ispChnId, ispOutportId_t ispOutportId)
{
    if (! (0 <= ispChnId && ispChnId < ISP_CHN_MAX_NUM)) {
        return false;
    }

    if (mIspCh[ispChnId] == nullptr) {
        return false;
    }

    return mIspCh[ispChnId]->uninitOutport(ispOutportId);
}

bool Isp::setDepth(uint8_t ispChnId, ispOutportId_t ispOutportId, uint8_t depth)
{
    if (! (0 <= ispChnId && ispChnId < ISP_CHN_MAX_NUM)) {
        return false;
    }

    if (mIspCh[ispChnId] == nullptr) {
        return false;
    }

    return mIspCh[ispChnId]->setDepth(ispOutportId, depth);
}

bool Isp::disableOutport(uint8_t ispChnId, ispOutportId_t ispOutportId)
{
    if (! (0 <= ispChnId && ispChnId < ISP_CHN_MAX_NUM)) {
        return false;
    }

    if (mIspCh[ispChnId] == nullptr) {
        return false;
    }

    return mIspCh[ispChnId]->disableOutport(ispOutportId);
}

bool Isp::enableOutport(uint8_t ispChnId, ispOutportId_t ispOutportId)
{
    if (! (0 <= ispChnId && ispChnId < ISP_CHN_MAX_NUM)) {
        return false;
    }

    if (mIspCh[ispChnId] == nullptr) {
        return false;
    }

    return mIspCh[ispChnId]->enableOutport(ispOutportId);
}

int32_t Isp::getfd(uint8_t ispChnId, ispOutportId_t ispOutportId)
{
    if (! (0 <= ispChnId && ispChnId < ISP_CHN_MAX_NUM)) {
        return -1;
    }

    if (mIspCh[ispChnId] == nullptr) {
        return -1;
    }

    return mIspCh[ispChnId]->getfd(ispOutportId);
}

bool Isp::getstrm(uint8_t ispChnId, ispOutportId_t ispOutportId, stream_t& stream)
{
    if (! (0 <= ispChnId && ispChnId < ISP_CHN_MAX_NUM)) {
        return false;
    }

    if (mIspCh[ispChnId] == nullptr) {
        return false;
    }

    return mIspCh[ispChnId]->getstrm(ispOutportId, stream);
}

bool Isp::freestrm(uint8_t ispChnId, ispOutportId_t ispOutportId)
{
    if (! (0 <= ispChnId && ispChnId < ISP_CHN_MAX_NUM)) {
        return false;
    }

    if (mIspCh[ispChnId] == nullptr) {
        return false;
    }

    return mIspCh[ispChnId]->freestrm(ispOutportId);
}
