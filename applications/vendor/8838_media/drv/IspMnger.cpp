#include "IspMnger.h"

IspMnger::IspMnger()
{
    for (uint8_t i = 0; i < E_ISP_DEV_ID_MAX; i ++) {
        mIsp[i] = nullptr;
    }
}

IspMnger::~IspMnger()
{
    for (uint8_t i = 0; i < E_ISP_DEV_ID_MAX; i ++) {
        uninit((ispDevId_t) i);
    }
}

bool IspMnger::init(ispDevId_t ispDevId)
{
    if (! (E_ISP_DEV_ID_0 <= ispDevId && ispDevId < E_ISP_DEV_ID_MAX)) {
        return false;
    }

    if (mIsp[ispDevId] == nullptr) {
        mIsp[ispDevId] = new Isp(ispDevId);

        if (mIsp[ispDevId]->init() == false) {
            loge("init error !");
            delete mIsp[ispDevId];
            mIsp[ispDevId] = nullptr;
            return false;
        }
    }

    return true;
}

bool IspMnger::uninit(ispDevId_t ispDevId)
{
    if (! (E_ISP_DEV_ID_0 <= ispDevId && ispDevId < E_ISP_DEV_ID_MAX)) {
        return false;
    }

    if (mIsp[ispDevId] != nullptr) {
        mIsp[ispDevId]->uninit();
    }

    delete mIsp[ispDevId];
    mIsp[ispDevId] = nullptr;

    return true;
}

//-----------------------

bool IspMnger::initCh(ispDevId_t ispDevId, uint8_t ispChnId)
{
    if (! (E_ISP_DEV_ID_0 <= ispDevId && ispDevId < E_ISP_DEV_ID_MAX)) {
        return false;
    }

    if (mIsp[ispDevId] == nullptr) {
        return false;
    }

    return mIsp[ispDevId]->initCh(ispChnId);
}

bool IspMnger::uninitCh(ispDevId_t ispDevId, uint8_t ispChnId)
{
    if (! (E_ISP_DEV_ID_0 <= ispDevId && ispDevId < E_ISP_DEV_ID_MAX)) {
        return false;
    }

    if (mIsp[ispDevId] == nullptr) {
        return false;
    }

    return mIsp[ispDevId]->uninitCh(ispChnId);
}

bool IspMnger::setChRot(ispDevId_t ispDevId, uint8_t ispChnId, IspRot_t rot)
{
    if (! (E_ISP_DEV_ID_0 <= ispDevId && ispDevId < E_ISP_DEV_ID_MAX)) {
        return false;
    }

    if (mIsp[ispDevId] == nullptr) {
        return false;
    }

    return mIsp[ispDevId]->setChRot(ispChnId, rot);
}

bool IspMnger::setChMirror(ispDevId_t ispDevId, uint8_t ispChnId, bool isMirror)
{
    if (! (E_ISP_DEV_ID_0 <= ispDevId && ispDevId < E_ISP_DEV_ID_MAX)) {
        return false;
    }

    if (mIsp[ispDevId] == nullptr) {
        return false;
    }

    return mIsp[ispDevId]->setChMirror(ispChnId, isMirror);
}

bool IspMnger::disableCh(ispDevId_t ispDevId, uint8_t ispChnId)
{
    if (! (E_ISP_DEV_ID_0 <= ispDevId && ispDevId < E_ISP_DEV_ID_MAX)) {
        return false;
    }

    if (mIsp[ispDevId] == nullptr) {
        return false;
    }

    return mIsp[ispDevId]->disableCh(ispChnId);
}

bool IspMnger::enableCh(ispDevId_t ispDevId, uint8_t ispChnId)
{
    if (! (E_ISP_DEV_ID_0 <= ispDevId && ispDevId < E_ISP_DEV_ID_MAX)) {
        return false;
    }

    if (mIsp[ispDevId] == nullptr) {
        return false;
    }

    return mIsp[ispDevId]->enableCh(ispChnId);
}

bool IspMnger::bind(ispDevId_t ispDevId, uint8_t ispChnId, 
    MI_ModuleId_e moduleId, MI_SYS_BindType_e bindType, uint8_t srcDevId, 
    uint8_t srcChnId, uint8_t srcOutportId, uint8_t inFps, uint8_t outFps)
{
    if (! (E_ISP_DEV_ID_0 <= ispDevId && ispDevId < E_ISP_DEV_ID_MAX)) {
        return false;
    }

    if (mIsp[ispDevId] == nullptr) {
        return false;
    }

    return mIsp[ispDevId]->bind(ispChnId, moduleId, bindType, srcDevId, srcChnId, srcOutportId, inFps, outFps);
}
//-----------------------

bool IspMnger::initOutport(ispDevId_t ispDevId, uint8_t ispChnId, ispOutportId_t ispOutportId, uint16_t inWidth, uint16_t inHeight, uint16_t outWidth, uint16_t outHeight)
{
    if (! (E_ISP_DEV_ID_0 <= ispDevId && ispDevId < E_ISP_DEV_ID_MAX)) {
        return false;
    }

    if (mIsp[ispDevId] == nullptr) {
        return false;
    }

    return mIsp[ispDevId]->initOutport(ispChnId, ispOutportId, inWidth, inHeight, outWidth, outHeight);
}

bool IspMnger::uninitOutport(ispDevId_t ispDevId, uint8_t ispChnId, ispOutportId_t ispOutportId)
{
    if (! (E_ISP_DEV_ID_0 <= ispDevId && ispDevId < E_ISP_DEV_ID_MAX)) {
        return false;
    }

    if (mIsp[ispDevId] == nullptr) {
        return false;
    }

    return mIsp[ispDevId]->uninitOutport(ispChnId, ispOutportId);
}

bool IspMnger::setDepth(ispDevId_t ispDevId, uint8_t ispChnId, ispOutportId_t ispOutportId, uint8_t depth)
{
    if (! (E_ISP_DEV_ID_0 <= ispDevId && ispDevId < E_ISP_DEV_ID_MAX)) {
        return false;
    }

    if (mIsp[ispDevId] == nullptr) {
        return false;
    }

    return mIsp[ispDevId]->setDepth(ispChnId, ispOutportId, depth);
}

bool IspMnger::disableOutport(ispDevId_t ispDevId, uint8_t ispChnId, ispOutportId_t ispOutportId)
{
    if (! (E_ISP_DEV_ID_0 <= ispDevId && ispDevId < E_ISP_DEV_ID_MAX)) {
        return false;
    }

    if (mIsp[ispDevId] == nullptr) {
        return false;
    }

    return mIsp[ispDevId]->disableOutport(ispChnId, ispOutportId);
}

bool IspMnger::enableOutport(ispDevId_t ispDevId, uint8_t ispChnId, ispOutportId_t ispOutportId)
{
    if (! (E_ISP_DEV_ID_0 <= ispDevId && ispDevId < E_ISP_DEV_ID_MAX)) {
        return false;
    }

    if (mIsp[ispDevId] == nullptr) {
        return false;
    }

    return mIsp[ispDevId]->enableOutport(ispChnId, ispOutportId);
}

int32_t IspMnger::getfd(ispDevId_t ispDevId, uint8_t ispChnId, ispOutportId_t ispOutportId)
{
    if (! (E_ISP_DEV_ID_0 <= ispDevId && ispDevId < E_ISP_DEV_ID_MAX)) {
        return -1;
    }

    if (mIsp[ispDevId] == nullptr) {
        return -1;
    }

    return mIsp[ispDevId]->getfd(ispChnId, ispOutportId);
}

bool IspMnger::getstrm(ispDevId_t ispDevId, uint8_t ispChnId, ispOutportId_t ispOutportId, stream_t& stream)
{
    if (! (E_ISP_DEV_ID_0 <= ispDevId && ispDevId < E_ISP_DEV_ID_MAX)) {
        return false;
    }

    if (mIsp[ispDevId] == nullptr) {
        return false;
    }

    return mIsp[ispDevId]->getstrm(ispChnId, ispOutportId, stream);
}

bool IspMnger::freestrm(ispDevId_t ispDevId, uint8_t ispChnId, ispOutportId_t ispOutportId)
{
    if (! (E_ISP_DEV_ID_0 <= ispDevId && ispDevId < E_ISP_DEV_ID_MAX)) {
        return false;
    }

    if (mIsp[ispDevId] == nullptr) {
        return false;
    }

    return mIsp[ispDevId]->freestrm(ispChnId, ispOutportId);
}

IspMnger *IspMnger::getInstance()
{
    static std::mutex mutex;
    static IspMnger *_instance = nullptr;

    if (_instance == nullptr) {
        std::lock_guard<std::mutex> lock(mutex);

        if (_instance == nullptr) {
            _instance = new IspMnger();
        }
    }
    return _instance;
}
