#ifndef __SENSOR_H__
#define __SENSOR_H__

#include "defcomm.h"

/**
 * @brief 
 * SNR(sensor)模块实现获取摄像头接口信息、调整分辨率和帧率等功能
 * Pad
 * Sensor 硬件插口位置
 * Plane
 * Pad 下的通道名称
 * Res
 * Resolution 分辨率简称
 * Orien
 * VC
 * Virtual Channel 虚拟通道
 */

class Sensor
{
public:
    Sensor(snrPadId_t padId);
    ~Sensor();

    bool init();

    bool setPlane(snrPlaneId_t planeId, uint16_t reslw, uint16_t reslh, uint8_t fps);

    bool disable();
    bool enable();

    bool dump();

private:

private:

    snrPadId_t          mPadId;

    bool                mIsEnabled;
};

#endif
