#ifndef __ISP_OUTPUT_H__
#define __ISP_OUTPUT_H__

#include "defcomm.h"

#define ISP_OUTPORT_DEPTH_MAX_NUM       6

class IspOutport
{
public:
    IspOutport(ispDevId_t ispDevId, uint8_t ispChnId, ispOutportId_t ispOutportId);
    ~IspOutport();

    bool init(uint16_t inWidth, uint16_t inHeight, uint16_t outWidth, uint16_t outHeight);
    bool uninit();

    bool setDepth(uint8_t depth);

    bool disable();
    bool enable();

    int32_t getfd();
    bool getstrm(stream_t& stream);
    bool freestrm();

private:
    bool clrDepth();
    bool freestrm_();

private:
    int32_t                     mFd;

    ispDevId_t                  mIspDevId;
    uint8_t                     mIspChnId;
    ispOutportId_t              mIspOutportId;

    bool                        mIsEnabled;

    uint8_t                     mDepth;
    MI_SYS_BUF_HANDLE           mHandle[ISP_OUTPORT_DEPTH_MAX_NUM];
    uint8_t                     mHandleIdx;
    uint32_t                    mFrameNum;

    MI_SYS_BufInfo_t            mBufInfo;

    bool                        mIsSetDepth;
};

#endif
