#ifndef __SCL_CH_H__
#define __SCL_CH_H__

#include "defcomm.h"
#include "SclOutport.h"

class SclCh
{
public:
    SclCh(sclDevId_t sclDevId, uint8_t sclChnId);
    ~SclCh();

    bool init();
    bool uninit();

    /**
     * @brief
     * 对输入图像做旋转，所有通道的output输出的是旋转后的图像
     */
    bool setRot(SclRot_t rot);

    /**
     * @brief
     * 对输入图像做裁剪，所有通道的output输出的是裁剪后的图像
     */
    bool setCrop(uint16_t x, uint16_t y, uint16_t w, uint16_t h);

    /**
     * @brief 
     * 禁用通道，禁用后output也不会有数据输出
     */
    bool disable();
    bool enable();

    bool bind(MI_ModuleId_e moduleId, MI_SYS_BindType_e bindType, uint8_t srcDevId, uint8_t srcChnId, uint8_t srcOutportId, uint8_t inFps, uint8_t outFps);

    //------------------------------------------------------------------

    bool initOutport(sclOutportId_t sclOutportId, uint16_t inWidth, uint16_t inHeight, uint16_t outWidth, uint16_t outHeight);
    bool uninitOutport(sclOutportId_t sclOutportId);

    bool setOutportSize(sclOutportId_t sclOutportId, uint16_t w, uint16_t h);

    /**
     * @brief
     * 针对指定的output图像做裁剪
     */
    bool setOutportCrop(sclOutportId_t sclOutportId, uint16_t x, uint16_t y, uint16_t w, uint16_t h);

    bool setOutportMirror(sclOutportId_t sclOutportId, bool isMirror);
    bool setOutportFlip(sclOutportId_t sclOutportId, bool isFlip);

    bool setDepth(sclOutportId_t sclOutportId, uint8_t depth);

    bool disableOutport(sclOutportId_t sclOutportId);
    bool enableOutport(sclOutportId_t sclOutportId);

    int32_t getfd(sclOutportId_t sclOutportId);
    bool getstrm(sclOutportId_t sclOutportId, stream_t& stream, bool mempa = false);
    bool freestrm(sclOutportId_t sclOutportId);

private:
    bool unBind();

private:
    sclDevId_t              mSclDevId;
    uint8_t                 mSclChnId;

    SclOutport              *mSclOutport[E_SCL_OUTPORT_ID_MAX];

    bool                    mIsInited;
    bool                    mIsEnabled;

    sysBindInfo_t           mBindInfo;
    bool                    mIsBound;
};

#endif
