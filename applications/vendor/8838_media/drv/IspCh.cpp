#include "IspCh.h"

#define __ISP_CH_LOGD__(fmt, args...)    logd("ispDevId[%d] ispChnId[%u] " fmt, mIspDevId, mIspChnId, ## args)
#define __ISP_CH_LOGE__(fmt, args...)    loge("ispDevId[%d] ispChnId[%u] " fmt, mIspDevId, mIspChnId, ## args)

IspCh::IspCh(ispDevId_t ispDevId, uint8_t ispChnId)
    : mIspDevId(ispDevId)
    , mIspChnId(ispChnId)
    , mIsInited(false)
    , mIsEnabled(false)
    , mIsBound(false)
{
    for (uint8_t i = 0; i < E_ISP_OUTPORT_ID_MAX; i ++) {
        mIspOutport[i] = nullptr;
    }

    memset(&mBindInfo, 0, sizeof(sysBindInfo_t));
}

IspCh::~IspCh()
{
    uninit();
}

bool IspCh::init()
{
    MI_S32 ret = -1;
    MI_ISP_ChannelAttr_t miIspChnAttr;

    if (mIsInited == false) {
        __ISP_CH_LOGD__("create channel");

        if (! (E_ISP_DEV_ID_0 <= mIspDevId && mIspDevId < E_ISP_DEV_ID_MAX)) {
            return false;
        }

        memset(&miIspChnAttr, 0, sizeof(MI_ISP_ChannelAttr_t));
        ret = MI_ISP_CreateChannel(mIspDevId, mIspChnId, &miIspChnAttr);
        if (ret != MI_SUCCESS) {
            __ISP_CH_LOGE__("MI_ISP_CreateChannel error ! err:%x", ret);

            /**
             * @brief
             * 如果上一次非法退出，会导致MI_ISP_CreateChannel失败
             * 在此把通道给销毁
             */
            MI_ISP_DestroyChannel(mIspDevId, mIspChnId);
            return false;
        }

        mIsInited = true;
    }

    return true;
}

bool IspCh::uninit()
{
    MI_S32 ret = -1;

    if (unBind() == false) {
        __ISP_CH_LOGE__("unBind error !");
        return false;
    }

    for (uint8_t i = 0; i < E_ISP_OUTPORT_ID_MAX; i ++) {
        uninitOutport((ispOutportId_t) i);
    }

    if (disable() == false) {
        __ISP_CH_LOGE__("disable error !");
        return false;
    }

    if (mIsInited == true) {

        __ISP_CH_LOGD__("destroy channel");

        if (! (E_ISP_DEV_ID_0 <= mIspDevId && mIspDevId < E_ISP_DEV_ID_MAX)) {
            return false;
        }

        ret = MI_ISP_DestroyChannel(mIspDevId, mIspChnId);
        if (ret != MI_SUCCESS) {
            __ISP_CH_LOGE__("MI_ISP_DestroyChannel error ! err:%x", ret);
            return false;
        }

        mIsInited = false;
    }

    return true;
}

bool IspCh::setRot(IspRot_t rot)
{
    MI_S32 ret = -1;

    MI_ISP_ChnParam_t miIspChnParam;

    bool isChEnable = mIsEnabled;

    __ISP_CH_LOGD__("rot[%d]", rot);

    disable();

    if (! (E_ISP_DEV_ID_0 <= mIspDevId && mIspDevId < E_ISP_DEV_ID_MAX)) {
        return false;
    }

    memset(&miIspChnParam, 0, sizeof(MI_ISP_ChnParam_t));
    ret = MI_ISP_GetChnParam(mIspDevId, mIspChnId, &miIspChnParam);
    if (ret != MI_SUCCESS) {
        __ISP_CH_LOGE__("MI_ISP_GetChnParam error ! err:%x", ret);
        return false;
    }

    miIspChnParam.eRot = (MI_SYS_Rotate_e) rot;

    ret = MI_ISP_SetChnParam(mIspDevId, mIspChnId, &miIspChnParam);
    if (ret != MI_SUCCESS) {
        __ISP_CH_LOGE__("MI_ISP_SetChnParam error ! err:%x", ret);
        return false;
    }

    if (isChEnable == true) {
        if (enable() == false) {
            __ISP_CH_LOGE__("enable error !");
            return false;
        }
    }

    return true;
}

bool IspCh::setMirror(bool isMirror)
{
    MI_S32 ret = -1;

    MI_ISP_ChnParam_t miIspChnParam;

    bool isChEnable = mIsEnabled;

    __ISP_CH_LOGD__("isMirror[%d]", isMirror);

    disable();

    if (! (E_ISP_DEV_ID_0 <= mIspDevId && mIspDevId < E_ISP_DEV_ID_MAX)) {
        return false;
    }

    memset(&miIspChnParam, 0, sizeof(MI_ISP_ChnParam_t));
    ret = MI_ISP_GetChnParam(mIspDevId, mIspChnId, &miIspChnParam);
    if (ret != MI_SUCCESS) {
        __ISP_CH_LOGE__("MI_ISP_GetChnParam error ! err:%x", ret);
        return false;
    }

    miIspChnParam.bMirror = (MI_BOOL) isMirror;

    ret = MI_ISP_SetChnParam(mIspDevId, mIspChnId, &miIspChnParam);
    if (ret != MI_SUCCESS) {
        __ISP_CH_LOGE__("MI_ISP_SetChnParam error ! err:%x", ret);
        return false;
    }

    if (isChEnable == true) {
        if (enable() == false) {
            __ISP_CH_LOGE__("enable error !");
            return false;
        }
    }

    return true;
}

bool IspCh::disable()
{
    MI_S32 ret = -1;

    if (mIsEnabled == true) {
        __ISP_CH_LOGD__("disable");

        if (! (E_ISP_DEV_ID_0 <= mIspDevId && mIspDevId < E_ISP_DEV_ID_MAX)) {
            return false;
        }

        ret = MI_ISP_StopChannel(mIspDevId, mIspChnId);
        if (ret != MI_SUCCESS) {
            __ISP_CH_LOGE__("MI_ISP_StopChannel error ! err:%x", ret);
            return false;
        }

        mIsEnabled = false;
    }

    return true;
}

bool IspCh::enable()
{
    MI_S32 ret = -1;

    if (mIsEnabled == false) {
        __ISP_CH_LOGD__("enable");

        if (! (E_ISP_DEV_ID_0 <= mIspDevId && mIspDevId < E_ISP_DEV_ID_MAX)) {
            return false;
        }

        ret = MI_ISP_StartChannel(mIspDevId, mIspChnId);
        if (ret != MI_SUCCESS) {
            __ISP_CH_LOGE__("MI_ISP_StartChannel error ! err:%x", ret);
            return false;
        }

        mIsEnabled = true;
    }

    return true;
}

bool IspCh::bind(MI_ModuleId_e moduleId, MI_SYS_BindType_e bindType, uint8_t srcDevId, uint8_t srcChnId, uint8_t srcOutportId, uint8_t inFps, uint8_t outFps)
{
    if (unBind() == false) {
        __ISP_CH_LOGE__("unBind error !");
        return false;
    }

    if (mIsBound == false) {
        __ISP_CH_LOGD__("bind ...");

        memset(&mBindInfo, 0, sizeof(sysBindInfo_t));

        mBindInfo.stSrcChnPort.eModId = moduleId;
        mBindInfo.stSrcChnPort.u32DevId = srcDevId;
        mBindInfo.stSrcChnPort.u32ChnId = srcChnId;
        mBindInfo.stSrcChnPort.u32PortId = srcOutportId;

        mBindInfo.stDstChnPort.eModId = E_MI_MODULE_ID_ISP;
        mBindInfo.stDstChnPort.u32DevId = mIspDevId;
        mBindInfo.stDstChnPort.u32ChnId = mIspChnId;
        mBindInfo.stDstChnPort.u32PortId = 0;

        mBindInfo.u32SrcFrmrate = inFps;
        mBindInfo.u32DstFrmrate = outFps;
        mBindInfo.eBindType = bindType;

        if (sysBind(&mBindInfo) == false) {
            return false;
        }

        mIsBound = true;
    }

    return true;
}

bool IspCh::unBind()
{
    if (mIsBound == true) {
        __ISP_CH_LOGD__("unBind ...");

        if (sysUnBind(&mBindInfo) == false) {
            return false;
        }
        memset(&mBindInfo, 0, sizeof(sysBindInfo_t));

        mIsBound = false;
    }

    return true;
}
//---------------------------------------------------
bool IspCh::initOutport(ispOutportId_t ispOutportId, uint16_t inWidth, uint16_t inHeight, uint16_t outWidth, uint16_t outHeight)
{
    if (! (E_ISP_OUTPORT_ID_0 <= ispOutportId && ispOutportId < E_ISP_OUTPORT_ID_MAX)) {
        return false;
    }

    if (mIspOutport[ispOutportId] == nullptr) {

        mIspOutport[ispOutportId] = new IspOutport(mIspDevId, mIspChnId, ispOutportId);
        if (mIspOutport[ispOutportId]->init(inWidth, inHeight, outWidth, outHeight) == false) {
            __ISP_CH_LOGE__("init error !");
            delete mIspOutport[ispOutportId];
            mIspOutport[ispOutportId] = nullptr;
            return false;
        }
    }

    return true;
}

bool IspCh::uninitOutport(ispOutportId_t ispOutportId)
{
    if (! (E_ISP_OUTPORT_ID_0 <= ispOutportId && ispOutportId < E_ISP_OUTPORT_ID_MAX)) {
        return false;
    }

    if (mIspOutport[ispOutportId] != nullptr) {
        mIspOutport[ispOutportId]->uninit();
    }

    delete mIspOutport[ispOutportId];
    mIspOutport[ispOutportId] = nullptr;

    return true;
}

bool IspCh::setDepth(ispOutportId_t ispOutportId, uint8_t depth)
{
    if (! (E_ISP_OUTPORT_ID_0 <= ispOutportId && ispOutportId < E_ISP_OUTPORT_ID_MAX)) {
        return false;
    }

    if (mIspOutport[ispOutportId] == nullptr) {
        return false;
    }

    return mIspOutport[ispOutportId]->setDepth(depth);
}

bool IspCh::disableOutport(ispOutportId_t ispOutportId)
{
    if (! (E_ISP_OUTPORT_ID_0 <= ispOutportId && ispOutportId < E_ISP_OUTPORT_ID_MAX)) {
        return false;
    }

    if (mIspOutport[ispOutportId] == nullptr) {
        return false;
    }

    return mIspOutport[ispOutportId]->disable();
}

bool IspCh::enableOutport(ispOutportId_t ispOutportId)
{
    if (! (E_ISP_OUTPORT_ID_0 <= ispOutportId && ispOutportId < E_ISP_OUTPORT_ID_MAX)) {
        return false;
    }

    if (mIspOutport[ispOutportId] == nullptr) {
        return false;
    }

    return mIspOutport[ispOutportId]->enable();
}

int32_t IspCh::getfd(ispOutportId_t ispOutportId)
{
    if (! (E_ISP_OUTPORT_ID_0 <= ispOutportId && ispOutportId < E_ISP_OUTPORT_ID_MAX)) {
        return -1;
    }

    if (mIspOutport[ispOutportId] == nullptr) {
        return -1;
    }

    return mIspOutport[ispOutportId]->getfd();
}

bool IspCh::getstrm(ispOutportId_t ispOutportId, stream_t& stream)
{
    if (! (E_ISP_OUTPORT_ID_0 <= ispOutportId && ispOutportId < E_ISP_OUTPORT_ID_MAX)) {
        return false;
    }

    if (mIspOutport[ispOutportId] == nullptr) {
        return false;
    }

    return mIspOutport[ispOutportId]->getstrm(stream);
}

bool IspCh::freestrm(ispOutportId_t ispOutportId)
{
    if (! (E_ISP_OUTPORT_ID_0 <= ispOutportId && ispOutportId < E_ISP_OUTPORT_ID_MAX)) {
        return false;
    }

    if (mIspOutport[ispOutportId] == nullptr) {
        return false;
    }

    return mIspOutport[ispOutportId]->freestrm();
}
