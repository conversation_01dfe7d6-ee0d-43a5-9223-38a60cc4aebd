#include "Sensor.h"

#define __SNR_LOGD__(fmt, args...)    logd("PAD[%d] " fmt, mPadId, ## args)
#define __SNR_LOGE__(fmt, args...)    loge("PAD[%d] " fmt, mPadId, ## args)

Sensor::Sensor(snrPadId_t padId)
    : mPadId(padId)
    , mIsEnabled(false)
{

}

Sensor::~Sensor()
{
    disable();
}

bool Sensor::init()
{
    MI_S32 ret = -1;
    MI_U32 reslcnt = 0;
    MI_U32 reslidx = 0;

    MI_SNR_Res_t miSnrRes;

    if (! (E_SNR_PAD_ID_0 <= mPadId && mPadId < E_SNR_PAD_ID_MAX)) {
        return false;
    }

    /**
     * @brief
     * 设置PlaneMode，禁用HDR，PAD与plane一对一
     */
    ret = MI_SNR_SetPlaneMode(mPadId, FALSE);
    if (ret != MI_SUCCESS) {
        __SNR_LOGE__("MI_SNR_SetPlaneMode error ! err:%x", ret);
        return false;
    }

    ret = MI_SNR_QueryResCount(mPadId, &reslcnt);
    if (ret != MI_SUCCESS) {
        __SNR_LOGE__("MI_SNR_QueryResCount error ! err:%x", ret);
        return false;
    }

    for (reslidx = 0; reslidx < reslcnt; reslidx ++) {
        memset(&miSnrRes, 0, sizeof(MI_SNR_Res_t));

        ret = MI_SNR_GetRes(mPadId, reslidx, &miSnrRes);
        if (ret != MI_SUCCESS) {
            __SNR_LOGE__("MI_SNR_QueryResCount error ! err:%x", ret);
            return false;
        }
    }
    reslidx = 0;

    /**
     * @brief
     * 设置分辨率
     */
    ret = MI_SNR_SetRes(mPadId, reslidx);
    if (ret != MI_SUCCESS) {
        __SNR_LOGE__("MI_SNR_SetRes error ! err:%x", ret);
        return false;
    }

    return true;
}

bool Sensor::setPlane(snrPlaneId_t planeId, uint16_t reslw, uint16_t reslh, uint8_t fps)
{
    MI_S32 ret = -1;
    bool isEnable = mIsEnabled;

    MI_SNR_Anadec_SrcAttr_t miAnadecSrcAttr;

    __SNR_LOGD__("planeId[%d] resl[%u x %u] fps[%u]", planeId, reslw, reslh, fps);

    /**
     * @brief 
     * 要先禁用设备
    */
    if (disable() == false) {
        __SNR_LOGE__("disable error !");
        return false;
    }

    memset(&miAnadecSrcAttr, 0, sizeof(MI_SNR_Anadec_SrcAttr_t));
    miAnadecSrcAttr.eStatus = E_MI_SNR_ANADEC_STATUS_NO_READY; //输入源状态枚举
    miAnadecSrcAttr.stRes.u16Width = reslw; //输入源分辨率
    miAnadecSrcAttr.stRes.u16Height = reslh; //输入源分辨率
    miAnadecSrcAttr.u32Fps = fps; //输入源帧率
    miAnadecSrcAttr.eFormat = E_MI_SNR_ANADEC_FORMAT_NTSC; //输入源传输制式枚举
    miAnadecSrcAttr.eTransferMode = E_MI_SNR_ANADEC_TRANSFERMODE_AHD; //输入源传输模式枚举

    if (! (E_SNR_PAD_ID_0 <= mPadId && mPadId < E_SNR_PAD_ID_MAX)) {
        return false;
    }

    if (! (E_SNR_PLANE_ID_0 <= planeId && planeId < E_SNR_PLANE_ID_MAX)) {
        return false;
    }

    ret = MI_SNR_SetAnadecSrcAttr(mPadId, planeId, &miAnadecSrcAttr);
    if (ret != MI_SUCCESS) {
        __SNR_LOGE__("MI_SNR_SetAnadecSrcAttr error ! err:%x", ret);
        return false;
    }

    /**
     * @brief
     * 如果上一次是enable，则设置完成后继续enable
    */
    if (isEnable == true) {
        if (enable() == false) {
            __SNR_LOGE__("enable error !");
            return false;
        }
    }

    return true;
}

bool Sensor::disable()
{
    MI_S32 ret = -1;

    if (mIsEnabled == true) {
        __SNR_LOGD__("disable");

        if (! (E_SNR_PAD_ID_0 <= mPadId && mPadId < E_SNR_PAD_ID_MAX)) {
            return false;
        }

        ret = MI_SNR_Disable(mPadId);
        if (ret != MI_SUCCESS) {
            __SNR_LOGE__("MI_SNR_Disable error ! err:%x", ret);
            return false;
        }

        mIsEnabled = false;
    }

    return true;
}

bool Sensor::enable()
{
    MI_S32 ret = -1;

    if (mIsEnabled == false) {
        __SNR_LOGD__("enable");

        if (! (E_SNR_PAD_ID_0 <= mPadId && mPadId < E_SNR_PAD_ID_MAX)) {
            return false;
        }

        ret = MI_SNR_Enable(mPadId);
        if (ret != MI_SUCCESS) {
            __SNR_LOGE__("MI_SNR_Enable error ! err:%x", ret);
            return false;
        }

        mIsEnabled = true;
    }

    return true;
}

bool Sensor::dump()
{
    MI_S32 ret = -1;
    MI_U32 reslcnt = 0;
    MI_U32 reslidx = 0;
    MI_SNR_Res_t miSnrRes;

    if (! (E_SNR_PAD_ID_0 <= mPadId && mPadId < E_SNR_PAD_ID_MAX)) {
        return false;
    }

    ret = MI_SNR_QueryResCount(mPadId, &reslcnt);
    if (ret != MI_SUCCESS) {
        __SNR_LOGE__("MI_SNR_QueryResCount error ! err:%x", ret);
        return false;
    }

    for (reslidx = 0; reslidx < reslcnt; reslidx ++) {
        ret = MI_SNR_GetRes(mPadId, reslidx, &miSnrRes);
        if (ret != MI_SUCCESS) {
            __SNR_LOGE__("MI_SNR_GetRes error ! err:%x", ret);
            return false;
        }

        __SNR_LOGD__("Crop(%d, %d, %d, %d), outputsize(%d, %d), maxfps %d, minfps %d, ResDesc %s",
            miSnrRes.stCropRect.u16X, miSnrRes.stCropRect.u16Y,
            miSnrRes.stCropRect.u16Width, miSnrRes.stCropRect.u16Height,
            miSnrRes.stOutputSize.u16Width, miSnrRes.stOutputSize.u16Height,
            miSnrRes.u32MaxFps, miSnrRes.u32MinFps, miSnrRes.strResDesc);
    }

    return true;
}
