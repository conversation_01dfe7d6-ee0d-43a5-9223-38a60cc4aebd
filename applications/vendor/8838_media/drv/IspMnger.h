#ifndef __ISP_MNGER_H__
#define __ISP_MNGER_H__

#include "defcomm.h"

#include "Isp.h"

class IspMnger
{
private:
    IspMnger();
    ~IspMnger();

public:

    bool init(ispDevId_t ispDevId);
    bool uninit(ispDevId_t ispDevId);

    //-----------------------

    bool initCh(ispDevId_t ispDevId, uint8_t ispChnId);
    bool uninitCh(ispDevId_t ispDevId, uint8_t ispChnId);

    bool setChRot(ispDevId_t ispDevId, uint8_t ispChnId, IspRot_t rot);
    bool setChMirror(ispDevId_t ispDevId, uint8_t ispChnId, bool isMirror);

    bool disableCh(ispDevId_t ispDevId, uint8_t ispChnId);
    bool enableCh(ispDevId_t ispDevId, uint8_t ispChnId);

    bool bind(ispDevId_t ispDevId, uint8_t ispChnId, 
        MI_ModuleId_e moduleId, MI_SYS_BindType_e bindType, uint8_t srcDevId, 
        uint8_t srcChnId, uint8_t srcOutportId, uint8_t inFps, uint8_t outFps);

    //-----------------------

    bool initOutport(ispDevId_t ispDevId, uint8_t ispChnId, ispOutportId_t ispOutportId, uint16_t inWidth, uint16_t inHeight, uint16_t outWidth, uint16_t outHeight);
    bool uninitOutport(ispDevId_t ispDevId, uint8_t ispChnId, ispOutportId_t ispOutportId);

    bool setDepth(ispDevId_t ispDevId, uint8_t ispChnId, ispOutportId_t ispOutportId, uint8_t depth);

    bool disableOutport(ispDevId_t ispDevId, uint8_t ispChnId, ispOutportId_t ispOutportId);
    bool enableOutport(ispDevId_t ispDevId, uint8_t ispChnId, ispOutportId_t ispOutportId);

    int32_t getfd(ispDevId_t ispDevId, uint8_t ispChnId, ispOutportId_t ispOutportId);
    bool getstrm(ispDevId_t ispDevId, uint8_t ispChnId, ispOutportId_t ispOutportId, stream_t& stream);
    bool freestrm(ispDevId_t ispDevId, uint8_t ispChnId, ispOutportId_t ispOutportId);

    static IspMnger *getInstance();

private:

private:

    Isp                 *mIsp[E_ISP_DEV_ID_MAX];
};

#endif
