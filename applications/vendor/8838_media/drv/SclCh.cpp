#include "SclCh.h"

#define __SCL_CH_LOGD__(fmt, args...)    logd("sclDevId[%d] sclChnId[%u] " fmt, mSclDevId, mSclChnId, ## args)
#define __SCL_CH_LOGE__(fmt, args...)    loge("sclDevId[%d] sclChnId[%u] " fmt, mSclDevId, mSclChnId, ## args)

SclCh::SclCh(sclDevId_t sclDevId, uint8_t sclChnId)
    : mSclDevId(sclDevId)
    , mSclChnId(sclChnId)
    , mIsInited(false)
    , mIsEnabled(false)
    , mIsBound(false)
{
    for (uint8_t i = 0; i < E_SCL_OUTPORT_ID_MAX; i ++) {
        mSclOutport[i] = nullptr;
    }

    memset(&mBindInfo, 0, sizeof(sysBindInfo_t));
}

SclCh::~SclCh()
{
    uninit();
}

bool SclCh::init()
{
    MI_S32 ret = -1;
    MI_SCL_ChannelAttr_t miSclChnAttr;

    if (mIsInited == false) {
        __SCL_CH_LOGD__("create channel");

        if (! (E_SCL_DEV_ID_0 <= mSclDevId && mSclDevId < E_SCL_DEV_ID_MAX)) {
            return false;
        }

        memset(&miSclChnAttr, 0, sizeof(MI_SCL_ChannelAttr_t));
        ret = MI_SCL_CreateChannel(mSclDevId, mSclChnId, &miSclChnAttr);
        if (ret != MI_SUCCESS) {
            __SCL_CH_LOGE__("MI_SCL_CreateChannel error ! err:%x", ret);

            /**
             * @brief
             * 如果上一次非法退出，会导致MI_SCL_CreateChannel失败
             * 在此把通道给销毁
             */
            MI_SCL_DestroyChannel(mSclDevId, mSclChnId);
            return false;
        }

        mIsInited = true;
    }

    return true;
}

bool SclCh::uninit()
{
    MI_S32 ret = -1;

    if (unBind() == false) {
        __SCL_CH_LOGE__("unBind error !");
        return false;
    }

    for (uint8_t i = 0; i < E_SCL_OUTPORT_ID_MAX; i ++) {
        uninitOutport((sclOutportId_t) i);
    }

    if (disable() == false) {
        __SCL_CH_LOGE__("disable error !");
        return false;
    }

    if (mIsInited == true) {

        __SCL_CH_LOGD__("destroy channel");

        if (! (E_SCL_DEV_ID_0 <= mSclDevId && mSclDevId < E_SCL_DEV_ID_MAX)) {
            return false;
        }

        ret = MI_SCL_DestroyChannel(mSclDevId, mSclChnId);
        if (ret != MI_SUCCESS) {
            __SCL_CH_LOGE__("MI_SCL_DestroyChannel error ! err:%x", ret);
            return false;
        }

        mIsInited = false;
    }

    return true;
}

bool SclCh::setRot(SclRot_t rot)
{
    MI_S32 ret = -1;

    MI_SCL_ChnParam_t miSclChnParam;

    bool isChEnable = mIsEnabled;

    __SCL_CH_LOGD__("rot[%d]", rot);

    if (mSclDevId == E_SCL_DEV_ID_3) {

        disable();

        if (! (E_SCL_DEV_ID_0 <= mSclDevId && mSclDevId < E_SCL_DEV_ID_MAX)) {
            return false;
        }

        memset(&miSclChnParam, 0, sizeof(MI_SCL_ChnParam_t));
        ret = MI_SCL_GetChnParam(mSclDevId, mSclChnId, &miSclChnParam);
        if (ret != MI_SUCCESS) {
            __SCL_CH_LOGE__("MI_SCL_GetChnParam error ! err:%x", ret);
            return false;
        }

        miSclChnParam.eRot = (MI_SYS_Rotate_e) rot;

        ret = MI_SCL_SetChnParam(mSclDevId, mSclChnId, &miSclChnParam);
        if (ret != MI_SUCCESS) {
            __SCL_CH_LOGE__("MI_SCL_SetChnParam error ! err:%x", ret);
            return false;
        }

        if (isChEnable == true) {
            if (enable() == false) {
                __SCL_CH_LOGE__("enable error !");
                return false;
            }
        }
    } else {
        __SCL_CH_LOGE__("sclDevId[%d] not support !", mSclDevId);
        return false;
    }

    return true;
}

bool SclCh::setCrop(uint16_t x, uint16_t y, uint16_t w, uint16_t h)
{
    MI_S32 ret = -1;

    MI_SYS_WindowRect_t miInputCropWin;

    bool mIsEnabled = mIsEnabled;

    logd("sclDevId[%d] crop[%u, %u, %u, %u]", mSclDevId, x, y, w, h);

    if (mSclDevId != E_SCL_DEV_ID_2) {

        disable();

        w = ALIGN_UP(w, 2);
        h = ALIGN_UP(h, 2);

        memset(&miInputCropWin, 0, sizeof(MI_SYS_WindowRect_t));
        miInputCropWin.u16X = x;
        miInputCropWin.u16Y = y;
        miInputCropWin.u16Width = w;
        miInputCropWin.u16Height = h;

        if (! (E_SCL_DEV_ID_0 <= mSclDevId && mSclDevId < E_SCL_DEV_ID_MAX)) {
            return false;
        }

        ret = MI_SCL_SetInputPortCrop(mSclDevId, mSclChnId, &miInputCropWin);
        if (ret != MI_SUCCESS) {
            __SCL_CH_LOGE__("MI_SCL_SetInputPortCrop error ! err:%x", ret);
            return false;
        }

        if (mIsEnabled == true) {
            if (enable() == false) {
                __SCL_CH_LOGE__("enable error !");
                return false;
            }
        }
    } else {
        __SCL_CH_LOGE__("sclDevId[%d] not support !", mSclDevId);
        return false;
    }

    return true;
}

bool SclCh::disable()
{
    MI_S32 ret = -1;

    if (mIsEnabled == true) {
        __SCL_CH_LOGD__("disable");

        if (! (E_SCL_DEV_ID_0 <= mSclDevId && mSclDevId < E_SCL_DEV_ID_MAX)) {
            return false;
        }

        ret = MI_SCL_StopChannel(mSclDevId, mSclChnId);
        if (ret != MI_SUCCESS) {
            __SCL_CH_LOGE__("MI_SCL_StopChannel error ! err:%x", ret);
            return false;
        }

        mIsEnabled = false;
    }

    return true;
}

bool SclCh::enable()
{
    MI_S32 ret = -1;

    if (mIsEnabled == false) {
        __SCL_CH_LOGD__("enable");

        if (! (E_SCL_DEV_ID_0 <= mSclDevId && mSclDevId < E_SCL_DEV_ID_MAX)) {
            return false;
        }

        ret = MI_SCL_StartChannel(mSclDevId, mSclChnId);
        if (ret != MI_SUCCESS) {
            __SCL_CH_LOGE__("MI_SCL_StartChannel error ! err:%x", ret);
            return false;
        }

        mIsEnabled = true;
    }

    return true;
}

bool SclCh::bind(MI_ModuleId_e moduleId, MI_SYS_BindType_e bindType, uint8_t srcDevId, uint8_t srcChnId, uint8_t srcOutportId, uint8_t inFps, uint8_t outFps)
{
    if (unBind() == false) {
        __SCL_CH_LOGE__("unBind error !");
        return false;
    }

    if (mIsBound == false) {
        __SCL_CH_LOGD__("bind ...");

        memset(&mBindInfo, 0, sizeof(sysBindInfo_t));

        mBindInfo.stSrcChnPort.eModId = moduleId;
        mBindInfo.stSrcChnPort.u32DevId = srcDevId;
        mBindInfo.stSrcChnPort.u32ChnId = srcChnId;
        mBindInfo.stSrcChnPort.u32PortId = srcOutportId;

        mBindInfo.stDstChnPort.eModId = E_MI_MODULE_ID_SCL;
        mBindInfo.stDstChnPort.u32DevId = mSclDevId;
        mBindInfo.stDstChnPort.u32ChnId = mSclChnId;
        mBindInfo.stDstChnPort.u32PortId = 0;

        mBindInfo.u32SrcFrmrate = inFps;
        mBindInfo.u32DstFrmrate = outFps;
        mBindInfo.eBindType = bindType;

        if (sysBind(&mBindInfo) == false) {
            return false;
        }

        mIsBound = true;
    }

    return true;
}

bool SclCh::unBind()
{
    if (mIsBound == true) {
        __SCL_CH_LOGD__("unBind ...");

        if (sysUnBind(&mBindInfo) == false) {
            return false;
        }
        memset(&mBindInfo, 0, sizeof(sysBindInfo_t));

        mIsBound = false;
    }

    return true;
}
//---------------------------------------------------
bool SclCh::initOutport(sclOutportId_t sclOutportId, uint16_t inWidth, uint16_t inHeight, uint16_t outWidth, uint16_t outHeight)
{
    if (! (E_SCL_OUTPORT_ID_0 <= sclOutportId && sclOutportId < E_SCL_OUTPORT_ID_MAX)) {
        return false;
    }

    if (mSclOutport[sclOutportId] == nullptr) {

        mSclOutport[sclOutportId] = new SclOutport(mSclDevId, mSclChnId, sclOutportId);
        if (mSclOutport[sclOutportId]->init(inWidth, inHeight, outWidth, outHeight) == false) {
            __SCL_CH_LOGE__("init error !");
            delete mSclOutport[sclOutportId];
            mSclOutport[sclOutportId] = nullptr;
            return false;
        }
    }

    return true;
}

bool SclCh::uninitOutport(sclOutportId_t sclOutportId)
{
    if (! (E_SCL_OUTPORT_ID_0 <= sclOutportId && sclOutportId < E_SCL_OUTPORT_ID_MAX)) {
        return false;
    }

    if (mSclOutport[sclOutportId] != nullptr) {
        mSclOutport[sclOutportId]->uninit();
    }

    delete mSclOutport[sclOutportId];
    mSclOutport[sclOutportId] = nullptr;

    return true;
}

bool SclCh::setOutportSize(sclOutportId_t sclOutportId, uint16_t w, uint16_t h)
{
    if (! (E_SCL_OUTPORT_ID_0 <= sclOutportId && sclOutportId < E_SCL_OUTPORT_ID_MAX)) {
        return false;
    }

    if (mSclOutport[sclOutportId] == nullptr) {
        return false;
    }

    return mSclOutport[sclOutportId]->setSize(w, h);
}

bool SclCh::setOutportCrop(sclOutportId_t sclOutportId, uint16_t x, uint16_t y, uint16_t w, uint16_t h)
{
    if (! (E_SCL_OUTPORT_ID_0 <= sclOutportId && sclOutportId < E_SCL_OUTPORT_ID_MAX)) {
        return false;
    }

    if (mSclOutport[sclOutportId] == nullptr) {
        return false;
    }

    return mSclOutport[sclOutportId]->setCrop(x, y, w, h);
}

bool SclCh::setOutportMirror(sclOutportId_t sclOutportId, bool isMirror)
{
    if (! (E_SCL_OUTPORT_ID_0 <= sclOutportId && sclOutportId < E_SCL_OUTPORT_ID_MAX)) {
        return false;
    }

    if (mSclOutport[sclOutportId] == nullptr) {
        return false;
    }

    return mSclOutport[sclOutportId]->setMirror(isMirror);
}

bool SclCh::setOutportFlip(sclOutportId_t sclOutportId, bool isFlip)
{
    if (! (E_SCL_OUTPORT_ID_0 <= sclOutportId && sclOutportId < E_SCL_OUTPORT_ID_MAX)) {
        return false;
    }

    if (mSclOutport[sclOutportId] == nullptr) {
        return false;
    }

    return mSclOutport[sclOutportId]->setFlip(isFlip);
}

bool SclCh::setDepth(sclOutportId_t sclOutportId, uint8_t depth)
{
    if (! (E_SCL_OUTPORT_ID_0 <= sclOutportId && sclOutportId < E_SCL_OUTPORT_ID_MAX)) {
        return false;
    }

    if (mSclOutport[sclOutportId] == nullptr) {
        return false;
    }

    return mSclOutport[sclOutportId]->setDepth(depth);
}

bool SclCh::disableOutport(sclOutportId_t sclOutportId)
{
    if (! (E_SCL_OUTPORT_ID_0 <= sclOutportId && sclOutportId < E_SCL_OUTPORT_ID_MAX)) {
        return false;
    }

    if (mSclOutport[sclOutportId] == nullptr) {
        return false;
    }

    return mSclOutport[sclOutportId]->disable();
}

bool SclCh::enableOutport(sclOutportId_t sclOutportId)
{
    if (! (E_SCL_OUTPORT_ID_0 <= sclOutportId && sclOutportId < E_SCL_OUTPORT_ID_MAX)) {
        return false;
    }

    if (mSclOutport[sclOutportId] == nullptr) {
        return false;
    }

    return mSclOutport[sclOutportId]->enable();
}

int32_t SclCh::getfd(sclOutportId_t sclOutportId)
{
    if (! (E_SCL_OUTPORT_ID_0 <= sclOutportId && sclOutportId < E_SCL_OUTPORT_ID_MAX)) {
        return -1;
    }

    if (mSclOutport[sclOutportId] == nullptr) {
        return -1;
    }

    return mSclOutport[sclOutportId]->getfd();
}

bool SclCh::getstrm(sclOutportId_t sclOutportId, stream_t& stream, bool mempa)
{
    if (! (E_SCL_OUTPORT_ID_0 <= sclOutportId && sclOutportId < E_SCL_OUTPORT_ID_MAX)) {
        return false;
    }

    if (mSclOutport[sclOutportId] == nullptr) {
        return false;
    }

    return mSclOutport[sclOutportId]->getstrm(stream, mempa);
}

bool SclCh::freestrm(sclOutportId_t sclOutportId)
{
    if (! (E_SCL_OUTPORT_ID_0 <= sclOutportId && sclOutportId < E_SCL_OUTPORT_ID_MAX)) {
        return false;
    }

    if (mSclOutport[sclOutportId] == nullptr) {
        return false;
    }

    return mSclOutport[sclOutportId]->freestrm();
}
