#ifndef __SCL_OUTPUT_H__
#define __SCL_OUTPUT_H__

#include "defcomm.h"

#define SCL_OUTPORT_DEPTH_MAX_NUM       6
#define SCL_PHY_BUF_MAX_NUM             6

class SclOutport
{
public:
    SclOutport(sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId);
    ~SclOutport();

    bool init(uint16_t inWidth, uint16_t inHeight, uint16_t outWidth, uint16_t outHeight);
    bool uninit();

    bool setSize(uint16_t outWidth, uint16_t outHeight);
    bool setCrop(uint16_t x, uint16_t y, uint16_t w, uint16_t h);

    bool setMirror(bool isMirror);
    bool setFlip(bool isFlip);

    bool setDepth(uint8_t depth);

    bool disable();
    bool enable();

    int32_t getfd();
    bool getstrm(stream_t& stream, bool mempa = false);
    bool freestrm();

private:
    bool clrDepth();
    bool freestrm_();

    bool phylloc();
    bool phyfree();

private:
    int32_t                     mFd;

    sclDevId_t                  mSclDevId;
    uint8_t                     mSclChnId;
    sclOutportId_t              mSclOutportId;

    bool                        mIsEnabled;

    uint8_t                     mDepth;
    MI_SYS_BUF_HANDLE           mHandle[SCL_OUTPORT_DEPTH_MAX_NUM];
    uint8_t                     mHandleIdx;
    uint32_t                    mFrameNum;

    MI_PHY                      mPhyBufAddr[SCL_PHY_BUF_MAX_NUM];
    uint8_t                     mPhyBufIdx;
    uint8_t                     mPhyFrameNum;

    bool                        mIsPhyInited;

    MI_SYS_BufInfo_t            mBufInfo;

    bool                        mIsSetDepth;

    uint16_t                    mReslW;
    uint16_t                    mReslH;
};

#endif
