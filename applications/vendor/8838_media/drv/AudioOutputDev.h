#ifndef __AUDIO_OUTPUT_DEV_H__
#define __AUDIO_OUTPUT_DEV_H__

#include "defcomm.h"

#include "AudioOutputCh.h"

class AudioOutputDev
{
public:
    AudioOutputDev(audioOutputDevId_t audioOutputDevId);
    ~AudioOutputDev();

    bool init();
    bool uninit();

    bool enable();
    bool disable();

    bool setSampleRate(audioSampleRate_t sampleRate);

    //-------------------------------------------

    bool initCh(audioOutputChnId_t audioOutputChnId);
    bool uninitCh(audioOutputChnId_t audioOutputChnId);

    bool enableCh(audioOutputChnId_t audioOutputChnId);
    bool disableCh(audioOutputChnId_t audioOutputChnId);

    bool setVolume(audioOutputChnId_t audioOutputChnId, audioVolumeLevel_t audioVolumeLevel);

    bool reset(audioOutputChnId_t audioOutputChnId);

    bool pause(audioOutputChnId_t audioOutputChnId);
    bool resume(audioOutputChnId_t audioOutputChnId);

    bool putstrm(audioOutputChnId_t audioOutputChnId, const char *pcm, int32_t len);

private:

private:
    audioOutputDevId_t          mAuDevId;
    bool                        mIsEnabled;

    AudioOutputCh               *mAudioOutputCh[E_AUDIO_OUTPUT_CHN_ID_MAX];
};

#endif
