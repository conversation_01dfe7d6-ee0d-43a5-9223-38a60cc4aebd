#include "RGN.h"
#include "YUVDraw.h"

static MI_RGN_PaletteTable_t _rgnPaletteTable = {
    {
        //index0 ~ index15
        {255,   0,   0,   0}, {255, 255,   0,   0}, {255, 255, 255, 255}, {255,   0,   0, 255},
        {255, 255, 255,   0}, {255,   0, 255,   0}, {255,   0, 112, 255}, {255,   0, 255, 255},
        {255, 128,   0,   0}, {255, 128, 128,   0}, {255, 128,   0, 128}, {255,   0, 128,   0},
        {255,   0,   0,   0}, {255,   0, 128, 128}, {255, 128, 128, 128}, {255,  64,  64,  64},
        //index16 ~ index31
        {  0,   0,   0,   0}, {  0,   0,   0,  30}, {  0,   0, 255,  60}, {  0, 128,   0,  90},
        {255,   0,   0, 120}, {  0, 255, 255, 150}, {255, 255,   0, 180}, {  0, 255,   0, 210},
        {255,   0, 255, 240}, {192, 192, 192, 255}, {128, 128, 128,  10}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        //index32 ~ index47
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        //index48 ~ index63
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        //index64 ~ index79
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        //index80 ~ index95
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        //index96 ~ index111
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        //index112 ~ index127
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        //index128 ~ index143
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        //index144 ~ index159
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        //index160 ~ index175
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        //index176 ~ index191
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        //index192 ~ index207
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        //index208 ~ index223
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        //index224 ~ index239
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        // (index236 :192,160,224 defalut colorkey)
        {192, 160, 224, 255}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        //index240 ~ index255
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
        {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {192, 160, 224, 255}
    }
};

RGN::RGN()
{

}

RGN::~RGN()
{

}

bool RGN::init()
{
    MI_S32 ret = -1;

    memset(&mRGNInfo, 0, sizeof(RGNInfo_t) * RGN_CHN_MAX_NUM);

    ret = MI_RGN_Init(0, &_rgnPaletteTable);
    if (ret != MI_SUCCESS) {
        loge("MI_RGN_Init error !");
        return false;
    }

    return true;
}

bool RGN::uninit()
{
    MI_S32 ret = -1;

    for (uint16_t i = 0; i < RGN_CHN_MAX_NUM; i ++) {
        uninitCh(i);
    }

    ret = MI_RGN_DeInit(0);
    if (ret != MI_SUCCESS) {
        loge("MI_RGN_DeInit error !");
        return false;
    }

    return true;
}

//------------------------------------

bool RGN::initCh(uint16_t chn, uint16_t w, uint16_t h)
{
    MI_S32 ret = -1;
    MI_RGN_Attr_t miRgnAttr;

    logd("CHN[%u] w[%u] h[%u]", chn, w, h);

    if (! (0 <= chn && chn < RGN_CHN_MAX_NUM)) {
        return false;
    }

    if (w <= 0 ||
        h <= 0
    ) {
        return false;
    }

    if (mRGNInfo[chn].isValid == false) {

        memset(&miRgnAttr, 0, sizeof(MI_RGN_Attr_t));
        miRgnAttr.eType = E_MI_RGN_TYPE_OSD;
        miRgnAttr.stOsdInitParam.ePixelFmt = E_MI_RGN_PIXEL_FORMAT_I2; // 4位图，减少内存操作，提升性能，支持4种颜色
        miRgnAttr.stOsdInitParam.stSize.u32Width  = w;
        miRgnAttr.stOsdInitParam.stSize.u32Height = h;

        ret = MI_RGN_Create(0, chn, &miRgnAttr);
        if (ret != MI_SUCCESS) {
            loge("MI_RGN_DeInit error !");
            return false;
        }

        mRGNInfo[chn].isValid = true;
        mRGNInfo[chn].handle = chn;
        mRGNInfo[chn].pixelFmt = miRgnAttr.stOsdInitParam.ePixelFmt;
        memset(&mRGNInfo[chn].canvasInfo, 0, sizeof(MI_RGN_CanvasInfo_t));
    }

    return true;
}

bool RGN::uninitCh(uint16_t chn)
{
    MI_S32 ret = -1;

    if (! (0 <= chn && chn < RGN_CHN_MAX_NUM)) {
        return false;
    }

    if (mRGNInfo[chn].isValid == false) {
        return false;
    }

    if (detach(chn) == false) {
        loge("CHN[%u] detach error !", chn);
        return false;
    }

    if (mRGNInfo[chn].isValid == true) {

        logd("CHN[%u] destroy", chn);

        ret = MI_RGN_Destroy(0, chn);
        if (ret != MI_SUCCESS) {
            loge("MI_RGN_DeInit error !");
            return false;
        }

        memset(&mRGNInfo[chn], 0, sizeof(RGNInfo_t));
    }

    return true;
}

bool RGN::attach(uint16_t chn, sclDevId_t sclDevId, uint8_t sclChnId, sclOutportId_t sclOutportId, uint16_t x, uint16_t y)
{
    MI_S32 ret = -1;
    MI_RGN_ChnPortParam_t miChnPortParam;
    MI_RGN_CanvasInfo_t miRgnCanvasInfo;

    if (! (0 <= chn && chn < RGN_CHN_MAX_NUM)) {
        return false;
    }

    if (mRGNInfo[chn].isValid == false) {
        return false;
    }

    if (detach(chn) == false) {
        loge("CHN[%u] detach error !", chn);
        return false;
    }

    if (mRGNInfo[chn].isAttach == false) {

        logd("CHN[%u] sclDevId[%d] sclChnId[%u] sclOutportId[%d] x[%u] y[%u]", chn, sclDevId, sclChnId, sclOutportId, x, y);

        memset(&miChnPortParam, 0, sizeof(MI_RGN_ChnPortParam_t));
        memset(&mRGNInfo[chn].miChnPort, 0, sizeof(MI_RGN_ChnPort_t));

        mRGNInfo[chn].miChnPort.eModId = E_MI_MODULE_ID_SCL;
        mRGNInfo[chn].miChnPort.s32DevId = sclDevId;
        mRGNInfo[chn].miChnPort.s32ChnId = sclChnId;
        mRGNInfo[chn].miChnPort.s32PortId = sclOutportId;

        miChnPortParam.bShow = TRUE;
        miChnPortParam.stPoint.u32X = x;
        miChnPortParam.stPoint.u32Y = y;

        miChnPortParam.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;    //背景alpha
        miChnPortParam.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 0;
        miChnPortParam.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 0xff;

        miChnPortParam.unPara.stOsdChnPort.u32Layer = 10;    //区域层次，数字低得在底层

        // miChnPortParam.unPara.stOsdChnPort.stColorInvertAttr.bEnableColorInv = true;
        // miChnPortParam.unPara.stOsdChnPort.stColorInvertAttr.eInvertColorMode = E_MI_RGN_BELOW_LUMA_THRESHOLD;
        // miChnPortParam.unPara.stOsdChnPort.stColorInvertAttr.u16LumaThreshold = 80;
        // miChnPortParam.unPara.stOsdChnPort.stColorInvertAttr.u16WDivNum = 32;
        // miChnPortParam.unPara.stOsdChnPort.stColorInvertAttr.u16HDivNum = 32;

        ret = MI_RGN_AttachToChn(0, chn, &mRGNInfo[chn].miChnPort, &miChnPortParam);
        if (ret != MI_SUCCESS) {
            loge("MI_RGN_AttachToChn error !");
            return false;
        }

        mRGNInfo[chn].isAttach = true;   
    }

    return true;
}

bool RGN::detach(uint16_t chn)
{
    MI_S32 ret = -1;

    if (! (0 <= chn && chn < RGN_CHN_MAX_NUM)) {
        return false;
    }

    if (mRGNInfo[chn].isValid == false) {
        return false;
    }

    if (mRGNInfo[chn].isAttach == true) {

        logd("CHN[%u] detach", chn);

        ret = MI_RGN_DetachFromChn(0, chn, &mRGNInfo[chn].miChnPort);
        if (ret != MI_SUCCESS) {
            loge("MI_RGN_DetachFromChn error !");
            return false;
        }

        mRGNInfo[chn].isAttach = false;
    }

    return true;
}

//-----------------------------------------------------------------------

bool RGN::OSDDrawPoint(uint16_t chn, uint16_t x, uint16_t y, color_t color)
{
    MI_U8 *addr = nullptr;

    MI_U32 stride = 0;
    MI_U8 value = 0;
    MI_U8 offset = 0;

    if (! (0 <= chn && chn < RGN_CHN_MAX_NUM)) {
        return false;
    }

    if (mRGNInfo[chn].isValid == false) {
        return false;
    }

    if (x < 0 || x >= mRGNInfo[chn].canvasInfo.stSize.u32Width ||
        y < 0 || y >= mRGNInfo[chn].canvasInfo.stSize.u32Height
    ) {
        return false;
    }

    addr = (MI_U8 *) mRGNInfo[chn].canvasInfo.virtAddr;
    stride = mRGNInfo[chn].canvasInfo.u32Stride;

    if (mRGNInfo[chn].pixelFmt == E_MI_RGN_PIXEL_FORMAT_I2) {

        offset = (x % 4) * 2;
        addr = ((MI_U8 *) addr + (stride * y) + x / 4);
        *addr = ((*addr) & (~(0x03 << offset))) | ((color & 0x03) << offset);

    } else if (mRGNInfo[chn].pixelFmt == E_MI_RGN_PIXEL_FORMAT_I4) {

        offset = (x % 2) * 4;
        addr = ((MI_U8 *) addr + (stride * y) + x / 2);
        *addr = ((*addr) & (~(0x0f << offset))) | ((color & 0x0f) << offset);
    }

    return true;
}

bool RGN::OSDDrawLine(uint16_t chn, const point_t& p0, const point_t& p1, color_t color, uint16_t lineWidth)
{
    point_t s, e;

    if (! (0 <= chn && chn < RGN_CHN_MAX_NUM)) {
        return false;
    }

    if (mRGNInfo[chn].isValid == false) {
        return false;
    }

    s = p0;
    e = p1;

    uint16_t iw = mRGNInfo[chn].canvasInfo.stSize.u32Width;
    uint16_t ih = mRGNInfo[chn].canvasInfo.stSize.u32Height;

    lineWidth = lineWidth <= 0 ? 2 : lineWidth;

    s.x = (s.x + lineWidth >= iw) ? (iw - lineWidth) : (s.x < 0 ? 0 : s.x);
    e.x = (e.x + lineWidth >= iw) ? (iw - lineWidth) : (e.x < 0 ? 0 : e.x);
    s.y = (s.y + lineWidth >= ih) ? (ih - lineWidth) : (s.y < 0 ? 0 : s.y);
    e.y = (e.y + lineWidth >= ih) ? (ih - lineWidth) : (e.y < 0 ? 0 : e.y);

    int32_t dx = (s.x > e.x) ? (s.x - e.x) : (e.x - s.x);
    int32_t dy = (s.y > e.y) ? (s.y - e.y) : (e.y - s.y);
    int32_t xstep = (s.x < e.x) ? 1 : -1;
	int32_t ystep = (s.y < e.y) ? 1 : -1;
    int32_t nstep = 0, eps = 0;
	int32_t pointX = s.x;
	int32_t	pointY = s.y;

    if (dx > dy) {
        while (nstep <= dx) {
            OSDDrawPoint(chn, pointX, pointY, color);

            eps += dy;
            if ((eps << 1) >= dx) {
                pointY += ystep;
                eps -= dx;
            }

            pointX += xstep;
            nstep ++;
        }
    } else {
        while (nstep <= dy) {
            OSDDrawPoint(chn, pointX, pointY, color);

            eps += dx;
            if ((eps << 1) >= dy) {
                pointX += xstep;
                eps -= dy;
            }

            pointY += ystep;
            nstep ++;
        }
    }

    return true;
}

bool RGN::OSDDrawRectSolid(uint16_t chn, const RGN::rect_t& rect, color_t color)
{
    int32_t x0 = rect.x;
    int32_t y0 = rect.y;
    int32_t x1 = rect.x + rect.w;
    int32_t y1 = rect.y + rect.h;
    int32_t i = 0, j = 0;
	float k, e, dx, dy;

    int32_t x = 0, y = 0;

    if (! (0 <= chn && chn < RGN_CHN_MAX_NUM)) {
        return false;
    }

    if (mRGNInfo[chn].isValid == false) {
        return false;
    }

    x0 = (x0 < 0) ? 0 : x0;
    x0 = (x0 > mRGNInfo[chn].canvasInfo.stSize.u32Width) ?
                        mRGNInfo[chn].canvasInfo.stSize.u32Width : x0;
    y0 = (y0 < 0) ? 0 : y0;
    y0 = (y0 > mRGNInfo[chn].canvasInfo.stSize.u32Height) ?
                        mRGNInfo[chn].canvasInfo.stSize.u32Height: y0;

    x1 = (x1 < 0) ? 0 : x1;
    x1 = (x1 > mRGNInfo[chn].canvasInfo.stSize.u32Width) ?
                        mRGNInfo[chn].canvasInfo.stSize.u32Width : x1;
    y1 = (y1 < 0) ? 0 : y1;
    y1 = (y1 > mRGNInfo[chn].canvasInfo.stSize.u32Height) ?
                        mRGNInfo[chn].canvasInfo.stSize.u32Height: y1;

    if (x1 > x0) {
        dx = x1 - x0;
        x = x0;
    } else {
        dx = x0 - x1;
        x = x1;
    }

	dy = (float) (y1 - y0);

    if (y1 > y0) {
        dy = y1 - y0;
        y = y0;
    } else {
        dy = y0 - y1;
        y = y1;
    }

    if (dx <= 0 ||
        dy <= 0
    ) {
        return false;
    }

    for (uint16_t row = 0; row < dy; row ++) {
        for (uint16_t col = 0; col < dx; col ++) {
            OSDDrawPoint(chn, x + col, y + row, color);
        }
    }

    return true;
}

bool RGN::OSDDrawRectHollow(uint16_t chn, const RGN::rect_t& rect, color_t color)
{
    point_t left0, left1;
    point_t right0, right1;
    point_t top0, top1;
    point_t bot0, bot1;

    //左边竖线
    left0.x = rect.x;
    left0.y = rect.y;
    left1.x = left0.x;
    left1.y = left0.y + rect.h;

    //右边竖线
    right0.x = rect.x + rect.w;
    right0.y = rect.y;
    right1.x = right0.x;
    right1.y = right0.y + rect.h;

    //上面横线
    top0.x = rect.x;
    top0.y = rect.y;
    top1.x = rect.x + rect.w;
    top1.y = top0.y;

    //下面横线
    bot0.x = rect.x;
    bot0.y = rect.y + rect.h;
    bot1.x = rect.x + rect.w;
    bot1.y = bot0.y;

    OSDDrawLine(chn, left0, left1, color);
    OSDDrawLine(chn, right0, right1, color);
    OSDDrawLine(chn, top0, top1, color);
    OSDDrawLine(chn, bot0, bot1, color);

    return true;
}

bool RGN::OSDDrawRect(uint16_t chn, const rect_t& rect, color_t color, bool solid)
{
    if (! (0 <= chn && chn < RGN_CHN_MAX_NUM)) {
        return false;
    }

    if (mRGNInfo[chn].isValid == false) {
        return false;
    }

    if (solid == true) {
        return OSDDrawRectSolid(chn, rect, color);
    } else {
        return OSDDrawRectHollow(chn, rect, color);
    }

    return true;
}

void RGN::OSDFontDataToCanvas(const uint8_t *fontData, uint16_t x, uint16_t y, uint16_t w,
    uint16_t h, uint16_t chn, color_t color)
{
    int32_t i = 0, j = 0;
    int32_t char_num;
	int32_t char_bit;
	char bit;
    uint8_t *pFontdataTemp = nullptr;

    uint16_t x_ = 0, y_ = 0;

    if (fontData == nullptr) {
        return;
    }

    for (i = h - 1; i >= 0; i--) {
        pFontdataTemp = (uint8_t *) fontData + (w + 7) / 8 * i;

        for (j = 0; j < w; j ++) {
            char_num = j / 8;
		    char_bit = 7 - j % 8;
            bit = pFontdataTemp[char_num] & (1 << char_bit);

            x_ = x + j;
            y_ = y + i;

            if (bit) {
                OSDDrawPoint(chn, x_, y_, color);
            } else {

            }
        }
    }
}

extern uint32_t _YUVGb2312codeToFontoffset(uint32_t gb2312code, uint32_t font_height);
extern YUVBitMapFile_t *_OSDBitMapFileInfo(YUVFontType_t enType, YUVFontSize_t enSize);
extern uint32_t _YUVAsciiToFontoffset(uint32_t ascii, uint32_t width, uint32_t height);
extern void _OSDCalcBMPWH(int32_t charTotalNum, int32_t *bmpWidth, int32_t *bmpHeight, uint8_t *pGb2312);
extern int32_t _OSDCalcCharNumGetGb2312Code(const char *text, int32_t *charTotalNum, uint8_t *gb2312buf, int32_t bufLen);

bool RGN::OSDDrawText(uint16_t chn, uint16_t x, uint16_t y, color_t color, const char *text, YUVFontSize_t ftSize)
{
#define MAX_BUF_LEN     2048
#define MAX_LINES       16

    YUVBitMapFile_t *pstYUVBitMapFile = nullptr;
    YUVBitMapAttr_t *pstYUVBitMapAttr = &BitMapAttr;

    int32_t charTotalNum = 0;
    uint8_t gb2312buf[MAX_BUF_LEN * 2] = { 0 };
    int32_t totalLines = 0;
    int32_t charNumPerLine[MAX_LINES] = { 0 };
    int32_t charRemainNum = 0;
    int32_t i = 0;
    int32_t j = 0;
    int32_t bmpWidth = 0;
    int32_t bmpHeight = 0;
    int32_t gb2312Offset = 0;
    int32_t gb2312LineOffset = 0;
    uint8_t *pGb2312Line = nullptr;
    int32_t fontoffset = 0;
    int32_t fontWidth = 0;
    int32_t fontHeight = 0;
    uint8_t *fontAddr = nullptr;
    int32_t fontTotalWidth = 0;
    int32_t fontTotalHeight = 0;
    int32_t xpos = 0;
    int32_t ypos = 0;

    if (text == nullptr) {
        return false;
    }

    if (! (0 <= chn && chn < RGN_CHN_MAX_NUM)) {
        return false;
    }

    if (mRGNInfo[chn].isValid == false) {
        return false;
    }

    _OSDCalcCharNumGetGb2312Code(text, &charTotalNum, gb2312buf, MAX_BUF_LEN * 2);

    totalLines = ALIGN_MULTI(charTotalNum, pstYUVBitMapAttr->charNumPerLine);
    totalLines = MIN(totalLines, MAX_LINES);

    charRemainNum = charTotalNum;
    for (i = 0; i < totalLines; i ++)
    {
        charNumPerLine[i] = MIN(charRemainNum, pstYUVBitMapAttr->charNumPerLine);

        charRemainNum -= charNumPerLine[i];
    }

#if 0
    _OSDCalcBMPWH(charTotalNum, &bmpWidth, &bmpHeight, gb2312buf);

    logd("text:\t%s", text);
    logd("gb2312:\t%s", gb2312buf);
    logd("charTotalNum:\t%d", charTotalNum);
    logd("charNumPerLine:\t");
    int32_t offset = 0;
    char debugBuff[4096] = { 0 };
    for (i = 0; i < totalLines; i ++) {
        offset += snprintf(debugBuff + offset, sizeof(debugBuff) - offset, "%d ", charNumPerLine[i]);
    }
    logd("totalLines:\t%d", totalLines);
    logd("bmpWidth:\t%d", bmpWidth);
    logd("bmpHeight:\t%d\n", bmpHeight);;
#endif

    gb2312Offset = 0;
    for (i = 0; i < totalLines; i ++) {
        pGb2312Line = gb2312buf + gb2312Offset;
        gb2312LineOffset = 0;

        // logd("%p", pGb2312Line);

        for (j = 0; j < charNumPerLine[i]; j ++) {
            if (pGb2312Line[gb2312LineOffset] > 0xA0 &&
                pGb2312Line[gb2312LineOffset] < 0xff
            ) {
                pstYUVBitMapFile = _OSDBitMapFileInfo(YUV_FONT_TYPE_HZ, ftSize);
                if (pstYUVBitMapFile == nullptr) {
                    continue;
                }

                fontoffset = _YUVGb2312codeToFontoffset(pGb2312Line[gb2312LineOffset] +
						        0x100 * pGb2312Line[gb2312LineOffset + 1],
						        pstYUVBitMapFile->height);

                fontWidth = pstYUVBitMapFile->width;
                fontHeight = pstYUVBitMapFile->height;

                gb2312LineOffset += 2;

                fontAddr = pstYUVBitMapFile->bitMapAddr;
            } else if (pGb2312Line[gb2312LineOffset] > 0x1f &&
                        pGb2312Line[gb2312LineOffset] < 0x80
            ) {
                pstYUVBitMapFile = _OSDBitMapFileInfo(YUV_FONT_TYPE_ASCII, ftSize);
                if (pstYUVBitMapFile == nullptr) {
                    continue;
                }

                fontWidth = pstYUVBitMapFile->width;
                fontHeight = pstYUVBitMapFile->height;

                fontoffset = _YUVAsciiToFontoffset(pGb2312Line[gb2312LineOffset], fontWidth, fontHeight);

                gb2312LineOffset ++;

                fontAddr = pstYUVBitMapFile->bitMapAddr;
            } else {
                continue;
            }

            if (pstYUVBitMapAttr->verticalFlag == 0) {
                xpos = pstYUVBitMapAttr->leftMargin + fontTotalWidth +
                        j * pstYUVBitMapAttr->lineSpace;
                ypos = pstYUVBitMapAttr->upMargin + i * pstYUVBitMapFile->height +
                        i * pstYUVBitMapAttr->lineSpace;

                fontTotalWidth += fontWidth;
            } else if (pstYUVBitMapAttr->verticalFlag == 1) {

                xpos = pstYUVBitMapAttr->leftMargin + i * pstYUVBitMapFile->width +
                        i * pstYUVBitMapAttr->lineSpace;
                ypos = pstYUVBitMapAttr->upMargin + fontTotalHeight +
                        j * pstYUVBitMapAttr->lineSpace;

                fontTotalHeight += fontHeight;
            }

            // logd("xpos:%d, ypos:%d, gb2312LineOffset:%d, fontoffset:%d, fontWidth:%d,fontHeight:%d, 0x%x",
            //            xpos, ypos, gb2312LineOffset, fontoffset, fontWidth, fontHeight, (fontAddr + fontoffset)[0]);
            if (fontAddr != nullptr) {
                OSDFontDataToCanvas(fontAddr + fontoffset,
                    xpos + x, ypos + y,
                    fontWidth, fontHeight, chn, color);
            }
        }

        fontTotalWidth = 0;
        fontTotalHeight = 0;

        gb2312Offset += gb2312LineOffset;
    }

    return true;
}

//-----------------------------------------------------------------------

bool RGN::OSDStart(uint16_t chn)
{
    MI_S32 ret = -1;

    if (! (0 <= chn && chn < RGN_CHN_MAX_NUM)) {
        return false;
    }

    if (mRGNInfo[chn].isValid == false) {
        return false;
    }

    ret = MI_RGN_GetCanvasInfo(0, chn, &mRGNInfo[chn].canvasInfo);
    if (ret != MI_SUCCESS) {
        loge("MI_RGN_GetCanvasInfo error !");
        return false;
    }

    MI_RGN_CanvasInfo_t *miRgnCanvasInfo = nullptr;
    miRgnCanvasInfo = &mRGNInfo[chn].canvasInfo;
    memset((void *) miRgnCanvasInfo->virtAddr, 0, miRgnCanvasInfo->stSize.u32Height * miRgnCanvasInfo->u32Stride);

    return true;
}

bool RGN::OSDEnd(uint16_t chn)
{
    MI_S32 ret = -1;

    if (! (0 <= chn && chn < RGN_CHN_MAX_NUM)) {
        return false;
    }

    if (mRGNInfo[chn].isValid == false) {
        return false;
    }

    ret = MI_RGN_UpdateCanvas(0, chn);
    if (ret != MI_SUCCESS) {
        loge("MI_RGN_UpdateCanvas error !");
        return false;
    }

    return true;
}

uint16_t RGN::getReslW(uint16_t chn)
{
    if (! (0 <= chn && chn < RGN_CHN_MAX_NUM)) {
        return 0;
    }

    if (mRGNInfo[chn].isValid == false) {
        return 0;
    }

    return mRGNInfo[chn].canvasInfo.stSize.u32Width;
}

uint16_t RGN::getReslH(uint16_t chn)
{
    if (! (0 <= chn && chn < RGN_CHN_MAX_NUM)) {
        return 0;
    }

    if (mRGNInfo[chn].isValid == false) {
        return 0;
    }

    return mRGNInfo[chn].canvasInfo.stSize.u32Height;
}

RGN *RGN::getInstance()
{
    static std::mutex mutex;
    static RGN *_instance = nullptr;

    if (_instance == nullptr) {
        std::lock_guard<std::mutex> lock(mutex);

        if (_instance == nullptr) {
            _instance = new RGN();
        }
    }
    return _instance;
}
