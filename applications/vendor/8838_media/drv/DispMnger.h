#ifndef __DISP_MNGER_H__
#define __DISP_MNGER_H__

#include "defcomm.h"
#include "DispInPort.h"
#include "DispLayer.h"
#include "Disp.h"

class DispMnger
{
private:
    DispMnger();
    ~DispMnger();

public:

    bool init();
    bool uninit();

    //-----------------------------------------------------------

    bool initDev(dispDevId_t dispDevId, dispIntface_t intf);
    bool uninitDev(dispDevId_t dispDevId);

    bool disableDev(dispDevId_t dispDevId);
    bool enableDev(dispDevId_t dispDevId);

    //-----------------------------------------------------------

    bool initLayer(dispLayerId_t dispLayerId);
    bool uninitLayer(dispLayerId_t dispLayerId);

    bool disableLayer(dispLayerId_t dispLayerId);
    bool enableLayer(dispLayerId_t dispLayerId);

    bool layerBindDev(dispLayerId_t dispLayerId, dispDevId_t dispDevId, uint16_t w, uint16_t h);
    bool layerUnBindDev(dispLayerId_t dispLayerId);
    //-----------------------------------------------------------

    bool initInPort(dispLayerId_t dispLayerId, dispInPortId_t inPortId);
    bool uninitInPort(dispLayerId_t dispLayerId, dispInPortId_t inPortId);

    bool setInPortWin(dispLayerId_t dispLayerId, dispInPortId_t inPortId, uint16_t x, uint16_t y, uint16_t w, uint16_t h);

    bool inPortBind(dispLayerId_t dispLayerId, dispInPortId_t inPortId, MI_ModuleId_e moduleId, 
        uint8_t srcDevId, uint8_t srcChnId, uint8_t srcOutportId, dispDevId_t dispDevId, uint8_t inFps, uint8_t outFps);
    bool inPortUnBind(dispLayerId_t dispLayerId, dispInPortId_t inPortId);

    bool inPortDisable(dispLayerId_t dispLayerId, dispInPortId_t inPortId);
    bool inPortEnable(dispLayerId_t dispLayerId, dispInPortId_t inPortId);
    //-----------------------------------------------------------

    static DispMnger *getInstance();

private:

private:

    Disp            *mDisp[E_DISP_DEV_ID_MAX];
    DispLayer       *mDispLayer[E_DISP_LAYER_ID_MAX];
};

#endif
