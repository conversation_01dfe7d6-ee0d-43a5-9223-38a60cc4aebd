#ifndef __ISP_CH_H__
#define __ISP_CH_H__

#include "defcomm.h"
#include "IspOutport.h"

class IspCh
{
public:
    IspCh(ispDevId_t ispDevId, uint8_t ispChnId);
    ~IspCh();

    bool init();
    bool uninit();

    /**
     * @brief
     * 对输入图像做旋转，所有通道的output输出的是旋转后的图像
     */
    bool setRot(IspRot_t rot);

    /**
     * @brief
     * 对输入图像做水平翻转，所有通道的output输出的是翻转后的图像
     */
    bool setMirror(bool isMirror);

    /**
     * @brief 
     * 禁用通道，禁用后output也不会有数据输出
     */
    bool disable();
    bool enable();

    bool bind(MI_ModuleId_e moduleId, MI_SYS_BindType_e bindType, uint8_t srcDevId, uint8_t srcChnId, uint8_t srcOutportId, uint8_t inFps, uint8_t outFps);

    //------------------------------------------------------------------

    bool initOutport(ispOutportId_t ispOutportId, uint16_t inWidth, uint16_t inHeight, uint16_t outWidth, uint16_t outHeight);
    bool uninitOutport(ispOutportId_t ispOutportId);

    bool setDepth(ispOutportId_t ispOutportId, uint8_t depth);

    bool disableOutport(ispOutportId_t ispOutportId);
    bool enableOutport(ispOutportId_t ispOutportId);

    int32_t getfd(ispOutportId_t ispOutportId);
    bool getstrm(ispOutportId_t ispOutportId, stream_t& stream);
    bool freestrm(ispOutportId_t ispOutportId);

private:
    bool unBind();

private:
    ispDevId_t              mIspDevId;
    uint8_t                 mIspChnId;

    IspOutport              *mIspOutport[E_ISP_OUTPORT_ID_MAX];

    bool                    mIsInited;
    bool                    mIsEnabled;

    sysBindInfo_t           mBindInfo;
    bool                    mIsBound;
};

#endif
