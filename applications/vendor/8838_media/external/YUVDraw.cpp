#include "YUVDraw.h"
#include "mystd.h"

#include "gb2312Code.h"

YUVBitMapAttr_t BitMapAttr = {
    .charNumPerLine = 32,
    .bgColor = 0x2323,
    .fgColor = 0xFFFFFF,
    .leftMargin = 1,
    .rightMargin = 1,
    .upMargin = 1,
    .downMargin = 1,
    .verticalFlag = 0,
    //.verticalFlag = 1,
    .charSpace = 1,
    .lineSpace = 1,
};

YUVBitMapFile_t BitMapFile[YUV_FONT_TYPE_MAX][E_YUV_FONT_SIZE_MAX] = {
    {
         {
            .file = YUV_FONT_PREFIX YUV_FONT_ASCII_8x16,
            .fd = -1,
            .bitMapAddr = nullptr,
            .width = 8,
            .height = 16,
        }, {
            .file = YUV_FONT_PREFIX YUV_FONT_ASCII_16x32,
            .fd = -1,
            .bitMapAddr = nullptr,
            .width = 16,
            .height = 32,
        },/* {
            .file = YUV_FONT_PREFIX YUV_FONT_ASCII_24x48,
            .fd = -1,
            .bitMapAddr = nullptr,
            .width = 24,
            .height = 48,
        }, {
            .file = YUV_FONT_PREFIX YUV_FONT_ASCII_32x64,
            .fd = -1,
            .bitMapAddr = nullptr,
            .width = 32,
            .height = 64,
        },*/
    }, {
        {
            .file = YUV_FONT_PREFIX YUV_FONT_HZ_16x16,
            .fd = -1,
            .bitMapAddr = nullptr,
            .width = 16,
            .height = 16,
        }, {
            .file = YUV_FONT_PREFIX YUV_FONT_HZ_32x32,
            .fd = -1,
            .bitMapAddr = nullptr,
            .width = 32,
            .height = 32,
        }, /*{
            .file = YUV_FONT_PREFIX YUV_FONT_HZ_48x48,
            .fd = -1,
            .bitMapAddr = nullptr,
            .width = 48,
            .height = 48,
        }, {
            .file = YUV_FONT_PREFIX YUV_FONT_HZ_64x64,
            .fd = -1,
            .bitMapAddr = nullptr,
            .width = 64,
            .height = 64,
        },*/
    },
};

/**
 * @brief 
 * YUV画框相关代码
 */
static inline bool _YUVDrawPoint(uint8_t *yuvData, int32_t yuvlen, int32_t iw, int32_t ih, const YUVPoint_t& point, const YUVColor_t& color, bool reverseY = false)
{
    int32_t yOffset = 0;
    int32_t uOffset = 0;
    int32_t vOffset = 0;

    if (yuvData == nullptr ||
        iw <= 0 ||
        ih <= 0 ||
        point.x < 0 ||
        point.y < 0 ||
        point.x >= iw ||
        point.y >= ih ||
        (iw * ih + iw * ih / 2) != yuvlen
    ) {
        return false;
    }

    yOffset = point.y * iw + point.x;
    uOffset = ih * iw + (point.y / 2) * iw + point.x / 2 * 2;
    vOffset = uOffset + 1;

    if (reverseY) {
        int32_t diffY = yuvData[yOffset] - color.y;

        yuvData[yOffset] = (color.y + (abs(diffY) < 80) * (diffY + 80)) % 0xff;
        yuvData[uOffset] = color.u;
        yuvData[vOffset] = color.v;
    } else {
        yuvData[yOffset] = color.y;
        yuvData[uOffset] = color.u;
        yuvData[vOffset] = color.v;
    }

    return true;
}

bool YUVDrawLine(uint8_t *yuvData, int32_t yuvlen, int32_t iw, int32_t ih, YUVLine_t& line, const YUVColor_t& color)
{
    if (yuvData == nullptr ||
        iw <= 0 ||
        ih <= 0 ||
        line.p0.x < 0 ||
        line.p0.y < 0 ||
        line.p1.x < 0 ||
        line.p1.y < 0 ||
        line.p0.x >= iw ||
        line.p0.y >= ih ||
        line.p1.x >= iw ||
        line.p1.y >= ih ||
        line.lineWidth <= 0
    ) {
        return false;
    }

    line.lineWidth = line.lineWidth <= 0 ? 2 : line.lineWidth;

    line.p0.x = (line.p0.x + line.lineWidth >= iw) ? (iw - line.lineWidth) : (line.p0.x < 0 ? 0 : line.p0.x);
    line.p1.x = (line.p1.x + line.lineWidth >= iw) ? (iw - line.lineWidth) : (line.p1.x < 0 ? 0 : line.p1.x);
    line.p0.y = (line.p0.y + line.lineWidth >= ih) ? (ih - line.lineWidth) : (line.p0.y < 0 ? 0 : line.p0.y);
    line.p1.y = (line.p1.y + line.lineWidth >= ih) ? (ih - line.lineWidth) : (line.p1.y < 0 ? 0 : line.p1.y);

    int32_t dx = (line.p0.x > line.p1.x) ? (line.p0.x - line.p1.x) : (line.p1.x - line.p0.x);
    int32_t dy = (line.p0.y > line.p1.y) ? (line.p0.y - line.p1.y) : (line.p1.y - line.p0.y);
    int32_t xstep = (line.p0.x < line.p1.x) ? 1 : -1;
	int32_t ystep = (line.p0.y < line.p1.y) ? 1 : -1;
    int32_t nstep = 0, eps = 0;
	int32_t pointX = line.p0.x;
	int32_t	pointY = line.p0.y;

    if (dx > dy) {
        while (nstep <= dx) {
            if (_YUVDrawPoint(yuvData, yuvlen, iw, ih, YUVPoint(pointX, pointY), color) == false) {
                return false;
            }

            eps += dy;
            if ((eps << 1) >= dx) {
                pointY += ystep;
                eps -= dx;
            }

            pointX += xstep;
            nstep ++;
        }
    } else {
        while (nstep <= dy) {
            if (_YUVDrawPoint(yuvData, yuvlen, iw, ih, YUVPoint(pointX, pointY), color) == false) {
                return false;
            }

            eps += dx;
            if ((eps << 1) >= dy) {
                pointX += xstep;
                eps -= dy;
            }

            pointY += ystep;
            nstep ++;
        }
    }

    return true;
}

/**
 * @brief 
 * YUV画矩形框接口
 * @param yuvData yuv原始数据
 * @param yuvlen yuv数据长度
 * @param iw    yuv宽度
 * @param ih    yuv高度
 * @param rect 需要画的矩形区域
 * @param color 矩形边框颜色，使用宏 YUV_COLOR_RED/YUV_COLOR_BLUE
 * @return true 
 * @return false 
 */
bool YUVDrawRect(uint8_t *yuvData, int32_t yuvlen, int32_t iw, int32_t ih, const YUVRect_t& rect, const YUVColor_t& color)
{
    YUVLine_t left;
    YUVLine_t right;
    YUVLine_t top;
    YUVLine_t bot;

    //左边竖线
    left.p0.x = rect.x;
    left.p0.y = rect.y;
    left.p1.x = left.p0.x;
    left.p1.y = left.p0.y + rect.h;
    left.lineWidth = 2;

    //右边竖线
    right.p0.x = rect.x + rect.w;
    right.p0.y = rect.y;
    right.p1.x = right.p0.x;
    right.p1.y = right.p0.y + rect.h;
    right.lineWidth = 2;

    //上面横线
    top.p0.x = rect.x;
    top.p0.y = rect.y;
    top.p1.x = rect.x + rect.w;
    top.p1.y = top.p0.y;
    top.lineWidth = 2;

    //下面横线
    bot.p0.x = rect.x;
    bot.p0.y = rect.y + rect.h;
    bot.p1.x = rect.x + rect.w;
    bot.p1.y = bot.p0.y;
    bot.lineWidth = 2;

    // printf("left %d %d %d %d\n", left.p0.x, left.p0.y, left.p1.x, left.p1.y);
    // printf("right %d %d %d %d\n", right.p0.x, right.p0.y, right.p1.x, right.p1.y);
    // printf("top %d %d %d %d\n", top.p0.x, top.p0.y, top.p1.x, top.p1.y);
    // printf("bot %d %d %d %d\n", bot.p0.x, bot.p0.y, bot.p1.x, bot.p1.y);

    if (YUVDrawLine(yuvData, yuvlen, iw, ih, left, color) == false) {
        printf("YUVDrawLine left error !\n");
        return false;
    }

    if (YUVDrawLine(yuvData, yuvlen, iw, ih, right, color) == false) {
        printf("YUVDrawLine right error !\n");
        return false;
    }

    if (YUVDrawLine(yuvData, yuvlen, iw, ih, top, color) == false) {
        printf("YUVDrawLine top error !\n");
        return false;
    }

    if (YUVDrawLine(yuvData, yuvlen, iw, ih, bot, color) == false) {
        printf("YUVDrawLine bottom error !\n");
        return false;
    }

    return true;
}

//-----------------------------------------------------------------------------------------------------------
//-----------------------------------------------------------------------------------------------------------
//-----------------------------------------------------------------------------------------------------------

/**
 * @brief 
 *  以下是测试代码
 */

static uint64_t _startMs = 0ULL;
static inline void _clockInit()
{
    struct timespec startTime = { 0 };
    clock_gettime(CLOCK_MONOTONIC, &startTime);
    if (_startMs <= 0) {
        _startMs = (startTime.tv_sec * 1000 + (startTime.tv_nsec / (1000 * 1000)));
    }
}

static inline uint64_t _getClockMs()
{
    struct timespec now = { 0 };
    uint64_t ms = 0;
    uint64_t current = 0;

    clock_gettime(CLOCK_MONOTONIC, &now);
    current = (now.tv_sec * 1000 + (now.tv_nsec / (1000 * 1000)));
    ms = current - _startMs;
    return (uint64_t) ms;
}

static inline int32_t _YUVLoadFileSize(const char *file)
{
    struct stat s;
    if (file == nullptr ||
        file[0] == '\0' ||
        access(file, F_OK) != 0
    ) {
        return -1;
    }

    stat(file, &s);
    return s.st_size;
}

static inline bool _YUVLoadData(const char *file, int32_t iw, int32_t ih, uint8_t *& yuvData, int32_t& yuvlen)
{
    int32_t n = 0;
    int32_t offset = 0;

    uint8_t *yuvData_ = nullptr;
    int32_t yuvlen_ = 0;
    int32_t yuvlenTmp_ = 0;

    FILE *fp = nullptr;
    
    yuvData = nullptr;
    yuvlen = 0;

    yuvlen_ = _YUVLoadFileSize(file);
    if (yuvlen_ <= 0) {
        printf("%s is empty or not exist !\n", file);
        return false;
    }

    if ((iw * ih + iw * ih / 2) != yuvlen_) {
        printf("just support YUV NV12 !\n");
        return false;
    }

    yuvData_ = (uint8_t *) calloc(1, yuvlen_);
    if (yuvData_ == nullptr) {
        printf("calloc error !\n");
        return false;
    }

    fp = fopen(file, "rb");
    if (fp == nullptr) {
        printf("fopen '%s' error !\n", file);
        goto error;
    }

    yuvlenTmp_ = yuvlen_;
    printf("yuvlen: %d\n", yuvlenTmp_);

    for ( ; yuvlenTmp_ > 0; ) {
        n = fread(yuvData_ + offset, 1, MIN(1024, yuvlenTmp_), fp);
        if (n <= 0) {
            printf("fread '%s' error !\n", file);
            goto error;
        }

        offset += n;
        yuvlenTmp_ -= n;
    }
    fclose(fp);

    yuvData = yuvData_;
    yuvlen = yuvlen_;

    return true;

error:
    if (fp)         fclose(fp);
    if (yuvData_)    free(yuvData_);
    return false;
}

static inline bool _YUVSaveData(const uint8_t *yuvData, int32_t yuvlen, const char *file)
{
    FILE *fp = nullptr;
    int32_t n = 0;
    int32_t offset = 0;

    if (yuvData == nullptr ||
        yuvlen <= 0 ||
        file == nullptr ||
        file[0] == '\0'
    ) {
        return false;
    }

    unlink(file);

    fp = fopen(file, "w+");
    if (fp == nullptr) {
        printf("fopen '%s' error !\n", file);
        return false;
    }

    for ( ; yuvlen > 0; ) {
        n = fwrite(yuvData + offset, 1, MIN(1024, yuvlen), fp);
        if (n <= 0) {
            printf("fwrite error !\n");
            goto error;
        }

        offset += n;
        yuvlen -= n;
    }

    fclose(fp);
    return true;

error:
    if (fp)         fclose(fp);
    return false;
}


static int32_t _YUVGetUtf8Length(const uint8_t *src)
{
	switch (*src) {
	case 0x0 ... 0x7f:
		return 1;
	case 0xC0 ... 0xDF:
		return 2;
	case 0xE0 ... 0xEF:
		return 3;
	case 0xF0 ... 0xF7:
		return 4;
	default:
		return -1;
	}
}
/*
 * see: https://github.com/JulienPalard/is_utf8/blob/master/is_utf8.c
 * Check if the given uint8_t * is a valid utf-8 sequence.
 *
 * Return value :
 * If the string is valid utf-8, 0 is returned.
 * IF -1 returned, it is very likely valid utf-8.
 * Else the position, starting from 1, is returned.
 *
 * Valid utf-8 sequences look like this :
 * 0xxxxxxx
 * 110xxxxx 10xxxxxx
 * 1110xxxx 10xxxxxx 10xxxxxx
 * 11110xxx 10xxxxxx 10xxxxxx 10xxxxxx
 * 111110xx 10xxxxxx 10xxxxxx 10xxxxxx 10xxxxxx
 * 1111110x 10xxxxxx 10xxxxxx 10xxxxxx 10xxxxxx 10xxxxxx
 */
static int32_t _YUVIsUtf8(uint8_t *string, int32_t len)
{
    size_t i = 0;
	size_t continuation_bytes = 0;
	int32_t quick_flag = 0;
    int32_t count = 0;

	while (i < len) {
        // printf("%s %d, 0x%X, len:%d\n", __func__, __LINE__, string[i], len);
		switch (string[i]) {
		case 0x0 ... 0x7f:
			continuation_bytes = 0;
			break;
		case 0xC0 ... 0xDF:
			continuation_bytes = 1;
			break;
		case 0xE0 ... 0xEF:
			continuation_bytes = 2;
			break;
		case 0xF0 ... 0xF4: /* Cause of RFC 3629 */
			continuation_bytes = 3;
			quick_flag = 1;
			break;
		default:
			return i + 1;
		}
		i += 1;
		while (i < len && continuation_bytes > 0
			&& string[i] >= 0x80
			&& string[i] <= 0xBF
        ) {
			i += 1;
			continuation_bytes -= 1;

            count ++;
		}

		if (continuation_bytes != 0) {
			return i + 1;
        } else if (quick_flag) {
			return -1;
        }
	}

	return count;
}

static int32_t _OSDUtf8ToUnicode(const uint8_t *src, uint8_t *dst)
{
    int32_t length;
	uint8_t unicode[2] = { 0 };

    length = _YUVGetUtf8Length(src);
    if (length < 0) {
        return -1;
    }

	switch (length) {
	case 1:
		*dst = *src;
		*(dst + 1) = 0;
		return 1;
	case 2:
		unicode[0] = *(src + 1) & 0x3f;
		unicode[0] += (*src & 0x3) << 6;
		unicode[1] = (*src & 0x7 << 2) >> 2;
		break;
	case 3:
		unicode[0] = *(src + 2) & 0x3f;
		unicode[0] += (*(src + 1) & 0x3) << 6;
		unicode[1] = (*(src + 1) & 0xF << 2) >> 2;
		unicode[1] += (*src & 0xf) << 4;
		break;
	case 4:
		/* not support now */
		return -1;
	}

	*dst = unicode[0];
	*(dst + 1) = unicode[1];

	return length;
}

static int32_t _OSDUnicodeToGb2312(uint16_t unicode, const uint16_t *mem_gb2312, int32_t gb2312_num)
{
    int32_t i = 0;

    for (i = 0; i < gb2312_num; i ++) {
        if (mem_gb2312[2 * i] == unicode) {
            return mem_gb2312[2 * i + 1];
        }
    }

	return -1;
}

int32_t _OSDCalcCharNumGetGb2312Code(const char *text, int32_t *charTotalNum, uint8_t *gb2312buf, int32_t bufLen)
{
    int32_t charNum = 0, strLen = 0, dealLen = 0;
    int32_t ret = 0;
    uint8_t unicode[2] = { 0 };
    const uint8_t *ptr = nullptr;
    uint16_t gb2312_code;
    int32_t gb2312codeLen = sizeof(gb2312code) / sizeof(gb2312code[0]) / 3;
    uint8_t *gb2312;

    if (text == nullptr) {
        return 0;
    }

    strLen = strlen(text);
    ptr = (uint8_t *) text;
    gb2312 = gb2312buf;

    do {
        ret = _OSDUtf8ToUnicode(ptr, unicode);
        // printf("%s %d, 0x%X, 0x%X\n", __func__, __LINE__, unicode[0], unicode[1]);
        if (ret < 0) {
            return -1;
        }

        ptr += ret;
        dealLen += ret;
        charNum ++;

        gb2312_code = _OSDUnicodeToGb2312(unicode[0] +
                unicode[1] * 0x100, gb2312code, gb2312codeLen);

        // printf("%s %d, 0x%X, %p\n", __func__, __LINE__, gb2312_code, gb2312);
        gb2312[0] = gb2312_code % 0x100;
        if (gb2312_code / 0x100 > 0) {
            gb2312[1] = gb2312_code / 0x100;
            gb2312 += 2;
        }
        else {
            gb2312 += 1;
        }
    } while (dealLen < strLen);

    *charTotalNum = charNum;

    return 0;
}

void _OSDCalcBMPWH(int32_t charTotalNum, int32_t *bmpWidth, int32_t *bmpHeight, uint8_t *pGb2312)
{
    YUVBitMapFile_t *pstYUVBitMapFile = &BitMapFile[YUV_FONT_TYPE_ASCII][E_YUV_FONT_SIZE_16x16];
    YUVBitMapAttr_t *pstYUVBitMapAttr = &BitMapAttr;

    int32_t width = 0, height = 0;
    int32_t lines = 0, i = 0, offset = 0;
    int32_t charNumPerLine = 0;

    charNumPerLine = MIN(charTotalNum, pstYUVBitMapAttr->charNumPerLine);

    lines = ALIGN_MULTI(charTotalNum, pstYUVBitMapAttr->charNumPerLine);

    offset = 0;
    if (pstYUVBitMapAttr->verticalFlag == 0) {
        for (i = 0; i < charNumPerLine; i ++) {
            if (pGb2312[offset] > 0xA0 &&
                pGb2312[offset] < 0xff
            ) {
                width += pstYUVBitMapFile->width;

                offset += 2;
            } else if (pGb2312[offset] > 0x1f &&
                    pGb2312[offset] < 0x80
            ) {
                width += pstYUVBitMapFile->width;

                offset ++;
            }
        }

        for (i = 0; i < lines; i ++) {
            height += pstYUVBitMapFile->height;
        }
    } else if (pstYUVBitMapAttr->verticalFlag == 1) {
        for (i = 0; i < charNumPerLine; i ++) {
            height += pstYUVBitMapFile->height;
        }

        for (i = 0; i < lines; i ++) {
            width += pstYUVBitMapFile->width;
        }
    }

    if (pstYUVBitMapAttr->verticalFlag == 0) {
        width += pstYUVBitMapAttr->leftMargin +
                pstYUVBitMapAttr->charSpace * (charTotalNum - 1) +
                pstYUVBitMapAttr->rightMargin;

        height += pstYUVBitMapAttr->upMargin +
                pstYUVBitMapAttr->lineSpace * (lines - 1) +
                pstYUVBitMapAttr->downMargin;
    } else if (pstYUVBitMapAttr->verticalFlag == 1) {

        width += pstYUVBitMapAttr->leftMargin +
                pstYUVBitMapAttr->lineSpace * (lines - 1) +
                pstYUVBitMapAttr->rightMargin;

        height += pstYUVBitMapAttr->upMargin +
                pstYUVBitMapAttr->charSpace * (charTotalNum - 1) +
                pstYUVBitMapAttr->downMargin;
    }

    *bmpWidth = width;
    *bmpHeight = height;
}

YUVBitMapFile_t *_OSDBitMapFileInfo(YUVFontType_t enType, YUVFontSize_t enSize)
{
    if (enType < YUV_FONT_TYPE_ASCII || enType >= YUV_FONT_TYPE_MAX) {
        return nullptr;
    }

    if (enSize < E_YUV_FONT_SIZE_16x16 || enSize >= E_YUV_FONT_SIZE_MAX) {
        return nullptr;
    }

    return &BitMapFile[enType][enSize];
}

uint32_t _YUVGb2312codeToFontoffset(uint32_t gb2312code, uint32_t font_height)
{
    uint32_t fontoffset;

	fontoffset = (gb2312code % 0x100 - 0xA1) * 94
		            + (gb2312code / 0x100 - 0xA1);
	fontoffset *= (font_height * font_height / 8);

	return fontoffset;
}

uint32_t _YUVAsciiToFontoffset(uint32_t ascii, uint32_t width, uint32_t height)
{
    uint32_t size = 0;

    size = (width / 8) * height;

    return ascii * size;
    // return (ascii)* 16;
	// return (ascii * 16) + 1;
}

static void _OSDFontDataToCanvas(const uint8_t *fontData, int32_t x, int32_t y, int32_t width,
    int32_t height, uint8_t *yuvData, int32_t yuvlen, int32_t iw, int32_t ih, const YUVColor_t& color)
{
    int32_t i = 0, j = 0;
    int32_t char_num;
	int32_t char_bit;
	char bit;
    uint8_t *pFontdataTemp = nullptr;
    YUVPoint_t point;

    if (fontData == nullptr) {
        return;
    }

    // OSDDrawRect(handle, point.x, point.y, width, height, color);

    for (i = height - 1; i >= 0; i--) {
        pFontdataTemp = (uint8_t *) fontData + (width + 7) / 8 * i;

        for (j = 0; j < width; j ++) {
            char_num = j / 8;
		    char_bit = 7 - j % 8;
            bit = pFontdataTemp[char_num] & (1 << char_bit);

            point.x = x + j;
            point.y = y + i;

            if (bit) {
                _YUVDrawPoint(yuvData, yuvlen, iw, ih, point, color, true);
            } else {

            }
        }
    }
}

static void _OSDDrawTextToCanvas(uint8_t *yuvData, int32_t yuvlen, int32_t iw, int32_t ih, 
    YUVPoint_t *point, const char *text, const YUVColor_t& color, YUVFontSize_t ftSize)
{
#define MAX_BUF_LEN     2048
#define MAX_LINES       16

    YUVBitMapFile_t *pstYUVBitMapFile = nullptr;
    YUVBitMapAttr_t *pstYUVBitMapAttr = &BitMapAttr;

    int32_t charTotalNum = 0;
    uint8_t gb2312buf[MAX_BUF_LEN * 2] = { 0 };
    int32_t totalLines = 0;
    int32_t charNumPerLine[MAX_LINES] = { 0 };
    int32_t charRemainNum = 0;
    int32_t i = 0;
    int32_t j = 0;
    int32_t bmpWidth = 0;
    int32_t bmpHeight = 0;
    int32_t gb2312Offset = 0;
    int32_t gb2312LineOffset = 0;
    uint8_t *pGb2312Line = nullptr;
    int32_t fontoffset = 0;
    int32_t fontWidth = 0;
    int32_t fontHeight = 0;
    uint8_t *fontAddr = nullptr;
    int32_t fontTotalWidth = 0;
    int32_t fontTotalHeight = 0;
    int32_t xpos = 0;
    int32_t ypos = 0;

    if (text == nullptr || text[0] == '\0') {
        return;
    }

    _OSDCalcCharNumGetGb2312Code(text, &charTotalNum, gb2312buf, MAX_BUF_LEN * 2);

    totalLines = ALIGN_MULTI(charTotalNum, pstYUVBitMapAttr->charNumPerLine);
    totalLines = MIN(totalLines, MAX_LINES);

    charRemainNum = charTotalNum;
    for (i = 0; i < totalLines; i ++)
    {
        charNumPerLine[i] = MIN(charRemainNum, pstYUVBitMapAttr->charNumPerLine);

        charRemainNum -= charNumPerLine[i];
    }

    _OSDCalcBMPWH(charTotalNum, &bmpWidth, &bmpHeight, gb2312buf);

#if 0
    logd("text:\t%s", text);
    logd("gb2312:\t%s", gb2312buf);
    logd("charTotalNum:\t%d", charTotalNum);
    logd("charNumPerLine:\t");
    int32_t offset = 0;
    char debugBuff[4096] = { 0 };
    for (i = 0; i < totalLines; i ++) {
        offset += snprintf(debugBuff + offset, sizeof(debugBuff) - offset, "%d ", charNumPerLine[i]);
    }
    logd("totalLines:\t%d", totalLines);
    logd("bmpWidth:\t%d", bmpWidth);
    logd("bmpHeight:\t%d\n", bmpHeight);;
#endif

    gb2312Offset = 0;
    for (i = 0; i < totalLines; i ++) {
        pGb2312Line = gb2312buf + gb2312Offset;
        gb2312LineOffset = 0;

        // logd("%p", pGb2312Line);

        for (j = 0; j < charNumPerLine[i]; j ++) {
            if (pGb2312Line[gb2312LineOffset] > 0xA0 &&
                pGb2312Line[gb2312LineOffset] < 0xff
            ) {
                pstYUVBitMapFile = _OSDBitMapFileInfo(YUV_FONT_TYPE_HZ, ftSize);
                if (pstYUVBitMapFile == nullptr) {
                    continue;
                }

                fontoffset = _YUVGb2312codeToFontoffset(pGb2312Line[gb2312LineOffset] +
						        0x100 * pGb2312Line[gb2312LineOffset + 1],
						        pstYUVBitMapFile->height);

                fontWidth = pstYUVBitMapFile->width;
                fontHeight = pstYUVBitMapFile->height;

                gb2312LineOffset += 2;

                fontAddr = pstYUVBitMapFile->bitMapAddr;
            } else if (pGb2312Line[gb2312LineOffset] > 0x1f &&
                        pGb2312Line[gb2312LineOffset] < 0x80
            ) {
                pstYUVBitMapFile = _OSDBitMapFileInfo(YUV_FONT_TYPE_ASCII, ftSize);
                if (pstYUVBitMapFile == nullptr) {
                    continue;
                }

                fontWidth = pstYUVBitMapFile->width;
                fontHeight = pstYUVBitMapFile->height;

                fontoffset = _YUVAsciiToFontoffset(pGb2312Line[gb2312LineOffset], fontWidth, fontHeight);

                gb2312LineOffset ++;

                fontAddr = pstYUVBitMapFile->bitMapAddr;
            } else {
                continue;
            }

            if (pstYUVBitMapAttr->verticalFlag == 0) {
                xpos = pstYUVBitMapAttr->leftMargin + fontTotalWidth +
                        j * pstYUVBitMapAttr->lineSpace;
                ypos = pstYUVBitMapAttr->upMargin + i * pstYUVBitMapFile->height +
                        i * pstYUVBitMapAttr->lineSpace;

                fontTotalWidth += fontWidth;
            } else if (pstYUVBitMapAttr->verticalFlag == 1) {

                xpos = pstYUVBitMapAttr->leftMargin + i * pstYUVBitMapFile->width +
                        i * pstYUVBitMapAttr->lineSpace;
                ypos = pstYUVBitMapAttr->upMargin + fontTotalHeight +
                        j * pstYUVBitMapAttr->lineSpace;

                fontTotalHeight += fontHeight;
            }

            // logd("xpos:%d, ypos:%d, gb2312LineOffset:%d, fontoffset:%d, fontWidth:%d,fontHeight:%d, 0x%x",
            //            xpos, ypos, gb2312LineOffset, fontoffset, fontWidth, fontHeight, (fontAddr + fontoffset)[0]);
            if (fontAddr != nullptr) {
                _OSDFontDataToCanvas(fontAddr + fontoffset,
                    xpos + point->x, ypos + point->y,
                    fontWidth, fontHeight, yuvData, yuvlen, iw, ih, color);
            }
        }

        fontTotalWidth = 0;
        fontTotalHeight = 0;

        gb2312Offset += gb2312LineOffset;
    }
}

bool YUVDrawInit()
{
    int32_t i = 0, j = 0;
    int32_t fd = -1;
    uint8_t *pBitMapAddr = nullptr;
    YUVBitMapFile_t *pstYUVBitMapFile = nullptr;

    for (i = 0; i < YUV_FONT_TYPE_MAX; i ++) {
        for (j = 0; j < E_YUV_FONT_SIZE_MAX; j ++) {
            pstYUVBitMapFile = &BitMapFile[i][j];
            fd = open(pstYUVBitMapFile->file, O_RDONLY);
            if (fd < 0) {
                logpe("open %s error !", pstYUVBitMapFile->file);
                continue;
            }

            if (-1 == fstat(fd, &pstYUVBitMapFile->st)) {
                logpe("fstat %s fail !", pstYUVBitMapFile->file);
                close(fd);
                fd = -1;
                continue;
            }

            pBitMapAddr = (uint8_t *) mmap(nullptr, (size_t) pstYUVBitMapFile->st.st_size,
                                            PROT_READ, MAP_PRIVATE, fd, (off_t) 0);
            if (pBitMapAddr == MAP_FAILED) {
                close(fd);
                fd = -1;
                continue;
            }

            pstYUVBitMapFile->fd = fd;
            pstYUVBitMapFile->bitMapAddr = pBitMapAddr;

            logd("%s pBitMapAddr:%p, end:%p", pstYUVBitMapFile->file, pstYUVBitMapFile->bitMapAddr,
                pstYUVBitMapFile->bitMapAddr + pstYUVBitMapFile->st.st_size);
        }
    }

    return true;
}

bool YUVDrawUnInit()
{
    int32_t i = 0, j = 0;
    int32_t fd = -1;
    uint8_t *bitMapAddr = nullptr;
    YUVBitMapFile_t *pstYUVBitMapFile = nullptr;

    for (i = 0; i < YUV_FONT_TYPE_MAX; i ++) {
        for (j = 0; j < E_YUV_FONT_SIZE_MAX; j ++) {
            pstYUVBitMapFile = &BitMapFile[i][j];

            if (pstYUVBitMapFile->bitMapAddr != nullptr) {
                munmap(pstYUVBitMapFile->bitMapAddr, (size_t) pstYUVBitMapFile->st.st_size);
                pstYUVBitMapFile->bitMapAddr = nullptr;
            }

            if (pstYUVBitMapFile->fd > 0) {
                close(pstYUVBitMapFile->fd);
                pstYUVBitMapFile->fd = -1;
            }
        }
    }

    return true;
}

bool YUVDrawText(uint8_t *yuvData, int32_t yuvlen, int32_t iw, int32_t ih, 
    uint32_t x, uint32_t y, const char *text, const YUVColor_t& color, YUVFontSize_t ftSize)
{
    YUVPoint_t point = YUVPoint(x, y);

    _OSDDrawTextToCanvas(yuvData, yuvlen, iw, ih, &point, text, color, ftSize);

    return true;
}
