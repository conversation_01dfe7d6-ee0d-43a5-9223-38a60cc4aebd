#include "Memap.h"
#include "defcomm.h"

bool phyMmap(uint32_t phyAddr, uint32_t len, std::function<bool(void *, uint32_t, void*)> cb, void *user)
{
    void *virBufAddr = (void *) nullptr;

    if (cb == nullptr) {
        return false;
    }

    MI_SYS_Mmap(phyAddr, len, &virBufAddr, FALSE);

    cb(virBufAddr, len, user);

    MI_SYS_Munmap(virBufAddr, len);

    return true;
}

void phyMmapInit()
{
    MI_SYS_Init(0);
}

void phyMmapUnInit()
{
    MI_SYS_Exit(0);
}
