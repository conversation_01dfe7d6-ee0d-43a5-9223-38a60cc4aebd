#include "MediaFlowMnger.h"

#include <algorithm>

#include "Mlog.h"

static std::map<std::string, phyMap_t> _phyMapCfg = {
    {
        "dfh-a", {
            .vifmap = {
                {"HOD",     E_VIF_DEV_ID_8},
                {"DMS",     E_VIF_DEV_ID_9},
                {"BSD",     E_VIF_DEV_ID_10},
                {"ADAS",    E_VIF_DEV_ID_11},
            }
        }
    }, {
        "m5pro", {
            .vifmap = {
                {"DMS",     E_VIF_DEV_ID_0},
                {"ADAS",    E_VIF_DEV_ID_1},
                {"BSD",     E_VIF_DEV_ID_2},
                {"HOD",     E_VIF_DEV_ID_3},
            }
        }
    }, {
        "g1h2", {
            .vifmap = {
                {"BSD",    E_VIF_DEV_ID_8},
                {"FBSD",   E_VIF_DEV_ID_9},
            }
        }
    }, {
        "dfh3", {
            .vifmap = {
                /**
                 * @brief 
                 * 新版硬件
                 * 默认使用新版硬件，老版硬件不再兼容
                 */
                {"AVIN5",   E_VIF_DEV_ID_0},
                {"AVIN6",   E_VIF_DEV_ID_1},
                {"AVIN7",   E_VIF_DEV_ID_2},
                {"DMS",     E_VIF_DEV_ID_8},
                {"ADAS",    E_VIF_DEV_ID_9},
                {"BSD",     E_VIF_DEV_ID_10},
                {"AVIN4",   E_VIF_DEV_ID_11},
            }
        }
    }, {
        "dfh3-old", {
            .vifmap = {
                /**
                 * @brief 
                 * 老版硬件
                 * 鉴于目前研发同事使用的是老版本硬件
                 * 可以通过设置property来切换成老版本兼容
                 * setprop persist.product.board dfh3-old
                 */
                {"AVIN5",   E_VIF_DEV_ID_0},
                {"AVIN6",   E_VIF_DEV_ID_1},
                {"AVIN7",   E_VIF_DEV_ID_2},
                {"AVIN4",   E_VIF_DEV_ID_3},
                {"DMS",     E_VIF_DEV_ID_8},
                {"ADAS",    E_VIF_DEV_ID_9},
                {"BSD",     E_VIF_DEV_ID_10},
            }
        }
    }
};

//--------------------------------------------------------------

MediaFlowMnger::MediaFlowMnger()
    : mCfg(nullptr)
    , mFullscChn(0xff)
    , mIsVencJpegInited(false)
    , mVencJpegChnId(0)
    , mIsVdecH264Inited(false)
    , mVdecH264ChnId(0)
    , mIsVdecH265Inited(false)
    , mVdecH265ChnId(mVdecH264ChnId + 1)
    , mIsVdecJpegInited(false)
    , mVdecJpegChnId(mVdecH264ChnId + 2)
    , mIsVdecDispH264Inited(false)
    , mVdecDispH264ChnId(mVdecH264ChnId + 3)
    , mIsVdecDispH265Inited(false)
    , mVdecDispH265ChnId(mVdecH264ChnId + 4)
    , mIsHidden(false)
    , mDispLock(false)
    , mIsInited(false)
{

}

MediaFlowMnger::~MediaFlowMnger()
{
    uninit();
}

//--------------------------------------------------------------

bool MediaFlowMnger::getBoardName(std::string& board)
{
    int32_t ret = -1;
    char propVal[PROP_VALUE_MAX] = { 0 };

    board = "";

    ret = __system_property_get("persist.product.board", propVal);
    if (ret <= 0) {
        loge("__system_property_get 'persist.product.board' error !");
        // return false;
    }

    if (propVal[0] == '\0') {
        ret = __system_property_get("ro.product.board", propVal);
        if (ret <= 0) {
            loge("__system_property_get 'ro.product.board' error !");
            return false;
        }
    }

    board = propVal;

    return true;
}

vifDevId_t MediaFlowMnger::getVifDevId(const char *board, const char *phy)
{
    if (phy == nullptr ||
        board == nullptr
    ) {
        return E_VIF_DEV_ID_UNKOWN;
    }

    auto iter = _phyMapCfg.find(board);
    if (iter == _phyMapCfg.end()) {
        loge("not found phy mapping of board '%s'", board);
        return E_VIF_DEV_ID_UNKOWN;
    }
    const phyMap_t& pm = iter->second;

    auto iter1 = pm.vifmap.find(phy);
    if (iter1 == pm.vifmap.end()) {
        return E_VIF_DEV_ID_UNKOWN;
    }

    return iter1->second;
}

const phyMap_t *MediaFlowMnger::getPhyMap()
{
    std::string board;
    getBoardName(board);

    auto iter = _phyMapCfg.find(board.c_str());
    if (iter == _phyMapCfg.end()) {
        loge("not found phy mapping of board '%s'", board.c_str());
        return nullptr;
    }

    return &iter->second;
}

//--------------------------------------------------------------

bool MediaFlowMnger::vifListInit(const phyMap_t *pm)
{
    vifDevId_t vifDevId = E_VIF_DEV_ID_0;
    snrPadId_t snrPadId = E_SNR_PAD_ID_UNKOWN;

    uint8_t chn = 0;
    bool isFind = false;

    std::string board;
    getBoardName(board);

    vifListUnInit();

    for ( ; chn < mCfg->cameras; chn ++) {
        vifDevId = getVifDevId(board.c_str(), mCfg->ch[chn].camPhy.c_str());
        if (vifDevId == E_VIF_DEV_ID_UNKOWN) {
            loge("BOARD[%s] CHN[%u] PYH[%s] VIFDEV[%d] getVifDevId error !", board.c_str(), chn, mCfg->ch[chn].camPhy.c_str(), vifDevId);
            return false;
        }

        chvifmap_t chvifmap;
        chvifmap.chn = chn;
        chvifmap.vifDevId = vifDevId;

        snrPadId = (snrPadId_t) getSnrPadIdByVifDevId(vifDevId);
        mVifList[snrPadId].valid.push_back(chvifmap);
    }

    for (auto iter = pm->vifmap.begin(); iter != pm->vifmap.end(); iter ++) {
        isFind = false;

        vifDevId = iter->second;
        snrPadId = (snrPadId_t) getSnrPadIdByVifDevId(vifDevId);
        for (const chvifmap_t& chvifmap : mVifList[snrPadId].valid) {
            if (vifDevId == chvifmap.vifDevId) {
                isFind = true;
                break;
            }
        }

        if (isFind == false) {

            chvifmap_t chvifmap;
            chvifmap.chn = chn ++;
            chvifmap.vifDevId = vifDevId;

            mVifList[snrPadId].invalid.push_back(chvifmap);
        }
    }

    for (auto iter = mVifList.begin(); iter != mVifList.end(); iter ++) {
        std::sort(iter->second.valid.begin(), iter->second.valid.end(), chVifMapNodeComp);
        std::sort(iter->second.invalid.begin(), iter->second.invalid.end(), chVifMapNodeComp);
    }

    return true;
}

bool MediaFlowMnger::vifListUnInit()
{
    for (auto iter = mVifList.begin(); iter != mVifList.end(); iter ++) {
        iter->second.valid.clear();
        iter->second.invalid.clear();
    }
    mVifList.clear();

    return true;
}

//--------------------------------------------------------------

bool MediaFlowMnger::flowListInit(const phyMap_t *pm)
{
    uint8_t j = 0;
    dispInPortId_t dispInPortId = E_DISP_INPORT_ID_UNKOWN;

    std::string board;
    getBoardName(board);

    for (auto iter = pm->vifmap.begin(); iter != pm->vifmap.end(); iter ++) {
        mMediaFlowList.push_back(nullptr);
    }

    for (auto iter = mVifList.begin(); iter != mVifList.end(); iter ++) {

        for (uint8_t i = 0; i < iter->second.valid.size(); i ++) {
            const chvifmap_t& chvifmap = iter->second.valid[i];

            miniui::rect_t winRect(0, 0, 0, 0);
            if (ThemeMnger::getInstance()->getWinRect(chvifmap.chn, winRect) == false) {
                logw("%s CHN[%u] 'display.win' of config ini not set ???", board.c_str(), chvifmap.chn, 
                    mCfg->ch[chvifmap.chn].camPhy.c_str(), chvifmap.vifDevId);

                winRect = miniui::Rect(0, 0, 0, 0);
                dispInPortId = E_DISP_INPORT_ID_UNKOWN;
            } else {
                dispInPortId = (dispInPortId_t) j ++;
            }

            logd("CHN[%u] VIFDEV[%d] DISPINPORT[%d] WIN[%d, %d, %d, %d] VALID[true]", 
                chvifmap.chn, chvifmap.vifDevId, dispInPortId, winRect.x, winRect.y, winRect.w, winRect.h);

            mMediaFlowList[chvifmap.chn] = new MediaFlow(mCfg, chvifmap.chn, chvifmap.vifDevId, chvifmap.chn, chvifmap.chn, dispInPortId, winRect, true);
        }

        for (uint8_t i = 0; i < iter->second.invalid.size(); i ++) {
            const chvifmap_t& chvifmap = iter->second.invalid[i];

            logd("CHN[%u] VIFDEV[%d] VALID[false] !", chvifmap.chn, chvifmap.vifDevId);
            mMediaFlowList[chvifmap.chn] = new MediaFlow(mCfg, chvifmap.chn, chvifmap.vifDevId, chvifmap.chn, chvifmap.chn, 
                E_DISP_INPORT_ID_UNKOWN, miniui::Rect(0, 0, 0, 0), false);
        }
    }

    return true;
}

bool MediaFlowMnger::flowListUnInit()
{
    for (int32_t i = 0; i < mMediaFlowList.size(); i ++) {
        delete mMediaFlowList[i];
        mMediaFlowList[i] = nullptr;
    }
    mMediaFlowList.clear();

    return true;
}

//--------------------------------------------------------------

bool MediaFlowMnger::sensorInit()
{
    bool isPadValid = false;
    snrPadId_t snrPadId = E_SNR_PAD_ID_UNKOWN;
    snrPlaneId_t snrPlaneId = E_SNR_PLANE_ID_UNKOWN;

    for (auto iter = mVifList.begin(); iter != mVifList.end(); iter ++) {
        snrPadId = iter->first;

        isPadValid = false;
        for (uint8_t i = 0; i < mMediaFlowList.size(); i ++) {
            if (snrPadId == (snrPadId_t) getSnrPadIdByVifDevId(mMediaFlowList[i]->getVifDevId())) {
                if (mMediaFlowList[i]->isValid()) {
                    isPadValid = true;
                    break;
                }
            }
        }

        if (isPadValid == true) {
            IF_ERR_RETURN(SensorMnger::getInstance()->init(snrPadId) == false, false, loge("init error !"));
            SensorMnger::getInstance()->dump(snrPadId);

            for (uint8_t i = 0; i < mMediaFlowList.size(); i ++) {

                if (snrPadId == (snrPadId_t) getSnrPadIdByVifDevId(mMediaFlowList[i]->getVifDevId())) {

                    snrPlaneId = (snrPlaneId_t) getPlaneIdByVifDevId(mMediaFlowList[i]->getVifDevId());

                    if (mMediaFlowList[i]->isValid()) {
                        IF_ERR_RETURN(SensorMnger::getInstance()->setPlane(snrPadId, snrPlaneId, 
                            mCfg->ch[i].camReslWidth, mCfg->ch[i].camReslHeight, mCfg->ch[i].camResFps) == false, false, loge("setPlane error !"));
                    } else {
                        /**
                         * @brief 
                         * 默认初始化降低规格，如1080P改为1080N，720P改为720N
                         * 即1920x1080改为1080x1920，可以降低vif输入带宽
                         */
                        IF_ERR_RETURN(SensorMnger::getInstance()->setPlane(snrPadId, snrPlaneId, 
                            1280, 720, 25) == false, false, loge("setPlane error !"));
                    }
                }
            }

            IF_ERR_RETURN(SensorMnger::getInstance()->enable(snrPadId) == false, false, loge("enable error !"));
        }
    }

    return true;
}

bool MediaFlowMnger::sensorUnInit()
{
    SensorMnger::getInstance()->uninit(E_SNR_PAD_ID_0);
    SensorMnger::getInstance()->uninit(E_SNR_PAD_ID_1);

    return true;
}

//--------------------------------------------------------------

bool MediaFlowMnger::vifInit()
{
    bool isPadValid = false;
    snrPadId_t snrPadId = E_SNR_PAD_ID_UNKOWN;

    uint8_t s = 0, e = 0;

    IF_ERR_RETURN(VifMnger::getInstance()->init() == false, false, loge("init error !"));

    for (auto iter = mVifList.begin(); iter != mVifList.end(); iter ++) {
        snrPadId = iter->first;

        isPadValid = false;
        for (uint8_t i = 0; i < mMediaFlowList.size(); i ++) {
            if (snrPadId == (snrPadId_t) getSnrPadIdByVifDevId(mMediaFlowList[i]->getVifDevId())) {
                if (mMediaFlowList[i]->isValid()) {
                    isPadValid = true;
                    break;
                }
            }
        }

        if (isPadValid == true) {
            switch (snrPadId) {
                case E_SNR_PAD_ID_0: {
                    s = E_VIF_DEV_ID_0;
                    e = E_VIF_DEV_ID_3;
                    break;
                }
                case E_SNR_PAD_ID_1: {
                    s = E_VIF_DEV_ID_8;
                    e = E_VIF_DEV_ID_11;
                    break;
                }
                default: {
                    loge("unkown snrPadId: %d !", snrPadId);
                    return false;
                }
            }

            for (uint8_t k = s; k <= e; k ++) {
                for (uint8_t i = 0; i < mMediaFlowList.size(); i ++) {

                    if (mMediaFlowList[i]->getVifDevId() == k) {
                        IF_ERR_RETURN(mMediaFlowList[i]->vifInit() == false, false, loge("ch[%u] vifInit error !", i));

                        break;
                    }
                }
            }
        }
    }

    return true;
}

bool MediaFlowMnger::vifUnInit()
{
    VifMnger::getInstance()->uninit();

    return true;
}

//--------------------------------------------------------------

bool MediaFlowMnger::ispInit()
{
#ifdef __USE_ISP__
    IF_ERR_RETURN(IspMnger::getInstance()->init(ISP_DEV_ID) == false, false, loge("init error !"));
#endif

    for (uint8_t i = 0; i < mMediaFlowList.size(); i ++) {
        IF_ERR_RETURN(mMediaFlowList[i]->ispInit(mCfg->ch[i].camReslWidth, mCfg->ch[i].camReslHeight, mCfg->ch[i].camResFps) == false, false, loge("ch[%u] ispInit error !", i));
    }

    return true;
}

bool MediaFlowMnger::ispUnInit()
{
    IspMnger::getInstance()->uninit(ISP_DEV_ID);

    return true;
}

//--------------------------------------------------------------

bool MediaFlowMnger::sclInit()
{
    IF_ERR_RETURN(RGN::getInstance()->init() == false, false, loge("init error !"));

    IF_ERR_RETURN(SclMnger::getInstance()->init(SCL_DEV_ID, 
        E_HWSCL_ID_0 | E_HWSCL_ID_1 | E_HWSCL_ID_2 
        | E_HWSCL_ID_3 | E_HWSCL_ID_4) == false, false, loge("init error !"));

    for (uint8_t i = 0; i < mMediaFlowList.size(); i ++) {
        IF_ERR_RETURN(mMediaFlowList[i]->sclInit() == false, false, loge("ch[%u] sclInit error !", i));
    }

    return true;
}

bool MediaFlowMnger::sclUnInit()
{
    RGN::getInstance()->uninit();
    SclMnger::getInstance()->uninit(SCL_DEV_ID);

    return true;
}

//--------------------------------------------------------------

bool MediaFlowMnger::dispInit()
{
    if (mCfg->displaySwitch == true) {
        IF_ERR_RETURN(DispMnger::getInstance()->init() == false, false, loge("init error !"));
        IF_ERR_RETURN(DispMnger::getInstance()->initDev(E_DISP_DEV_ID_0, E_DISP_INTFACE_CVBS_PAL) == false, false, loge("initDev error !"));
        IF_ERR_RETURN(DispMnger::getInstance()->enableDev(E_DISP_DEV_ID_0) == false, false, loge("enableDev error !"));

        /**
         * @brief 
         * 小窗口初始化
         */
        IF_ERR_RETURN(DispMnger::getInstance()->initLayer(E_DISP_LAYER_ID_0) == false, false, loge("initLayer error !"));
        IF_ERR_RETURN(DispMnger::getInstance()->layerBindDev(E_DISP_LAYER_ID_0, E_DISP_DEV_ID_0, 720, 576) == false, false, loge("layerBindDev error !"));
        IF_ERR_RETURN(DispMnger::getInstance()->enableLayer(E_DISP_LAYER_ID_0) == false, false, loge("enableLayer error !"));

        for (uint8_t i = 0; i < mMediaFlowList.size(); i ++) {
            IF_ERR_RETURN(mMediaFlowList[i]->dispInit() == false, false, loge("ch[%u] dispInit error !", i));
        }
    }

    return true;
}

bool MediaFlowMnger::dispUnInit()
{
    DispMnger::getInstance()->uninit();

    return true;
}

//--------------------------------------------------------------

bool MediaFlowMnger::vencH26xInit()
{
    VencH26xChMnger::getInstance()->init();

    for (uint8_t i = 0; i < mMediaFlowList.size(); i ++) {
        IF_ERR_RETURN(mMediaFlowList[i]->vencH26xMainInit() == false, false, loge("ch[%u] vencH26xMainInit error !", i));
        IF_ERR_RETURN(mMediaFlowList[i]->vencH26xSubInit() == false, false, loge("ch[%u] vencH26xSubInit error !", i));
    }

    return true;
}

bool MediaFlowMnger::vencH26xUnInit()
{
    VencH26xChMnger::getInstance()->uninit();

    return true;
}

//--------------------------------------------------------------

bool MediaFlowMnger::vencJpegUnInit()
{
    VencJpegChMnger::getInstance()->uninit(mVencJpegChnId);

    for (uint8_t i = 0; i < mMediaFlowList.size(); i ++) {
        mMediaFlowList[i]->vencJpegUnInit();
    }

    return true;
}

//--------------------------------------------------------------

bool MediaFlowMnger::vdecUnInit()
{
    VdeChMnger::getInstance()->uninit();

    return true;
}

//--------------------------------------------------------------

bool MediaFlowMnger::audioOutputInit()
{
    IF_ERR_RETURN(AudioOutputMnger::getInstance()->init() == false, false, loge("init error !"));

    IF_ERR_RETURN(AudioOutputMnger::getInstance()->initDev(E_AUDIO_OUTPUT_DEV_ID_0) == false, false, loge("initDev error !"));
    IF_ERR_RETURN(AudioOutputMnger::getInstance()->setSampleRate(E_AUDIO_OUTPUT_DEV_ID_0, E_AUDIO_SAMPLE_RATE_8000) == false, false, loge("setSampleRate error !"));
    IF_ERR_RETURN(AudioOutputMnger::getInstance()->enableDev(E_AUDIO_OUTPUT_DEV_ID_0) == false, false, loge("enableDev error !"));

    IF_ERR_RETURN(AudioOutputMnger::getInstance()->initCh(E_AUDIO_OUTPUT_DEV_ID_0, E_AUDIO_OUTPUT_CHN_ID_0) == false, false, loge("initCh error !"));
    IF_ERR_RETURN(AudioOutputMnger::getInstance()->enableCh(E_AUDIO_OUTPUT_DEV_ID_0, E_AUDIO_OUTPUT_CHN_ID_0) == false, false, loge("enableCh error !"));

    return true;
}

bool MediaFlowMnger::audioOutputUnInit()
{
    AudioOutputMnger::getInstance()->uninit();

    return true;
}

bool MediaFlowMnger::audioInputInit()
{
    IF_ERR_RETURN(AudioInputMnger::getInstance()->init() == false, false, loge("init error !"));

    IF_ERR_RETURN(AudioInputMnger::getInstance()->initDev(E_AUDIO_INPUT_DEV_ID_5) == false, false, loge("initDev error !"));
    IF_ERR_RETURN(AudioInputMnger::getInstance()->setSampleRate(E_AUDIO_INPUT_DEV_ID_5, E_AUDIO_SAMPLE_RATE_8000) == false, false, loge("setSampleRate error !"));
    IF_ERR_RETURN(AudioInputMnger::getInstance()->enableDev(E_AUDIO_INPUT_DEV_ID_5) == false, false, loge("enableDev error !"));

    for (uint8_t i = 0; i < E_AUDIO_INPUT_CHN_ID_MAX; i ++) {
        IF_ERR_RETURN(AudioInputMnger::getInstance()->initCh(E_AUDIO_INPUT_DEV_ID_5, (audioInputChnId_t) i) == false, false, loge("initCh error !"));
        IF_ERR_RETURN(AudioInputMnger::getInstance()->enableCh(E_AUDIO_INPUT_DEV_ID_5, (audioInputChnId_t) i) == false, false, loge("enableCh error !"));

        IF_ERR_RETURN(AudioInputMnger::getInstance()->setVolume(E_AUDIO_INPUT_DEV_ID_5, (audioInputChnId_t) i, 
            (audioVolumeLevel_t) mCfg->soundRec[i].gain) == false, false, loge("setVolume error !"));
    }

    return true;
}

bool MediaFlowMnger::audioInputUnInit()
{
    AudioInputMnger::getInstance()->uninit();

    return true;
}

//--------------------------------------------------------------

bool MediaFlowMnger::init_(idvrMediaConf_t *cfg)
{
    int32_t ret = -1;
    vifDevId_t vifDevId = E_VIF_DEV_ID_0;
    snrPadId_t snrPadId = E_SNR_PAD_ID_UNKOWN;

    const phyMap_t *pm = nullptr;

    bool flag = false;

    /**
     * @brief 
     * 必须要先初始化，idvr.ui依赖DISP设备
     */
    // IF_ERR_RETURN(DispMnger::getInstance()->initDev(E_DISP_DEV_ID_0, E_DISP_INTFACE_CVBS_PAL) == false, false, loge("initDev error !"));
    // IF_ERR_RETURN(DispMnger::getInstance()->enableDev(E_DISP_DEV_ID_0) == false, false, loge("enableDev error !"));

    IF_ERR_RETURN(cfg == nullptr, false, loge("cfg parm is null !"));
    mCfg = cfg;

    IF_ERR_RETURN((pm = getPhyMap()) == nullptr, false, loge("getPhyMap error!"));
    IF_ERR_RETURN(mCfg->cameras > pm->vifmap.size(), false, loge("the 'cameras' of config ini is more than %d", pm->vifmap.size()));

    logd("================ vifListInit =================");
    IF_ERR_RETURN(vifListInit(pm) == false, false, loge("vifListInit error!"));
    logd("\n");

    logd("================ flowListInit =================");
    IF_ERR_RETURN(flowListInit(pm) == false, false, loge("flowListInit error!"));
    logd("\n");

    //todo --> idvr.media初始化一下sensor后退出，然后再运行此程序看是否能出图
    //---验证是DISP的问题，idvr.media初始化到DISP后，退出程序，再运行此程序，可以出图

    /**
     * @brief 
     * 初始化sensor
    */
    logd("================ Sensor init =================");
    IF_ERR_RETURN(sensorInit() == false, false, loge("sensorInit error !"));
    logd("\n");

    /**
     * @brief 
     * 初始化VIF
     */
    logd("================ Vif init =================");
    IF_ERR_RETURN(vifInit() == false, false, loge("vifInit error !"));
    logd("\n");

    /**
     * @brief
     * 初始化ISP
     */
    logd("================ ISP init =================");
    IF_ERR_RETURN(ispInit() == false, false, loge("ispInit error !"));
    logd("\n");

    /**
     * @brief
     * 初始化SCL
     */
    logd("================ SCL init =================");
    IF_ERR_RETURN(sclInit() == false, false, loge("sclInit error !"));
    logd("\n");

    /**
     * @brief 
     * 初始化DISP
    */
    logd("================ DISP init =================");
    IF_ERR_RETURN(dispInit() == false, false, loge("dispInit error !"));
    logd("\n");

    /**
     * @brief 
     * 初始化VENC H26X
    */
    logd("================ VENC H26x init =================");
    IF_ERR_RETURN(vencH26xInit() == false, false, loge("vencH26xInit error !"));
    logd("\n");

    /**
     * @brief 
     * 初始化AO
    */
    logd("================ AUDIO OUTPUT init =================");
    IF_ERR_RETURN(audioOutputInit() == false, false, loge("audioOutputInit error !"));
    logd("\n");

    /**
     * @brief 
     * 初始化AI
    */
    logd("================ AUDIO INPUT init =================");
    IF_ERR_RETURN(audioInputInit() == false, false, loge("audioInputInit error !"));
    logd("\n");

    return true;
}

bool MediaFlowMnger::init(idvrMediaConf_t *cfg)
{
    if (init_(cfg) == false) {
        uninit();

        return false;
    }

    mIsInited = true;

    return true;
}

bool MediaFlowMnger::uninit()
{
    mIsInited = false;

    MLOGW("~~~ AUDIO INPUT uninit ~~~");
    audioInputUnInit();

    MLOGW("~~~ AUDIO OUTPUT uninit ~~~");
    audioOutputUnInit();

    MLOGW("~~~ VENC JPEG uninit ~~~");
    vencJpegUnInit();

    MLOGW("~~~ VENC H26x uninit ~~~");
    vencH26xUnInit();

    MLOGW("~~~ VDEC uninit ~~~");
    vdecUnInit();

    MLOGW("~~~ DISP uninit ~~~");
    dispUnInit();

    MLOGW("~~~ SCL uninit ~~~");
    sclUnInit();

    MLOGW("~~~ ISP uninit ~~~");
    ispUnInit();

    MLOGW("~~~ VIF uninit ~~~");
    vifUnInit();

    MLOGW("~~~ SENSOR uninit ~~~");
    sensorUnInit();

    MLOGW("~~~ vifListUnInit ~~~");
    vifListUnInit();

    MLOGW("~~~ flowListUnInit ~~~");
    flowListUnInit();

    return true;
}

bool MediaFlowMnger::isInited()
{
    return mIsInited;
}

//--------------------------------------------------------------

bool MediaFlowMnger::hidden()
{
    if (mCfg->displaySwitch == false) {
        logw("'displaySwitch' of config.ini not enable !");
        return false;
    }

    std::lock_guard<std::mutex> lock(mScMtx);

    if (mIsHidden == false) {

        /**
         * @brief 
         * 需要先回到小屏
         */

        if (mFullscChn != 0xff) {
            if (smallsc_() == false) {
                loge("CHN[%u] smallsc_ error !", mFullscChn);
                return false;
            }
        }

        DispMnger::getInstance()->disableLayer(E_DISP_LAYER_ID_0);
        DispMnger::getInstance()->disableLayer(E_DISP_LAYER_ID_1);

        for (uint8_t i = 0; i < mMediaFlowList.size(); i ++) {
            mMediaFlowList[i]->disableDispStrm();
        }

        mIsHidden = true;
    } else {
        logd("already hidden ...");
    }

    return true;
}

bool MediaFlowMnger::show_()
{
    if (mCfg->displaySwitch == false) {
        logw("'displaySwitch' of config.ini not enable !");
        return false;
    }

    if (mIsHidden == true) {

        for (uint8_t i = 0; i < mMediaFlowList.size(); i ++) {
            mMediaFlowList[i]->enableDispStrm();
        }

        //------------------------------------------------------------------
        IF_ERR_RETURN(DispMnger::getInstance()->enableLayer(E_DISP_LAYER_ID_0) == false, false, loge("enableLayer error !"));
        //------------------------------------------------------------------
        DispMnger::getInstance()->inPortDisable(E_DISP_LAYER_ID_1, E_DISP_INPORT_ID_0);
        //------------------------------------------------------------------
        for (uint8_t i = 0; i < mMediaFlowList.size(); i ++) {
            IF_ERR_RETURN(mMediaFlowList[i]->smallsc() == false, false, loge("set smallsc resl error !"));
        }
        //-------------------------------------------------------------------
        IF_ERR_RETURN(DispMnger::getInstance()->uninitLayer(E_DISP_LAYER_ID_1) == false, false, loge("uninitLayer error !"));
        //-------------------------------------------------------------------

        mFullscChn = 0xff;

        mIsHidden = false;
    } else {
        logd("already show ...");
    }

    return true;
}

bool MediaFlowMnger::show()
{
    std::lock_guard<std::mutex> lock(mScMtx);

    return show_();
}

//--------------------------------------------------------------

bool MediaFlowMnger::fullsc(uint8_t chn, uint16_t x, uint16_t y, uint16_t w, uint16_t h)
{
    if (!mIsInited || mCfg->displaySwitch == false) {
        logw("'displaySwitch' of config.ini not enable !");
        return false;
    }

    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr ||
        mMediaFlowList[chn]->isValid() == false ||
        mIsHidden == true ||
        mDispLock == true
    ) {
        return false;
    }

    miniui::rect_t rect = miniui::Painter::getInstance()->getFBRect();

    if (w <= 0 || h <= 0 ||
        w > rect.w ||
        h > rect.h ||
        x >= w ||
        y >= h
    ) {
        return false;
    }

    if (mCfg->displaySwitch == false) {
        logw("CHN[%u] 'displaySwitch' of config.ini not enable !", chn);
        return true;
    }

    std::lock_guard<std::mutex> lock(mScMtx);

    /**
     * @brief 
     * 需要先回到小屏
     */

    if (mFullscChn != 0xff &&
        mFullscChn != chn
    ) {
        if (smallsc_() == false) {
            loge("CHN[%u] smallsc_ error !", chn);
            return false;
        }
    }

    if (mFullscChn == chn) {
        logd("================= CHN[%u] already fullsc ...", chn);
        return true;
    }
    mFullscChn = chn;

    logd("================= CHN[%u] fullsc ...", chn);

    /**
     * @brief 
     * 注意：
     * 先全屏显示，再在把小窗口给回收掉，
     * 这样可以有更好的过度效果
     */

    //---------------------------------------------------------
    IF_ERR_RETURN(mMediaFlowList[chn]->fullsc(w, h) == false, 
        false, loge("CHN[%u] set fullsc resl error !", chn));
    //---------------------------------------------------------

    DispMnger::getInstance()->uninitLayer(E_DISP_LAYER_ID_1);
    IF_ERR_RETURN(DispMnger::getInstance()->initLayer(E_DISP_LAYER_ID_1) == false, false, loge("CHN[%u] initLayer error !", chn));
    IF_ERR_RETURN(DispMnger::getInstance()->layerBindDev(E_DISP_LAYER_ID_1, E_DISP_DEV_ID_0, w, h) == false, false, loge("CHN[%u] layerBindDev error !", chn));

    IF_ERR_RETURN(DispMnger::getInstance()->initInPort(E_DISP_LAYER_ID_1, E_DISP_INPORT_ID_0) == false, false, loge("CHN[%u] initInPort error !", chn));
    IF_ERR_RETURN(DispMnger::getInstance()->setInPortWin(E_DISP_LAYER_ID_1, E_DISP_INPORT_ID_0, x, y, w, h) == false, false, loge("CHN[%u] setInPortWin error !", chn));
    //---------------------------------------------------------
    IF_ERR_RETURN(DispMnger::getInstance()->inPortBind(
        E_DISP_LAYER_ID_1, E_DISP_INPORT_ID_0, E_MI_MODULE_ID_SCL, 
        SCL_DEV_ID, chn, SCL_DISP_OUTPORT, E_DISP_DEV_ID_0, 
        mMediaFlowList[chn]->getResFps(), mMediaFlowList[chn]->getDispFps()) == false, 
        false, loge("CHN[%u] inPortBind error !", chn));

    //---------------------------------------------------------
    IF_ERR_RETURN(DispMnger::getInstance()->enableLayer(E_DISP_LAYER_ID_1) == false, false, loge("CHN[%u] enableLayer error !", chn));
    IF_ERR_RETURN(DispMnger::getInstance()->inPortEnable(E_DISP_LAYER_ID_1, E_DISP_INPORT_ID_0) == false, false, loge("CHN[%u] inPortEnable error !", chn));
    //---------------------------------------------------------

    IF_ERR_RETURN(DispMnger::getInstance()->disableLayer(E_DISP_LAYER_ID_0) == false, false, loge("CHN[%u] disableLayer error !", chn));
    //---------------------------------------------------------

    for (uint8_t i = 0; i < mMediaFlowList.size(); i ++) {
        if (i != chn) {
            mMediaFlowList[i]->disableDispStrm();
        }
    }

    return true;
}

//--------------------------------------------------------------

bool MediaFlowMnger::smallsc_()
{
    if (mCfg->displaySwitch == false) {
        logw("'displaySwitch' of config.ini not enable !");
        return false;
    }

    if (! (0 <= mFullscChn && mFullscChn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[mFullscChn] == nullptr ||
        mMediaFlowList[mFullscChn]->isValid() == false
    ) {
        return false;
    }

    /**
     * @brief 
     * 注意：
     * 先开启小窗口，再在把大窗口给回收掉，
     * 这样可以有更好的过度效果
     */

    for (uint8_t i = 0; i < mMediaFlowList.size(); i ++) {
        mMediaFlowList[i]->enableDispStrm();
    }

    //------------------------------------------------------------------
    IF_ERR_RETURN(DispMnger::getInstance()->enableLayer(E_DISP_LAYER_ID_0) == false, false, loge("enableLayer error !"));
    //------------------------------------------------------------------
    IF_ERR_RETURN(DispMnger::getInstance()->inPortDisable(E_DISP_LAYER_ID_1, E_DISP_INPORT_ID_0) == false, false, loge("inPortDisable error !"));
    //------------------------------------------------------------------
    IF_ERR_RETURN(mMediaFlowList[mFullscChn]->smallsc() == false, false, loge("set smallsc resl error !"));
    //-------------------------------------------------------------------
    IF_ERR_RETURN(DispMnger::getInstance()->uninitLayer(E_DISP_LAYER_ID_1) == false, false, loge("uninitLayer error !"));
    //-------------------------------------------------------------------

    mFullscChn = 0xff;

    return true;
}

bool MediaFlowMnger::smallsc()
{
    std::lock_guard<std::mutex> lock(mScMtx);

    if (!mIsInited ||
        mIsHidden == true ||
        mDispLock == true
    ) {
        return false;
    }

    return smallsc_();
}

//--------------------------------------------------------------

bool MediaFlowMnger::speak(const char *pcm, int32_t len, audioVolumeLevel_t audioVolumeLevel)
{
    if (!mIsInited) {
        return false;
    }
    IF_ERR_RETURN(AudioOutputMnger::getInstance()->setVolume(E_AUDIO_OUTPUT_DEV_ID_0, E_AUDIO_OUTPUT_CHN_ID_0, audioVolumeLevel) == false, false, loge("putstrm error !"));
    IF_ERR_RETURN(AudioOutputMnger::getInstance()->putstrm(E_AUDIO_OUTPUT_DEV_ID_0, E_AUDIO_OUTPUT_CHN_ID_0, pcm, len) == false, false, loge("putstrm error !"));

    return true;
}

//--------------------------------------------------------------

bool MediaFlowMnger::enableMainStrm(uint8_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->enableMainStrm();
}

bool MediaFlowMnger::disableMainStrm(uint8_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->disableMainStrm();
}

bool MediaFlowMnger::setMainStrmResl(uint8_t chn, uint16_t w, uint16_t h)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->setMainStrmResl(w, h);
}

bool MediaFlowMnger::setMainStrmFps(uint8_t chn, uint8_t fps)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->setMainStrmFps(fps);
}

bool MediaFlowMnger::setMainStrmBps(uint8_t chn, uint32_t bps)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->setMainStrmBps(bps);
}

//--------------------------------------------------------------

bool MediaFlowMnger::enableSubStrm(uint8_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->enableSubStrm();
}

bool MediaFlowMnger::disableSubStrm(uint8_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->disableSubStrm();
}

bool MediaFlowMnger::setSubStrmResl(uint8_t chn, uint16_t w, uint16_t h)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->setSubStrmResl(w, h);
}

bool MediaFlowMnger::setSubStrmFps(uint8_t chn, uint8_t fps)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->setSubStrmFps(fps);
}

bool MediaFlowMnger::setSubStrmBps(uint8_t chn, uint32_t bps)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->setSubStrmBps(bps);
}

//--------------------------------------------------------------

bool MediaFlowMnger::enableSnap(uint8_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->enableSnap();
}

bool MediaFlowMnger::disableSnap(uint8_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->disableSnap();
}

bool MediaFlowMnger::setSnapResl(uint8_t chn, uint16_t w, uint16_t h)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->setSnapResl(w, h);
}

//--------------------------------------------------------------

bool MediaFlowMnger::setAiCrop(uint8_t chn, uint16_t w, uint16_t h)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->setAiCrop(w, h);
}

//--------------------------------------------------------------

bool MediaFlowMnger::cntstatus(uint8_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->cntstatus();
}

uint16_t MediaFlowMnger::getAnadecReslW(uint8_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->getAnadecReslW();
}

uint16_t MediaFlowMnger::getAnadecReslH(uint8_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->getAnadecReslH();
}

uint8_t MediaFlowMnger::getAnadecFps(uint8_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->getAnadecFps();
}

bool MediaFlowMnger::isReslChanged(uint8_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->isReslChanged();
}

//--------------------------------------------------------------
std::string MediaFlowMnger::getPhyName(uint8_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return "";
    }

    if (mMediaFlowList[chn] == nullptr) {
        return "";
    }

    return mMediaFlowList[chn]->getPhyName();
}

//--------------------------------------------------------------

bool MediaFlowMnger::yuv2jpeg(uint8_t chn, const char *yuv, int32_t yuvlen, uint16_t w, uint16_t h, uint8_t quality, uint64_t pts, stream_t& stream, bool lock)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->yuv2jpeg(yuv, yuvlen, w, h, quality, pts, stream, lock);
}

bool MediaFlowMnger::yuv2jpeg_(const char *yuv, int32_t yuvlen, uint16_t w, uint16_t h, uint64_t pts, stream_t& stream)
{
    /**
     * @brief 
     * 第一次注入YUV不会编码出JPG，而是要等到第二次的YUV数据到来，才会把第一次的JPG输出
     * 所以put两次，目的就是为了得到第一次的jpg图片
     * 目前还没有更好的方法，暂时使用这种不入流的方式
     */

    IF_ERR_RETURN(VencJpegChMnger::getInstance()->reset(mVencJpegChnId) == false, false, loge("reset error !"));
    IF_ERR_RETURN(VencJpegChMnger::getInstance()->putstrm(mVencJpegChnId, yuv, yuvlen, w, h, pts) == false, false, loge("putstrm error !"));
    IF_ERR_RETURN(VencJpegChMnger::getInstance()->putstrm(mVencJpegChnId, yuv, yuvlen, w, h, pts) == false, false, loge("putstrm error !"));
    IF_ERR_RETURN(VencJpegChMnger::getInstance()->getstrm(mVencJpegChnId, stream, 60) == false, false, loge("getstrm error !"));

    return true;
}

bool MediaFlowMnger::yuv2jpeg(const char *yuv, int32_t yuvlen, uint16_t w, uint16_t h, uint8_t quality, uint64_t pts, stream_t& stream)
{
    std::lock_guard<std::mutex> lock(mYUV2JpegMtx);

    bool ret = false;
    uint64_t ms = my::getClockMs();

    if (mIsVencJpegInited == false ||
        w != VencJpegChMnger::getInstance()->getReslW(mVencJpegChnId) ||
        h != VencJpegChMnger::getInstance()->getReslH(mVencJpegChnId)
    ) {
        VencJpegChMnger::getInstance()->uninit(mVencJpegChnId);
        IF_ERR_RETURN(VencJpegChMnger::getInstance()->init(mVencJpegChnId, w, h, 25, quality) == false, false, loge("init error !"));
        IF_ERR_RETURN(VencJpegChMnger::getInstance()->enable(mVencJpegChnId) == false, false, loge("enable error !"));
        mIsVencJpegInited = true;
    }

    for (uint8_t i = 0; i < 2; i ++) {
        logd("====== num.%u yuv2jpeg_", i);
        ret = yuv2jpeg_(yuv, yuvlen, w, h, pts, stream);
        if (ret == true) {
            break;
        }
    }

    //统计花费的时间
    if (ret == true) {
        logd("====== take %llu ms", my::getClockMs() - ms);
    }

    return ret;
}

//--------------------------------------------------------------

bool MediaFlowMnger::jpeg2yuv(const char *jpg, int32_t jpglen, uint16_t iw, uint16_t ih, uint16_t ow, uint16_t oh, uint64_t pts, stream_t& stream)
{
    int32_t fd[1] = { -1 };
    bool status[1] = { false };

    std::lock_guard<std::mutex> lock(mJpeg2YUVMtx);

    uint64_t ms = my::getClockMs();

    if (iw <= 0 || ih <= 0 ||
        ow <= 0 || oh <= 0 ||
        jpg == nullptr ||
        jpglen <= 0
    ) {
        return false;
    }

    if (mIsVdecJpegInited == false ||
        iw != VdeChMnger::getInstance()->getInReslW(mVdecJpegChnId) ||
        ih != VdeChMnger::getInstance()->getInReslH(mVdecJpegChnId) ||
        ow != VdeChMnger::getInstance()->getOutReslW(mVdecJpegChnId) ||
        oh != VdeChMnger::getInstance()->getOutReslH(mVdecJpegChnId)
    ) {
        VdeChMnger::getInstance()->uninit(mVdecJpegChnId);
        IF_ERR_RETURN(VdeChMnger::getInstance()->init(mVdecJpegChnId, E_VDEC_MODE_JPEG, iw, ih, ow, oh) == false, false, loge("init error !"));
        IF_ERR_RETURN(VdeChMnger::getInstance()->setDepth(mVdecJpegChnId, 4) == false, false, loge("setDepth error !"));
        IF_ERR_RETURN(VdeChMnger::getInstance()->enable(mVdecJpegChnId) == false, false, loge("enable error !"));
        mIsVdecJpegInited = true;
    }

    VdeChMnger::getInstance()->freestrm(mVdecJpegChnId); //一定要执行，不然buf没有还进去
    IF_ERR_RETURN(VdeChMnger::getInstance()->putstrm(mVdecJpegChnId, jpg, jpglen, pts, 40) == false, false, loge("putstrm error !"));

    fd[0] = VdeChMnger::getInstance()->getfd(mVdecJpegChnId);

    if (select(fd, ARRAY_SIZE(fd), status, 100) > 0) {
        IF_ERR_RETURN(VdeChMnger::getInstance()->getstrm(mVdecJpegChnId, stream) == false, false, loge("getstrm error !"));
    } else {
        // loge("select timeout !");
        return false;
    }

    //统计花费的时间
    logd("====== take %llu ms", my::getClockMs() - ms);

    return true;
}

bool MediaFlowMnger::jpeg2yuvfree()
{
    if (mIsVdecJpegInited == true) {

        VdeChMnger::getInstance()->uninit(mVdecJpegChnId);

        mIsVdecJpegInited = false;
    }

    return true;
}

//--------------------------------------------------------------

bool MediaFlowMnger::h26x2yuv(uint8_t codec, const char *h26x, int32_t h26xlen, 
    uint64_t pts, uint16_t iw, uint16_t ih, uint16_t ow, uint16_t oh, stream_t& stream)
{
    int32_t fd[1] = { -1 };
    bool status[1] = { false };

    bool *isInited = codec ? &mIsVdecH265Inited : &mIsVdecH264Inited;
    uint8_t vdecH26xChnId = codec ? mVdecH265ChnId : mVdecH264ChnId;

    uint64_t ms = my::getClockMs();

    if (iw <= 0 || ih <= 0 ||
        ow <= 0 || oh <= 0 ||
        h26x == nullptr ||
        h26xlen <= 0
    ) {
        return false;
    }

    if (*isInited == false ||
        iw != VdeChMnger::getInstance()->getInReslW(vdecH26xChnId) ||
        ih != VdeChMnger::getInstance()->getInReslH(vdecH26xChnId) ||
        ow != VdeChMnger::getInstance()->getOutReslW(vdecH26xChnId) ||
        oh != VdeChMnger::getInstance()->getOutReslH(vdecH26xChnId)
    ) {
        VdeChMnger::getInstance()->uninit(vdecH26xChnId);
        IF_ERR_RETURN(VdeChMnger::getInstance()->init(vdecH26xChnId, E_VDEC_MODE_H264, iw, ih, ow, oh) == false, false, loge("init error !"));
        IF_ERR_RETURN(VdeChMnger::getInstance()->setDepth(vdecH26xChnId, 4) == false, false, loge("setDepth error !"));
        IF_ERR_RETURN(VdeChMnger::getInstance()->enable(vdecH26xChnId) == false, false, loge("enable error !"));
        *isInited = true;
    }

    VdeChMnger::getInstance()->freestrm(vdecH26xChnId); //一定要执行，不然buf没有还进去
    IF_ERR_RETURN(VdeChMnger::getInstance()->putstrm(vdecH26xChnId, h26x, h26xlen, pts, 100) == false, false, loge("putstrm error !"));

    fd[0] = VdeChMnger::getInstance()->getfd(vdecH26xChnId);

    if (select(fd, ARRAY_SIZE(fd), status, 20) > 0) {
        IF_ERR_RETURN(VdeChMnger::getInstance()->getstrm(vdecH26xChnId, stream) == false, false, loge("getstrm error !"));
    } else {
        // loge("select timeout !");
        return false;
    }

    //统计花费的时间
    logd("====== take %llu ms", my::getClockMs() - ms);

    return true;
}

bool MediaFlowMnger::h26x2yuvfree(uint8_t codec)
{
    bool *isInited = codec ? &mIsVdecH265Inited : &mIsVdecH264Inited;
    uint8_t vdecH26xChnId = codec ? mVdecH265ChnId : mVdecH264ChnId;

    if (*isInited == true) {

        VdeChMnger::getInstance()->uninit(vdecH26xChnId);

        *isInited = false;
    }

    return true;
}

bool MediaFlowMnger::h264strm2yuv(const char *h264strm, int32_t len, 
    uint64_t pts, uint16_t iw, uint16_t ih, uint16_t ow, uint16_t oh, stream_t& stream)
{
    return h26x2yuv(0, h264strm, len, pts, iw, ih, ow, oh, stream);
}

bool MediaFlowMnger::h264strm2yuvfree()
{
    return h26x2yuvfree(0);
}

bool MediaFlowMnger::h265strm2yuv(const char *h265strm, int32_t len, 
    uint64_t pts, uint16_t iw, uint16_t ih, uint16_t ow, uint16_t oh, stream_t& stream)
{
    return h26x2yuv(1, h265strm, len, pts, iw, ih, ow, oh, stream);
}

bool MediaFlowMnger::h265strm2yuvfree()
{
    return h26x2yuvfree(1);
}

//--------------------------------------------------------------

/**
 * @brief 
 * 播放过程中，使用的是DISP DEV 0，LAYER ID 1
 * 播放接口不会关闭 LAYER ID 0，交给业务处理
 */

bool MediaFlowMnger::h26x2disp(uint8_t codec, const char *h26x, 
    int32_t h26xlen, uint64_t pts, uint16_t iw, uint16_t ih, 
    uint16_t x, uint16_t y, uint16_t ow, uint16_t oh)
{
    bool *isInited = codec ? &mIsVdecDispH265Inited : &mIsVdecDispH264Inited;
    uint8_t vdecH26xChnId = codec ? mVdecDispH265ChnId : mVdecDispH264ChnId;

    miniui::rect_t rect = miniui::Painter::getInstance()->getFBRect();

    if (mCfg->displaySwitch == false) {
        logw("'displaySwitch' of config.ini not enable !");
        return false;
    }

    if (iw <= 0 || ih <= 0 ||
        ow <= 0 || oh <= 0 ||
        ow > iw || oh > ih ||
        x >= rect.w ||
        y >= rect.h ||
        ow > rect.w ||
        oh > rect.h ||
        h26x == nullptr ||
        h26xlen <= 0
    ) {
        loge("parm error !");
        return false;
    }

    if (*isInited == false ||
        iw != VdeChMnger::getInstance()->getInReslW(vdecH26xChnId) ||
        ih != VdeChMnger::getInstance()->getInReslH(vdecH26xChnId) ||
        ow != VdeChMnger::getInstance()->getOutReslW(vdecH26xChnId) ||
        oh != VdeChMnger::getInstance()->getOutReslH(vdecH26xChnId)
    ) {
        VdeChMnger::getInstance()->uninit(vdecH26xChnId);
        IF_ERR_RETURN(VdeChMnger::getInstance()->init(vdecH26xChnId, E_VDEC_MODE_H264, iw, ih, ow, oh) == false, false, loge("init error !"));
        IF_ERR_RETURN(VdeChMnger::getInstance()->setDepth(vdecH26xChnId, 4) == false, false, loge("setDepth error !"));
        IF_ERR_RETURN(VdeChMnger::getInstance()->enable(vdecH26xChnId) == false, false, loge("enable error !"));
        *isInited = true;

        mH26x2dispRect = miniui::Rect();
    }

    if (x != mH26x2dispRect.x ||
        y != mH26x2dispRect.y ||
        ow != mH26x2dispRect.w ||
        oh != mH26x2dispRect.h
    ) {
        //---------------------------------------------------------

        smallsc_();

        DispMnger::getInstance()->uninitLayer(E_DISP_LAYER_ID_1);
        IF_ERR_RETURN(DispMnger::getInstance()->initLayer(E_DISP_LAYER_ID_1) == false, false, loge("initLayer error !"));
        IF_ERR_RETURN(DispMnger::getInstance()->layerBindDev(E_DISP_LAYER_ID_1, E_DISP_DEV_ID_0, rect.w, rect.h) == false, false, loge("layerBindDev error !"));

        IF_ERR_RETURN(DispMnger::getInstance()->initInPort(E_DISP_LAYER_ID_1, E_DISP_INPORT_ID_0) == false, false, loge("initInPort error !"));
        IF_ERR_RETURN(DispMnger::getInstance()->setInPortWin(E_DISP_LAYER_ID_1, E_DISP_INPORT_ID_0, 
            x, y, ow, oh) == false, false, loge("setInPortWin error !"));
        //---------------------------------------------------------
        IF_ERR_RETURN(DispMnger::getInstance()->inPortBind(
            E_DISP_LAYER_ID_1, E_DISP_INPORT_ID_0, E_MI_MODULE_ID_VDEC, 
            VDEC_DEV_ID, vdecH26xChnId, 0, E_DISP_DEV_ID_0, 25, 25) == false, false, loge("inPortBind error !"));

        //---------------------------------------------------------
        IF_ERR_RETURN(DispMnger::getInstance()->enableLayer(E_DISP_LAYER_ID_1) == false, false, loge("enableLayer error !"));
        IF_ERR_RETURN(DispMnger::getInstance()->inPortEnable(E_DISP_LAYER_ID_1, E_DISP_INPORT_ID_0) == false, false, loge("inPortEnable error !"));
        //---------------------------------------------------------

        mH26x2dispRect = miniui::Rect(x, y, ow, oh);

        mDispLock = true;
    }

    IF_ERR_RETURN(VdeChMnger::getInstance()->putstrm(vdecH26xChnId, h26x, h26xlen, pts, 80) == false, false, loge("putstrm error !"));

    return true;
}

bool MediaFlowMnger::h26x2dispfree(uint8_t codec)
{
    bool *isInited = codec ? &mIsVdecDispH265Inited : &mIsVdecDispH264Inited;
    uint8_t vdecH26xChnId = codec ? mVdecDispH265ChnId : mVdecDispH264ChnId;

    if (*isInited == true) {

        DispMnger::getInstance()->uninitLayer(E_DISP_LAYER_ID_1);
        VdeChMnger::getInstance()->uninit(vdecH26xChnId);

        mH26x2dispRect = miniui::Rect();
        mDispLock = false;

        *isInited = false;
    }

    return true;
}

bool MediaFlowMnger::h264strm2disp(const char *h264strm, int32_t len, 
    uint64_t pts, uint16_t iw, uint16_t ih, uint16_t x, uint16_t y, uint16_t ow, uint16_t oh)
{
    return h26x2disp(0, h264strm, len, pts, iw, ih, x, y, ow, oh);
}

bool MediaFlowMnger::h264strm2dispfree()
{
    return h26x2dispfree(0);
}

bool MediaFlowMnger::h265strm2disp(const char *h264strm, int32_t len, 
    uint64_t pts, uint16_t iw, uint16_t ih, uint16_t x, uint16_t y, uint16_t ow, uint16_t oh)
{
    return h26x2disp(1, h264strm, len, pts, iw, ih, x, y, ow, oh);
}

bool MediaFlowMnger::h265strm2dispfree()
{
    return h26x2dispfree(1);
}

//--------------------------------------------------------------

bool MediaFlowMnger::OSDMainStrmDrawStart(uint8_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->OSDMainStrmDrawStart();
}

uint16_t MediaFlowMnger::OSDMainStrmGetReslW(uint16_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->OSDMainStrmGetReslW();
}

uint16_t MediaFlowMnger::OSDMainStrmGetReslH(uint16_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->OSDMainStrmGetReslH();
}

bool MediaFlowMnger::OSDMainStrmDrawText(uint8_t chn, uint16_t x, uint16_t y, RGN::color_t color, const char *text, YUVFontSize_t ftSize)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->OSDMainStrmDrawText(x, y, color, text, ftSize);
}

bool MediaFlowMnger::OSDMainStrmDrawEnd(uint8_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->OSDMainStrmDrawEnd();
}

//--------------------------------------------------------------

bool MediaFlowMnger::OSDSubStrmDrawStart(uint8_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->OSDSubStrmDrawStart();
}

uint16_t MediaFlowMnger::OSDSubStrmGetReslW(uint16_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->OSDSubStrmGetReslW();
}

uint16_t MediaFlowMnger::OSDSubStrmGetReslH(uint16_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->OSDSubStrmGetReslH();
}

bool MediaFlowMnger::OSDSubStrmDrawText(uint8_t chn, uint16_t x, uint16_t y, RGN::color_t color, const char *text, YUVFontSize_t ftSize)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->OSDSubStrmDrawText(x, y, color, text, ftSize);
}

bool MediaFlowMnger::OSDSubStrmDrawEnd(uint8_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->OSDSubStrmDrawEnd();
}

//--------------------------------------------------------------

bool MediaFlowMnger::OSDDispDrawStart(uint8_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->OSDDispDrawStart();
}

uint16_t MediaFlowMnger::OSDDispGetReslW(uint16_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->OSDDispGetReslW();
}

uint16_t MediaFlowMnger::OSDDispGetReslH(uint16_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->OSDDispGetReslH();
}

bool MediaFlowMnger::OSDDispDrawText(uint8_t chn, uint16_t x, uint16_t y, RGN::color_t color, const char *text, YUVFontSize_t ftSize)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->OSDDispDrawText(x, y, color, text, ftSize);
}

bool MediaFlowMnger::OSDDispDrawEnd(uint8_t chn)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->OSDDispDrawEnd();
}

//--------------------------------------------------------------

bool MediaFlowMnger::getstrm(uint8_t chn, gotstrmcb gotcb, void *user, int32_t tmoutms)
{
    if (! (0 <= chn && chn < mMediaFlowList.size())) {
        return false;
    }

    if (mMediaFlowList[chn] == nullptr) {
        return false;
    }

    return mMediaFlowList[chn]->getstrm(gotcb, user, tmoutms);
}

//--------------------------------------------------------------

bool MediaFlowMnger::getMicStrm(gotMicStrmCallback gotcb, void *user, int32_t tmoutms)
{
    int32_t ret = -1;
    bool isGot = false;
    stream_t stream;

    int32_t fd[E_AUDIO_INPUT_CHN_ID_MAX] = { -1 };
    bool status[E_AUDIO_INPUT_CHN_ID_MAX] = { false };

    for (uint8_t i = 0; i < E_AUDIO_INPUT_CHN_ID_MAX; i ++) {
        status[i] = false;

        fd[i] = AudioInputMnger::getInstance()->getfd(E_AUDIO_INPUT_DEV_ID_5, (audioInputChnId_t) i);
    }

    ret = select(fd, E_AUDIO_INPUT_CHN_ID_MAX, status, tmoutms);
    if (ret > 0) {
        for (uint8_t i = 0; i < E_AUDIO_INPUT_CHN_ID_MAX; i ++) {
            if (status[i] == true) {
                isGot = AudioInputMnger::getInstance()->getstrm(E_AUDIO_INPUT_DEV_ID_5, (audioInputChnId_t) i, stream);

                if (isGot && gotcb) {
                    gotcb((audioInputChnId_t) i, stream, user);
                }

                /**
                 * @brief 
                 * 流获取后，需要调用此接口，否则下次无法获取到流
                 */
                AudioInputMnger::getInstance()->freestrm(E_AUDIO_INPUT_DEV_ID_5, (audioInputChnId_t) i);
            }
        }
    }

    return ret >= 0;
}

//--------------------------------------------------------------

MediaFlowMnger *MediaFlowMnger::getInstance()
{
    static std::mutex mutex;
    static MediaFlowMnger *_instance = nullptr;

    if (_instance == nullptr) {
        std::lock_guard<std::mutex> lock(mutex);

        if (_instance == nullptr) {
            _instance = new MediaFlowMnger();
        }
    }

    return _instance;
}
