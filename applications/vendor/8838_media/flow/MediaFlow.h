#ifndef __MEDIA_FLOW_H__
#define __MEDIA_FLOW_H__

#include "SensorMnger.h"
#include "VifMnger.h"
#include "SclMnger.h"
#include "DispMnger.h"
#include "VencH26xChMnger.h"
#include "VencJpegChMnger.h"

#include "IspMnger.h"

#include "RGN.h"

#include "ThemeMnger.h"

#include "idvr.media.config.h"

// #define __USE_ISP__
#define ISP_DEV_ID                  E_ISP_DEV_ID_0

#ifdef __USE_ISP__
#define SCL_DEV_ID                  E_SCL_DEV_ID_0
#else
#define SCL_DEV_ID                  E_SCL_DEV_ID_1
#endif

#define RGN_MAX_W                   1920
#define RGN_MAX_H                   1080

#define __MAIN_STRM_RGN_ENABLE__
#define __SUB_STRM_RGN_ENABLE__
// #define __DISP_RGN_ENABLE__

#define SCL_MAIN_STRM_OUTPORT       E_SCL_OUTPORT_ID_0
#define SCL_SUB_STRM_OUTPORT        E_SCL_OUTPORT_ID_1
#define SCL_DISP_OUTPORT            E_SCL_OUTPORT_ID_2
#define SCL_SNAP_OUTPORT            E_SCL_OUTPORT_ID_3
#define SCL_AI_OUTPORT              E_SCL_OUTPORT_ID_4

class MediaFlow
{
public:
    typedef struct OSDText
    {

    } OSDText_t;

public:
    MediaFlow(idvrMediaConf_t *cfg, uint8_t chn, vifDevId_t vifDevId, uint8_t ispChnId, uint8_t sclChnId,
        dispInPortId_t dispInPortId, const miniui::rect_t& winRect, bool valid);
    ~MediaFlow();

    bool vifInit();
    bool vifUnInit();

    bool ispInit(uint16_t reslw, uint16_t reslh, uint8_t resfps);
    bool sclInit();
    bool dispInit();

    bool vencJpegUnInit();

    bool vencH26xMainInit();
    bool vencH26xMainUnInit();

    bool vencH26xSubInit();
    bool vencH26xSubUnInit();

    //----------------------------------------

    bool enableMainStrm();
    bool disableMainStrm();

    bool enableSubStrm();
    bool disableSubStrm();

    bool enableSnap();
    bool disableSnap();

    bool enableDispStrm();
    bool disableDispStrm();

    bool setMainStrmResl(uint16_t w, uint16_t h);
    bool setSubStrmResl(uint16_t w, uint16_t h);

    bool setMainStrmFps(uint8_t fps);
    bool setSubStrmFps(uint8_t fps);

    bool setMainStrmBps(uint32_t bps);
    bool setSubStrmBps(uint32_t bps);

    bool setSnapResl(uint16_t w, uint16_t h);
    bool setAiCrop(uint16_t w, uint16_t h);

    //----------------------------------------

    bool fullsc(uint16_t w, uint16_t h);
    bool smallsc();

    //----------------------------------------

    bool yuv2jpeg(const char *yuv, int32_t yuvlen, uint16_t w, uint16_t h, uint8_t quality, uint64_t pts, stream_t& stream, bool lock);

    //----------------------------------------

    bool getstrm(gotstrmcb gotcb, void *user, int32_t tmoutms = 10);

    //----------------------------------------

    uint8_t getResFps();
    uint8_t getDispFps();

    uint16_t getReslW();
    uint16_t getReslH();

    //---------------------------------------

    bool cntstatus();
    uint16_t getAnadecReslW();
    uint16_t getAnadecReslH();

    uint8_t getAnadecFps();

    bool isReslChanged();

    //---------------------------------------

    bool OSDMainStrmDrawStart();
    uint16_t OSDMainStrmGetReslW();
    uint16_t OSDMainStrmGetReslH();
    bool OSDMainStrmDrawText(uint16_t x, uint16_t y, RGN::color_t color, const char *text, YUVFontSize_t ftSize);
    bool OSDMainStrmDrawEnd();

    bool OSDSubStrmDrawStart();
    uint16_t OSDSubStrmGetReslW();
    uint16_t OSDSubStrmGetReslH();
    bool OSDSubStrmDrawText(uint16_t x, uint16_t y, RGN::color_t color, const char *text, YUVFontSize_t ftSize);
    bool OSDSubStrmDrawEnd();

    bool OSDDispDrawStart();
    uint16_t OSDDispGetReslW();
    uint16_t OSDDispGetReslH();
    bool OSDDispDrawText(uint16_t x, uint16_t y, RGN::color_t color, const char *text, YUVFontSize_t ftSize);
    bool OSDDispDrawEnd();

    //----------------------------------------

    vifDevId_t getVifDevId();
    bool isValid();

    miniui::rect_t getWinRect();

    std::string getPhyName();

private:
    bool alignVencResl(uint16_t& w, uint16_t& h);

    bool vifInit_();
    bool vifSuspend();

    bool ispInit_(uint16_t reslw, uint16_t reslh, uint8_t resfps);
    bool sclInit_();

    bool vencH26xInit_(uint8_t strmIdx);
    bool vencH26xUnInit_(uint8_t strmIdx);

    bool yuv2jpeg_(const char *yuv, int32_t yuvlen, uint16_t w, uint16_t h, uint64_t pts, stream_t& stream);
    bool yuv2jpeg_(const char *yuv, int32_t yuvlen, uint16_t w, uint16_t h, uint8_t quality, uint64_t pts, stream_t& stream);

    bool getstrm_(strmFrom_t strmFrom, gotstrmcb gotcb, void *user);

    bool OSDDrawStart_(uint16_t RGNChnId);
    bool OSDDrawText_(uint16_t RGNChnId, uint16_t x, uint16_t y, RGN::color_t color, const char *text, YUVFontSize_t ftSize);
    bool OSDDrawEnd_(uint16_t RGNChnId);

private:
    std::mutex              mMutex;

    idvrMediaConf_t      	*mCfg;

    uint8_t                 mChn; //逻辑通道

    uint8_t                 mIspChnId;

    uint8_t                 mSclChnId;
    dispInPortId_t          mDispInPortId;
    uint8_t                 mVenH26xChnId[2];

    uint8_t                 mVencJpegChnId;

    uint16_t                mMainRGNChnId;
    uint16_t                mSubRGNChnId;
    uint16_t                mDispRGNChnId;

    vifDevId_t              mVifDevId;

    uint16_t                mReslW;
    uint16_t                mReslH;

    uint8_t                 mResFps;
    uint8_t                 mDispFps;

    miniui::rect_t    		mWinRect;

    bool                    mIsValid;

    uint8_t                 mSclOutFps;

    bool                    mIsVencJpegInited;

    bool                    mIsVencEnabled[2];

    uint32_t                mGetAiStrmNum;
    uint64_t                mGetAiStrmTm;
    uint32_t                mAiFrameNum;
    uint32_t                mLastGetAiTm;
};

#endif
