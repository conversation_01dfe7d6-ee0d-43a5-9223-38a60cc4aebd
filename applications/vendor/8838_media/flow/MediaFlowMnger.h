#ifndef __MEDIA_FLOW_MNGER_H__
#define __MEDIA_FLOW_MNGER_H__

#include "SensorMnger.h"
#include "VifMnger.h"
#include "SclMnger.h"
#include "DispMnger.h"

#include "VencJpegCh.h"
#include "VdeChMnger.h"

#include "AudioInputMnger.h"
#include "AudioOutputMnger.h"

#include "MediaFlow.h"

#include "ThemeMnger.h"

#include "idvr.media.config.h"

typedef struct PhyMap
{
    std::map<std::string, vifDevId_t>   vifmap;
} phyMap_t;

class MediaFlowMnger
{
private:
    typedef struct
    {
        uint8_t                     chn = 0;
        vifDevId_t                  vifDevId = E_VIF_DEV_ID_UNKOWN;
    } chvifmap_t;

    typedef struct
    {
        std::vector<chvifmap_t>     invalid;
        std::vector<chvifmap_t>     valid;
    } viflist_t;

private:
    Media<PERSON>low<PERSON>nger();
    ~MediaFlowMnger();

public:

    //----------------------------------------------------------

    bool init(idvrMediaConf_t *cfg);
    bool uninit();

    bool isInited();
    //----------------------------------------------------------

    bool hidden();
    bool show();

    bool fullsc(uint8_t chn, uint16_t x, uint16_t y, uint16_t w, uint16_t h);
    bool smallsc();

    //----------------------------------------------------------

    bool speak(const char *pcm, int32_t len, audioVolumeLevel_t audioVolumeLevel);

    //----------------------------------------------------------

    bool enableMainStrm(uint8_t chn);
    bool disableMainStrm(uint8_t chn);

    bool enableSubStrm(uint8_t chn);
    bool disableSubStrm(uint8_t chn);

    //----------------------------------------------------------

    bool enableSnap(uint8_t chn);
    bool disableSnap(uint8_t chn);
    
    //----------------------------------------------------------

    bool setMainStrmResl(uint8_t chn, uint16_t w, uint16_t h);
    bool setSubStrmResl(uint8_t chn, uint16_t w, uint16_t h);

    bool setMainStrmFps(uint8_t chn, uint8_t fps);
    bool setSubStrmFps(uint8_t chn, uint8_t fps);

    bool setMainStrmBps(uint8_t chn, uint32_t bps);
    bool setSubStrmBps(uint8_t chn, uint32_t bps);

    //----------------------------------------------------------

    bool setSnapResl(uint8_t chn, uint16_t w, uint16_t h);
    bool setAiCrop(uint8_t chn, uint16_t w, uint16_t h);

    //----------------------------------------------------------

    bool cntstatus(uint8_t chn);
    uint16_t getAnadecReslW(uint8_t chn);
    uint16_t getAnadecReslH(uint8_t chn);

    uint8_t getAnadecFps(uint8_t chn);

    bool isReslChanged(uint8_t chn);

    std::string getPhyName(uint8_t chn);
    //----------------------------------------------------------

    /**
     * @brief 
     * yuv转jpeg接口
     * 此接口作用于通道，由指定通道的jpeg编码器来编码jpeg
     * 用于通道内的yuv转jpeg
     */
    bool yuv2jpeg(uint8_t chn, const char *yuv, int32_t yuvlen, uint16_t w, uint16_t h, uint8_t quality, uint64_t pts, stream_t& stream, bool lock);

    /**
     * @brief 
     * yuv转jpeg接口
     * 此接口使用公共的全局jpeg编码器来编码jpeg
     * 用于外部yuv文件转jpeg
     */
    bool yuv2jpeg(const char *yuv, int32_t yuvlen, uint16_t w, uint16_t h, uint8_t quality, uint64_t pts, stream_t& stream);

    //----------------------------------------------------------

    /**
     * @brief 
     * jpeg转yuv接口
     * 此接口使用公共的全局jpeg解码器
     * 用于外部jpeg文件转yuv
     * 
     * 如果不再调用jpeg2yuv，请使用jpeg2yuvfree腾出资源
     */
    bool jpeg2yuv(const char *jpg, int32_t jpglen, uint16_t iw, uint16_t ih, uint16_t ow, uint16_t oh, uint64_t pts, stream_t& stream);
    bool jpeg2yuvfree();

    /**
     * @brief 
     * h264/h265转yuv接口
     * 此接口使用公共的全局h264/h265解码器
     * 用于外部h264/h265文件转yuv
     * 
     * 如果不再调用h26xstrm2yuv，请使用h26xstrm2yuvfree腾出资源
     */
    bool h264strm2yuv(const char *h264strm, int32_t len, uint64_t pts, 
        uint16_t iw, uint16_t ih, uint16_t ow, uint16_t oh, stream_t& stream);

    bool h264strm2yuvfree();

    bool h265strm2yuv(const char *h265strm, int32_t len, uint64_t pts, 
        uint16_t iw, uint16_t ih, uint16_t ow, uint16_t oh, stream_t& stream);

    bool h265strm2yuvfree();

    /**
     * @brief 
     * h264/h265输送到disp设备
     * 此接口使用公共的全局h264/h265解码器
     * 用于外部h264/h265文件通过DISP显示
     * 
     * 如果不再调用h26xstrm2disp，请使用h26xstrm2dispfree腾出资源
     */
    bool h264strm2disp(const char *h264strm, int32_t len, uint64_t pts, 
        uint16_t iw, uint16_t ih, uint16_t x, uint16_t y, uint16_t ow, uint16_t oh);

    bool h264strm2dispfree();

    bool h265strm2disp(const char *h265strm, int32_t len, uint64_t pts, 
        uint16_t iw, uint16_t ih, uint16_t x, uint16_t y, uint16_t ow, uint16_t oh);

    bool h265strm2dispfree();

    //----------------------------------------------------------

    bool getstrm(uint8_t chn, gotstrmcb gotcb, void *user, int32_t tmoutms = 10);
    bool getMicStrm(gotMicStrmCallback gotcb, void *user, int32_t tmoutms = 10);

    //----------------------------------------------------------

    bool OSDMainStrmDrawStart(uint8_t chn);
    uint16_t OSDMainStrmGetReslW(uint16_t chn);
    uint16_t OSDMainStrmGetReslH(uint16_t chn);
    bool OSDMainStrmDrawText(uint8_t chn, uint16_t x, uint16_t y, RGN::color_t color, const char *text, YUVFontSize_t ftSize);
    bool OSDMainStrmDrawEnd(uint8_t chn);

    bool OSDSubStrmDrawStart(uint8_t chn);
    uint16_t OSDSubStrmGetReslW(uint16_t chn);
    uint16_t OSDSubStrmGetReslH(uint16_t chn);
    bool OSDSubStrmDrawText(uint8_t chn, uint16_t x, uint16_t y, RGN::color_t color, const char *text, YUVFontSize_t ftSize);
    bool OSDSubStrmDrawEnd(uint8_t chn);

    bool OSDDispDrawStart(uint8_t chn);
    uint16_t OSDDispGetReslW(uint16_t chn);
    uint16_t OSDDispGetReslH(uint16_t chn);
    bool OSDDispDrawText(uint8_t chn, uint16_t x, uint16_t y, RGN::color_t color, const char *text, YUVFontSize_t ftSize);
    bool OSDDispDrawEnd(uint8_t chn);

    //----------------------------------------------------------

    static MediaFlowMnger *getInstance();

    static bool chVifMapNodeComp(const chvifmap_t& node1, const chvifmap_t& node2)
    {
        if (node1.vifDevId < node2.vifDevId) {
            return true;
        }

        return false;
    }

private:

    bool init_(idvrMediaConf_t *cfg);

    bool getBoardName(std::string& board);

    vifDevId_t getVifDevId(const char *board, const char *phy);

    const phyMap_t *getPhyMap();

    bool vifListInit(const phyMap_t *pm);
    bool vifListUnInit();

    bool flowListInit(const phyMap_t *pm);
    bool flowListUnInit();

    bool sensorInit();
    bool sensorUnInit();

    bool vifInit();
    bool vifUnInit();

    bool ispInit();
    bool ispUnInit();

    bool sclInit();
    bool sclUnInit();

    bool dispInit();
    bool dispUnInit();

    bool vencH26xInit();
    bool vencH26xUnInit();

    bool vencJpegUnInit();

    bool vdecUnInit();

    bool audioOutputInit();
    bool audioOutputUnInit();

    bool audioInputInit();
    bool audioInputUnInit();

    bool smallsc_();

    bool show_();

    bool h26x2yuv(uint8_t codec, const char *h26x, int32_t h26xlen, 
        uint64_t pts, uint16_t iw, uint16_t ih, uint16_t ow, uint16_t oh, stream_t& stream);

    bool h26x2yuvfree(uint8_t codec);

    bool h26x2disp(uint8_t codec, const char *h26x, int32_t h26xlen, 
        uint64_t pts, uint16_t iw, uint16_t ih, uint16_t x, uint16_t y, uint16_t ow, uint16_t oh);

    bool h26x2dispfree(uint8_t codec);

    bool yuv2jpeg_(const char *yuv, int32_t yuvlen, uint16_t w, uint16_t h, uint64_t pts, stream_t& stream);

private:
    std::mutex                      mJpeg2YUVMtx;
    std::mutex                      mYUV2JpegMtx;

    idvrMediaConf_t                 *mCfg;

    std::mutex                      mScMtx;
    uint8_t                         mFullscChn;

    std::map<snrPadId_t, viflist_t> mVifList;

    std::vector<MediaFlow *>        mMediaFlowList;

    bool                            mIsVencJpegInited;
    uint8_t                         mVencJpegChnId;

    bool                            mIsVdecH264Inited;
    uint8_t                         mVdecH264ChnId;

    bool                            mIsVdecH265Inited;
    uint8_t                         mVdecH265ChnId;

    bool                            mIsVdecJpegInited;
    uint8_t                         mVdecJpegChnId;

    bool                            mIsVdecDispH264Inited;
    uint8_t                         mVdecDispH264ChnId;

    bool                            mIsVdecDispH265Inited;
    uint8_t                         mVdecDispH265ChnId;

    bool                            mIsHidden;

    miniui::rect_t                  mH26x2dispRect;
    bool                            mDispLock;

    bool                            mIsInited;
};

#endif
