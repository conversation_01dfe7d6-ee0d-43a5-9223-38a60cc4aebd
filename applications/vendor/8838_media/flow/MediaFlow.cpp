#include "MediaFlow.h"

#define __MEDIA_FLOW_LOGD__(fmt, args...)    logd("CHN[%u] " fmt, mChn, ## args)
#define __MEDIA_FLOW_LOGW__(fmt, args...)    logw("CHN[%u] " fmt, mChn, ## args)
#define __MEDIA_FLOW_LOGE__(fmt, args...)    loge("CHN[%u] " fmt, mChn, ## args)

#define __CHECKVALID__() \
    if (mIsValid == false) { \
        __MEDIA_FLOW_LOGD__("invalid"); \
        return true; \
    }

MediaFlow::MediaFlow(idvrMediaConf_t *cfg, uint8_t chn, vifDevId_t vifDevId, uint8_t ispChnId, uint8_t sclChnId,
    dispInPortId_t dispInPortId, const miniui::rect_t& winRect, bool valid)
    : mCfg(cfg)
    , mChn(chn)
    , mIspChnId(ispChnId)
    , mSclChnId(sclChnId)
    , mDispInPortId(dispInPortId)
    , mVencJpegChnId(chn + 1) //venc jpeg ch 0用于全局
    , mVifDevId(vifDevId)
    , mReslW(0)
    , mReslH(0)
    , mResFps(0)
    , mDispFps(0)
    , mWinRect(winRect)
    , mIsValid(valid)
    , mSclOutFps(0)
    , mGetAiStrmTm(0)
    , mGetAiStrmNum(0)
    , mAiFrameNum(0)
    , mLastGetAiTm(0)
{
    if (mCfg == nullptr) {
        loge("mCfg is null !");
        exit(-1);
    }

    mVenH26xChnId[0] = chn;
    mVenH26xChnId[1] = mVenH26xChnId[0] + cfg->cameras;

    mMainRGNChnId = chn;
    mSubRGNChnId = mMainRGNChnId + cfg->cameras;
    mDispRGNChnId = mSubRGNChnId + cfg->cameras;

    mIsVencEnabled[0] = false;
    mIsVencEnabled[1] = false;
}

MediaFlow::~MediaFlow()
{

}

//--------------------------------------------------------------

bool MediaFlow::alignVencResl(uint16_t& w, uint16_t& h)
{
    /**
     * @brief 
     * 根据官方VENC编码支持的分辨率
     * 推荐的编码宽高为：
     * 3840x2160（4k*2k）
     * 1920x1080(1080P)
     * 1280x720（720P
     * 960x540
     * 640x360
     * 704x576
     * 704x480
     * 352x288
     * 352x240
     */

#if 0 //那就不按照官方推荐的分辨率来了， 让用户随意设置
    uint16_t support[9][2] = {
        { 3840, 2160 },
        { 1920, 1080 },
        { 1280, 720 },
        { 960,  540 },
        { 704,  576 },
        { 704,  480 },
        { 640,  360 },
        { 352,  288 },
        { 352,  240 },
    };

    for (uint8_t i = 0; i < 9; i ++) {
        if (w == support[i][0] &&
            h == support[i][1]
        ) {
            return true;
        }
    }

    __MEDIA_FLOW_LOGW__("venc not support resl[%ux%u] use default resl[704x480] !!!", w, h);

    w = 704;
    h = 480;
#endif
    return true;
}

//--------------------------------------------------------------

bool MediaFlow::vifInit_()
{
    vifGroupId_t vifGroupId = (vifGroupId_t) getVifGroupIdByVifDevId(mVifDevId);
    IF_ERR_RETURN(VifMnger::getInstance()->initGroup(vifGroupId) == false, false, __MEDIA_FLOW_LOGE__("initVifGroup error !"));

    VifMnger::getInstance()->initDev(mVifDevId);
    IF_ERR_RETURN(VifMnger::getInstance()->setResl(mVifDevId, 
        mCfg->ch[mChn].camReslWidth, mCfg->ch[mChn].camReslHeight) == false, false, __MEDIA_FLOW_LOGE__("setResl error !"));
    IF_ERR_RETURN(VifMnger::getInstance()->setFps(mVifDevId, mResFps) == false, false, __MEDIA_FLOW_LOGE__("setFps error !"));
    IF_ERR_RETURN(VifMnger::getInstance()->enable(mVifDevId) == false, false, __MEDIA_FLOW_LOGE__("enable error !"));

    return true;
}

bool MediaFlow::vifSuspend()
{
    /**
     * @brief 
     * 初始化VIF
     * 
     * 由于VIF与SCL绑定不能跨设备号，因此先直接对每一个VIF初始化一下
     * 为了节省性能，初始化后disable该vif，只有用到的vif才
     * 会enable，
     * 默认初始化降低规格，如1080P改为1080N，720P改为720N
     * 即1920x1080改为1080x1920，可以降低vif输入带宽
     */

    __MEDIA_FLOW_LOGW__("vif suspend !!!");

    vifGroupId_t vifGroupId = (vifGroupId_t) getVifGroupIdByVifDevId(mVifDevId);
    IF_ERR_RETURN(VifMnger::getInstance()->initGroup(vifGroupId) == false, false, __MEDIA_FLOW_LOGE__("initVifGroup error !"));

    IF_ERR_RETURN(VifMnger::getInstance()->initDev(mVifDevId) == false, false, __MEDIA_FLOW_LOGE__("initDev error !"));
    IF_ERR_RETURN(VifMnger::getInstance()->setResl(mVifDevId, 1280, 720) == false, false, __MEDIA_FLOW_LOGE__("setResl error !"));
    IF_ERR_RETURN(VifMnger::getInstance()->setDepth(mVifDevId, 4) == false, false, __MEDIA_FLOW_LOGE__("setDepth error !"));
    IF_ERR_RETURN(VifMnger::getInstance()->enable(mVifDevId) == false, false, __MEDIA_FLOW_LOGE__("enable error !"));

    return true;
}

bool MediaFlow::vifInit()
{
    if (mIsValid == true) {
        return vifInit_();
    } else {
        return vifSuspend();
    }

    return true;
}

bool MediaFlow::vifUnInit()
{
    VifMnger::getInstance()->uninitDev(mVifDevId);

    return true;
}

//--------------------------------------------------------------

bool MediaFlow::ispInit_(uint16_t reslw, uint16_t reslh, uint8_t resfps)
{
    mReslW = reslw;
    mReslH = reslh;

    mResFps = resfps;

#ifdef __USE_ISP__
    IF_ERR_RETURN(IspMnger::getInstance()->initCh(ISP_DEV_ID, mIspChnId) == false, false, __MEDIA_FLOW_LOGE__("initCh error !"));
    IF_ERR_RETURN(IspMnger::getInstance()->setChRot(ISP_DEV_ID, mIspChnId, E_ISP_ROT_NONE) == false, false, __MEDIA_FLOW_LOGE__("setChRot error !"));
    IF_ERR_RETURN(IspMnger::getInstance()->setChMirror(ISP_DEV_ID, mIspChnId, false) == false, false, __MEDIA_FLOW_LOGE__("setChMirror error !"));

    IF_ERR_RETURN(IspMnger::getInstance()->uninitOutport(ISP_DEV_ID, mIspChnId, E_ISP_OUTPORT_ID_0) == false, false, __MEDIA_FLOW_LOGE__("uninitOutport error !"));
    IF_ERR_RETURN(IspMnger::getInstance()->initOutport(ISP_DEV_ID, mIspChnId, E_ISP_OUTPORT_ID_0, 
        mReslW, mReslH, mReslW, mReslH) == false, false, "initOutport error !");

    IF_ERR_RETURN(IspMnger::getInstance()->enableOutport(ISP_DEV_ID, mIspChnId, E_ISP_OUTPORT_ID_0) == false, false, __MEDIA_FLOW_LOGE__("enableOutport error !"));

    IF_ERR_RETURN(IspMnger::getInstance()->bind(ISP_DEV_ID, mIspChnId, 
        E_MI_MODULE_ID_VIF, E_MI_SYS_BIND_TYPE_FRAME_BASE, mVifDevId, 0, 0, mResFps, mResFps) == false, false, __MEDIA_FLOW_LOGE__("bind error !"));

    IF_ERR_RETURN(IspMnger::getInstance()->enableCh(ISP_DEV_ID, mIspChnId) == false, false, __MEDIA_FLOW_LOGE__("enableCh error !"));
#endif

    return true;
}

bool MediaFlow::ispInit(uint16_t reslw, uint16_t reslh, uint8_t resfps)
{
    if (mIsValid == true) {
        return ispInit_(reslw, reslh, resfps);
    } else {

    }

    return true;
}

//--------------------------------------------------------------

bool MediaFlow::sclInit_()
{
    uint16_t OSDPosX = 0, OSDPosY = 0;

    mSclOutFps = MAX(mCfg->ch[mChn].video[0].fps, mCfg->ch[mChn].video[1].fps);
    mSclOutFps = MIN(mResFps, mSclOutFps);

    mDispFps = MIN(mSclOutFps, mCfg->displayFps);

    mCfg->ch[mChn].video[0].width = MIN(mCfg->ch[mChn].video[0].width, mReslW);
    mCfg->ch[mChn].video[0].height = MIN(mCfg->ch[mChn].video[0].height, mReslH);

    mCfg->ch[mChn].video[1].width = MIN(mCfg->ch[mChn].video[1].width, mReslW);
    mCfg->ch[mChn].video[1].height = MIN(mCfg->ch[mChn].video[1].height, mReslH);

    IF_ERR_RETURN(SclMnger::getInstance()->initCh(SCL_DEV_ID, mSclChnId) == false, false, __MEDIA_FLOW_LOGE__("initCh error !"));

    //----------------------------------------------------------------

    /**
     * @brief
     * 主码流
     */
    alignVencResl(mCfg->ch[mChn].video[0].width, mCfg->ch[mChn].video[0].height);
    IF_ERR_RETURN(SclMnger::getInstance()->uninitOutport(SCL_DEV_ID, mSclChnId, SCL_MAIN_STRM_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("uninitOutport error !"));
    IF_ERR_RETURN(SclMnger::getInstance()->initOutport(SCL_DEV_ID, mSclChnId, SCL_MAIN_STRM_OUTPORT, mReslW, mReslH, 
        mCfg->ch[mChn].video[0].width, mCfg->ch[mChn].video[0].height) == false, false, __MEDIA_FLOW_LOGE__("initOutport error !"));

    if (mCfg->ch[mChn].video[0].flip == true) {
        IF_ERR_RETURN(SclMnger::getInstance()->setOutportFlip(SCL_DEV_ID, mSclChnId, SCL_MAIN_STRM_OUTPORT, true) == false, 
            false, __MEDIA_FLOW_LOGE__("setOutportFlip error !"));
    }

    if (mCfg->ch[mChn].video[0].mirror == true) {
        IF_ERR_RETURN(SclMnger::getInstance()->setOutportMirror(SCL_DEV_ID, mSclChnId, SCL_MAIN_STRM_OUTPORT, true) == false, 
            false, __MEDIA_FLOW_LOGE__("setOutportMirror error !"));
    }

    if ((
            mCfg->ch[mChn].ai.enable == true &&
            mCfg->ch[mChn].ai.attachment == 0
        ) || (
            (
                mCfg->ch[mChn].save == 2 ||
                mCfg->ch[mChn].save == 3
            ) && 
            mCfg->ch[mChn].recordInput == 0
        )
    ) {
        IF_ERR_RETURN(SclMnger::getInstance()->enableOutport(SCL_DEV_ID, mSclChnId, SCL_MAIN_STRM_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("enableOutport error !"));

        if (mCfg->ch[mChn].video[0].width > RGN_MAX_W) {
            OSDPosX = (mCfg->ch[mChn].video[0].width - RGN_MAX_W) / 2;
        } else {
            OSDPosX = 0;
        }

        if (mCfg->ch[mChn].video[0].height > RGN_MAX_H) {
            OSDPosY = (mCfg->ch[mChn].video[0].height - RGN_MAX_H) / 2;
        } else {
            OSDPosY = 0;
        }

    #ifdef __MAIN_STRM_RGN_ENABLE__
        IF_ERR_RETURN(RGN::getInstance()->initCh(mMainRGNChnId, MIN(mCfg->ch[mChn].video[0].width, RGN_MAX_W), MIN(mCfg->ch[mChn].video[0].height, RGN_MAX_H)) == false, 
            false, __MEDIA_FLOW_LOGE__("initCh error !"));
        IF_ERR_RETURN(RGN::getInstance()->attach(mMainRGNChnId, SCL_DEV_ID, mSclChnId, SCL_MAIN_STRM_OUTPORT, OSDPosX, OSDPosY) == false, 
            false, __MEDIA_FLOW_LOGE__("attach error !"));
    #endif
    }

    /**
     * @brief
     * 子码流
     */
    alignVencResl(mCfg->ch[mChn].video[1].width, mCfg->ch[mChn].video[1].height);
    IF_ERR_RETURN(SclMnger::getInstance()->uninitOutport(SCL_DEV_ID, mSclChnId, SCL_SUB_STRM_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("uninitOutport error !"));
    IF_ERR_RETURN(SclMnger::getInstance()->initOutport(SCL_DEV_ID, mSclChnId, SCL_SUB_STRM_OUTPORT, mReslW, mReslH, 
        mCfg->ch[mChn].video[1].width, mCfg->ch[mChn].video[1].height) == false, false, "initOutport error !");

    if (mCfg->ch[mChn].video[1].flip == true) {
        IF_ERR_RETURN(SclMnger::getInstance()->setOutportFlip(SCL_DEV_ID, mSclChnId, SCL_SUB_STRM_OUTPORT, true) == false, 
            false, __MEDIA_FLOW_LOGE__("setOutportFlip error !"));
    }

    if (mCfg->ch[mChn].video[1].mirror == true) {
        IF_ERR_RETURN(SclMnger::getInstance()->setOutportMirror(SCL_DEV_ID, mSclChnId, SCL_SUB_STRM_OUTPORT, true) == false, 
            false, __MEDIA_FLOW_LOGE__("setOutportMirror error !"));
    }

    if ((
            mCfg->ch[mChn].ai.enable == true &&
            mCfg->ch[mChn].ai.attachment == 1
        ) || (
            (
                mCfg->ch[mChn].save == 2 ||
                mCfg->ch[mChn].save == 3
            ) && 
            mCfg->ch[mChn].recordInput == 1
        )
    ) {
        IF_ERR_RETURN(SclMnger::getInstance()->enableOutport(SCL_DEV_ID, mSclChnId, SCL_SUB_STRM_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("enableOutport error !"));

        if (mCfg->ch[mChn].video[1].width > RGN_MAX_W) {
            OSDPosX = (mCfg->ch[mChn].video[1].width - RGN_MAX_W) / 2;
        } else {
            OSDPosX = 0;
        }

        if (mCfg->ch[mChn].video[1].height > RGN_MAX_H) {
            OSDPosY = (mCfg->ch[mChn].video[1].height - RGN_MAX_H) / 2;
        } else {
            OSDPosY = 0;
        }

    #ifdef __SUB_STRM_RGN_ENABLE__
        IF_ERR_RETURN(RGN::getInstance()->initCh(mSubRGNChnId, MIN(mCfg->ch[mChn].video[1].width, RGN_MAX_W), MIN(mCfg->ch[mChn].video[1].height, RGN_MAX_H)) == false, 
            false, __MEDIA_FLOW_LOGE__("initCh error !"));
        IF_ERR_RETURN(RGN::getInstance()->attach(mSubRGNChnId, SCL_DEV_ID, mSclChnId, SCL_SUB_STRM_OUTPORT, OSDPosX, OSDPosY) == false, 
            false, __MEDIA_FLOW_LOGE__("attach error !"));
    #endif
    }

    /**
     * @brief 
     * 显示流
     */
    if (mCfg->displaySwitch == true ||
        mCfg->AHDSwitch == true
    ) {
        if (mWinRect.w > 0 &&
            mWinRect.h > 0
        ) {
            IF_ERR_RETURN(SclMnger::getInstance()->uninitOutport(SCL_DEV_ID, mSclChnId, SCL_DISP_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("uninitOutport error !"));
            IF_ERR_RETURN(SclMnger::getInstance()->initOutport(SCL_DEV_ID, mSclChnId, SCL_DISP_OUTPORT, mReslW, mReslH, 
                mWinRect.w, mWinRect.h) == false, false, "initOutport error !");

            if (mCfg->ch[mChn].dispFlip == true) {
                IF_ERR_RETURN(SclMnger::getInstance()->setOutportFlip(SCL_DEV_ID, mSclChnId, SCL_DISP_OUTPORT, true) == false, 
                    false, __MEDIA_FLOW_LOGE__("setOutportFlip error !"));
            }

            if (mCfg->ch[mChn].dispMirror == true) {
                IF_ERR_RETURN(SclMnger::getInstance()->setOutportMirror(SCL_DEV_ID, mSclChnId, SCL_DISP_OUTPORT, true) == false, 
                    false, __MEDIA_FLOW_LOGE__("setOutportMirror error !"));
            }

            IF_ERR_RETURN(SclMnger::getInstance()->enableOutport(SCL_DEV_ID, mSclChnId, SCL_DISP_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("enableOutport error !"));

            /**
             * @brief 
             * OSD FOR TEST
             */
            if (mWinRect.w > RGN_MAX_W) {
                OSDPosX = (mWinRect.w - RGN_MAX_W) / 2;
            } else {
                OSDPosX = 0;
            }

            if (mWinRect.h > RGN_MAX_H) {
                OSDPosY = (mWinRect.h - RGN_MAX_H) / 2;
            } else {
                OSDPosY = 0;
            }

        #ifdef __DISP_RGN_ENABLE__ //调试测试用
            IF_ERR_RETURN(RGN::getInstance()->initCh(mDispRGNChnId, MIN(mWinRect.w, RGN_MAX_W), MIN(mWinRect.h, RGN_MAX_H)) == false, false, __MEDIA_FLOW_LOGE__("initCh error !"));
            IF_ERR_RETURN(RGN::getInstance()->attach(mDispRGNChnId, SCL_DEV_ID, mSclChnId, SCL_DISP_OUTPORT, OSDPosX, OSDPosY) == false, false, __MEDIA_FLOW_LOGE__("attach error !"));
        #endif
        }
    }

    /**
     * @brief 
     * 算法流
     */
    if (mCfg->ch[mChn].ai.enable == true &&
        mCfg->ch[mChn].ai.fps > 0 &&
        mCfg->ch[mChn].ai.width > 0 &&
        mCfg->ch[mChn].ai.height > 0
    ) {
        mCfg->ch[mChn].ai.width = MIN(mCfg->ch[mChn].ai.width, mReslW);
        mCfg->ch[mChn].ai.height = MIN(mCfg->ch[mChn].ai.height, mReslH);

        IF_ERR_RETURN(SclMnger::getInstance()->uninitOutport(SCL_DEV_ID, mSclChnId, SCL_AI_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("uninitOutport error !"));
        IF_ERR_RETURN(SclMnger::getInstance()->initOutport(SCL_DEV_ID, mSclChnId, SCL_AI_OUTPORT, 
            mReslW, mReslH, mCfg->ch[mChn].ai.width, mCfg->ch[mChn].ai.height) == false, false, "initOutport error !");
        IF_ERR_RETURN(SclMnger::getInstance()->setDepth(SCL_DEV_ID, mSclChnId, SCL_AI_OUTPORT, 6) == false, false, __MEDIA_FLOW_LOGE__("setDepth error !"));

        if (mCfg->ch[mChn].ai.flip == true) {
            IF_ERR_RETURN(SclMnger::getInstance()->setOutportFlip(SCL_DEV_ID, mSclChnId, SCL_AI_OUTPORT, true) == false, 
                false, __MEDIA_FLOW_LOGE__("setOutportFlip error !"));
        }

        if (mCfg->ch[mChn].ai.mirror == true) {
            IF_ERR_RETURN(SclMnger::getInstance()->setOutportMirror(SCL_DEV_ID, mSclChnId, SCL_AI_OUTPORT, true) == false, 
                false, __MEDIA_FLOW_LOGE__("setOutportMirror error !"));
        }

        if ((720 == mCfg->ch[mChn].ai.height) &&
            (setAiCrop(mCfg->ch[mChn].ai.width, mCfg->ch[mChn].ai.height) == false)) {
            __MEDIA_FLOW_LOGE__("setAiCrop error !");
            return false;
        }

        IF_ERR_RETURN(SclMnger::getInstance()->enableOutport(SCL_DEV_ID, mSclChnId, SCL_AI_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("enableOutport error !"));
    }

    /**
     * @brief 
     * 抓拍流
     * 从SCL_SNAP_OUTPORT读取原图，OSD软处理，再送入到VENC JPG编码
     */
    IF_ERR_RETURN(SclMnger::getInstance()->uninitOutport(SCL_DEV_ID, mSclChnId, SCL_SNAP_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("uninitOutport error !"));
    IF_ERR_RETURN(SclMnger::getInstance()->initOutport(SCL_DEV_ID, mSclChnId, SCL_SNAP_OUTPORT, mReslW, mReslH, 704,  480) == false, false, "initOutport error !");
    IF_ERR_RETURN(SclMnger::getInstance()->setDepth(SCL_DEV_ID, mSclChnId, SCL_SNAP_OUTPORT, 6) == false, false, __MEDIA_FLOW_LOGE__("setDepth error !"));

    if (mCfg->ch[mChn].snapFlip == true) {
        IF_ERR_RETURN(SclMnger::getInstance()->setOutportFlip(SCL_DEV_ID, mSclChnId, SCL_SNAP_OUTPORT, true) == false, 
            false, __MEDIA_FLOW_LOGE__("setOutportFlip error !"));
    }

    if (mCfg->ch[mChn].snapMirror == true) {
        IF_ERR_RETURN(SclMnger::getInstance()->setOutportMirror(SCL_DEV_ID, mSclChnId, SCL_SNAP_OUTPORT, true) == false, 
            false, __MEDIA_FLOW_LOGE__("setOutportMirror error !"));
    }

    /**
     * @brief 
     * SCL绑定VIF
     * 按照config ini中主子码流中最大的fps来设置
     * vif输入fps也采用ini中配置的fps来设置，降低vif
     * 采样频率，节省性能
     * 
     * 注意：
     * 方案一:
     * 为了节约性能，VIF->SCL环节进行了降帧mResFps降低到mSclOutFps
     * SCL后续的环节全部采用mSclOutFps帧率作为输入帧率
     * SCL的inport全部都是mSclOutFps帧率输入，导致SCL后的环节最大也只能是mSclOutFps帧
     * mResFps->mResFps
     * 
     * 方案二:
     * 也可以VIF->SCL环节满帧率，与VIF帧率一致
     * SCL的outport输出的帧率可以通过SCL的outport下级设备进行bind时候设置输出帧率
     * 但是对于算法用到的outport没有下级设备，导致算法outport一直满帧率工作
     * mResFps->mSclOutFps
     * 
     * 综上所述，采用方案一
     */

#ifdef __USE_ISP__
    IF_ERR_RETURN(SclMnger::getInstance()->bind(SCL_DEV_ID, mSclChnId, 
        E_MI_MODULE_ID_ISP, E_MI_SYS_BIND_TYPE_REALTIME, ISP_DEV_ID, mIspChnId, E_ISP_OUTPORT_ID_0, mResFps, mSclOutFps) == false, 
            false, __MEDIA_FLOW_LOGE__("bind error !"));
#else
    IF_ERR_RETURN(SclMnger::getInstance()->bind(SCL_DEV_ID, mSclChnId, 
        E_MI_MODULE_ID_VIF, E_MI_SYS_BIND_TYPE_FRAME_BASE, mVifDevId, 0, 0, mResFps, mSclOutFps) == false, false, __MEDIA_FLOW_LOGE__("bind error !"));
#endif

    /**
     * @brief 
     * SCL,VIF使能
     */
    IF_ERR_RETURN(SclMnger::getInstance()->enableCh(SCL_DEV_ID, mSclChnId) == false, false, __MEDIA_FLOW_LOGE__("enableCh error !"));

    return true;
}

bool MediaFlow::sclInit()
{
    if (mIsValid == true) {
        return sclInit_();
    } else {

    }

    return true;
}

//--------------------------------------------------------------

bool MediaFlow::dispInit()
{
    __CHECKVALID__();

    if (mCfg->displaySwitch == true) {
        __MEDIA_FLOW_LOGD__("RECT[%u, %u, %u, %u]", mWinRect.x, mWinRect.y, mWinRect.w, mWinRect.h);

        if (mWinRect.w > 0 &&
            mWinRect.h > 0
        ) {
            IF_ERR_RETURN(DispMnger::getInstance()->initInPort(E_DISP_LAYER_ID_0, mDispInPortId) == false, false, __MEDIA_FLOW_LOGE__("initInPort error !"));
            IF_ERR_RETURN(DispMnger::getInstance()->setInPortWin(E_DISP_LAYER_ID_0, mDispInPortId, 
                mWinRect.x, mWinRect.y, mWinRect.w, mWinRect.h) == false, false, "initOutport error !");
            IF_ERR_RETURN(DispMnger::getInstance()->inPortEnable(E_DISP_LAYER_ID_0, mDispInPortId) == false, false, __MEDIA_FLOW_LOGE__("inPortEnable error !"));

            IF_ERR_RETURN(DispMnger::getInstance()->inPortBind(
                E_DISP_LAYER_ID_0, mDispInPortId, 
                E_MI_MODULE_ID_SCL, SCL_DEV_ID, mSclChnId, 
                SCL_DISP_OUTPORT, E_DISP_DEV_ID_0, mSclOutFps, mDispFps) == false, false, __MEDIA_FLOW_LOGE__("inPortBind error !"));
        }
    }

    if (mCfg->AHDSwitch == true) {
        if (mWinRect.w > 0 &&
            mWinRect.h > 0
        ) {

            IF_ERR_RETURN(DispMnger::getInstance()->initInPort(E_DISP_LAYER_ID_2, mDispInPortId) == false, false, __MEDIA_FLOW_LOGE__("initInPort error !"));
            IF_ERR_RETURN(DispMnger::getInstance()->setInPortWin(E_DISP_LAYER_ID_2, mDispInPortId, 
                mWinRect.x, (1080 - mWinRect.h) / 2, mWinRect.w, mWinRect.h) == false, false, "initOutport error !");
            IF_ERR_RETURN(DispMnger::getInstance()->inPortEnable(E_DISP_LAYER_ID_2, mDispInPortId) == false, false, __MEDIA_FLOW_LOGE__("inPortEnable error !"));

            IF_ERR_RETURN(DispMnger::getInstance()->inPortBind(
                E_DISP_LAYER_ID_2, mDispInPortId, 
                E_MI_MODULE_ID_SCL, SCL_DEV_ID, mSclChnId, 
                SCL_DISP_OUTPORT, E_DISP_DEV_ID_1, mSclOutFps, mSclOutFps) == false, false, __MEDIA_FLOW_LOGE__("inPortBind error !"));
        }
    }

    return true;
}

//--------------------------------------------------------------

bool MediaFlow::vencH26xInit_(uint8_t strmIdx)
{
    vencH26xMode_t vencH26xMode = E_VENC_H26X_MODE_UNKOWN;
    vencProfile_t vencProfile = E_VENC_PROFILE_BASELINE;

    if (! (0 <= strmIdx && strmIdx < 2)) {
        return false;
    }

    if (mCfg->ch[mChn].video[strmIdx].codec == 0) {
        if (mCfg->ch[mChn].video[strmIdx].rcMode == 0) {
            vencH26xMode = E_VENC_H26X_MODE_H264_CBR;
        } else {
            vencH26xMode = E_VENC_H26X_MODE_H264_VBR;
        }
    } else {
        if (mCfg->ch[mChn].video[strmIdx].rcMode == 0) {
            vencH26xMode = E_VENC_H26X_MODE_H265_CBR;
        } else {
            vencH26xMode = E_VENC_H26X_MODE_H265_VBR;
        }
    }

    if (mCfg->ch[mChn].video[strmIdx].profile == 0 ||
        mCfg->ch[mChn].video[strmIdx].profile == 1 ||
        mCfg->ch[mChn].video[strmIdx].profile == 2
    ) {
        vencProfile = (vencProfile_t) mCfg->ch[mChn].video[strmIdx].profile;
    }

    alignVencResl(mCfg->ch[mChn].video[strmIdx].width, mCfg->ch[mChn].video[strmIdx].height);
    IF_ERR_RETURN(VencH26xChMnger::getInstance()->init(mVenH26xChnId[strmIdx], vencH26xMode, vencProfile, 
        mCfg->ch[mChn].video[strmIdx].width, mCfg->ch[mChn].video[strmIdx].height, 
        MIN(mCfg->ch[mChn].video[strmIdx].fps, mSclOutFps), mCfg->ch[mChn].video[strmIdx].bps) == false, 
        false, __MEDIA_FLOW_LOGE__("init error !"));

    IF_ERR_RETURN(VencH26xChMnger::getInstance()->bind(
        mVenH26xChnId[strmIdx], E_MI_MODULE_ID_SCL, SCL_DEV_ID, mSclChnId, 
        strmIdx == 0 ? SCL_MAIN_STRM_OUTPORT : SCL_SUB_STRM_OUTPORT, mSclOutFps, 
        MIN(mCfg->ch[mChn].video[strmIdx].fps, mSclOutFps)) == false, false, __MEDIA_FLOW_LOGE__("bind error !"));

    if ((
            mCfg->ch[mChn].ai.enable == true &&
            mCfg->ch[mChn].ai.attachment == strmIdx
        ) || (
            (
                mCfg->ch[mChn].save == 2 ||
                mCfg->ch[mChn].save == 3
            ) && 
            mCfg->ch[mChn].recordInput == strmIdx
        )
    ) {
        IF_ERR_RETURN(VencH26xChMnger::getInstance()->enable(mVenH26xChnId[strmIdx]) == false, false, __MEDIA_FLOW_LOGE__("enable error !"));
        mIsVencEnabled[strmIdx] = true;
    }

    return true;
}

bool MediaFlow::vencH26xUnInit_(uint8_t strmIdx)
{
    if (! (0 <= strmIdx && strmIdx < 2)) {
        return false;
    }

    VencH26xChMnger::getInstance()->uninit(mVenH26xChnId[strmIdx]);
    mIsVencEnabled[strmIdx] = false;

    return true;
}

//--------------------------------------------------------------

bool MediaFlow::vencH26xMainInit()
{
    __CHECKVALID__();

    return vencH26xInit_(0);
}

bool MediaFlow::vencH26xMainUnInit()
{
    return vencH26xUnInit_(0);
}

//--------------------------------------------------------------

bool MediaFlow::vencH26xSubInit()
{
    __CHECKVALID__();

    return vencH26xInit_(1);
}

bool MediaFlow::vencH26xSubUnInit()
{
    return vencH26xUnInit_(1);
}

//--------------------------------------------------------------

bool MediaFlow::vencJpegUnInit()
{
    VencJpegChMnger::getInstance()->uninit(mVencJpegChnId);

    return true;
}

//-------------------------------------------------------------------

bool MediaFlow::enableMainStrm()
{
    __CHECKVALID__();

    uint16_t OSDPosX = 0, OSDPosY = 0;

    if (mIsVencEnabled[0] == true) {
        return true;
    }

    if (mCfg->ch[mChn].video[0].width > RGN_MAX_W) {
        OSDPosX = (mCfg->ch[mChn].video[0].width - RGN_MAX_W) / 2;
    } else {
        OSDPosX = 0;
    }

    if (mCfg->ch[mChn].video[0].height > RGN_MAX_H) {
        OSDPosY = (mCfg->ch[mChn].video[0].height - RGN_MAX_H) / 2;
    } else {
        OSDPosY = 0;
    }

    std::lock_guard<std::mutex> lock(mMutex);

    RGN::getInstance()->uninitCh(mMainRGNChnId);

    IF_ERR_RETURN(SclMnger::getInstance()->enableOutport(SCL_DEV_ID, mSclChnId, SCL_MAIN_STRM_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("enableOutport error !"));

#ifdef __MAIN_STRM_RGN_ENABLE__
    IF_ERR_RETURN(RGN::getInstance()->initCh(mMainRGNChnId, MIN(mCfg->ch[mChn].video[0].width, RGN_MAX_W), MIN(mCfg->ch[mChn].video[0].height, RGN_MAX_H)) == false, 
        false, __MEDIA_FLOW_LOGE__("initCh error !"));
    IF_ERR_RETURN(RGN::getInstance()->attach(mMainRGNChnId, SCL_DEV_ID, mSclChnId, SCL_MAIN_STRM_OUTPORT, OSDPosX, OSDPosY) == false, 
        false, __MEDIA_FLOW_LOGE__("attach error !"));
#endif

    IF_ERR_RETURN(VencH26xChMnger::getInstance()->enable(mVenH26xChnId[0]) == false, false, __MEDIA_FLOW_LOGE__("enable error !"));

    mIsVencEnabled[0] = true;

    return true;
}

bool MediaFlow::disableMainStrm()
{
    __CHECKVALID__();

    if ((
            mCfg->ch[mChn].ai.enable == true &&
            mCfg->ch[mChn].ai.attachment == 0
        ) || (
            (
                mCfg->ch[mChn].save == 2 ||
                mCfg->ch[mChn].save == 3
            ) && 
            mCfg->ch[mChn].recordInput == 0
        )
    ) {
        return true;
    }

    std::lock_guard<std::mutex> lock(mMutex);

    IF_ERR_RETURN(VencH26xChMnger::getInstance()->disable(mVenH26xChnId[0]) == false, false, __MEDIA_FLOW_LOGE__("enable error !"));

    RGN::getInstance()->uninitCh(mMainRGNChnId);
    IF_ERR_RETURN(SclMnger::getInstance()->disableOutport(SCL_DEV_ID, mSclChnId, SCL_MAIN_STRM_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("disableOutport error !"));

    mIsVencEnabled[0] = false;

    return true;
}

//--------------------------------------------------------------

bool MediaFlow::enableSubStrm()
{
    __CHECKVALID__();

    uint16_t OSDPosX = 0, OSDPosY = 0;

    if (mIsVencEnabled[1] == true) {
        return true;
    }

    if (mCfg->ch[mChn].video[1].width > RGN_MAX_W) {
        OSDPosX = (mCfg->ch[mChn].video[1].width - RGN_MAX_W) / 2;
    } else {
        OSDPosX = 0;
    }

    if (mCfg->ch[mChn].video[1].height > RGN_MAX_H) {
        OSDPosY = (mCfg->ch[mChn].video[1].height - RGN_MAX_H) / 2;
    } else {
        OSDPosY = 0;
    }

    std::lock_guard<std::mutex> lock(mMutex);

    RGN::getInstance()->uninitCh(mSubRGNChnId);

    IF_ERR_RETURN(SclMnger::getInstance()->enableOutport(SCL_DEV_ID, mSclChnId, SCL_SUB_STRM_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("enableOutport error !"));

#ifdef __SUB_STRM_RGN_ENABLE__
    IF_ERR_RETURN(RGN::getInstance()->initCh(mSubRGNChnId, MIN(mCfg->ch[mChn].video[1].width, RGN_MAX_W), MIN(mCfg->ch[mChn].video[1].height, RGN_MAX_H)) == false, 
        false, __MEDIA_FLOW_LOGE__("initCh error !"));
    IF_ERR_RETURN(RGN::getInstance()->attach(mSubRGNChnId, SCL_DEV_ID, mSclChnId, SCL_SUB_STRM_OUTPORT, OSDPosX, OSDPosY) == false, 
        false, __MEDIA_FLOW_LOGE__("attach error !"));
#endif

    IF_ERR_RETURN(VencH26xChMnger::getInstance()->enable(mVenH26xChnId[1]) == false, false, __MEDIA_FLOW_LOGE__("enable error !"));

    mIsVencEnabled[1] = true;

    return true;
}

bool MediaFlow::disableSubStrm()
{
    __CHECKVALID__();

    if ((
            mCfg->ch[mChn].ai.enable == true &&
            mCfg->ch[mChn].ai.attachment == 1
        ) || (
            (
                mCfg->ch[mChn].save == 2 ||
                mCfg->ch[mChn].save == 3
            ) && 
            mCfg->ch[mChn].recordInput == 1
        )
    ) {
        return true;
    }

    std::lock_guard<std::mutex> lock(mMutex);

    IF_ERR_RETURN(VencH26xChMnger::getInstance()->disable(mVenH26xChnId[1]) == false, false, __MEDIA_FLOW_LOGE__("enable error !"));

    RGN::getInstance()->uninitCh(mSubRGNChnId);
    IF_ERR_RETURN(SclMnger::getInstance()->disableOutport(SCL_DEV_ID, mSclChnId, SCL_SUB_STRM_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("disableOutport error !"));

    mIsVencEnabled[1] = false;

    return true;
}

//--------------------------------------------------------------

bool MediaFlow::enableSnap()
{
    __CHECKVALID__();

    std::lock_guard<std::mutex> lock(mMutex);

    IF_ERR_RETURN(SclMnger::getInstance()->enableOutport(SCL_DEV_ID, mSclChnId, SCL_SNAP_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("enableOutport error !"));

    return true;
}

bool MediaFlow::disableSnap()
{
    __CHECKVALID__();

    std::lock_guard<std::mutex> lock(mMutex);

    IF_ERR_RETURN(SclMnger::getInstance()->disableOutport(SCL_DEV_ID, mSclChnId, SCL_SNAP_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("disableOutport error !"));

    return true;
}

//--------------------------------------------------------------

bool MediaFlow::setMainStrmResl(uint16_t w, uint16_t h)
{
    __CHECKVALID__();

    uint16_t OSDPosX = 0, OSDPosY = 0;

    if (w > RGN_MAX_W) {
        OSDPosX = (w - RGN_MAX_W) / 2;
    } else {
        OSDPosX = 0;
    }

    if (h > RGN_MAX_H) {
        OSDPosY = (h - RGN_MAX_H) / 2;
    } else {
        OSDPosY = 0;
    }

    std::lock_guard<std::mutex> lock(mMutex);

    RGN::getInstance()->uninitCh(mMainRGNChnId);

    alignVencResl(w, h);
    IF_ERR_RETURN(SclMnger::getInstance()->setOutportSize(SCL_DEV_ID, mSclChnId, SCL_MAIN_STRM_OUTPORT, w, h) == false, false, __MEDIA_FLOW_LOGE__("uninitOutport error !"));
    IF_ERR_RETURN(VencH26xChMnger::getInstance()->setResl(mVenH26xChnId[0], w, h) == false, false, "setResl error !");

#ifdef __MAIN_STRM_RGN_ENABLE__
    IF_ERR_RETURN(RGN::getInstance()->initCh(mMainRGNChnId, MIN(w, RGN_MAX_W), MIN(h, RGN_MAX_H)) == false, false, __MEDIA_FLOW_LOGE__("initCh error !"));
    IF_ERR_RETURN(RGN::getInstance()->attach(mMainRGNChnId, SCL_DEV_ID, mSclChnId, SCL_MAIN_STRM_OUTPORT, OSDPosX, OSDPosY) == false, 
        false, __MEDIA_FLOW_LOGE__("attach error !"));
#endif

    return true;
}

bool MediaFlow::setMainStrmFps(uint8_t fps)
{
    __CHECKVALID__();

    std::lock_guard<std::mutex> lock(mMutex);

    IF_ERR_RETURN(VencH26xChMnger::getInstance()->setFps(mVenH26xChnId[0], fps) == false, false, __MEDIA_FLOW_LOGE__("setFps error !"));

    return true;
}

bool MediaFlow::setMainStrmBps(uint32_t bps)
{
    __CHECKVALID__();

    std::lock_guard<std::mutex> lock(mMutex);

    IF_ERR_RETURN(VencH26xChMnger::getInstance()->setBps(mVenH26xChnId[0], bps) == false, false, __MEDIA_FLOW_LOGE__("setBps error !"));

    return true;
}

//--------------------------------------------------------------

bool MediaFlow::setSubStrmResl(uint16_t w, uint16_t h)
{
    __CHECKVALID__();

    uint16_t OSDPosX = 0, OSDPosY = 0;

    if (w > RGN_MAX_W) {
        OSDPosX = (w - RGN_MAX_W) / 2;
    } else {
        OSDPosX = 0;
    }

    if (h > RGN_MAX_H) {
        OSDPosY = (h - RGN_MAX_H) / 2;
    } else {
        OSDPosY = 0;
    }

    std::lock_guard<std::mutex> lock(mMutex);

    RGN::getInstance()->uninitCh(mSubRGNChnId);

    alignVencResl(w, h);
    IF_ERR_RETURN(SclMnger::getInstance()->setOutportSize(SCL_DEV_ID, mSclChnId, SCL_SUB_STRM_OUTPORT, w, h) == false, false, __MEDIA_FLOW_LOGE__("uninitOutport error !"));
    IF_ERR_RETURN(VencH26xChMnger::getInstance()->setResl(mVenH26xChnId[1], w, h) == false, false, "setResl error !");

#ifdef __SUB_STRM_RGN_ENABLE__
    IF_ERR_RETURN(RGN::getInstance()->initCh(mSubRGNChnId, MIN(w, RGN_MAX_W), MIN(h, RGN_MAX_H)) == false, false, __MEDIA_FLOW_LOGE__("initCh error !"));
    IF_ERR_RETURN(RGN::getInstance()->attach(mSubRGNChnId, SCL_DEV_ID, mSclChnId, SCL_SUB_STRM_OUTPORT, OSDPosX, OSDPosY) == false, 
        false, __MEDIA_FLOW_LOGE__("attach error !"));
#endif

    return true;
}

bool MediaFlow::setSubStrmFps(uint8_t fps)
{
    __CHECKVALID__();

    std::lock_guard<std::mutex> lock(mMutex);

    IF_ERR_RETURN(VencH26xChMnger::getInstance()->setFps(mVenH26xChnId[1], fps) == false, false, __MEDIA_FLOW_LOGE__("setFps error !"));

    return true;
}

bool MediaFlow::setSubStrmBps(uint32_t bps)
{
    __CHECKVALID__();

    std::lock_guard<std::mutex> lock(mMutex);

    IF_ERR_RETURN(VencH26xChMnger::getInstance()->setBps(mVenH26xChnId[1], bps) == false, false, __MEDIA_FLOW_LOGE__("setBps error !"));

    return true;
}

//--------------------------------------------------------------

bool MediaFlow::disableDispStrm()
{
    __CHECKVALID__();

    if (mCfg->displaySwitch == true) {
        if (mCfg->AHDSwitch == false) {
            std::lock_guard<std::mutex> lock(mMutex);

            RGN::getInstance()->uninitCh(mDispRGNChnId);

            IF_ERR_RETURN(SclMnger::getInstance()->disableOutport(SCL_DEV_ID, mSclChnId, SCL_DISP_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("disableOutport error !"));
        }
    }

    return true;
}

bool MediaFlow::enableDispStrm()
{
    __CHECKVALID__();

    if (mCfg->displaySwitch == true) {
        if (mWinRect.w > 0 &&
            mWinRect.h > 0
        ) {
            std::lock_guard<std::mutex> lock(mMutex);

            IF_ERR_RETURN(SclMnger::getInstance()->enableOutport(SCL_DEV_ID, mSclChnId, SCL_DISP_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("enableOutport error !"));
        }
    }

    return true;
}

//--------------------------------------------------------------

bool MediaFlow::fullsc(uint16_t w, uint16_t h)
{
    __CHECKVALID__();

    uint16_t OSDPosX = 0, OSDPosY = 0;

    if (mCfg->displaySwitch == true) {
        if ((0 < w && w <= mReslW) &&
            (0 < h && h <= mReslH)
        ) {

            if (w > RGN_MAX_W) {
                OSDPosX = (w - RGN_MAX_W) / 2;
            } else {
                OSDPosX = 0;
            }

            if (h > RGN_MAX_H) {
                OSDPosY = (h - RGN_MAX_H) / 2;
            } else {
                OSDPosY = 0;
            }

            std::lock_guard<std::mutex> lock(mMutex);
            RGN::getInstance()->uninitCh(mDispRGNChnId);

            /**
             * @brief 
             * 未配置显示窗口的通道，切换全屏时，直接创建显示流
             * 未配置显示窗口的通道，开机初始化不会创建显示流
             */
            if (mWinRect.w <= 0 ||
                mWinRect.h <= 0
            ) {
                IF_ERR_RETURN(SclMnger::getInstance()->uninitOutport(SCL_DEV_ID, mSclChnId, SCL_DISP_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("uninitOutport error !"));
                IF_ERR_RETURN(SclMnger::getInstance()->initOutport(SCL_DEV_ID, mSclChnId, SCL_DISP_OUTPORT, mReslW, mReslH, w, h) == false, false, "initOutport error !");
                IF_ERR_RETURN(SclMnger::getInstance()->enableOutport(SCL_DEV_ID, mSclChnId, SCL_DISP_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("enableOutport error !"));
            } else {
                IF_ERR_RETURN(SclMnger::getInstance()->setOutportSize(SCL_DEV_ID, mSclChnId, SCL_DISP_OUTPORT, w, h) == false, false, __MEDIA_FLOW_LOGE__("setOutportSize error !"));
            }

        #ifdef __DISP_RGN_ENABLE__
            IF_ERR_RETURN(RGN::getInstance()->initCh(mDispRGNChnId, MIN(w, RGN_MAX_W), MIN(h, RGN_MAX_H)) == false, false, __MEDIA_FLOW_LOGE__("initCh error !"));
            IF_ERR_RETURN(RGN::getInstance()->attach(mDispRGNChnId, SCL_DEV_ID, mSclChnId, SCL_DISP_OUTPORT, OSDPosX, OSDPosY) == false, 
                false, __MEDIA_FLOW_LOGE__("attach error !"));
        #endif
        }
    }

    return true;
}

bool MediaFlow::smallsc()
{
    __CHECKVALID__();

    uint16_t OSDPosX = 0, OSDPosY = 0;

    if (mCfg->displaySwitch == true) {

        std::lock_guard<std::mutex> lock(mMutex);
        RGN::getInstance()->uninitCh(mDispRGNChnId);

        if (mWinRect.w > 0 &&
            mWinRect.h > 0
        ) {
            if (mWinRect.w > RGN_MAX_W) {
                OSDPosX = (mWinRect.w - RGN_MAX_W) / 2;
            } else {
                OSDPosX = 0;
            }

            if (mWinRect.h > RGN_MAX_H) {
                OSDPosY = (mWinRect.h - RGN_MAX_H) / 2;
            } else {
                OSDPosY = 0;
            }

            IF_ERR_RETURN(SclMnger::getInstance()->setOutportSize(SCL_DEV_ID, mSclChnId, SCL_DISP_OUTPORT, mWinRect.w, mWinRect.h) == false, 
                false, __MEDIA_FLOW_LOGE__("uninitOutport error !"));

        #ifdef __DISP_RGN_ENABLE__
            IF_ERR_RETURN(RGN::getInstance()->initCh(mDispRGNChnId, MIN(mWinRect.w, RGN_MAX_W), MIN(mWinRect.h, RGN_MAX_H)) == false, false, __MEDIA_FLOW_LOGE__("initCh error !"));
            IF_ERR_RETURN(RGN::getInstance()->attach(mDispRGNChnId, SCL_DEV_ID, mSclChnId, SCL_DISP_OUTPORT, OSDPosX, OSDPosY) == false, 
                false, __MEDIA_FLOW_LOGE__("attach error !"));
        #endif

        } else {

            /**
             * @brief 
             * 未配置显示窗口的通道，切换回小屏时，直接回收显示的scl outport
             */
            IF_ERR_RETURN(SclMnger::getInstance()->uninitOutport(SCL_DEV_ID, mSclChnId, SCL_DISP_OUTPORT) == false, false, __MEDIA_FLOW_LOGE__("uninitOutport error !"));
        }
    }

    return true;
}

//--------------------------------------------------------------

bool MediaFlow::setSnapResl(uint16_t w, uint16_t h)
{
    __CHECKVALID__();

    std::lock_guard<std::mutex> lock(mMutex);

    IF_ERR_RETURN(SclMnger::getInstance()->setOutportSize(SCL_DEV_ID, mSclChnId, SCL_SNAP_OUTPORT, w, h) == false, false, __MEDIA_FLOW_LOGE__("setOutportSize error !"));

    return true;
}

//--------------------------------------------------------------

bool MediaFlow::setAiCrop(uint16_t w, uint16_t h)
{
    __CHECKVALID__();

    uint16_t cropX = 0;
    uint16_t cropY = 0;

    if (mCfg->ch[mChn].ai.enable == false
    ) {
        __MEDIA_FLOW_LOGW__("'ai.enable' of config.ini not enable !");
        return true;
    }

    if (mCfg->ch[mChn].ai.fps <= 0) {
        __MEDIA_FLOW_LOGW__("'ai.fps' of config.ini not set !");
        return true;
    }

    if (mReslW > w) {
        cropX = (mReslW - w) / 2;
    } else {
        w = mReslW;
    }

    if (mReslH > h) {
        cropY = (mReslH - h) / 2;
    } else {
        h = mReslH;
    }

    std::lock_guard<std::mutex> lock(mMutex);

    IF_ERR_RETURN(SclMnger::getInstance()->setOutportCrop(SCL_DEV_ID, mSclChnId, SCL_AI_OUTPORT, cropX, cropY, w, h) == false, false, __MEDIA_FLOW_LOGE__("setOutportCrop error !"));

    return true;
}

//--------------------------------------------------------------

uint8_t MediaFlow::getResFps()
{
    return mSclOutFps;
}

uint8_t MediaFlow::getDispFps()
{
    return mDispFps;
}

uint16_t MediaFlow::getReslW()
{
    return mReslW;
}

uint16_t MediaFlow::getReslH()
{
    return mReslH;
}

vifDevId_t MediaFlow::getVifDevId()
{
    return mVifDevId;
}

bool MediaFlow::isValid()
{
    return mIsValid;
}

miniui::rect_t MediaFlow::getWinRect()
{
    return mWinRect;
}

std::string MediaFlow::getPhyName()
{
    return mCfg->ch[mChn].camPhy;
}

bool MediaFlow::cntstatus()
{
    return VifMnger::getInstance()->cntstatus(mVifDevId);
}

uint16_t MediaFlow::getAnadecReslW()
{
    return VifMnger::getInstance()->getAnadecReslW(mVifDevId);
}

uint16_t MediaFlow::getAnadecReslH()
{
    return VifMnger::getInstance()->getAnadecReslH(mVifDevId);
}

uint8_t MediaFlow::getAnadecFps()
{
    return VifMnger::getInstance()->getAnadecFps(mVifDevId);
}

bool MediaFlow::isReslChanged()
{
    uint16_t w = VifMnger::getInstance()->getAnadecReslW(mVifDevId);
    uint16_t h = VifMnger::getInstance()->getAnadecReslH(mVifDevId);
    uint8_t fps = VifMnger::getInstance()->getAnadecFps(mVifDevId);

    if ((
            w > 0 &&
            h > 0 &&
            fps > 0
        ) && (
            mReslW != w ||
            mReslH != h ||
            mResFps != fps
        )
    ) {
        __MEDIA_FLOW_LOGE__("LAST RESL[%ux%u] FPS[%u] => ANADEC RESL[%ux%u] FPS[%u] !", mReslW, mReslH, mResFps, w, h, fps);

        return true;
    }

    return false;
}

//--------------------------------------------------------------

bool MediaFlow::OSDDrawStart_(uint16_t RGNChnId)
{
    return RGN::getInstance()->OSDStart(RGNChnId);
}

bool MediaFlow::OSDDrawText_(uint16_t RGNChnId, uint16_t x, uint16_t y, RGN::color_t color, const char *text, YUVFontSize_t ftSize)
{
    return RGN::getInstance()->OSDDrawText(RGNChnId, x, y, color, text, ftSize);
}

bool MediaFlow::OSDDrawEnd_(uint16_t RGNChnId)
{
    return RGN::getInstance()->OSDEnd(RGNChnId);
}

//--------------------------------------------------------------

bool MediaFlow::OSDMainStrmDrawStart()
{
    __CHECKVALID__();

    return OSDDrawStart_(mMainRGNChnId);
}

uint16_t MediaFlow::OSDMainStrmGetReslW()
{
    __CHECKVALID__();

    return RGN::getInstance()->getReslW(mMainRGNChnId);
}

uint16_t MediaFlow::OSDMainStrmGetReslH()
{
    __CHECKVALID__();

    return RGN::getInstance()->getReslH(mMainRGNChnId);
}

bool MediaFlow::OSDMainStrmDrawText(uint16_t x, uint16_t y, RGN::color_t color, const char *text, YUVFontSize_t ftSize)
{
    __CHECKVALID__();

    return OSDDrawText_(mMainRGNChnId, x, y, color, text, ftSize);
}

bool MediaFlow::OSDMainStrmDrawEnd()
{
    __CHECKVALID__();

    return OSDDrawEnd_(mMainRGNChnId);
}

//--------------------------------------------------------------

bool MediaFlow::OSDSubStrmDrawStart()
{
    __CHECKVALID__();

    return OSDDrawStart_(mSubRGNChnId);
}

uint16_t MediaFlow::OSDSubStrmGetReslW()
{
    __CHECKVALID__();

    return RGN::getInstance()->getReslW(mSubRGNChnId);
}

uint16_t MediaFlow::OSDSubStrmGetReslH()
{
    __CHECKVALID__();

    return RGN::getInstance()->getReslH(mSubRGNChnId);
}

bool MediaFlow::OSDSubStrmDrawText(uint16_t x, uint16_t y, RGN::color_t color, const char *text, YUVFontSize_t ftSize)
{
    __CHECKVALID__();

    return OSDDrawText_(mSubRGNChnId, x, y, color, text, ftSize);
}

bool MediaFlow::OSDSubStrmDrawEnd()
{
    __CHECKVALID__();

    return OSDDrawEnd_(mSubRGNChnId);
}

//--------------------------------------------------------------

bool MediaFlow::OSDDispDrawStart()
{
    __CHECKVALID__();

    return OSDDrawStart_(mDispRGNChnId);
}

uint16_t MediaFlow::OSDDispGetReslW()
{
    __CHECKVALID__();

    return RGN::getInstance()->getReslW(mDispRGNChnId);
}

uint16_t MediaFlow::OSDDispGetReslH()
{
    __CHECKVALID__();

    return RGN::getInstance()->getReslH(mDispRGNChnId);
}

bool MediaFlow::OSDDispDrawText(uint16_t x, uint16_t y, RGN::color_t color, const char *text, YUVFontSize_t ftSize)
{
    __CHECKVALID__();

    return OSDDrawText_(mDispRGNChnId, x, y, color, text, ftSize);
}

bool MediaFlow::OSDDispDrawEnd()
{
    __CHECKVALID__();

    return OSDDrawEnd_(mDispRGNChnId);
}

//--------------------------------------------------------------

bool MediaFlow::yuv2jpeg_(const char *yuv, int32_t yuvlen, uint16_t w, uint16_t h, uint64_t pts, stream_t& stream)
{

    auto solve = [this](const char *yuv, int32_t yuvlen, uint16_t w, uint16_t h, uint64_t pts, stream_t& stream){
        /**
        * @brief 
        * 第一次注入YUV不会编码出JPG，而是要等到第二次的YUV数据到来，才会把第一次的JPG输出
        * 所以put两次，目的就是为了得到第一次的jpg图片
        * 目前还没有更好的方法，暂时使用这种不入流的方式
        */

        IF_ERR_RETURN(VencJpegChMnger::getInstance()->reset(mVencJpegChnId) == false, false, __MEDIA_FLOW_LOGE__("reset error !"));
        IF_ERR_RETURN(VencJpegChMnger::getInstance()->putstrm(mVencJpegChnId, yuv, yuvlen, w, h, pts) == false, false, __MEDIA_FLOW_LOGE__("putstrm error !"));
        IF_ERR_RETURN(VencJpegChMnger::getInstance()->putstrm(mVencJpegChnId, yuv, yuvlen, w, h, pts) == false, false, __MEDIA_FLOW_LOGE__("putstrm error !"));
        IF_ERR_RETURN(VencJpegChMnger::getInstance()->getstrm(mVencJpegChnId, stream, 60) == false, false, __MEDIA_FLOW_LOGE__("getstrm error !"));
        return true;
    };

    for (int i = 0; i < 3; i++) {
        /**
         * @brief
         * func会有失败的情况，尝试3次都失败那就放弃
         */
        if (solve(yuv, yuvlen, w, h, pts, stream)) {
            return true;
        }
    }

    return false;
}

bool MediaFlow::yuv2jpeg_(const char *yuv, int32_t yuvlen, uint16_t w, uint16_t h, uint8_t quality, uint64_t pts, stream_t& stream)
{
    bool ret = false;

    uint64_t ms = my::getClockMs();

    if (mIsVencJpegInited == false ||
        w != VencJpegChMnger::getInstance()->getReslW(mVencJpegChnId) ||
        h != VencJpegChMnger::getInstance()->getReslH(mVencJpegChnId)
    ) {
        VencJpegChMnger::getInstance()->uninit(mVencJpegChnId);
        IF_ERR_RETURN(VencJpegChMnger::getInstance()->init(mVencJpegChnId, w, h, 25, quality) == false, false, __MEDIA_FLOW_LOGE__("init error !"));
        IF_ERR_RETURN(VencJpegChMnger::getInstance()->enable(mVencJpegChnId) == false, false, __MEDIA_FLOW_LOGE__("enable error !"));
        mIsVencJpegInited = true;
    }

    for (uint8_t i = 0; i < 2; i ++) {
        __MEDIA_FLOW_LOGD__("====== num.%u yuv2jpeg_", i);
        ret = yuv2jpeg_(yuv, yuvlen, w, h, pts, stream);
        if (ret == true) {
            break;
        }
    }

    //统计花费的时间
    if (ret == true) {
        __MEDIA_FLOW_LOGD__("====== take %llu ms", my::getClockMs() - ms);
    }

    return ret;
}

bool MediaFlow::yuv2jpeg(const char *yuv, int32_t yuvlen, uint16_t w, uint16_t h, uint8_t quality, uint64_t pts, stream_t& stream, bool lock)
{
    __CHECKVALID__();

    if (lock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return yuv2jpeg_(yuv, yuvlen, w, h, quality, pts, stream);
    } else {
        return yuv2jpeg_(yuv, yuvlen, w, h, quality, pts, stream);
    }

    return true;
}

//--------------------------------------------------------------

bool MediaFlow::getstrm_(strmFrom_t strmFrom, gotstrmcb gotcb, void *user)
{
    bool mempa = false;
    double fps = 0;
    stream_t stream;

#define __GOTCB__() if (gotcb) gotcb(strmFrom, stream, user)

    {
        std::lock_guard<std::mutex> lock(mMutex);

        switch (strmFrom) {
            case E_STRM_FROM_MAIN_H26X: {
                if (VencH26xChMnger::getInstance()->getstrm(mVenH26xChnId[0], stream) == false) {
                    __MEDIA_FLOW_LOGE__("[MAIN_H26X] getstrm error !");
                    return false;
                }

                __GOTCB__();

                /**
                 * @brief 
                 * 流获取后，需要调用此接口，否则下次无法获取到流
                 */
                VencH26xChMnger::getInstance()->freestrm(mVenH26xChnId[0]);
                break;
            }
            case E_STRM_FROM_SUB_H26X: {
                if (VencH26xChMnger::getInstance()->getstrm(mVenH26xChnId[1], stream) == false) {
                    __MEDIA_FLOW_LOGE__("[SUB_H26X] getstrm error !");
                    return false;
                }

                __GOTCB__();

                /**
                 * @brief 
                 * 流获取后，需要调用此接口，否则下次无法获取到流
                 */
                VencH26xChMnger::getInstance()->freestrm(mVenH26xChnId[1]);
                break;
            }
            case E_STRM_FROM_AI: {

                /**
                 * @brief 
                 * 控制fps帧，通过mempa来获取物理内存送给算法
                 * 其他无效帧仅仅只是读取，不做物理内存拷贝
                 * 一定要读取，然后freestrm，不然scl会不工作
                 */
                mempa = false;
                if (mCfg->ch[mChn].ai.fps > 0) {

                    if (mGetAiStrmTm <= 0 ||
                        my::getClockSec() - mLastGetAiTm > 1
                    ) {
                        mGetAiStrmTm = my::getClockMs();
                        mGetAiStrmNum = 0;

                        mempa = true;
                    } else {
                        if (my::getClockMs() == mGetAiStrmTm) {
                            mempa = true;
                        } else {
                            fps = ((double) 1000 / ((double) (my::getClockMs() - mGetAiStrmTm) / (double) mGetAiStrmNum));
                            // __MEDIA_FLOW_LOGD__("cfgfps[%u] calfps[%lf]", mCfg->ch[mChn].ai.fps, fps);
                            if (fps <= 0.1) {
                                mGetAiStrmTm = 0;
                                mempa = true;
                            } else {
                                if (fps < mCfg->ch[mChn].ai.fps) {
                                    mempa = true;
                                }
                            }
                        }
                    }
                }

                if (SclMnger::getInstance()->getstrm(SCL_DEV_ID, mSclChnId, SCL_AI_OUTPORT, stream, mempa) == false) {
                    __MEDIA_FLOW_LOGE__("[AI] getstrm error !");
                    return false;
                }

                if (mempa) {
                    mGetAiStrmNum ++;
                    mLastGetAiTm = my::getClockSec();

                    stream.YUVStrm.frameNum = mAiFrameNum ++;

                    __GOTCB__();
                }

                /**
                 * @brief 
                 * 流获取后，需要调用此接口，否则下次无法获取到流
                 */
                SclMnger::getInstance()->freestrm(SCL_DEV_ID, mSclChnId, SCL_AI_OUTPORT);
                break;
            }
            case E_STRM_FROM_SNAP_YUV: {
                if (SclMnger::getInstance()->getstrm(SCL_DEV_ID, mSclChnId, SCL_SNAP_OUTPORT, stream) == false) {
                    __MEDIA_FLOW_LOGE__("[SNAP_YUV] getstrm error !");
                    return false;
                }

                __GOTCB__();

                /**
                 * @brief 
                 * 流获取后，需要调用此接口，否则下次无法获取到流
                 */
                SclMnger::getInstance()->freestrm(SCL_DEV_ID, mSclChnId, SCL_SNAP_OUTPORT);
                break;
            }
            case E_STRM_FROM_SNAP_JPEG: {
                if (VencJpegChMnger::getInstance()->getstrm(mVencJpegChnId, stream) == false) {
                    __MEDIA_FLOW_LOGE__("[SUB_H26X] getstrm error !");
                    return false;
                }

                __GOTCB__();

                /**
                 * @brief 
                 * 流获取后，需要调用此接口，否则下次无法获取到流
                 */
                VencJpegChMnger::getInstance()->freestrm(mVencJpegChnId);
                break;
            }
            default: {
                __MEDIA_FLOW_LOGE__("unkown strm from: %d !", strmFrom);
                return false;
            }
        }
    }

    return true;
#undef __GOTCB__
}

bool MediaFlow::getstrm(gotstrmcb gotcb, void *user, int32_t tmoutms)
{
    __CHECKVALID__();

    int32_t ret = -1;

    int32_t fd[E_STRM_FROM_MAX] = { -1 };
    bool status[E_STRM_FROM_MAX] = { false };

    for (uint8_t i = 0; i < E_STRM_FROM_MAX; i ++) {
        fd[i] = -1;
        status[i] = false;
    }

    {
        std::lock_guard<std::mutex> lock(mMutex);

        fd[E_STRM_FROM_SNAP_YUV] = SclMnger::getInstance()->getfd(SCL_DEV_ID, mSclChnId, SCL_SNAP_OUTPORT);

        /**
         * @brief 
         * jpeg使用yuv2jpeg接口，此接口会获取jpg流
         */
        fd[E_STRM_FROM_SNAP_JPEG] = -1;

        fd[E_STRM_FROM_AI] = SclMnger::getInstance()->getfd(SCL_DEV_ID, mSclChnId, SCL_AI_OUTPORT);
        fd[E_STRM_FROM_MAIN_H26X] = VencH26xChMnger::getInstance()->getfd(mVenH26xChnId[0]);
        fd[E_STRM_FROM_SUB_H26X] = VencH26xChMnger::getInstance()->getfd(mVenH26xChnId[1]);
    }

    ret = select(fd, E_STRM_FROM_MAX, status, tmoutms);
    if (ret > 0) {
        for (uint8_t i = 0; i < E_STRM_FROM_MAX; i ++) {
            if (status[i] == true) {
                getstrm_((strmFrom_t) i, gotcb, user);
            }
        }
    }

    return ret >= 0;
}
