#ifndef __UI_PREVIEW_WIN_H__
#define __UI_PREVIEW_WIN_H__

#include "mystd.h"
#include "DrivingStatus.h"
#include "UISubWin.h"
#include "UIMainWin.h"
#include "idvr.media.config.h"
#include "ThemeMnger.h"
#include "WidgetsCfg.h"

namespace miniui
{
    namespace preview
    {
#define UI_SUB_WIN_MAX_NUM      9

        class UIPreviewWin
        {
        public:
            UIPreviewWin();
            ~UIPreviewWin();

            void init(idvrMediaConf_t *conf);
            bool uninit();

            /**
             * 界面参数更新接口
            */
            void updateGps(double longitude, double latitude, int8_t satNum, uint8_t antennaStatus);
            void updatePlateNum(const std::string& plateNum);
            void updateSteering(steeringWheel_t steering);
            void updateBrakingStatus(bool isBraking);
            void updateCanLossStatus(bool isLoss);
            void updateSpeed(uint8_t speed);
            void updateMsgBox(const UIWidgetMessage_t& message, int32_t timeOutMs = 3000);
            void updateAlarm(uint32_t event, const char *bmpFile, uint32_t duration, bool blink, int32_t startX, int32_t startY, double scale);
            void updateTip(uint32_t event, const char *bmpFile, uint32_t duration, bool blink, int32_t startX, int32_t startY, double scale);
            void updateCustom(const std::string& text, uint32_t bgr, uint32_t bgRgb, const char *id);

            void updateCameraStatus(uint8_t chn, bool isErr);
            void updateRecordStatus(uint8_t chn, bool isRecording);
            void updateLabel(uint8_t chn, const std::string& label);
            void updateRadarDistance(uint32_t chn, const std::vector<uint8_t> &distance);
            void updateChnsOcclusionStatus(uint32_t occlusionChnBits);
            void updateShutdownImg(const char *img);

            void updateNetWork(bool network);
            void updateDeviceId(std::string deviceId);
            void updateCalibLinesInfo(int8_t chn, const CalibLines_t & calibLines);
            void updateObjRectInfo(int8_t chn, const ObjectRect_t & objRect);
            void updateObjLineInfo(int8_t chn, const ObjectLine_t & objLine);

            bool fullsc(uint8_t chn);
            bool smallsc(uint8_t chn);

            void refresh();

            void extinctsc(bool enable);

            static UIPreviewWin *getInstance();

        private:

            bool smallsc_(uint8_t chn);

            bool subWinInit(uint8_t chn, const miniui::rect_t& subWinRect);

            void subWinLayer();
            void mainWinLayer();

        private:

            std::mutex          mMutex;

            uint64_t            mTimeClk;

            UISubWin            *mUISubWins[UI_SUB_WIN_MAX_NUM];
            UIMainWin           *mUIMainWin;

            idvrMediaConf_t     *mCfg;

            bool                mIsRecording[UI_SUB_WIN_MAX_NUM];
            uint32_t            mUpRecordingTimeCnt[UI_SUB_WIN_MAX_NUM];
            bool                mIsCirleLight;

            uint8_t             mSubWinCnt;

            uint8_t             mFullscChn;
            bool                mIsFullsc;

            bool                mIsRefreshNow;

            bool                mIsExtinction;

            uint32_t            mOcclusionChnBitTbl = 0;
        };
    }
}

#endif
