#include "UISubWin.h"
#include "UIPreviewCfg.h"
#include "miniui.h"
#include "ThemeMnger.h"
#include "CfgReceiver.h"
#include "jsonUtil.h"

namespace miniui
{
    namespace preview
    {
        UISubWin::UISubWin(idvrMediaConf_t *cfg, const miniui::rect_t& rect, uint8_t chn, bool isValid)
            : mCfg(cfg)
            , mWinRect(rect)
            , mChn(chn)
            , mIsValid(isValid)
        {
            FBSDRgnInit();
            reverseRgnInit();
        }

        UISubWin::~UISubWin()
        {

        }

        int32_t UISubWin::getBSDFlagValue(char *line, const char *key)
        {
            if (line == nullptr ||
                line[0] == '\0'
            ) {
                return -1;
            }

            char *p = nullptr;
            char *q = nullptr;

            std::string s = std::string("--") + std::string(key) + std::string("=");
            p = strstr(line, s.c_str());
            if (p == nullptr) {
                return -1;
            }

            p += strlen(s.c_str());
            q = strchr(p, '\r');
            if (q) *q = '\0';
            q = strchr(p, '\n');
            if (q) *q = '\0';

            return atoi(p);
        }

        bool UISubWin::reverseRgnInit()
        {
            mRearCarBodyY = 700;
            mRearLevel1y = 500;
            mRearLevel2y = 300;
            mRearLevel3y = 100;

            const UIPreviewCfg::switch_t *sw = UIPreviewCfg::getInstance()->getSwCfg();

            if (! sw->fbsdRgn ||
                mCfg->ch[mChn].camPhy != "ADAS"
            ) {
                return true;
            }

            const char *path = "/sdcard/run/reserve_car.flag";
            FILE *fp = fopen(path, "r");
            if (fp == nullptr) {
                //logpe("fopen '%s' error !", path);
                return false;
            }

            while (true) {
                char buff[2048] = { 0 };
                if (fgets(buff, sizeof(buff) - 1, fp) == nullptr) {
                    break;
                }

                if (strstr(buff, "resev_car_body_y")) {
                    mRearCarBodyY = getBSDFlagValue(buff, "resev_car_body_y");
                } else if (strstr(buff, "resev_level1_y")) {
                    mRearLevel1y = getBSDFlagValue(buff, "resev_meter1_y");
                } else if (strstr(buff, "resev_level2_y")) {
                    mRearLevel2y = getBSDFlagValue(buff, "resev_meter2_y");
                } else if (strstr(buff, "resev_level3_y")) {
                    mRearLevel3y = getBSDFlagValue(buff, "resev_meter3_y");
                }
            }
            fclose(fp);

            mRearLevel1y = MIN(mRearLevel1y, mCfg->ch[mChn].camReslHeight - 1);
            mRearLevel2y = MIN(mRearLevel2y, mCfg->ch[mChn].camReslHeight - 1);
            mRearLevel3y = MIN(mRearLevel3y, mCfg->ch[mChn].camReslHeight - 1);
            mRearCarBodyY = MIN(mRearCarBodyY, mCfg->ch[mChn].camReslHeight - 1);

            mRearLevel1y = MAX(mRearLevel1y, 0);
            mRearLevel2y = MAX(mRearLevel2y, 0);
            mRearLevel3y = MAX(mRearLevel3y, 0);
            mRearCarBodyY = MAX(mRearCarBodyY, 0);

            //logd("mLevel1Y %d mCarBodyY %d", mRearLevel1y, mRearCarBodyY);
            
            return true;
        }


        bool UISubWin::FBSDRgnInit()
        {
            const UIPreviewCfg::switch_t *sw = UIPreviewCfg::getInstance()->getSwCfg();

            if (! sw->fbsdRgn ||
                mCfg->ch[mChn].camPhy != "BSD"
            ) {
                return true;
            }

            const char *path = "/sdcard/run/fbsd_setup.flag";
            FILE *fp = fopen(path, "r");
            if (fp == nullptr) {
                logpe("fopen '%s' error !", path);
                return false;
            }

            while (true) {
                char buff[2048] = { 0 };
                if (fgets(buff, sizeof(buff) - 1, fp) == nullptr) {
                    break;
                }

                if (strstr(buff, "fbsd_car_body_y")) {
                    mCalibLines.body_y = getBSDFlagValue(buff, "fbsd_car_body_y");
                } else if (strstr(buff, "fbsd_level1_y")) {
                    mCalibLines.level1_y = getBSDFlagValue(buff, "fbsd_level1_y");
                }
            }
            fclose(fp);

            mCalibLines.level1_y = MIN(mCalibLines.level1_y, mCfg->ch[mChn].camReslHeight - 1);
            mCalibLines.body_y = MIN(mCalibLines.body_y, mCfg->ch[mChn].camReslHeight - 1);

            mCalibLines.level1_y = MAX(mCalibLines.level1_y, 0);
            mCalibLines.body_y = MAX(mCalibLines.body_y, 0);

            //logd("mLevel1Y %d mCarBodyY %d", mCalibLines.level1_y, mCalibLines.body_y);

            return true;
        }

        void UISubWin::updateCameraStatus(uint8_t isErr)
        {
            std::lock_guard<std::mutex> lock(mMutex);
            mUpdate.isCameraErr = isErr;
        }

        void UISubWin::updateCirleStatus(bool isCirleLight)
        {
            std::lock_guard<std::mutex> lock(mMutex);
            mUpdate.isCirleLight = isCirleLight;
        }

        void UISubWin::updateLabel(const std::string& label)
        {
            std::lock_guard<std::mutex> lock(mMutex);
            mUpdate.label = label;
        }

        void UISubWin::updateRadarDistance(const std::vector<uint8_t> &distance)
        {
            std::lock_guard<std::mutex> lock(mMutex);
            mRadarDistance.assign(distance.begin(), distance.end());
        }

        void UISubWin::updateCalibLinesInfo(const CalibLines_t & calibLines)
        {
            std::lock_guard<std::mutex> lock(mMutex);
            mCalibLines = calibLines;
            //logd("chn %d poly %d, %d", mChn, mCalibLines.polyArea.size(), calibLines.polyArea.size());
        }
        void UISubWin::updateObjRectInfo(const ObjectRect_t & objRect)
        {
            std::lock_guard<std::mutex> lock(mMutex);
            mObjRects.push_back(objRect);
            //logd("updateObjRectInfo %d", mObjRects.size());
            if (mObjRects.size() > 16) {
                mObjRects.erase(mObjRects.begin());
            }
        }
        void UISubWin::updateObjLineInfo(const ObjectLine_t & objLine)
        {
            std::lock_guard<std::mutex> lock(mMutex);
            mObjLines.push_back(objLine);
            if (mObjLines.size() > 16) {
                mObjLines.erase(mObjLines.begin());
            }
        }
        bool UISubWin::updateMarginLayer()
        {
            miniui::rect_t winRect;
            {
                std::lock_guard<std::mutex> lock(mMutex);
                winRect = mWinRect;
            }
            point_t point0;
            point_t point1;

        #define __BASE_X__      (winRect.x + 9)
        #define __BASE_Y__      (winRect.y - 5)
            point0.x = __BASE_X__;
            point0.y = __BASE_Y__;
            point1.x = __BASE_X__;
            point1.y = __BASE_Y__ + winRect.h;
            miniui::Painter::getInstance()->drawLine(point0, point1, WIDGETS_DEFAULT_FONT_COLOR, 2);

            point0.x = __BASE_X__;
            point0.y = __BASE_Y__;
            point1.x = __BASE_X__ + winRect.w;
            point1.y = __BASE_Y__;
            miniui::Painter::getInstance()->drawLine(point0, point1, WIDGETS_DEFAULT_FONT_COLOR, 2);

            point0.x = __BASE_X__ + winRect.w;
            point0.y = __BASE_Y__;
            point1.x = __BASE_X__ + winRect.w;
            point1.y = __BASE_Y__ + winRect.h;
            miniui::Painter::getInstance()->drawLine(point0, point1, WIDGETS_DEFAULT_FONT_COLOR, 2);

            point0.x = __BASE_X__;
            point0.y = __BASE_Y__ + winRect.h;
            point1.x = __BASE_X__ + winRect.w;
            point1.y = __BASE_Y__ + winRect.h;
            miniui::Painter::getInstance()->drawLine(point0, point1, WIDGETS_DEFAULT_FONT_COLOR, 2);
            return true;
        #undef __BASE_X__
        #undef __BASE_Y__
        }

        bool UISubWin::updateReserveRgnLayer()
        {
            const UIPreviewCfg::switch_t *sw = UIPreviewCfg::getInstance()->getSwCfg();

            if (mCfg->ch[mChn].camPhy != "HOD" || 
                ! sw-> reserveRgn) {
                return true;
            }
            miniui::rect_t winRect;
            {
                std::lock_guard<std::mutex> lock(mMutex);
                winRect = mWinRect;
            }
            if (mRearLevel1y < 0 || mRearLevel1y >mCfg->ch[mChn].camReslHeight ||
                mRearLevel2y < 0 || mRearLevel2y >mCfg->ch[mChn].camReslHeight ||
                mRearLevel3y < 0 || mRearLevel3y >mCfg->ch[mChn].camReslHeight ||
                mRearCarBodyY < 0 || mRearCarBodyY >mCfg->ch[mChn].camReslHeight
                ) {
                return true;
            }
        
            double scaleH = (double) winRect.h / (double) mCfg->ch[mChn].camReslHeight;
            int32_t level1Y = round((double) mRearLevel1y * scaleH);
            int32_t level2Y = round((double) mRearLevel2y * scaleH);
            int32_t level3Y = round((double) mRearLevel3y * scaleH);
            int32_t carBodyY = round((double) mRearCarBodyY * scaleH);

        #define RESERVE_LINE_WIDTH      2
        #define Y_RESERVE_PERCENT       0.6
        #define X_RESERVE               15
        #define X_LEVEL                 15
        
            int32_t xOffset = (winRect.w - winRect.w * 0.4) / 2;
            
            line_t greenLine;
            line_t yellowLine;
            line_t redLine;
            line_t carBodyLine;

            /**
             * @brief 
             * 一米告警线
             */
            redLine.p0.x = winRect.x + xOffset + X_RESERVE;
            redLine.p0.y = winRect.y + level1Y;
            redLine.p1.x = winRect.x + xOffset + X_RESERVE + X_LEVEL;
            redLine.p1.y = winRect.y + level1Y;
            redLine.color = 0xff0000;
            redLine.lineWidth = RESERVE_LINE_WIDTH;
            miniui::Painter::getInstance()->drawLine(redLine.p0, redLine.p1, redLine.color, redLine.lineWidth);

            redLine.p0.x = winRect.x + winRect.w - xOffset -  X_RESERVE - X_LEVEL;
            redLine.p0.y = winRect.y + level1Y;
            redLine.p1.x = winRect.x + winRect.w - xOffset -  X_RESERVE;
            redLine.p1.y = winRect.y + level1Y;
            redLine.color = 0xff0000;
            redLine.lineWidth = RESERVE_LINE_WIDTH;
            miniui::Painter::getInstance()->drawLine(redLine.p0, redLine.p1, redLine.color, redLine.lineWidth);


            redLine.p0.x = winRect.x + xOffset +  X_RESERVE;
            redLine.p0.y = winRect.y + level1Y;
            redLine.p1.x = winRect.x + xOffset;
            redLine.p1.y = winRect.y + level1Y + (carBodyY - level1Y) * Y_RESERVE_PERCENT;
            redLine.color = 0xff0000;
            redLine.lineWidth = RESERVE_LINE_WIDTH;
            miniui::Painter::getInstance()->drawLine(redLine.p0, redLine.p1, redLine.color, redLine.lineWidth);

            redLine.p0.x = winRect.x + winRect.w - xOffset - X_RESERVE;
            redLine.p0.y = winRect.y + level1Y;
            redLine.p1.x = winRect.x + winRect.w - xOffset;
            redLine.p1.y = winRect.y + level1Y + (carBodyY - level1Y) * Y_RESERVE_PERCENT;
            redLine.color = 0xff0000;
            redLine.lineWidth = RESERVE_LINE_WIDTH;
            miniui::Painter::getInstance()->drawLine(redLine.p0, redLine.p1, redLine.color, redLine.lineWidth);

            /**
             * @brief 
             * 二米告警线
             */
            yellowLine.p0.x = winRect.x + xOffset  + 2 * X_RESERVE;
            yellowLine.p0.y = winRect.y + level2Y;
            yellowLine.p1.x = winRect.x + xOffset  + 2 * X_RESERVE + X_LEVEL;
            yellowLine.p1.y = winRect.y + level2Y;
            yellowLine.color = 0xffff00;
            yellowLine.lineWidth = RESERVE_LINE_WIDTH;
            miniui::Painter::getInstance()->drawLine(yellowLine.p0, yellowLine.p1, yellowLine.color, yellowLine.lineWidth);

            yellowLine.p0.x = winRect.x + winRect.w - xOffset - 2 * X_RESERVE - X_LEVEL;
            yellowLine.p0.y = winRect.y + level2Y;
            yellowLine.p1.x = winRect.x + winRect.w - xOffset - 2 * X_RESERVE;
            yellowLine.p1.y = winRect.y + level2Y;
            yellowLine.color = 0xffff00;
            yellowLine.lineWidth = RESERVE_LINE_WIDTH;
            miniui::Painter::getInstance()->drawLine(yellowLine.p0, yellowLine.p1, yellowLine.color, yellowLine.lineWidth);

            yellowLine.p0.x = winRect.x + xOffset + 2 * X_RESERVE;
            yellowLine.p0.y = winRect.y + level2Y;
            yellowLine.p1.x = winRect.x + xOffset + X_RESERVE;
            yellowLine.p1.y = winRect.y + level2Y + (level1Y - level2Y) * Y_RESERVE_PERCENT;
            yellowLine.color = 0xffff00;
            yellowLine.lineWidth = RESERVE_LINE_WIDTH;
            miniui::Painter::getInstance()->drawLine(yellowLine.p0, yellowLine.p1, yellowLine.color, yellowLine.lineWidth);

            yellowLine.p0.x = winRect.x + winRect.w - xOffset - 2 * X_RESERVE;
            yellowLine.p0.y = winRect.y + level2Y;
            yellowLine.p1.x = winRect.x + winRect.w - xOffset - X_RESERVE;
            yellowLine.p1.y = winRect.y + level2Y + (level1Y - level2Y) * Y_RESERVE_PERCENT;
            yellowLine.color = 0xffff00;
            yellowLine.lineWidth = RESERVE_LINE_WIDTH;
            miniui::Painter::getInstance()->drawLine(yellowLine.p0, yellowLine.p1, yellowLine.color, yellowLine.lineWidth);

            /**
             * @brief 
             * 三米告警线
             */
            greenLine.p0.x = winRect.x + xOffset + 3 * X_RESERVE;
            greenLine.p0.y = winRect.y + level3Y;
            greenLine.p1.x = winRect.x + xOffset + 3 * X_RESERVE + X_LEVEL;
            greenLine.p1.y = winRect.y + level3Y;
            greenLine.color = 0x00ff00;
            greenLine.lineWidth = RESERVE_LINE_WIDTH;
            miniui::Painter::getInstance()->drawLine(greenLine.p0, greenLine.p1, greenLine.color, greenLine.lineWidth);

            greenLine.p0.x = winRect.x + winRect.w - xOffset - 3 * X_RESERVE - X_LEVEL;
            greenLine.p0.y = winRect.y + level3Y;
            greenLine.p1.x = winRect.x + winRect.w - xOffset - 3 * X_RESERVE;
            greenLine.p1.y = winRect.y + level3Y;
            greenLine.color = 0x00ff00;
            greenLine.lineWidth = RESERVE_LINE_WIDTH;
            miniui::Painter::getInstance()->drawLine(greenLine.p0, greenLine.p1, greenLine.color, greenLine.lineWidth);

            greenLine.p0.x = winRect.x + xOffset + 3 * X_RESERVE;
            greenLine.p0.y = winRect.y + level3Y;
            greenLine.p1.x = winRect.x + xOffset + 2 * X_RESERVE;
            greenLine.p1.y = winRect.y + level3Y + (level2Y - level3Y) * Y_RESERVE_PERCENT;
            greenLine.color = 0x00ff00;
            greenLine.lineWidth = RESERVE_LINE_WIDTH;
            miniui::Painter::getInstance()->drawLine(greenLine.p0, greenLine.p1, greenLine.color, greenLine.lineWidth);
            
            greenLine.p0.x = winRect.x + winRect.w - xOffset - 3 * X_RESERVE;
            greenLine.p0.y = winRect.y + level3Y;
            greenLine.p1.x = winRect.x + winRect.w - xOffset - 2 * X_RESERVE;
            greenLine.p1.y = winRect.y + level3Y + (level2Y - level3Y) * Y_RESERVE_PERCENT;
            greenLine.color = 0x00ff00;
            greenLine.lineWidth = RESERVE_LINE_WIDTH;
            miniui::Painter::getInstance()->drawLine(greenLine.p0, greenLine.p1, greenLine.color, greenLine.lineWidth);

            return true;
        }

        bool UISubWin::updateFBSDRgnLayer()
        {
            std::vector<ObjectRect_t> objRects;
            std::vector<ObjectLine_t> objLines;
            UISubWinUpdate_t winUpdate;
            miniui::rect_t winRect;
            {
                std::lock_guard<std::mutex> lock(mMutex);
                winRect = mWinRect;
                winUpdate = mUpdate;
                auto it = mObjRects.begin();
                while (it != mObjRects.end()) {
                    int64_t e = my::getClockMs() - it->ts;
                    if (e >= 500) {
                        it = mObjRects.erase(it);
                    } else {
                        objRects.push_back(*it);
                        it++;
                    }
                }
            }
            {
                std::lock_guard<std::mutex> lock(mMutex);
                winRect = mWinRect;
                winUpdate = mUpdate;
                auto it = mObjLines.begin();
                while (it != mObjLines.end()) {
                    int64_t e = my::getClockMs() - it->ts;
                    if (e >= 500) {
                        it = mObjLines.erase(it);
                    } else {
                        objLines.push_back(*it);
                        it++;
                    }
                }
            }
            const UIPreviewCfg::switch_t *sw = UIPreviewCfg::getInstance()->getSwCfg();

            if (mIsValid == false || winUpdate.isCameraErr == 1) {
                //loge("sw->fbsdRgn %d, %d, %d, %d, %d, ", sw->fbsdRgn, mIsValid, winUpdate.isCameraErr, mCalibLines.body_y, mCalibLines.level1_y);
                return true;
            }
            //logd("chn %d", mChn);
            double scaleH = (double) winRect.h / (double) mCfg->ch[mChn].camReslHeight;
            double scaleW = (double) winRect.w / (double) mCfg->ch[mChn].camReslWidth;
            int32_t level1Y = round((double) mCalibLines.level1_y * scaleH);
            int32_t level2Y = round((double) mCalibLines.level2_y * scaleH);
            int32_t level3Y = round((double) mCalibLines.level3_y * scaleH);
            int32_t carBodyY = round((double) mCalibLines.body_y * scaleH);

#define FBSD_LINE_WIDTH     2
        
            line_t greenLine;
            line_t yellowLine;
            line_t redLine;
            line_t carBodyLine;
            if (mCalibLines.level1_y > 0) {
                /**
                 * @brief 
                 * 一级告警线
                 */
                greenLine.p0.x = winRect.x + 5;
                greenLine.p0.y = winRect.y + level1Y;
                greenLine.p0.y = MIN(greenLine.p0.y, winRect.y + winRect.h - 1 - redLine.lineWidth);
                greenLine.p1.x = winRect.x + winRect.w + 5;
                greenLine.p1.y = greenLine.p0.y;
                greenLine.color = 0xff0000; // red
                greenLine.lineWidth = FBSD_LINE_WIDTH;
                //Draw::getInstance()->drawLine(greenLine.p0, greenLine.p1, greenLine.color, greenLine.lineWidth);
                miniui::Painter::getInstance()->drawLine(greenLine.p0, greenLine.p1, greenLine.color, greenLine.lineWidth);
            }
            if (mCalibLines.level2_y > 0) {
                /**
                 * @brief 
                 * 二级告警线
                 */
                yellowLine.p0.x = winRect.x + 5;
                yellowLine.p0.y = winRect.y + level2Y;
                yellowLine.p0.y = MIN(yellowLine.p0.y, winRect.y + winRect.h - 1 - redLine.lineWidth);
                yellowLine.p1.x = winRect.x + winRect.w + 5;
                yellowLine.p1.y = yellowLine.p0.y;
                yellowLine.color = 0xffff00;
                yellowLine.lineWidth = FBSD_LINE_WIDTH;
                miniui::Painter::getInstance()->drawLine(yellowLine.p0, yellowLine.p1, yellowLine.color, yellowLine.lineWidth);
            }
            if (mCalibLines.level3_y > 0) {
                /**
                 * @brief 
                 * 三级告警线
                 */
                redLine.p0.x = winRect.x + 5;
                redLine.p0.y = winRect.y + level3Y - 15;
                redLine.p0.y = MIN(redLine.p0.y, winRect.y + winRect.h - 1 - redLine.lineWidth);
                redLine.p1.x = winRect.x + winRect.w + 5;
                redLine.p1.y = redLine.p0.y;
                redLine.color = 0x00ff00;
                redLine.lineWidth = FBSD_LINE_WIDTH;
                miniui::Painter::getInstance()->drawLine(redLine.p0, redLine.p1, redLine.color, redLine.lineWidth);
            }
            if (mCalibLines.body_y > 0) {
                /**
                 * @brief 
                 * 车身线
                 */
                carBodyLine.p0.x = winRect.x + 5;
                carBodyLine.p0.y = winRect.y + carBodyY + 10;
                carBodyLine.p0.y = MIN(carBodyLine.p0.y, winRect.y + winRect.h - 1 - redLine.lineWidth);
                carBodyLine.p1.x = winRect.x + winRect.w + 5;
                carBodyLine.p1.y = carBodyLine.p0.y;
                carBodyLine.color = 0x0000ff;
                carBodyLine.lineWidth = FBSD_LINE_WIDTH;
                miniui::Painter::getInstance()->drawLine(carBodyLine.p0, carBodyLine.p1, carBodyLine.color, carBodyLine.lineWidth);
            }
            /**
             * @brief
             * 算法检测区域
            */
           std::vector<point_t> vv;
            for (auto r : mCalibLines.polyArea) {
                point_t pt;
                pt.x = winRect.x + round(((double) r.first) * scaleW) + 10;
                pt.y = winRect.y + round(((double) r.second) * scaleH) - 5;
                //logd("vv %d : (%d, %d)", vv.size(), pt.x, pt.y);
                vv.push_back(pt);
            }

            if (vv.size() >= 3) {
                point_t * ppt = nullptr;
                auto it = vv.begin();
                auto it2 = vv.begin();
                it2++;
                while (it2 != vv.end()) {
                    miniui::Painter::getInstance()->drawLine(*it, *it2, 0xffffff, FBSD_LINE_WIDTH/2);
                    ppt = &(*it2);
                    it++;
                    it2++;
                }
                miniui::Painter::getInstance()->drawLine(*it, *vv.begin(), 0xffffff, FBSD_LINE_WIDTH/2);
            } else {
                //logd("vv.size %d < 3", vv.size());
            }
            /**
             * @brief 
             * 检测框
             */
            //CfgReceiver::getInstance()->getResl(mChn);
            std::vector<text_t> texts;
            //logd("objRects size %d/%d", objRects.size(), mObjRects.size());
            auto it = objRects.begin();
            while (it != objRects.end()) {
                rect_t alarmRect;
                alarmRect.x = winRect.x + round(((double) it->x) * scaleW) + 5;
                alarmRect.y = winRect.y + round(((double) it->y) * scaleH);
                alarmRect.w = round(((double) it->w) * scaleW);
                alarmRect.h = round(((double) it->h) * scaleH);
                //logd("+winRect   %d, %d, %d, %d", winRect.x, winRect.y, winRect.w, winRect.h);
                //logd("+alarmRect %d, %d, %d, %d", alarmRect.x, alarmRect.y, alarmRect.w, alarmRect.h);
                miniui::Painter::getInstance()->drawRectHollow(alarmRect, COLOR_BLUE, 2);
                if (it->txt.length()) {
                    text_t t;
                    t.x = alarmRect.x;
                    t.y = (alarmRect.y >= 30) ? (alarmRect.y - 30) : 0;
                    t.color = COLOR_BLUE;
                    t.text = it->txt;
                    t.text = it->txt;
                    texts.push_back(t);
                }
                it++;
            }
            if (texts.size()) {
                miniui::Painter::getInstance()->drawText(texts);
            }
            /**
             * @brief 
             * 画线
             */
            //logd("objLines size %d/%d", objLines.size(), mObjLines.size());
            auto it1 = objLines.begin();
            while (it1 != objLines.end()) {
                line_t line;
                line.p0.x = winRect.x + round(((double) it1->x1) * scaleW) + 5;
                line.p0.y = winRect.y + round(((double) it1->y1) * scaleH);
                line.p1.x = winRect.x + round(((double) it1->x2) * scaleW) + 5;
                line.p1.y = winRect.y + round(((double) it1->y2) * scaleH);

                miniui::Painter::getInstance()->drawLine(line.p0, line.p1, COLOR_BLUE, FBSD_LINE_WIDTH);
                it1++;
            }
            return true;
        #undef FBSD_LINE_WIDTH
        }

        bool UISubWin::updateCirleLayer()
        {
            if (mIsValid == false) {
                return true;
            }
            UISubWinUpdate_t winUpdate;
            miniui::rect_t winRect;
            {
                std::lock_guard<std::mutex> lock(mMutex);
                winRect = mWinRect;
                winUpdate = mUpdate;
            }
            if (winUpdate.isCirleLight == false) {
                return true;
            }

            miniui::rect_t rect;
            rect.x = winRect.x + winRect.w - 25;
            rect.y = winRect.y + winRect.h - 36;
            rect.w = 12;
            rect.h = 14;

            miniui::Painter::getInstance()->drawRect(Rect(rect.x, rect.y, rect.w, rect.h), 0xff0000);

            return true;
        }

        #define __OSD__(__sw, __v, __text, __x, __y, __fs, __color, __bgclr, __str) { \
                __text.x = __x; \
                __text.y = __y; \
                __text.ftSize = __fs; \
                __text.color = __color; \
                __text.bgColor = __bgclr; \
                __text.text = __str; \
                __v.push_back(__text); \
                miniui::Painter::getInstance()->drawText(v); \
            }

        bool UISubWin::updateLabelLayer()
        {
            UISubWinUpdate_t winUpdate;
            miniui::rect_t winRect;
            {
                std::lock_guard<std::mutex> lock(mMutex);
                winRect = mWinRect;
                winUpdate = mUpdate;
            }
            const UIPreviewCfg::switch_t *sw = UIPreviewCfg::getInstance()->getSwCfg();
            miniui::fontSize_t fontSize = DEFAULT_FONT_SIZE;
            int32_t offsetX = 0;
            int32_t i = (int32_t) DEFAULT_FONT_SIZE;
            my::timestamp ts = my::timestamp::now();
            for (; i > (int32_t) miniui::E_FONT_SIZE_UNKOWN; i --) {
                fontSize = (miniui::fontSize_t)i;
                miniui::rsize_t size;
                miniui::Font::getInstance()->getFontSize(winUpdate.label.c_str(), fontSize, size);
                offsetX = (winRect.w - size.w) / 2;
                if (offsetX < 0) {
                    offsetX = 0;
                    //logd("+font %d, offsetX %d", i, offsetX);
                } else {
                    //logd("-font %d, offsetX %d, %s", i, offsetX, winUpdate.label.c_str());
                    break;
                }
            }
            if (ts.elapsed() > 1000) {
                logd("getFontSize cost %f ms, font %d", ts.elapsed(), i);
            }
            ts = my::timestamp::now();

            int32_t x = winRect.x + offsetX;
            int32_t y = winRect.y + winRect.h - 40;

            std::vector<text_t> v;
            text_t text;
            if (mCfg->bgColorSwitch) {
                __OSD__(sw->label, v, text, x, y, fontSize, WIDGETS_DEFAULT_FONT_COLOR, WIDGETS_DEFAULT_BG_COLOR, winUpdate.label);
            } else {
                __OSD__(sw->label, v, text, x, y, fontSize, WIDGETS_DEFAULT_FONT_COLOR, 0, winUpdate.label);
            }
            if (ts.elapsed() > 1000) {
                logd("drawText cost %f ms", ts.elapsed());
            }

            return true;
        }

        bool UISubWin::updateCameraStatusLayer()
        {
            std::vector<text_t> v;
            text_t text;

            std::string status;
            UISubWinUpdate_t winUpdate;
            miniui::rect_t winRect;
            {
                std::lock_guard<std::mutex> lock(mMutex);
                winRect = mWinRect;
                winUpdate = mUpdate;
            }
            if (mIsValid == false) {
                status = miniui::StringIdMnger::getInstance()->get("chn_invalid");
            } else {
                int32_t wbmp = 64, hbmp = 64;
                std::string showIcon = "/data/img/";
                showIcon += mCfg->ch[mChn].camPhy;
                showIcon += ".bmp";
                if (winUpdate.isCameraErr) {
                    if (!access(showIcon.c_str(), R_OK)) {//drawBmp
                        bmp_t bmp;
                        bmp.x = winRect.x + winRect.w / 2 - wbmp;
                        bmp.y = winRect.y + winRect.h / 2 - hbmp / 2 - 10;
                        bmp.scale = 1.0;
                        bmp.path = showIcon;
                        miniui::Painter::getInstance()->drawBmp(bmp);

                        bmp.x += wbmp;
                        bmp.scale = 1.0;
                        if (1 == winUpdate.isCameraErr) {
                            bmp.path = "/system/etc/img/camLoss.bmp";
                        } else if (2 == winUpdate.isCameraErr) {
                            bmp.path = "/system/etc/img/camOcclusion.bmp";
                        }
                        if (!access("/tmp/testCamOcclusion", R_OK)) {
                            bmp.path = "/system/etc/img/camOcclusion.bmp";
                        }
                        miniui::Painter::getInstance()->drawBmp(bmp);
                        return true;
                    }
                    status = miniui::StringIdMnger::getInstance()->get("video_loss");
                } else {
                    if (mCfg->ch[mChn].ai.enable && (mCfg->ch[mChn].ai.func == "objp")) {
                        if (!mAlgoCalibFile.length() || (mAlgoCalibFileChkTs.elapsed() >= 10000)) {
                            mAlgoCalibFileChkTs = my::timestamp::now();
                            if (mAlgoCalibFile.length()) {
                                mAlgoCalibFile = "/data/calib/";
                                mAlgoCalibFile += mCfg->ch[mChn].ai.func;
                            }
                            std::string chnRb = "raw_ch";
                            chnRb += std::to_string(mChn);
                            jsonUtil::jsonObject_t jsonObject = jsonUtil::fileToJsonObject("/system/algo/transfer_tools/conf/RingbufTodds.json");
                            jsonUtil::jsonObject_t datasObj = jsonUtil::getJsonObjectValue(jsonObject, "datas");
                            JSON_ARRAY_FOREACH(datasObj, __subObj, {
                                std::string rbName = jsonUtil::getStringValue(__subObj, "rbName");
                                if (rbName == chnRb) {
                                    mAlgoCalibFile = "/data/calib/";
                                    mAlgoCalibFile += jsonUtil::getStringValue(__subObj, "ddsTopic");
                                    mAlgoCalibFile += ".json";
                                    break;
                                }
                            });
                            mbShowCalibIcon = !!access(mAlgoCalibFile.c_str(), R_OK);
                            if (!mbShowCalibIcon) {
                                jsonUtil::jsonObject_t calibJO = jsonUtil::fileToJsonObject(mAlgoCalibFile.c_str());
                                int64_t calibTs = jsonUtil::getLongLongValue(calibJO, "calib_timestamp");
                                mbShowCalibIcon = (calibTs <= 0);
                            }
                        }
                        if (!access(showIcon.c_str(), R_OK)) {//drawBmp
                            if (mbShowCalibIcon) {
                                bmp_t bmp;
                                bmp.x = winRect.x + winRect.w / 2 - wbmp;
                                bmp.y = winRect.y + winRect.h / 2 - hbmp / 2 - 10;
                                bmp.scale = 1.0;
                                bmp.path = showIcon;
                                miniui::Painter::getInstance()->drawBmp(bmp);

                                bmp.x += wbmp;
                                bmp.scale = 1.0;
                                bmp.path = "/system/etc/img/noCalib.bmp";
                                miniui::Painter::getInstance()->drawBmp(bmp);
                            }
                            return true;
                        }
                    }
                    return true;
                }
            }

            miniui::rsize_t size;
            miniui::Font::getInstance()->getFontSize(status.c_str(), DEFAULT_FONT_SIZE, size);
            int32_t x = winRect.x + winRect.w / 2 - size.w / 2;
            int32_t y = winRect.y + winRect.h / 2 - size.h / 2 - 10;

            __OSD__(sw->videoStatus, v, text, x, y, DEFAULT_FONT_SIZE, WIDGETS_DEFAULT_FONT_COLOR, WIDGETS_DEFAULT_BG_COLOR, status);

            return true;
        }
        
        bool UISubWin::updateRadarLayer()
        {
            const UIPreviewCfg::switch_t *sw = UIPreviewCfg::getInstance()->getSwCfg();
            if (mCfg->ch[mChn].camPhy != "HOD" ||
                ! sw-> reserveRgn) {
                return true;
            }
            UISubWinUpdate_t winUpdate;
            miniui::rect_t winRect;
            {
                std::lock_guard<std::mutex> lock(mMutex);
                winRect = mWinRect;
                winUpdate = mUpdate;
            }
            double scaleH = (double) winRect.h / (double) mCfg->ch[mChn].camReslHeight;

            int32_t xDiff = winRect.w / 9;
            int32_t xBegin = winRect.x + xDiff;
            int32_t yBegin = winRect.y + winRect.h - 40;

            for (int32_t i = 0; i < mRadarDistance.size(); i++) {
                updateRadarVal(xBegin + i * xDiff, yBegin, mRadarDistance[i]);
            }

            for (int32_t i = 0; i < 8; i++) {
                updateRadarLogo(xBegin + i * xDiff, yBegin);
            }
            
            return true;
        }

        bool UISubWin::updateRadarVal(int32_t x, int32_t y, uint8_t distance)
        {
            if (distance != 0xff && distance != 0xfe && distance != 0xfd && distance != 0) {
                std::vector<text_t> v;
                text_t text;

                const UIPreviewCfg::switch_t *sw = UIPreviewCfg::getInstance()->getSwCfg();

                miniui::rsize_t size;

                char buf[50] = {0};
                double d = (double)(((double)distance * 2) / 100.0);
                snprintf(buf, sizeof(buf), "%0.1lf", d);

                color_t fontColor = WIDGETS_DEFAULT_FONT_COLOR;
                if (d > 0.0 && d < 1.0) {
                    fontColor = 0xff0000;
                } else if (d > 1.0 && d < 2.0) {
                    fontColor = 0xffff00;
                } else if (d > 2.0){
                    fontColor = 0x00ff00;
                }
                UISubWinUpdate_t winUpdate;
                miniui::rect_t winRect;
                {
                    std::lock_guard<std::mutex> lock(mMutex);
                    winRect = mWinRect;
                    winUpdate = mUpdate;
                }
                fontSize_t fontSize = DEFAULT_FONT_SIZE;
                if (winRect.w >= 700 && winRect.h >= 500) {
                    fontSize = DEFAULT_FONT_SIZE;
                }

                miniui::Font::getInstance()->getFontSize(buf, fontSize, size);

                int32_t x1 = x - size.w / 2 + 10;
                int32_t y1 = y - 40;

                __OSD__(sw->label, v, text, x1, y1, fontSize, fontColor, WIDGETS_DEFAULT_BG_COLOR, buf);
            }
            return true;
        }

        bool UISubWin::updateRadarLogo(int32_t x, int32_t y)
        {
            line_t greenLine;
            line_t yellowLine;
            line_t redLine;

            greenLine.p0.x = x;
            greenLine.p0.y = y;
            greenLine.p1.x = x + 20;
            greenLine.p1.y = y;
            greenLine.color = 0x00ff00;
            greenLine.lineWidth = 2;
            miniui::Painter::getInstance()->drawLine(greenLine.p0, greenLine.p1, greenLine.color, greenLine.lineWidth);

            yellowLine.p0.x = x + 3;
            yellowLine.p0.y = y + 5;
            yellowLine.p1.x = x + 20 - 3;
            yellowLine.p1.y = y + 5;
            yellowLine.color = 0xffff00;
            yellowLine.lineWidth = 2;
            miniui::Painter::getInstance()->drawLine(yellowLine.p0, yellowLine.p1, yellowLine.color, yellowLine.lineWidth);

            redLine.p0.x = x + 6;
            redLine.p0.y = y + 10;
            redLine.p1.x = x + 20 - 6;
            redLine.p1.y = y + 10;
            redLine.color = 0xff0000;
            redLine.lineWidth = 2;
            miniui::Painter::getInstance()->drawLine(redLine.p0, redLine.p1, redLine.color, redLine.lineWidth);
            return true;
        }

        #undef __OSD__

        bool UISubWin::updateLayer()
        {
            my::timestamp ts1 = my::timestamp::now();
            my::timestamp ts = my::timestamp::now();
            //显示边框
            updateMarginLayer();
            if (ts.elapsed() > 1000) {
                logd("updateMarginLayer cost %f ms", ts.elapsed());
            }
            ts = my::timestamp::now();

            //显示FBSD区域预警线
            updateFBSDRgnLayer();
            if (ts.elapsed() > 1000) {
                logd("updateFBSDRgnLayer cost %f ms", ts.elapsed());
            }
            ts = my::timestamp::now();

            //显示label
            updateLabelLayer();
            if (ts.elapsed() > 1000) {
                logd("updateLabelLayer cost %f ms", ts.elapsed());
            }
            ts = my::timestamp::now();

            //显示红点
            updateCirleLayer();
            if (ts.elapsed() > 1000) {
                logd("updateCirleLayer cost %f ms", ts.elapsed());
            }
            ts = my::timestamp::now();

            //显示摄像头状态
            updateCameraStatusLayer();
            if (ts.elapsed() > 1000) {
                logd("updateCameraStatusLayer cost %f ms", ts.elapsed());
            }
            ts = my::timestamp::now();

            // 显示倒车区域预警线
            updateReserveRgnLayer();
            if (ts.elapsed() > 1000) {
                logd("updateReserveRgnLayer cost %f ms", ts.elapsed());
            }
            ts = my::timestamp::now();

            // 显示超声波雷达探头
            updateRadarLayer();
            if (ts.elapsed() > 1000) {
                logd("updateRadarLayer cost %f ms", ts.elapsed());
            }
            ts = my::timestamp::now();
            if (ts1.elapsed() > 1000) {
                logd("UISubWin::updateLayer cost %f ms", ts1.elapsed());
            }

            return true;
        }

        uint8_t UISubWin::getChn()
        {
            return mChn;
        }
        bool UISubWin::getRect(miniui::rect_t & rect)
        {
            std::lock_guard<std::mutex> lock(mMutex);
            rect = mWinRect;
            return true;
        }
    }
}
