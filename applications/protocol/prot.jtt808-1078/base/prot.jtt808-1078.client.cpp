#include "prot.jtt808-1078.client.h"
#include "jsonUtil.h"

string getSwVer(const char * swName)
{
    char line[4 << 10];
    string v = swName;
    FILE * fp = fopen(PKG_VER_PATH, "r");

    if (fp) {
        if(nullptr == fgets(line, sizeof(line), fp)){
            loge("fgets error!");
        } else {
            char * p = line + strlen(line) - 1;
            while (*p && (p > line)) {
                if ((*p != ' ') && (*p != '\t') &&
                    (*p != '\r') && (*p != '\n')) {
                    break;
                }
                *p = 0;
                p--;
            }
            v = line;
        }
        fclose(fp);
    }

    return v;
}

bool getSimInfo(my::string & imei, my::string & iccid)
{
    bool bSimPresent = false;
    if (access("/data/test_sim_info", R_OK)) {
        int count = 0;
        while (imei.empty()) {
            char propValue[PROP_VALUE_MAX] = {0};
            __system_property_get("rw.minieye.iccid", propValue);
            iccid = propValue;

            __system_property_get("rw.minieye.imei", propValue);
            imei = propValue;
            count++;
            sleep(1);
        }
        string cmd = "echo ";
        cmd += my::to_string(count);
        cmd += " > /tmp/get_sim_sec";
        system(cmd.c_str());
        bSimPresent = true;
    } else {
        char propValue[PROP_VALUE_MAX] = {0};
        __system_property_get(PROP_PERSIST_MINIEYE_ICCID, propValue);
        iccid = propValue;

        __system_property_get(PROP_PROP_PERSIST_MINIEYE_IMEI_SET, propValue);
        imei = propValue;
        bSimPresent = true;
    }
    return bSimPresent;
}

void string_replace(std::string &strBig, const std::string &strsrc, const std::string &strdst)
{
    std::string::size_type pos = 0;
    std::string::size_type srclen = strsrc.size();
    std::string::size_type dstlen = strdst.size();

    while ((pos = strBig.find(strsrc, pos)) != std::string::npos) {
        strBig.replace(pos, srclen, strdst);
        pos += dstlen;
    }
}

// 得到文件名
my::string get_name(const char* path)
{
    size_t len = strlen(path);

    if (len == 0) {
        return "";
    }

    std::string full_path = path;
    string_replace(full_path, "/", "\\");
    std::string::size_type iPos = full_path.find_last_of('\\') + 1;
    return full_path.substr(iPos, full_path.length() - iPos).c_str();
}

inline my::string getMedia(int i)
{
    return !i ? "V" : "v";
}

static bool parseFtpUrl(my::string & url, my::string & addr, my::string & usrPwd)
{
    char * p = (char*)url.c_str();
    char * q = strchr(p, '@');
    if (!q) {
        return false;
    }
    my::string tmp = "ftp://";
    p += 6;//skip ftp://

    *q = 0;//end pwd
    tmp.append(q + 1);

    usrPwd = p;

    addr = tmp.c_str();
    logd("addr %s", addr.c_str());
    return true;
}


// 检查应答
int JttClient::check_res(bool jt808_new)
{
    my::string msg;
    int ret = read(msg);
    logd("\n%s", my::hex(msg, true).c_str());
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::check_res] failed to read msg, ret=[%d]", ret);

    // 解码
    my::constr dec(msg);
    my::string sim;
    my::ushort cmd, flag, sn;
    my::uchar  protol;

    if (jt808_new) {
        if (msg.length() < 17) {
            return false;
        }

        dec >> my::ntoh >> cmd >> flag >> protol;
        bcd2numstr(sim, dec, 10);
        dec += 10;
        dec >> sn;

    } else {

        if (msg.length() < 12) {
            return false;
        }

        // 解码cmd和flag
        dec >> my::ntoh >> cmd >> flag;

        // 解码sim
        bcd2numstr(sim, dec, 6);
        dec += 6;

        // 解码sn
        dec >> sn;
    }

    X8001 rsp;
    LOG_RETURN_IF(!rsp.decode(dec), -2, loge, "[JttClient::check_res] failed to decode: %s\n", my::hex(dec, true).c_str());
    return rsp.res == rsp.RES_OK ? 0 : -1;
}

// 检查应答
int JttClient::check_9212(bool jt808_new, X9212 & rsp)
{
    my::string msg;
    int ret = read(msg);
    logd("\n%s", my::hex(msg, true).c_str());
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::check_9212] failed to read msg, ret=[%d]", ret);

    // 解码
    my::constr dec(msg);
    my::string sim;
    my::ushort cmd, flag, sn;
    my::uchar  protol;

    if (jt808_new) {
        if (msg.length() < 17) {
            return false;
        }

        dec >> my::ntoh >> cmd >> flag >> protol;
        bcd2numstr(sim, dec, 10);
        dec += 10;
        dec >> sn;

    } else {
        if (msg.length() < 12) {
            return false;
        }

        // 解码cmd和flag
        dec >> my::ntoh >> cmd >> flag;

        // 解码sim
        bcd2numstr(sim, dec, 6);
        dec += 6;

        // 解码sn
        dec >> sn;

    }

    LOG_RETURN_IF(!rsp.decode(dec), -1, loge, "[JttClient::check_res] failed to decode: %s\n", my::hex(dec, true).c_str());
    return rsp.res == 0 ? 0 : 1;
}

int JttClient::read(my::string& msg)
{
    bool x7e = false;
    my::string buf, tmp;

    int pos = 0, ret = 0, maxTry = 10;

    while (!x7e) {
        tmp.length(16 * 1024);
        LOG_RETURN_IF(!get(tmp), -1, loge, "[JttClient::read] failed to get msg.");
        ret = tmp.length();

        if (!ret) {
            if (maxTry-- < 0) {
                loge("[JttClient::read] failed to get msg 1, try out!");
                break;
            }

            usleep(300e3);
            continue;
        }

        for (pos = 0; pos < ret; pos++) {
            if (tmp[pos] == 0x7e) {
                x7e = true;
                break;
            }
        }
    }

    pos++;
    int e_pos = pos;
    maxTry = 10;

    while (x7e) {
        for (; e_pos < tmp.length(); e_pos++) {
            if (tmp[e_pos] == 0x7e) {
                x7e = false;
                break;
            }
        }

        buf.append((char*)tmp + pos, e_pos - pos);

        if (x7e) {
            tmp.length(16 * 1024);
            LOG_RETURN_IF(!get(tmp), -2, loge, "[JttClient::read] failed to get msg-ending.");
            e_pos = pos = 0;
            ret = tmp.length();

            if (!ret) {
                if (maxTry-- < 0) {
                    loge("[JttClient::read] failed to get msg-ending, try out!");
                    return -2;
                }

                usleep(300e3);
            }
        }
    }

    char chk = 0;
    int i = 0;

    while (i < buf.length()) {
        char c = buf[i++];

        if (c == 0x7d) {
            char cc = buf[i++];

            if (cc == 0x01) {
                c = 0x7d;

            } else if (cc == 0x02) {
                c = 0x7e;

            } else {
                return -2;
            }
        }

        msg << c;
        chk ^= c;
    }

    LOG_RETURN_IF(chk != 0, -1, loge, "[JttClient::read] failed verify chk=[%d], val=[%d]", chk, msg[i]);
    msg.length(msg.length() - 1);
    return 0;
}


// 注册
int JttClient::enregister()
{
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();
    X0100 req;
    req.state = config.sys.vehicle.province;
    req.city = config.sys.vehicle.city;
    req.vendor = config.sys.product.vendor;
    req.model = config.sys.product.model;
    req.device = mpSrvInfo->id;
    req.vehicle_plate_color = (char)config.sys.vehicle.plate_color;
    req.vehicle_plate_num = config.sys.vehicle.plate_num;
    req.vin = config.sys.vehicle.vin;

    JttMsg t = req.encode(sim, mProtVersion);
    return send(t) ? 0 : -1;
}

// 注销
int JttClient::deregister()
{
    return 0;
}

int JttClient::queryServerTime()
{
    X0004 req;
    JttMsg t = req.encode(sim, mProtVersion);
    return send(t) ? 0 : -1;
}

int JttClient::responseDriverInfo()
{
    Current st = ServiceHelper::getInstance().getStatus();
    X0702 res;
    res.state = st.ic.state;
    my::numstr2bcd(res.time, my::timestamp::YYMMDD_HHMMSS_S(st.ic.time), sizeof(res.time) * 2);
    res.ic_status = st.ic.ic_status;

    char name[128] = {0};
    my::utf8ToGbk((char*)st.ic.name.c_str(), name);
    res.name = name;

    char qc_code[128] = {0};
    my::utf8ToGbk((char*)st.ic.qc_code.c_str(), qc_code);
    res.qc_code = qc_code;

    char ca[128] = {0};
    my::utf8ToGbk((char*)st.ic.ca.c_str(), ca);
    res.ca = ca;

    my::date2bcd(st.ic.validity_date, res.validity_date);

    JttMsg msg = res.encode(sim, mProtVersion);

    if (!send(msg)) {
        logd("[0x0702] failed to send ic driver info to server.");
        return false;
    }

    return true;
}


bool JttClient::uploadEBill(my::string & ebill)
{
    X0701 r;
    r.ebillLen = ebill.length();
    my::file f;
    int len = 0;
    if (0 < (len = f.open("/data/0x8900_0xf0.log"))) {
        my::string msg(len);
        f.gets((char*)msg, len);
        f.close();
        r.ebill = msg;
    } else {
        r.ebill = ebill;
    }
    JttMsg msg = r.encode(sim, mProtVersion);
    return send(msg) ? 0 : -1;
}

// 鉴权
int JttClient::authroize()
{
    X0102 req;
    if (mbNeedAuth) {
        req.auth = mpSrvInfo->auth;
    }
    memset(req.imei, 0, sizeof(req.imei));
    memset(req.version, 0, sizeof(req.version));
    strncpy(req.imei, mpSrvInfo->imei.c_str(), sizeof(req.imei));
    strncpy(req.version, mpSrvInfo->bsp_version.c_str(), sizeof(req.version));
    JttMsg t = req.encode(sim, mProtVersion);
    return send(t) ? 0 : -1;
}

// 心跳消息
int JttClient::heart_beat()
{
    JttMsg t(sim, 0x0002, mProtVersion);
    return send(t) ? 0 : -1;
}

// 位置/状态/告警上报
int JttClient::report(const Current& st, X0200 & req)
{
    InputService & is = InputService::getInstance();
    Current * c = is.getCurrent();
    if (!c->isLbsInited()) {
        loge("lbs not init yet!");
        return 0;
    }

    Config & config = is.getConfig();
    bool acc = st.getStateAcc();
    // 里程
    req.add_mileage((my::uint)(st.car.mileage * 10));
    //speed 附加信息中填充的速度为安装选定速度包括GPS速度、模拟速度、假速度
    req.add_spd(c->lbs.speed * 10);

    // 扩展表:视频相关报警状态
    if (1 == mSpecialRecordAlarmRsp) {
        videoAlarmMask_t mask;
        mask.val = mCustom.videoAlarmMask;
        mask.set.SpecialRecordOverFlow = 0;
        req.add_extended_status(st.getExtendAlarm() & ~(mask.val));
    } else {
        req.add_extended_status(st.getExtendAlarm() & ~(mCustom.videoAlarmMask));
        if (-1 == mSpecialRecordAlarmRsp && st.getSpecialRecordOverFlow()) {
            mSpecialRecordAlarmRsp = 0;
        }
    }

    //扩展表:视频信号丢失报警状态
    videoAlarmMask_t videoAlarmMask;
    videoAlarmMask.val = mCustom.videoAlarmMask;
    if (!videoAlarmMask.set.sigalLose) {
        req.add_cam_signal_status(st.getExtSignalStatus());
    }

    //扩展表:摄像头遮挡报警状态
    if (!videoAlarmMask.set.sigalOcclusion) {
        req.add_cam_occlusion_status(st.getExtCamOcclusionStatus());
    }

    //扩展表:存储器状态
    if (!videoAlarmMask.set.storageFailure) {
        req.add_storage_status(st.getExtStorageFailStatus());
    }

    // acc off时，不产生摄像头故障
    if (!acc) {
        uint32_t m = 0;
        alarmMaskBits_t * ab = (alarmMaskBits_t*)&m;
        ab->camFail = 1;
        m = ~m;
        req.lbi.alarm_tag &= m;
    }

    // 速度小于0.1时，不超速告警
    if (c->lbs.speed < 0.1) {
        uint32_t m = 0;
        alarmMaskBits_t * ab = (alarmMaskBits_t*)&m;
        ab->speeding = 1;
        m = ~m;
        req.lbi.alarm_tag &= m;
    }

    // 告警屏蔽字
    if (mCustom.alarmMask) {
        req.lbi.alarm_tag &= mCustom.alarmMask;
    }

    // 扩展信号位
    req.add_extern_io(st.car.ext_signal);
    // 模拟量
    req.add_analogs(st.car.ad1, st.car.ad2);
    // 无线通信网络信号强度
    req.add_wireless_intensity(st.car.wireless);
    // GNSS卫星定位数
    req.add_gnss_stars(st.car.gnss_stars);

    req.add_io_status(mSleepMode);

    ext_x0200_addition_info(st, req);

    return report(req);
}

// 位置/状态/告警上报
int JttClient::report(const Current& st)
{
    ServiceHelper & sh = ServiceHelper::getInstance();
    InputService & is = InputService::getInstance();
    Current * c = is.getCurrent();
    if (!c->isLbsInited()) {
        loge("lbs not init yet!");
        return 0;
    }
    X0200 req;
    req.lbi = sh.getLocationBaseInfo(st, req.libTmS);
    req.lbi.alarm_tag &= ~mbChkAlarmBits;

    my::string t;
    bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));
    logd("[JttClient::report] alarm_tag=[0x%04x], state=[0x%04x], lat=[%d], lng=[%d], alt=[%d], speed=[%d], dir=[%d], time=[%s]"
        "custom [0x%x, %d, 0x%x]",
         req.lbi.alarm_tag, req.lbi.status, req.lbi.latitude, req.lbi.longitude,
         req.lbi.height, req.lbi.speed, req.lbi.direction, t.c_str(),
         mCustom.videoAlarmMask, mCustom.bTTSPwrShort, mCustom.alarmMask);


    return report(st, req);
}

int JttClient::report(X0200& msg)
{
    InputService & is = InputService::getInstance();
    Current * c = is.getCurrent();
    if (!c->isLbsInited()) {
        loge("lbs not init yet!");
        return 0;
    }
    JttMsg t = msg.encode(sim, mProtVersion);
    bool r = false;
    if (mProtState & AUTHORIZED) {
        r = send(t);
    }
    if (!r) {
        std::shared_ptr<minieye::AMessage> postMsg = std::make_shared<minieye::AMessage>(EVT_TYPE_PROT_DB_SAVE_RPT_DATA, shared_from_this());
        postMsg->setString("data", t.body.c_str(), t.body.length());
        postMsg->setInt32("time", msg.libTmS);
        postMsg->post();
    }
    return r ? 0 : -1;
}
int JttClient::triggerAdasAlarm(const char * evt_name)
{
    InputService & is = InputService::getInstance();
    Current st = ServiceHelper::getInstance().getStatus();
    AdasAlarm alarm;
    int ch = is.getAlgoChIdx("adas");
    EVT_TYPE e = name2value("adas", evt_name);

    if (e == EVT_TYPE_INVALID) {
        loge("bad evt name %s!", evt_name);
        return 0;
    }

    alarm.event_type = adasEvt2protEvt(e);
    alarm.evt_name = evt_name;
    //alarm.ts = (my::uint)time(nullptr);
    alarm.ts_ms = my::timestamp::milliseconds_from_19700101();
    alarm.ts = (my::uint)(alarm.ts_ms / 1000);
    alarm.speed = (my::uchar)st.getSpeed();
    value2voice("adas", e);
    return adas_report(ch, alarm);
}

int JttClient::save(const Current& st)
{
    if (mRptMsgSaveTs.elapsed() < 30 * 1000) {
        logd("skip the first 30s data.");
        return 0;
    }
//    if (!is_main_server()) {
//        return 0;
//    }
    InputService & is = InputService::getInstance();
    Current * c = is.getCurrent();
    if (!c->isLbsInited()) {
        loge("lbs not init yet!");
        return 0;
    }
    ServiceHelper &serverHelper = ServiceHelper::getInstance();
    X0200 req;
    my::uint libTmS;
    req.lbi = serverHelper.getLocationBaseInfo(st, libTmS);
    //my::string t;
    //bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));
    //logd("[JttClient::save] alarm_tag=[0x%04x], state=[0x%04x], lat=[%d], lng=[%d], alt=[%d], speed=[%d], dir=[%d], time=[%s]",
    //  req.lbi.alarm_tag, req.lbi.status, req.lbi.latitude, req.lbi.longitude,
    //  req.lbi.height, req.lbi.speed, req.lbi.direction, t.c_str());

    // 里程
    req.add_mileage((my::uint)(st.car.mileage * 10));
    //speed 附加信息中填充的速度为安装选定速度包括GPS速度、模拟速度、假速度
    req.add_spd(c->lbs.speed * 10);
    #if 0
    if (!mCustom.b0200NoAlarmAtt) {
        // 扩展表:报警状态
        req.add_extended_status(st.getExtendAlarm());

        //扩展表:视频信号丢失报警状态
        req.add_cam_signal_status(st.getExtSignalStatus());

        //扩展表:存储器状态
        req.add_storage_status(st.getExtStorageFailStatus());
    }
    #else
    Config & config = is.getConfig();
    // 扩展表:报警状态
    if (1 == mSpecialRecordAlarmRsp) {
        videoAlarmMask_t mask;
        mask.val = mCustom.videoAlarmMask;
        mask.set.SpecialRecordOverFlow = 0;
        req.add_extended_status(st.getExtendAlarm() & ~(mask.val));
    } else {
        req.add_extended_status(st.getExtendAlarm() & ~(mCustom.videoAlarmMask));
    }

    videoAlarmMask_t videoAlarmMask;
    videoAlarmMask.val = mCustom.videoAlarmMask;
    //扩展表:视频信号丢失报警状态
    if (!videoAlarmMask.set.sigalLose) {
        req.add_cam_signal_status(st.getExtSignalStatus());
    }
    //扩展表:存储器状态
    if (!videoAlarmMask.set.storageFailure) {
        req.add_storage_status(st.getExtStorageFailStatus());
    }
    #endif

    if (mCustom.alarmMask) {
        req.lbi.alarm_tag &= mCustom.alarmMask;
    }

    // 扩展信号位
    req.add_extern_io(st.car.ext_signal);
    // 无线通信网络信号强度
    req.add_wireless_intensity(st.car.wireless);
    // GNSS卫星定位数
    req.add_gnss_stars(st.car.gnss_stars);

    ext_x0200_addition_info(st, req);

    JttMsg tt = req.encode(sim, mProtVersion);

    std::shared_ptr<minieye::AMessage> msg = std::make_shared<minieye::AMessage>(EVT_TYPE_PROT_DB_SAVE_RPT_DATA, shared_from_this());
    msg->setString("data", tt.body.c_str(), tt.body.length());
    msg->setInt32("time", libTmS);
    msg->post();

    return 0;
}
bool JttClient::check_TPMS()
{
    bool bRpt = false;
    int tempHiThrs = 0;
    float tyrePressThrsHi = 0, tyrePressThrsLo = 0;
    std :: map < int, float > tyreTemps;
    std :: map < int, float > tyrePresses;
    InputService & is = InputService::getInstance();
    Current * current = is.getCurrent();
    int32_t ret = ExpandReceiver::getInstance().getTpmsSensorMessage(tempHiThrs, tyrePressThrsHi, tyrePressThrsLo, tyreTemps, tyrePresses);
    if (!ret) {
        Current st = ServiceHelper::getInstance().getStatus();
        TpmsAlarm tpms;

        tpms.ts_ms = my::timestamp::utc_milliseconds();
        tpms.ts = tpms.ts_ms / 1000;
        tpms.speed = (my::uchar)st.getSpeed();
        tpms.evt_name = value2name("tpms", EVT_TYPE_TPMS);
        int tyreNum = 0;
        for (auto it : tyreTemps) {
            struct TyreData td;
            memset(&td, 0, sizeof(td));
            td.tyreIdx = it.first;
            auto itp = tyrePresses.find(it.first);
            if (itp == tyrePresses.end()) {
                loge("not find tyre %d press record!", it.first);
            }
            td.tyrePressure = (my::ushort)itp->second;
            td.tyreTemp = (short)it.second;
            if (td.tyrePressure && td.tyreTemp) {
                tyreNum++;
            }
            td.batteryPwr = 0;

            if (tyrePressThrsHi && td.tyrePressure && (td.tyrePressure > tyrePressThrsHi)) {
                bRpt = true;
                td.alarmBits |= (1 << 1);//胎压过高
            }
            if (tyrePressThrsLo && td.tyrePressure && (td.tyrePressure < tyrePressThrsLo)) {
                bRpt = true;
                td.alarmBits |= (1 << 2);//胎压过低
            }
            if (tempHiThrs && td.tyreTemp && (td.tyreTemp > tempHiThrs)) {
                bRpt = true;
                td.alarmBits |= (1 << 3);//温度异常
            }
            if (!td.tyrePressure || !td.tyreTemp) {
                td.alarmBits |= (1 << 4);//传感器异常
                bRpt = true;
            }
            if (!td.alarmBits) {
                td.alarmBits = 1;//周期上报
            }
            tpms.tyreData.push_back(td);
        }
        //alarmMaskBits_t * pw = (alarmMaskBits_t*)&current->alarm;
        //pw->tirePressureAlarm = bRpt;
        current->setAlarmtirePressure(bRpt);

        if (tyreNum && (tyreNum == tyrePresses.size())) {//周期上报
            bRpt = true;
        }
        if (bRpt) {
            tpms_report(-1, tpms);
        }
    }

    return bRpt;
}
int JttClient::check_alarm(bool rpt)
{
    AreaHelper & ah = AreaHelper::getInstance();
    ServiceHelper & sh = ServiceHelper::getInstance();
    Current st = sh.getStatus();

    X0200 loc;
    loc.lbi = sh.getLocationBaseInfo(st);

    std::vector<Area::Ptr> areas;
    int ret = ah.getAreaList(areas);
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::check_alarm] failed to get areas.");

    int alarm = 0;
    for (auto it = areas.begin(); it != areas.end(); ++it) {
        auto areaMap = mAreaStatus.find((*it)->id);
        if (areaMap != mAreaStatus.end()) {
            (*it)->setLocStatus(areaMap->second);
        } else {
            mAreaStatus[(*it)->id] = 0;
        }

        //logd("check area %d high speed %d", (*it)->id, (*it)->getHighSpeed());

        if ((*it)->checkAlarm(this, st, loc)) {
            alarm++;
        }

        areaMap = mAreaStatus.find((*it)->id);
        if (areaMap != mAreaStatus.end()) {
            areaMap->second = (*it)->getLocStatus();
        } else {
            mAreaStatus[(*it)->id] = (*it)->getLocStatus();
        }
    }

    if (alarm > 0) {
        int id = 0;
        DbHelper & dbh = DbHelper::getInstance();
        int ret = dbh.fetchAlarmId(id);
        if (ret == 0) {
            // 需要人工确认报警的s事件ID
            loc.lai_list[0x04] = (loc::getAlarmItem((my::ushort)id));
            /* 过检测试记录人工确认报警消息时间，避免在立即上报人工确认消息后短时间内再次触发定时上报 */
            artificialAlarmTime = (my::uint64)my::timestamp::now();
            logd("artificialAlarmTime:%d, id:%d!\n", artificialAlarmTime, id);
        } else {
            logd("fetchAlarmId failed!\n");
        }
    }

    if (st.getAlarmRolloverWarning()) {
        /* 侧翻 */
        if (!mRollover) {
            alarm++;
            mRollover = true;
        }
    } else {
        mRollover = false;
    }

    if (st.getAlarmCollisionWarning()) {
        /* 碰撞 */
        if (!mCollision) {
            alarm++;
            mCollision = true;
        }
    } else {
        mCollision = false;
    }

    if (rpt) {
        // 无线通信网络信号强度
        loc.add_wireless_intensity(st.car.wireless);
        // GNSS卫星定位数
        loc.add_gnss_stars(st.car.gnss_stars);
        return alarm > 0 ? report(loc) : 0;
    }
    return rpt;
}

// 检查待上报信息
int JttClient::check_report_data()
{
    if (!is_main_server()) {
        return 0;
    }
    // 上报数据
    DbHelper & dbh = DbHelper::getInstance();
    std::vector<ReportData> rds;
    int r = dbh.getReportDataList(rds, tag);
    LOG_RETURN_IF(r != 0, -1, loge, "[JttClient::check_report_data] Failed to get report data list.");

    if (!rds.empty()) {
        X0704 rsp;
        rsp.num = (my::ushort)rds.size();
        rsp.type = 1;
        rsp.data << my::hton;

        for (auto it = rds.begin(); it != rds.end(); ++it) {
            my::ushort len = it->data.length();
            rsp.data << len;
            rsp.data.append(it->data);
        }

        JttMsg msg = rsp.encode(sim, mProtVersion);
        logi("[JttClient::check_report_data] send report data, size=[%d]", rsp.num);

        if (send(msg)) {
            for (auto it = rds.begin(); it != rds.end(); ++it) {
                LOG_IF(dbh.delReportedData(it->id) != 0, logw,
                       "[JttClient::check_report_data] Failed to delete report data, id=[%d]", it->id);
            }

        } else {
            logw("[JttClient::check_report_data] failed to send report data.");
            return -1;
        }
    }

    return 0;
}

// 检查IC卡状态
int JttClient::check_iccard()
{
    Current st = ServiceHelper::getInstance().getStatus();

    //logi("check_iccard state change %d -> %d", iccard_state, st.ic.state);
    if (st.ic.seq > 0) {
        bool report = false;

        if (iccard_state != st.ic.state) {
            logi("check_iccard state change %d -> %d ic_status %d name %s qc_code %s ca %s ",
                 iccard_state, st.ic.state, st.ic.ic_status, st.ic.name.c_str(), st.ic.qc_code.c_str(), st.ic.ca.c_str());
            iccard_state = st.ic.state;
            report = true;
        }

        if (report) {
            X0702 res;
            res.state = st.ic.state;
            my::numstr2bcd(res.time, my::timestamp::YYMMDD_HHMMSS_S(st.ic.time), sizeof(res.time) * 2);
            res.ic_status = st.ic.ic_status;

            char name[128] = {0};
            my::utf8ToGbk((char*)st.ic.name.c_str(), name);
            res.name = name;

            char qc_code[128] = {0};
            my::utf8ToGbk((char*)st.ic.qc_code.c_str(), qc_code);
            res.qc_code = qc_code;

            char ca[128] = {0};
            my::utf8ToGbk((char*)st.ic.ca.c_str(), ca);
            res.ca = ca;
            my::date2bcd(st.ic.validity_date, res.validity_date);
            JttMsg msg = res.encode(sim, mProtVersion);

            if (!send(msg)) {
                loge("[X0702] failed to send ic driver info to server.");
                return -1;
            }
        }
    }

    return 0;
}

void JttClient::check_bgn_end_time(int alarm_type, time_t cur, time_t &bgn, time_t &end)
{
    int seek = -5;
    int duration = 10;

    bgn = cur + seek;
    end = cur + seek + duration;

    char propValue[PROP_VALUE_MAX] = {0};

    if (__system_property_get(PROP_PERSIST_808_ATT_SEEK, propValue) > 0) {
        if (propValue[0]) {
            seek = atoi(propValue);
            logd("seek = %d", seek);
            bgn = cur + seek;
        }
    }
    memset(propValue, 0, sizeof(propValue));
    if (__system_property_get(PROP_PERSIST_808_ATT_DURATION, propValue) > 0) {
        if (propValue[0]) {
            duration = atoi(propValue);
            logd("duration = %d", duration);
            end = cur + seek + duration;
        }
    }
}

void JttClient::get_attachCh(const char *algName, std::vector<int> & chVector){
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();
    conf_t & cfg = config.sys;
    uint32_t attachChBitTbl = 0;
    for (int i = 0; i < (cfg.cameras + cfg.ipcs); ++i){
        if(strcmp(algName, cfg.ch[i].ai.func) == 0) {
            attachChBitTbl = cfg.ch[i].ai.attachChBitTbl;
            attachChBitTbl |= (1 << i);//默认加当前通道
            break;
        }
    }

    logd("get_attachCh %s, attachChBitTbl = %x!\n", algName,  attachChBitTbl);
    if (attachChBitTbl){
        for (int i = 0; i < 32; i++){
            if (attachChBitTbl & (0x1 << i)) {
                chVector.push_back(i + 1);
            }
        }
    }
}

void JttClient::clearStoppedFtpTsk()
{
    if (mFtpTasks.size()) {
        auto it = mFtpTasks.begin();
        while (it != mFtpTasks.end()) {
            shared_ptr<JttFtpUpload> sp = it->second;
            if (!sp->working()) {
                it = mFtpTasks.erase(it);

            } else {
                it++;
            }
        }
    }

}

int JttClient::creat_dms_accessory(int ch, DmsAlarm *dms, AlarmInfoItem *alarm_info, my::string *path)
{
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();
    int sendAtt = 1;
    for (int i = 0; i < MAX_CLIENTS; i++) {
        char tagStr[32];
        snprintf(tagStr, 32, "conn%d", i + 1);
        if ((config.sys.net[i].prot_type == "jtt808-1078") && (tag == (const char *)tagStr)) {
            IP_CTL_BITS * bits = (IP_CTL_BITS*)&config.sys.net[i].ipCtlBits[mode];
            logd("bits->attEnUpload %d", bits->attEnUpload);
            sendAtt = !!bits->attEnUpload;
            sendAtt &= !config.sys.net[i].is_algo_evt_att_disable("dms", dms->evt_name.c_str());
            break;
        }
    }

    if (sendAtt &&
        ((dms->level >= attSndLvl(0x65, dms->event_type)) ||
        (dms->evt_name == "snapshotDMS") ||
        (dms->evt_name == "eyeocclusion") ||
        (dms->evt_name == "occlusion") ||
        (dms->evt_name == "mask"))) {
        int picNum = get_picture_num(dms->event_type);
        time_t bgn = 0, end = 0;
        time_t cur = time(nullptr);

        if (access("/data/no_video_att", R_OK)) {
            check_bgn_end_time(dms->event_type, cur, bgn, end);
        }

        if (dms->evt_name == "snapshotDMS") {
            bgn = end = 0;
        }

        // 创建目录
        path->assignf("/mnt/obb/mprot/%s_ch%d_%s_%02x", my::timestamp::YYYYMMDD_HHMMSS_MS(dms->ts_ms).c_str(), ch, dms->evt_name.c_str(), dms->alarm_tag.sn);
        my::file::mkdir(path->c_str());

        // 通道附件生成及信息填充
        std::vector<int>    accessoryCh;
        get_attachCh("dms", accessoryCh);
        if ((dms->evt_name != "snapshotDMS") && (accessoryCh.size() > 0)) {
            for (int channel = 0; channel < accessoryCh.size(); ++channel) {
                /* pic */
                int pics = (accessoryCh[channel] == ch) * picNum;
                for (int32_t i = 0; i < pics; i++) {
                    /* 生成pic附件 */
                    my::string picCmd;
                    my::string fileName;
                    fileName.assignf("%s/%s.jpg", path->c_str(), my::to_string(i).c_str());
                    picCmd.assignf("cmd lms %d %s %d %d %d", accessoryCh[channel], fileName.c_str(), 1280, 720, 1);

                    if (!LogCallProxyCmd::sendReq("media", picCmd.c_str())) {//ask media to product picture files
                       loge("cmd failed %s", picCmd.c_str());
                    }
                    
                    /* pic附件信息填充 */
                    AlarmInfoAtItem atJpg;
                    atJpg.path.assignf("%s/%d.jpg", path->c_str(), i);
                    atJpg.size = 0;
                    atJpg.at_state = 0;
                    alarm_info->attachment.push_back(atJpg);    // 增加pic附件
                    dms->alarm_tag.cnt++;
                }

                /* video */
                if (access("/data/no_video_att", R_OK)) { // 是否发送视频附件
                    /* 生成video附件 */
                    my::string attCmd;
                    //"[event] [sn] [ch] [storage path] [previous seconds of video] [next seconds of video] [number of pictures].\n"
                    attCmd.assignf("cmd snapshot %d %d %d %s %d %d", dms->event_type, dms->alarm_tag.sn,
                                                                            accessoryCh[channel], path->c_str(), bgn, end);
                    if (!LogCallProxyCmd::sendReq("muxer", attCmd.c_str())) {//ask media to product media files
                        loge("cmd failed %s", attCmd.c_str());
                    }

                    /* video附件信息填充 */
                    AlarmInfoAtItem at;
                    at.path.assignf("%s/%d.mp4", path->c_str(), accessoryCh[channel]);
                    at.size = 0;
                    at.at_state = 0;
                    alarm_info->attachment.push_back(at);    // 增加附件
                    dms->alarm_tag.cnt++;
                    
                }
            }
        }

        logd("dms.alarm_tag.cnt %d, sendAtt %d", dms->alarm_tag.cnt, sendAtt);
        alarm_info->alarm_tag = dms->alarm_tag;      // 报警标识
    }
    return 0;
}

int JttClient::creat_adas_accessory(int ch, AdasAlarm *adas, AlarmInfoItem *alarm_info, my::string *path)
{
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();
    int sendAtt = 1;
    for (int i = 0; i < MAX_CLIENTS; i++) {
        char tagStr[32];
        snprintf(tagStr, 32, "conn%d", i + 1);
        if ((config.sys.net[i].prot_type == "jtt808-1078") && (tag == (const char *)tagStr)) {
            IP_CTL_BITS * bits = (IP_CTL_BITS*)&config.sys.net[i].ipCtlBits[mode];
            sendAtt = !!bits->attEnUpload;
            sendAtt &= !config.sys.net[i].is_algo_evt_att_disable("adas", adas->evt_name.c_str());
            logd("sendAtt %d", sendAtt);
            break;
        }
    }

    if (sendAtt &&
        ((adas->level >= attSndLvl(0x64, adas->event_type)) ||
        (adas->evt_name == "BLUR") ||
        (adas->evt_name == "PCW") ||
        (adas->evt_name == "FCW"))) {
        int picNum = get_picture_num(adas->event_type);
        time_t bgn = 0, end = 0;
        time_t cur = time(nullptr);
        if (access("/data/no_video_att", R_OK)) {
            check_bgn_end_time(adas->event_type, cur, bgn, end);
        }
        if (adas->evt_name == "snapshotADAS") {
            bgn = end = 0;
        }

        // 创建目录
        path->assignf("/mnt/obb/mprot/%s_ch%d_%s_%02x", my::timestamp::YYYYMMDD_HHMMSS_MS(adas->ts_ms).c_str(), ch, adas->evt_name.c_str(), adas->alarm_tag.sn);
        my::file::mkdir(path->c_str());

        // 通道附件生成及信息填充
        std::vector<int>    accessoryCh;
        get_attachCh("adas", accessoryCh);
        int chCnt = accessoryCh.size();
        if ((adas->evt_name != "snapshotADAS") && (chCnt > 0)) {
            for (int channel = 0; channel < chCnt; ++channel) {
                int pics = (accessoryCh[channel] == ch) * picNum;
                for (int32_t i = 0; i < pics; i++) {
                    /* 生成pic附件 */
                    my::string picCmd;
                    my::string fileName;
                    fileName.assignf("%s/%s.jpg", path->c_str(), my::to_string(i).c_str());
                    picCmd.assignf("cmd lms %d %s %d %d %d", accessoryCh[channel], fileName.c_str(), 1280, 720, 1);

                    if (!LogCallProxyCmd::sendReq("media", picCmd.c_str())) {//ask media to product picture files
                       loge("cmd failed %s", picCmd.c_str());
                    }

                    /* 填充pic附件信息 */
                   AlarmInfoAtItem atJpg;
                   atJpg.path.assignf("%s/%d.jpg", path->c_str(), i);
                   atJpg.size = 0;
                   atJpg.at_state = 0;
                   alarm_info->attachment.push_back(atJpg);    // 增加附件
                   adas->alarm_tag.cnt++;
                }

                if (access("/data/no_video_att", R_OK)) {
                    /* 生成video附件 */
                    my::string attCmd;
                    attCmd.assignf("cmd snapshot %d %d %d %s %d %d",
                        adas->event_type, adas->alarm_tag.sn, accessoryCh[channel], path->c_str(), bgn, end);

                    if (!LogCallProxyCmd::sendReq("muxer", attCmd.c_str())) {//ask muxer to product video files
                       loge("cmd failed %s", attCmd.c_str());
                    } else {
                       loge("cmd  %s!", attCmd.c_str());
                    }

                    /* 填充video附件信息 */
                   AlarmInfoAtItem at;
                   at.path.assignf("%s/%d.mp4", path->c_str(), accessoryCh[channel]);
                   at.size = 0;
                   at.at_state = 0;
                   alarm_info->attachment.push_back(at);    // 增加附件
                   adas->alarm_tag.cnt++;
                }
            }
        }

        if (adas->evt_name == "TSR") {
            /* 限速告警仅本地存储不上传至平台 */
           adas->alarm_tag.cnt = 0;
        }

        logd("adas.alarm_tag.cnt %d, sendAtt %d", adas->alarm_tag.cnt, sendAtt);
        alarm_info->alarm_tag = adas->alarm_tag;      // 报警标识
    }
    return 0;
}


int JttClient::creat_speeding_accessory(int ch, speedingAlarm *speeding, AlarmInfoItem *alarm_info, my::string *path)
{
    int sendAtt = 1;
    if (sendAtt) {
        int picNum = get_picture_num(speeding->event_type);
        time_t bgn = 0, end = 0;
        time_t cur = time(nullptr);

        path->assignf("/mnt/obb/mprot/%s_ch%d_%s_%02x", my::timestamp::YYYYMMDD_HHMMSS_MS(speeding->ts_ms).c_str(), 
                                                            1, speeding->evt_name.c_str(), speeding->alarm_tag.sn);
        my::file::mkdir(path->c_str());

        //pic
        for (int i = 0; i < picNum; i++) {
            /* 生成pic附件 */
            my::string picCmd;
            my::string fileName;
            fileName.assignf("%s/%s.jpg", path->c_str(), my::to_string(i).c_str());
            picCmd.assignf("cmd lms %d %s %d %d %d", 1, fileName.c_str(), 1280, 720, 1);

            if (!LogCallProxyCmd::sendReq("media", picCmd.c_str())) {//ask media to product picture files
               loge("cmd failed %s", picCmd.c_str());
            }
            
            /* 填充pic附件信息 */
            AlarmInfoAtItem atJpg;
            atJpg.path.assignf("%s/%d.jpg", path->c_str(), i);
            atJpg.size = 0;
            atJpg.at_state = 0;
            alarm_info->attachment.push_back(atJpg);    // 增加附件
            speeding->alarm_tag.cnt++;
        }

        /* video */
        if (access("/data/no_video_att", R_OK)) {
            check_bgn_end_time(speeding->event_type, cur, bgn, end);

            /* 生成video附件 */
            path->assignf("/mnt/obb/mprot/%s_ch%d_%s_%02x", my::timestamp::YYYYMMDD_HHMMSS_MS(speeding->ts_ms).c_str(), 1, speeding->evt_name.c_str(), speeding->alarm_tag.sn);
            my::file::mkdir(path->c_str());
            my::string attCmd;
            attCmd.assignf("cmd snapshot %d %d %d %s %d %d", speeding->event_type, speeding->alarm_tag.sn, 1, path->c_str(), bgn, end);

            if (!LogCallProxyCmd::sendReq("muxer", attCmd.c_str())) {//ask muxer to product media files
                loge("cmd failed %s", attCmd.c_str());
            }

            /* 填充video附件信息 */
            AlarmInfoAtItem at;
            at.path.assignf("%s/%d.mp4", path->c_str(), 1);
            at.size = 0;
            at.at_state = 0;
            alarm_info->attachment.push_back(at);    // 增加附件
            speeding->alarm_tag.cnt++;
            
        }
        logd("speeding.alarm_tag.cnt %d, sendAtt %d", speeding->alarm_tag.cnt, sendAtt);
        alarm_info->alarm_tag = speeding->alarm_tag;      // 报警标识
    }
    return 0;
}

int JttClient::creat_over_height_accessory(int ch, overHeightAlarm *overHeight, AlarmInfoItem *alarm_info, my::string *path)
{
    int sendAtt = 1;

    if (sendAtt) {
        int picNum = get_picture_num(overHeight->event_type);
        time_t bgn = 0, end = 0;
        time_t cur = time(nullptr);

        path->assignf("/mnt/obb/mprot/%s_ch%d_%s_%02x", my::timestamp::YYYYMMDD_HHMMSS_MS(overHeight->ts_ms).c_str(), ch, overHeight->evt_name.c_str(), overHeight->alarm_tag.sn);
        my::file::mkdir(path->c_str());

        /* pic */
        for (int i = 0; i < picNum; i++) {
            /* 生成pic附件 */
            my::string picCmd;
            my::string fileName;
            fileName.assignf("%s/%s.jpg", path->c_str(), my::to_string(i).c_str());
            picCmd.assignf("cmd lms %d %s %d %d %d", 1, fileName.c_str(), 1280, 720, 1);

            if (!LogCallProxyCmd::sendReq("media", picCmd.c_str())) {//ask media to product picture files
               loge("cmd failed %s", picCmd.c_str());
            }
            /* 填充pic附件信息 */
            AlarmInfoAtItem atJpg;
            atJpg.path.assignf("%s/%d.jpg", path->c_str(), i);
            atJpg.size = 0;
            atJpg.at_state = 0;
            alarm_info->attachment.push_back(atJpg);    // 增加附件
            overHeight->alarm_tag.cnt++;
        }

        /* video */
        if (access("/data/no_video_att", R_OK)) {
            check_bgn_end_time(overHeight->event_type, cur, bgn, end);
            /* 生成video附件 */
            my::string attCmd;
            attCmd.assignf("cmd snapshot %d %d %d %s %d %d", overHeight->event_type, overHeight->alarm_tag.sn, ch, path->c_str(), bgn, end);

            if (!LogCallProxyCmd::sendReq("muxer", attCmd.c_str())) {//ask muxer to product media files
                loge("cmd failed %s", attCmd.c_str());
            }
            
            /* 填充video附件信息 */
            AlarmInfoAtItem at;
            at.path.assignf("%s/%d.mp4", path->c_str(), 1);
            at.size = 0;
            at.at_state = 0;
            alarm_info->attachment.push_back(at);    // 增加附件
            overHeight->alarm_tag.cnt++;
        }
        logd("overHeight.alarm_tag.cnt %d, sendAtt %d", overHeight->alarm_tag.cnt, sendAtt);
        alarm_info->alarm_tag = overHeight->alarm_tag;      // 报警标识
    }
    return 0;
}

int JttClient::creat_over_load_accessory(int ch, overLoadAlarm *overLoad, AlarmInfoItem *alarm_info, my::string *path)
{
    int sendAtt = 1;

    if (sendAtt) {
        int picNum = get_picture_num(overLoad->event_type);
        time_t bgn = 0, end = 0;
        time_t cur = time(nullptr);

        path->assignf("/mnt/obb/mprot/%s_ch%d_%s_%02x", my::timestamp::YYYYMMDD_HHMMSS_MS(overLoad->ts_ms).c_str(), ch, overLoad->evt_name.c_str(), overLoad->alarm_tag.sn);
        my::file::mkdir(path->c_str());

        /* pic */
        for (int i = 0; i < picNum; i++) {
            /* 生成pic附件 */
            my::string picCmd;
            my::string fileName;
            fileName.assignf("%s/%s.jpg", path->c_str(), my::to_string(i).c_str());
            picCmd.assignf("cmd lms %d %s %d %d %d", 1, fileName.c_str(), 1280, 720, 1);

            if (!LogCallProxyCmd::sendReq("media", picCmd.c_str())) {//ask media to product picture files
               loge("cmd failed %s", picCmd.c_str());
            }
            /* 填充pic附件信息 */
            AlarmInfoAtItem atJpg;
            atJpg.path.assignf("%s/%d.jpg", path->c_str(), i);
            atJpg.size = 0;
            atJpg.at_state = 0;
            alarm_info->attachment.push_back(atJpg);    // 增加附件
            overLoad->alarm_tag.cnt++;
        }
        
        /* video */
        if (access("/data/no_video_att", R_OK)) {
            check_bgn_end_time(overLoad->event_type, cur, bgn, end);
            /* 生成video附件 */

            my::string attCmd;
            attCmd.assignf("cmd snapshot %d %d %d %s %d %d", overLoad->event_type, overLoad->alarm_tag.sn, ch, path->c_str(), bgn, end);

            if (!LogCallProxyCmd::sendReq("muxer", attCmd.c_str())) {//ask muxer to product media files
                loge("cmd failed %s", attCmd.c_str());
            }
            /* 填充video附件信息 */
            AlarmInfoAtItem at;
            at.path.assignf("%s/%d.mp4", path->c_str(), 1);
            at.size = 0;
            at.at_state = 0;
            alarm_info->attachment.push_back(at);    // 增加附件
            overLoad->alarm_tag.cnt++;
        }

        logd("overLoad.alarm_tag.cnt %d, sendAtt %d", overLoad->alarm_tag.cnt, sendAtt);
        alarm_info->alarm_tag = overLoad->alarm_tag;      // 报警标识
    }
    return 0;
}

// 胎压监测
int JttClient::creat_tpms_accessory(int ch, TpmsAlarm *tpms, AlarmInfoItem *alarm_info, my::string *path)
{
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();

    int sendAtt = 1;
    for (int i = 0; i < MAX_CLIENTS; i++) {
        char tagStr[32];
        snprintf(tagStr, 32, "conn%d", i + 1);
        if ((config.sys.net[i].prot_type == "jtt808-1078") && (tag == (const char *)tagStr)) {
            IP_CTL_BITS * bits = (IP_CTL_BITS*)&config.sys.net[i].ipCtlBits[mode];
            sendAtt = !!bits->attEnUpload;
            sendAtt &= !config.sys.net[i].is_algo_evt_att_disable("tpms", "all");
            break;
        }
    }

    if (ch > 0) { //bsd.speed > mAlarmLvlSpdThrs)
        int picNum = get_picture_num(alarm_info->event_type);
        time_t bgn = 0, end = 0;
        time_t cur = time(nullptr);

        path->assignf("/mnt/obb/mprot/%s_ch%d_%s_%02x", my::timestamp::YYYYMMDD_HHMMSS_MS(tpms->ts_ms).c_str(), ch, tpms->evt_name.c_str(), tpms->alarm_tag.sn);
        my::file::mkdir(path->c_str());

        /* pic */
        for (int i = 0; i < picNum; i++) {
            /* 生成pic附件 */
            my::string picCmd;
            my::string fileName;
            fileName.assignf("%s/%s.jpg", path->c_str(), my::to_string(i).c_str());
            picCmd.assignf("cmd lms %d %s %d %d %d", 1, fileName.c_str(), 1280, 720, 1);

            if (!LogCallProxyCmd::sendReq("media", picCmd.c_str())) {//ask media to product picture files
               loge("cmd failed %s", picCmd.c_str());
            }
            /* 填充pic附件信息 */
            AlarmInfoAtItem atJpg;
            atJpg.path.assignf("%s/%d.jpg", path->c_str(), i);
            atJpg.size = 0;
            atJpg.at_state = 0;
            alarm_info->attachment.push_back(atJpg);    // 增加附件
            tpms->alarm_tag.cnt++;
        }
        
        /* video */
        if (access("/data/no_video_att", R_OK)) {
            check_bgn_end_time(0, cur, bgn, end);
            /* 生成video附件 */
            my::string attCmd;
            attCmd.assignf("cmd snapshot %d %d %d %s %d %d", EVT_TYPE_TPMS, tpms->alarm_tag.sn, ch, path->c_str(), bgn, end);

            if (!LogCallProxyCmd::sendReq("muxer", attCmd.c_str())) {//ask muxer to product media files
                loge("cmd failed %s", __FUNCTION__, attCmd.c_str());
            }

            /* 填充video附件信息 */
            AlarmInfoAtItem at;
            at.path.assignf("%s/%d.mp4", path->c_str(), ch);
            at.size = 0;
            at.at_state = 0;
            alarm_info->attachment.push_back(at);    // 增加附件
            tpms->alarm_tag.cnt++;
        }

        tpms->alarm_tag.cnt *= sendAtt;
        logd("tpms.alarm_tag.cnt %d, sendAtt %d", tpms->alarm_tag.cnt, sendAtt);

        alarm_info->alarm_tag = tpms->alarm_tag;      // 报警标识
    }
    return 0;
}


int JttClient::creat_bsd_accessory(int ch, BsdAlarm *bsd, AlarmInfoItem *alarm_info, my::string *path)
{
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();

    int sendAtt = 1;
    for (int i = 0; i < MAX_CLIENTS; i++) {
        char tagStr[32];
        snprintf(tagStr, 32, "conn%d", i + 1);
        if ((config.sys.net[i].prot_type == "jtt808-1078") && (tag == (const char *)tagStr)) {
            IP_CTL_BITS * bits = (IP_CTL_BITS*)&config.sys.net[i].ipCtlBits[mode];
            sendAtt = !!bits->attEnUpload;
            sendAtt &= !config.sys.net[i].is_algo_evt_att_disable("bsd", bsd->evt_name.c_str());
            break;
        }
    }

    if (sendAtt) { //bsd.speed > mAlarmLvlSpdThrs)
        time_t bgn = 0, end = 0;
        time_t cur = time(nullptr);
        
        /* 创建目录 */
        path->assignf("/mnt/obb/mprot/%s_ch%d_%s_%02x", my::timestamp::YYYYMMDD_HHMMSS_MS(bsd->ts_ms).c_str(), ch, bsd->evt_name.c_str(), bsd->alarm_tag.sn);
        my::file::mkdir(path->c_str());

        /* pic */
        int picNum = get_picture_num(bsd->event_type);
        for (int i = 0; i < picNum; i++) {
            /* 生成pic附件 */
            my::string picCmd;
            my::string fileName;
            fileName.assignf("%s/%s.jpg", path->c_str(), my::to_string(i).c_str());
            picCmd.assignf("cmd lms %d %s %d %d %d", ch, fileName.c_str(), 1280, 720, 1);

            if (!LogCallProxyCmd::sendReq("media", picCmd.c_str())) {//ask media to product picture files
               loge("cmd failed %s", picCmd.c_str());
            }
            /* 填充pic附件信息 */
            AlarmInfoAtItem atJpg;
            atJpg.path.assignf("%s/%d.jpg", path->c_str(), i);
            atJpg.size = 0;
            atJpg.at_state = 0;
            alarm_info->attachment.push_back(atJpg);    // 增加附件
            bsd->alarm_tag.cnt++;
        }

        /* video */
        if (access("/data/no_video_att", R_OK)) {
            check_bgn_end_time(bsd->event_type, cur, bgn, end);
            /* 生成video附件 */
            my::string attCmd;
            attCmd.assignf("cmd snapshot %d %d %d %s %d %d", bsd->event_type, bsd->alarm_tag.sn, ch, path->c_str(), bgn, end);

            if (!LogCallProxyCmd::sendReq("muxer", attCmd.c_str())) {//ask muxer to product media files
                loge("[JttClient::%s] cmd failed %s", __FUNCTION__, attCmd.c_str());
            }
            /* 填充video附件信息 */
            AlarmInfoAtItem at;
            at.path.assignf("%s/%d.mp4", path->c_str(), ch);
            at.size = 0;
            at.at_state = 0;
            alarm_info->attachment.push_back(at);    // 增加附件
            bsd->alarm_tag.cnt++;
        }

        bsd->alarm_tag.cnt *= sendAtt;
        logd("bsd.alarm_tag.cnt %d, sendAtt %d", bsd->alarm_tag.cnt, sendAtt);

        alarm_info->alarm_tag = bsd->alarm_tag;      // 报警标识
    }
    return 0;
}


int JttClient::tpms_report(int ch, TpmsAlarm & tpms)
{
    ServiceHelper & sh = ServiceHelper::getInstance();
    DbHelper & dh = DbHelper::getInstance();
    AlarmHelper & ah = AlarmHelper::getInstance();
    Current st = sh.getStatus();
    
    X0200 req;
    req.lbi = sh.getLocationBaseInfo(st);

    my::string t;
    bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));

    // 填写tpms相关信息

    int ret = dh.fetchAlarmId(tpms.alarm_id);
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::%s] failed to fetch alarm id.", __FUNCTION__);
    tpms.alt = (short)st.lbs.alt;
    tpms.lat = (my::uint)(st.lbs.lat * 1000000);
    tpms.lng = (my::uint)(st.lbs.lng * 1000000);
    tpms.car_state.acc       = st.getStateAcc();
    tpms.car_state.location  = (1 == st.lbs.status);
    tpms.car_state.left_signal   = st.sensor.left_turn;
    tpms.car_state.right_signal  = st.sensor.right_turn;
    tpms.car_state.brakes        = st.sensor.brake;
    tpms.car_state.card          = st.getIcCardState();
    tpms.alarm_tag.term_id = mpSrvInfo->id;
    tpms.alarm_tag.ts = tpms.ts;
    tpms.alarm_tag.sn = ah.fetchSn(tpms.ts);
    tpms.alarm_tag.cnt = 0;

    // 保存信息到数据库
    AlarmInfoItem alarm_info;
    alarm_info.protVer = (mpSrvInfo->attSndProtVer < 0) ? mProtVersion : mpSrvInfo->attSndProtVer;
    alarm_info.prot_subtype = prot_subtype;
    alarm_info.term_id = mpSrvInfo->id;        // 终端号
    alarm_info.ts = tpms.ts;     // 时间
    alarm_info.alarm_tag = tpms.alarm_tag;       // 报警标识
    alarm_info.state = 0;
    alarm_info.channel = 0x66;
    alarm_info.event_type = 0;

    /* 生成附件 */
    my::string path;
    creat_tpms_accessory(ch, &tpms, &alarm_info, &path);
    if (mpMediaMgr) {
        mpMediaMgr->addMediaPath(path);
    }
    /* 保存数据库 */
    ret = dh.addAlarmInfo(alarm_info);

    if (ret != 0) {
        loge("[JttClient::%s] addAlarmInfo FAIL!", __FUNCTION__);
        return -1;
    }

    logd("----- tpms report ----- speed = %d, event %s, mProtVersion:%d!\n", tpms.speed, tpms.evt_name.c_str(), mProtVersion);
    req.lai_list[alarm_info.channel] = (tpms.str(mProtVersion, prot_subtype));
    if (mProtVersion) {
        my::string data;
        data << (my::uchar)0x05 << (my::uchar)30;
        for (struct TyreData & td : tpms.tyreData) {
            my::uchar d = td.tyrePressure;
            data << d;
        }
        int l = 30 - data.length();
        my::uchar pad = 0xff;
        for (int i = 0; i < l; i++) {
            data << pad;
        }
        req.lai_list[0x05] = data;
    }
    auto it = alarm_info.attachment.begin();

    while (it != alarm_info.attachment.end()) {
        add2cp(it->path.c_str());
        it++;
    }

    return report(st, req);
}

// bsd上报接口
int JttClient::bsd_report(int ch, BsdAlarm & bsd)
{
    InputService & is = InputService::getInstance();
    OutputService &os = OutputService::getInstance();
    
    Config & config = is.getConfig();
    ServiceHelper & sh = ServiceHelper::getInstance();
    DbHelper & dh = DbHelper::getInstance();
    AlarmHelper & ah = AlarmHelper::getInstance();
    conf_t & cfg = config.sys;
    Current st = sh.getStatus();
    X0200 req;
    req.lbi = sh.getLocationBaseInfo(st);

    my::string t;
    bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));

    os.triggerAlertor();
    os.camFullScreen(ch, 10);
    os.displayWarnBmp(bsd.evt_level);

    // 填写bsd相关信息
    int ret = dh.fetchAlarmId(bsd.alarm_id);
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::%s] failed to fetch alarm id.", __FUNCTION__);
    bsd.alt = (short)st.lbs.alt;
    bsd.lat = (my::uint)(st.lbs.lat * 1000000);
    bsd.lng = (my::uint)(st.lbs.lng * 1000000);
    bsd.car_state.acc       = st.getStateAcc();
    bsd.car_state.location  = (1 == st.lbs.status);
    bsd.car_state.left_signal   = st.sensor.left_turn;
    bsd.car_state.right_signal  = st.sensor.right_turn;
    bsd.car_state.brakes        = st.sensor.brake;
    bsd.car_state.card          = st.getIcCardState();
    bsd.alarm_tag.term_id = mpSrvInfo->id;
    bsd.alarm_tag.ts = bsd.ts;
    bsd.alarm_tag.sn = ah.fetchSn(bsd.ts);
    bsd.alarm_tag.cnt = 0;

    // 保存信息到数据库
    AlarmInfoItem alarm_info;
    alarm_info.protVer = (mpSrvInfo->attSndProtVer < 0) ? mProtVersion : mpSrvInfo->attSndProtVer;
    alarm_info.prot_subtype = prot_subtype;
    alarm_info.term_id = mpSrvInfo->id;        // 终端号
    alarm_info.ts = bsd.ts;     // 时间
    alarm_info.alarm_tag = bsd.alarm_tag;       // 报警标识
    alarm_info.state = 0;
    alarm_info.channel = 0x67;
    alarm_info.event_type = bsd.event_type;

    /* 生成附件 */
    my::string path;
    if (mProtState & AUTHORIZED) {
        creat_bsd_accessory(ch, &bsd, &alarm_info, &path);
        if (mpMediaMgr) {
            mpMediaMgr->addMediaPath(path);
        }
    }
    /* 保存数据库 */
    ret = dh.addAlarmInfo(alarm_info);
    if (ret != 0) {
        loge("[JttClient::%s] addAlarmInfo FAIL!", __FUNCTION__);
        my::file::rm(path.c_str());
        return -1;
    }

    logd("----- bsd report ----- speed = %d, event %s, mProtVersion:%d!\n", bsd.speed, bsd.evt_name.c_str(), mProtVersion);
    req.lai_list[alarm_info.channel] = (bsd.str(mProtVersion, prot_subtype));
    auto it = alarm_info.attachment.begin();

    while (it != alarm_info.attachment.end()) {
        add2cp(it->path.c_str());
        it++;
    }

    if (st.getSpeed() < cfg.warn.bsd_rpt.spd_thres) {
        logd("speed less than %d, not rpt bsd to server", cfg.warn.bsd_rpt.spd_thres);
        return 0;
    }
    //PROT_MLOG("%s > %s", __FUNCTION__, bsd.evt_name.c_str());
    return report(st, req);
}


// dms上报接口
int JttClient::dms_report(int ch, DmsAlarm& dms)
{
    ServiceHelper & sh = ServiceHelper::getInstance();
    DbHelper & dh = DbHelper::getInstance();
    AlarmHelper & ah = AlarmHelper::getInstance();
    Current st = sh.getStatus();
    X0200 req;
    req.lbi = sh.getLocationBaseInfo(st);

    my::string t;
    bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));

    // 填写dms相关信息
    int ret = dh.fetchAlarmId(dms.alarm_id);
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::dms_report] failed to fetch alarm id.");
    dms.level = get_dms_alarm_level(dms.speed, dms.event_type, dms.ts_ms);
    dms.alt = (short)st.lbs.alt;
    dms.lat = (my::uint)(st.lbs.lat * 1000000);
    dms.lng = (my::uint)(st.lbs.lng * 1000000);
    dms.car_state.acc       = st.getStateAcc();
    dms.car_state.location  = (1 == st.lbs.status);
    dms.car_state.left_signal   = st.sensor.left_turn;
    dms.car_state.right_signal  = st.sensor.right_turn;
    dms.car_state.brakes        = st.sensor.brake;
    dms.car_state.card          = st.getIcCardState();
    dms.alarm_tag.term_id = mpSrvInfo->id;
    dms.alarm_tag.ts = dms.ts;
    dms.alarm_tag.sn = ah.fetchSn(dms.ts);
    dms.alarm_tag.cnt = 0;

    // 保存信息到数据库
    AlarmInfoItem alarm_info;
    alarm_info.protVer = (mpSrvInfo->attSndProtVer < 0) ? mProtVersion : mpSrvInfo->attSndProtVer;
    alarm_info.prot_subtype = prot_subtype;
    alarm_info.term_id = mpSrvInfo->id;        // 终端号
    alarm_info.ts = dms.ts;     // 时间
    alarm_info.alarm_tag = dms.alarm_tag;       // 报警标识
    alarm_info.state = 0;
    alarm_info.channel = 0x65;
    alarm_info.event_type = dms.event_type;

    /* 生成附件 */
    my::string path;
    if (mProtState & AUTHORIZED) {
        creat_dms_accessory(ch, &dms, &alarm_info, &path);
        if (mpMediaMgr) {
            mpMediaMgr->addMediaPath(path); // 添加管理删除附件
        }
    }
    /* 保存数据库 */
    ret = dh.addAlarmInfo(alarm_info);
    if (ret != 0) {
        if (dms.level > 1) {
            my::file::rm(path.c_str());
        }

        loge("[JttClient::dms_report]");
        return -1;
    }

    logd("----- dms report ----- speed = %d, event %s, level:%d, attSndLvl:%d, mProtVersion:%d!\n", dms.speed, dms.evt_name.c_str(), dms.level, attSndLvl(0x65, dms.event_type), mProtVersion);
    req.lai_list[alarm_info.channel] = (dms.str(mProtVersion, prot_subtype));
    auto it = alarm_info.attachment.begin();

    while (it != alarm_info.attachment.end()) {
        add2cp(it->path.c_str());
        it++;
    }
    //PROT_MLOG("%s > %s", __FUNCTION__, dms.evt_name.c_str());
    return report(st, req);
}
int JttClient::evt2voice(const char * algo, const char* evtName)
{
    return !name2voice(algo, evtName);
}
// adas上报接口
int JttClient::adas_report(int ch, AdasAlarm& adas)
{
    ServiceHelper & sh = ServiceHelper::getInstance();
    DbHelper & dh = DbHelper::getInstance();
    AlarmHelper & ah = AlarmHelper::getInstance();
    Current st = sh.getStatus();
    X0200 req;
    req.lbi = sh.getLocationBaseInfo(st);

    my::string t;
    bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));

    // 填写adas相关信息
    int ret = dh.fetchAlarmId(adas.alarm_id);
    LOG_RETURN_IF(ret != 0, -1, loge, "failed to fetch alarm id.");
    adas.level = get_adas_alarm_level(adas.speed, adas.event_type, adas.ts_ms);
    if (adas.level < 1) {
        return -1;
    }
    if (adas.evt_name == "ldw-left") {
        adas.ldw_type = 1;
    } else if (adas.evt_name == "ldw-right") {
        adas.ldw_type = 2;
    }
    adas.alt = (short)st.lbs.alt;
    adas.lat = (my::uint)(st.lbs.lat * 1000000);
    adas.lng = (my::uint)(st.lbs.lng * 1000000);
    adas.car_state.acc      = st.getStateAcc();
    adas.car_state.location = (1 == st.lbs.status);
    adas.car_state.left_signal  = st.sensor.left_turn;
    adas.car_state.right_signal = st.sensor.right_turn;
    adas.car_state.brakes       = st.sensor.brake;
    adas.car_state.card         = st.getIcCardState();
    adas.alarm_tag.term_id = mpSrvInfo->id;
    adas.alarm_tag.ts = adas.ts;
    adas.alarm_tag.sn = ah.fetchSn(adas.ts);
    adas.alarm_tag.cnt = 0;

    // 保存信息到数据库
    AlarmInfoItem alarm_info;
    alarm_info.protVer = (mpSrvInfo->attSndProtVer < 0) ? mProtVersion : mpSrvInfo->attSndProtVer;
    alarm_info.prot_subtype = prot_subtype;
    alarm_info.term_id = mpSrvInfo->id;        // 终端号
    alarm_info.ts = adas.ts;        // 时间
    alarm_info.alarm_tag = adas.alarm_tag;      // 报警标识
    alarm_info.state = 0;
    alarm_info.channel = 0x64;
    alarm_info.event_type = adas.event_type;
    my::string path;

    /* 生成附件 */
    if (mProtState & AUTHORIZED) {
        creat_adas_accessory(ch, &adas, &alarm_info, &path);
        if (mpMediaMgr) {
            mpMediaMgr->addMediaPath(path);
        }
    }
    /* 保存数据库 */
    ret = dh.addAlarmInfo(alarm_info);
    if (ret != 0) {
        if (adas.level > 1) {
            my::file::rm(path.c_str());
        }

        loge("addAlarmInfo");
        return -1;
    }

    logd("----- adas report ----- speed = %d, event %s,level:%d, attSndLvl:%d, mProtVersion:%d!\n", adas.speed, adas.evt_name.c_str(), adas.level, attSndLvl(0x64, adas.event_type), mProtVersion);
    req.lai_list[alarm_info.channel] = (adas.str(mProtVersion, prot_subtype));
    auto it = alarm_info.attachment.begin();

    while (it != alarm_info.attachment.end()) {
        /* 本地存储告警附件 */
        add2cp(it->path.c_str());
        it++;
    }

    return report(st, req);
}

/* 川标超速告警上报 */
int JttClient::speeding_report(int ch, speedingAlarm& speeding) {
    ServiceHelper & sh = ServiceHelper::getInstance();
    DbHelper & dh = DbHelper::getInstance();
    AlarmHelper & ah = AlarmHelper::getInstance();
    Current st = sh.getStatus();
    X0200 req;
    req.lbi = sh.getLocationBaseInfo(st);

    my::string t;
    bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));

    // 填写speeding相关信息
    int ret = dh.fetchAlarmId(speeding.alarm_id);
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::dms_report] failed to fetch alarm id.");

    speeding.alt = (short)st.lbs.alt;
    speeding.lat = (my::uint)(st.lbs.lat * 1000000);
    speeding.lng = (my::uint)(st.lbs.lng * 1000000);
    speeding.car_state.acc       = st.getStateAcc();
    speeding.car_state.location  = (1 == st.lbs.status);
    speeding.car_state.left_signal   = st.sensor.left_turn;
    speeding.car_state.right_signal  = st.sensor.right_turn;
    speeding.car_state.brakes        = st.sensor.brake;
    speeding.car_state.card          = st.getIcCardState();
    speeding.alarm_tag.term_id = mpSrvInfo->id;
    speeding.alarm_tag.ts = speeding.ts;
    speeding.alarm_tag.sn = ah.fetchSn(speeding.ts);
    speeding.alarm_tag.cnt = 0;

    // 保存信息到数据库
    AlarmInfoItem alarm_info;
    alarm_info.protVer = (mpSrvInfo->attSndProtVer < 0) ? mProtVersion : mpSrvInfo->attSndProtVer;
    alarm_info.prot_subtype = prot_subtype;
    alarm_info.term_id = mpSrvInfo->id;        // 终端号
    alarm_info.ts = speeding.ts;     // 时间
    alarm_info.alarm_tag = speeding.alarm_tag;       // 报警标识
    alarm_info.state = speeding.state;
    alarm_info.channel = 0x71;
    alarm_info.event_type = speeding.event_type;

    /* 生成附件 */
    my::string path;
    if (mProtState & AUTHORIZED) {
        creat_speeding_accessory(ch, &speeding, &alarm_info, &path);
        if (mpMediaMgr) {
            mpMediaMgr->addMediaPath(path);
        }
    }
    /* 保存数据库 */
    ret = dh.addAlarmInfo(alarm_info);
    if (ret != 0) {
        loge("[JttClient::overSpeed_report]");
        my::file::rm(path.c_str());
        return -1;
    }

    logd("----- overspeed report ----- speed = %d, attSndLvl:%d!\n", speeding.speed, attSndLvl(0x71, speeding.event_type));
    req.lai_list[alarm_info.channel] = (speeding.str(mProtVersion, prot_subtype));
    auto it = alarm_info.attachment.begin();

    while (it != alarm_info.attachment.end()) {
        add2cp(it->path.c_str());
        it++;
    }

    return report(st, req);
}

/* 川标超过限高告警上报 */
int JttClient::over_height_report(int ch, overHeightAlarm& overHeight) {
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();
    ServiceHelper & sh = ServiceHelper::getInstance();
    DbHelper & dh = DbHelper::getInstance();
    AlarmHelper & ah = AlarmHelper::getInstance();
    RoadnetManager & rmh = RoadnetManager::getInstance();
    Current st = sh.getStatus();
    X0200 req;
    req.lbi = sh.getLocationBaseInfo(st);

    my::string t;
    bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));

    if (!overHeight.roadLimits) {
        overHeight.carHeight = config.sys.carHeight;
        if (rmh.mRoadInfo.restrictInfo.heightInfo.value > 0) {
            overHeight.roadLimits = rmh.mRoadInfo.restrictInfo.heightInfo.value * 10;
        } else {
            char propValue[PROP_VALUE_MAX] = {0};
            if (__system_property_get("rw.algo.height_limit", propValue) > 0) {
                overHeight.roadLimits = atoi(propValue);
                logd("rw.algo.height_limit:%d!\n", overHeight.roadLimits);

            } else {
                overHeight.roadLimits = 3500;
            }
        }
    }

    // 填写overHeight相关信息
    int ret = dh.fetchAlarmId(overHeight.alarm_id);
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::overHeight_report] failed to fetch alarm id.");

    overHeight.alt = (short)st.lbs.alt;
    overHeight.lat = (my::uint)(st.lbs.lat * 1000000);
    overHeight.lng = (my::uint)(st.lbs.lng * 1000000);
    overHeight.car_state.acc       = st.getStateAcc();
    overHeight.car_state.location  = (1 == st.lbs.status);
    overHeight.car_state.left_signal   = st.sensor.left_turn;
    overHeight.car_state.right_signal  = st.sensor.right_turn;
    overHeight.car_state.brakes        = st.sensor.brake;
    overHeight.car_state.card          = st.getIcCardState();
    overHeight.alarm_tag.term_id = mpSrvInfo->id;
    overHeight.alarm_tag.ts = overHeight.ts;
    overHeight.alarm_tag.sn = ah.fetchSn(overHeight.ts);
    overHeight.alarm_tag.cnt = 0;

    // 保存信息到数据库
    AlarmInfoItem alarm_info;
    alarm_info.protVer = (mpSrvInfo->attSndProtVer < 0) ? mProtVersion : mpSrvInfo->attSndProtVer;
    alarm_info.prot_subtype = prot_subtype;
    alarm_info.term_id = mpSrvInfo->id;        // 终端号
    alarm_info.ts = overHeight.ts;     // 时间
    alarm_info.alarm_tag = overHeight.alarm_tag;       // 报警标识
    alarm_info.state = overHeight.state;
    alarm_info.channel = 0x73;
    alarm_info.event_type = overHeight.event_type;

    /* 生成附件 */
    my::string path;
    creat_over_height_accessory(ch, &overHeight, &alarm_info, &path);
    if (mpMediaMgr) {
        mpMediaMgr->addMediaPath(path);
    }

    logd("overHeight alarm info to db, speed = %d, dattSndLvl:%d!\n", overHeight.speed, attSndLvl(0x73, overHeight.event_type));

    /* 保存数据库 */
    ret = dh.addAlarmInfo(alarm_info);
    if (ret != 0) {
        my::file::rm(path.c_str());
        loge("[JttClient::overHeight_report]");
        return -1;
    }

    logd("----- overHeight report ----- ch = %d, speed = %d\n", ch, overHeight.speed);
    req.lai_list[alarm_info.channel] = (overHeight.str(mProtVersion, prot_subtype));
    auto it = alarm_info.attachment.begin();

    while (it != alarm_info.attachment.end()) {
        add2cp(it->path.c_str());
        it++;
    }

    PROT_MLOG("%s", __FUNCTION__);//over height report
    return report(st, req);
}

/* 川标超过限重上报 */
int JttClient::over_load_report(int ch, overLoadAlarm& overLoad) {
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();
    ServiceHelper & sh = ServiceHelper::getInstance();
    DbHelper & dh = DbHelper::getInstance();
    AlarmHelper & ah = AlarmHelper::getInstance();
    RoadnetManager & rmh = RoadnetManager::getInstance();
    Current st = sh.getStatus();
    X0200 req;
    req.lbi = sh.getLocationBaseInfo(st);

    my::string t;
    bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));

    if (!overLoad.roadLimits) {
        overLoad.carLoad = config.sys.carHeight;
        if (rmh.mRoadInfo.restrictInfo.weightInfo.value > 0) {
            overLoad.roadLimits = rmh.mRoadInfo.restrictInfo.weightInfo.value * 10;
        } else {
            char propValue[PROP_VALUE_MAX] = {0};
            if (__system_property_get("rw.algo.weight_limit", propValue) > 0) {
                overLoad.roadLimits = atoi(propValue);
                logd("rw.algo.weight_limit:%d!\n", overLoad.roadLimits);

            } else {
                overLoad.roadLimits = 4500;
            }
        }
    }

    // 填写overLoad相关信息
    int ret = dh.fetchAlarmId(overLoad.alarm_id);
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::over_load_report] failed to fetch alarm id.");

    overLoad.alt = (short)st.lbs.alt;
    overLoad.lat = (my::uint)(st.lbs.lat * 1000000);
    overLoad.lng = (my::uint)(st.lbs.lng * 1000000);
    overLoad.car_state.acc       = st.getStateAcc();
    overLoad.car_state.location  = (1 == st.lbs.status);
    overLoad.car_state.left_signal   = st.sensor.left_turn;
    overLoad.car_state.right_signal  = st.sensor.right_turn;
    overLoad.car_state.brakes        = st.sensor.brake;
    overLoad.car_state.card          = st.getIcCardState();
    overLoad.alarm_tag.term_id = mpSrvInfo->id;
    overLoad.alarm_tag.ts = overLoad.ts;
    overLoad.alarm_tag.sn = ah.fetchSn(overLoad.ts);
    overLoad.alarm_tag.cnt = 0;

    // 保存信息到数据库
    AlarmInfoItem alarm_info;
    alarm_info.protVer = (mpSrvInfo->attSndProtVer < 0) ? mProtVersion : mpSrvInfo->attSndProtVer;
    alarm_info.prot_subtype = prot_subtype;
    alarm_info.term_id = mpSrvInfo->id;        // 终端号
    alarm_info.ts = overLoad.ts;     // 时间
    alarm_info.alarm_tag = overLoad.alarm_tag;       // 报警标识
    alarm_info.state = overLoad.state;
    alarm_info.channel = 0x72;
    alarm_info.event_type = overLoad.event_type;

    /* 生成附件 */
    my::string path;
    creat_over_load_accessory(ch, &overLoad, &alarm_info, &path);
    if (mpMediaMgr) {
        mpMediaMgr->addMediaPath(path);
    }
    /* 保存数据库 */
    ret = dh.addAlarmInfo(alarm_info);
    if (ret != 0) {
        my::file::rm(path.c_str());
        loge("[JttClient::over_load_report]");
        return -1;
    }

    logd("----- overLoad report ----- ch = %d, speed = %d, attSndLvl:%d!\n", ch, overLoad.speed, attSndLvl(0x72, overLoad.event_type));
    req.lai_list[alarm_info.channel] = (overLoad.str(mProtVersion, prot_subtype));
    auto it = alarm_info.attachment.begin();

    while (it != alarm_info.attachment.end()) {
        add2cp(it->path.c_str());
        it++;
    }

    // PROT_MLOG("%s", __FUNCTION__);//over load 
    return report(st, req);
}

// 川标激烈驾驶告警上报
int JttClient::hard_driving_report(int ch, HardDrivingAlarm & hardDriving)
{
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();
    ServiceHelper & sh = ServiceHelper::getInstance();
    DbHelper & dh = DbHelper::getInstance();
    AlarmHelper & ah = AlarmHelper::getInstance();
    Current st = sh.getStatus();
    X0200 req;
    req.lbi = sh.getLocationBaseInfo(st);

    my::string t;
    bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));

    // HAW 告警加速度阈值
    std::string alertThreshold = "rw.algo.limit";
    alertThreshold += hardDriving.evt_name;
    char propValue[PROP_VALUE_MAX] = {0};
    if (__system_property_get(alertThreshold.c_str(), propValue) > 0) {
        hardDriving.alarm_threshold1 = static_cast<my::ushort>(atoi(propValue));
    } else {
        hardDriving.alarm_threshold1 = static_cast<my::ushort>(2);
    }

    // 填写hardDriving相关信息
    int ret = dh.fetchAlarmId(hardDriving.alarm_id);
    LOG_RETURN_IF(ret != 0, -1, loge, "[JttClient::hard_driving_report] failed to fetch alarm id.");

    hardDriving.alt = (short)st.lbs.alt;
    hardDriving.lat = (my::uint)(st.lbs.lat * 1000000);
    hardDriving.lng = (my::uint)(st.lbs.lng * 1000000);
    hardDriving.car_state.acc       = st.getStateAcc();
    hardDriving.car_state.location  = (1 == st.lbs.status);
    hardDriving.car_state.left_signal   = st.sensor.left_turn;
    hardDriving.car_state.right_signal  = st.sensor.right_turn;
    hardDriving.car_state.brakes        = st.sensor.brake;
    hardDriving.car_state.card          = st.getIcCardState();
    hardDriving.alarm_tag.term_id = mpSrvInfo->id;
    hardDriving.alarm_tag.ts = hardDriving.ts;
    hardDriving.alarm_tag.sn = ah.fetchSn(hardDriving.ts);
    hardDriving.alarm_tag.cnt = 0;

    // 保存信息到数据库
    AlarmInfoItem alarm_info;
    alarm_info.protVer = (mpSrvInfo->attSndProtVer < 0) ? mProtVersion : mpSrvInfo->attSndProtVer;
    alarm_info.prot_subtype = prot_subtype;
    alarm_info.term_id = mpSrvInfo->id;        // 终端号
    alarm_info.ts = hardDriving.ts;     // 时间
    alarm_info.alarm_tag = hardDriving.alarm_tag;       // 报警标识
    alarm_info.state = hardDriving.state;
    alarm_info.channel = 0x70;
    alarm_info.event_type = hardDriving.event_type;

    logd("----- hard_driving_report ----- speed = %d, evt = %s, threshold = %d\n", hardDriving.speed, hardDriving.evt_name.c_str(), hardDriving.alarm_threshold1);
    req.lai_list[alarm_info.channel] = (hardDriving.str(mProtVersion, prot_subtype));

#if 0
    // 暂时不需要上传附件
    my::string path;
    logd("hard_driving_report alarm info to db, speed = %d, dattSndLvl:%d!\n", hardDriving.speed, attSndLvl(0x70, hardDriving.event_type));
    ret = dh.addAlarmInfo(alarm_info);
    if (ret != 0) {
        my::file::rm(gh.getAlarmVideo(path).c_str());

        loge("[JttClient::hard_driving_report]");
        return -1;
    }
    auto it = alarm_info.attachment.begin();
    while (it != alarm_info.attachment.end()) {
        add2cp(it->path.c_str());
        it++;
    }
#endif

    // PROT_MLOG("%s", __FUNCTION__);//hard driving
    return report(st, req);
}

// 车辆监测系统上报接口
int JttClient::vms_report(int ch, VmsAlarm& vms)
{
    return -1;
}

// 获取服务器信息
ServerInfo * JttClient::get_server_info()
{
    return mpSrvInfo;
}

JttClient& JttClient::set_srv_info(ServerInfo * si)
{
    this->mpSrvInfo = si;
    return *this;
}

const my::string& JttClient::get_tag() const
{
    return tag;
}

// 通用应答
bool JttClient::reply(const my::string& sim, my::ushort cmd, my::ushort sn, char res)
{
    if (!is_main_server()) {
        switch (cmd) {
            case 0x8300: {
                return true;
            }
            default: {
                break;
            }
        }
    }
    JttMsg msg = X0001::encode(sim, cmd, sn, res, mProtVersion);
    return send(msg);
}

void JttClient::onProtMsg(const my::string& sim, my::ushort cmd, my::ushort sn, const my::constr& data)
{
    mLastMsgRcvTs = my::timestamp::now();
    setProtRcvTimeout(PROT_REC_TIMEOUT_DEFAULT); /* 收到消息后设置接收超时时间为10min */

    if (mbDummy) {
        logd("dummy");
        return;
    }
    if (!checkRcvCmdFilter(cmd)) {
        logd("cmd 0x%04x not find in filter!", cmd);
        return;
    }

    if (!is_main_server()) {
        switch (cmd) {
            case 0x8100:
            case 0x8001:
            case 0x8300:
            //case 0x8104:
                break;

            default:
                logw("[JttClient::onProtMsg] secondary server sim=[%s], cmd=[0x%04x], sn=[%d], data=[%d]\n%s",
                     sim.c_str(), cmd, sn, data.length(), my::hex(data, true).c_str());
                return;

        }
    }
    if ((cmd != 0x8103) && mDump808Rcv2File) {
        my::string hex = my::hex(data, true);
        PROT_MLOG("0x%04x", cmd);
        PROT_MLOG_RAW(hex.c_str(), hex.length());
    }

    logd("[JttClient::onProtMsg] sim=[%s], cmd=[0x%04x], sn=[%d], data=[%d]\n%s", sim.c_str(), cmd, sn, data.length(), my::hex(data, true).c_str());

    switch (cmd) {
        case 0x8100:
            proc8100(sim, sn, data);
            break; // 注册应答

        case 0x8001:
            proc8001(sim, sn, data);
            break; // 通用应答

        case 0x8003:
            proc8003(sim, sn, data);
            break;

        case 0x8004: {
            PROT_MLOG("Server time : %02x-%02x-%02x %02x:%02x:%02x\n",
                (my::uchar)data[0], (my::uchar)data[1], (my::uchar)data[2],
                (my::uchar)data[3], (my::uchar)data[4], (my::uchar)data[5]);
            break;
        }

        case 0x8103:
            proc8103(sim, sn, data);
            break; // 设置终端参数

        case 0x8104:
            proc8104(sim, sn, data);
            break; // 查询终端参数

        case 0x8105:
            proc8105(sim, sn, data);
            break; // 查询终端参数

        case 0x8106:
            proc8106(sim, sn, data);
            break; // 查询终端的指定参数

        case 0x8107:
            proc8107(sim, sn, data);
            break; // 查询终端参数

        case 0x8108:
            proc8108(sim, sn, data);
            break; // 终端升级

        case 0x8201:
            proc8201(sim, sn, data);
            break; // 位置信息查询

        case 0x8202:
            proc8202(sim, sn, data);
            break; // 临时位置跟踪控制

        case 0x8203:
            proc8203(sim, sn, data);
            break; // 清除告警标志

        case 0x8204: {//链路检测
            // 回复通用应答
            reply(sim, 0x8204, sn, 0);
            break;
        }

        case 0x8300:
            proc8300(sim, sn, data);
            break; // 文本信息下发

        case 0x8301:
            proc8301(sim, sn, data);
            break;

        case 0x8302:
            proc8302(sim, sn, data);
            break; // 提问下发

        case 0x8303:
            proc8303(sim, sn, data);
            break; // 信息点播菜单设置

        case 0x8304:
            proc8304(sim, sn, data);
            break; // 信息服务

        case 0x8401:
            proc8401(sim, sn, data);
            break; // 设置电话本

        case 0x8500:
            proc8500(sim, sn, data);
            break; // 车辆控制

        case 0x8600:
            proc8600(sim, sn, data);
            break; // 设置圆形区域

        case 0x8601:
            proc8601(sim, sn, data);
            break; // 删除圆形区域

        case 0x8602:
            proc8602(sim, sn, data);
            break; // 设置矩形区域

        case 0x8603:
            proc8603(sim, sn, data);
            break; // 删除矩形区域

        case 0x8604:
            proc8604(sim, sn, data);
            break; // 设置多边形区域

        case 0x8605:
            proc8605(sim, sn, data);
            break; // 删除多边形区域

        case 0x8606:
            proc8606(sim, sn, data);
            break; // 设置路线

        case 0x8607:
            proc8607(sim, sn, data);
            break; // 删除路线

        case 0x8608:
            proc8608(sim, sn, data);
            break; // 查询

        case 0x8701:
            proc8701(sim, sn, data);
            break;

        case 0x8702:
            proc8702(sim, sn, data);
            break; // 上报驾驶员身份信息请求

        case 0x8800:
            proc8800(sim, sn, data);
            break; // 多媒体数据上传应答

        case 0x8801:
            proc8801(sim, sn, data);
            break; // 抓取图片

        case 0x8802:
            proc8802(sim, sn, data);
            break; // 存储多媒体数据检索

        case 0x8803:
            proc8803(sim, sn, data);
            break; // 存储多媒体数据上传命令

        case 0x8804:
            proc8804(sim, sn, data);
            break; // 录音命令

        case 0x8805:
            proc8805(sim, sn, data);
            break; // 单条存储多媒体数据检索上传命令

        case 0x8900:
            proc8900(sim, sn, data);
            break;// 苏标设备信息查询

        case 0x9003:
            proc9003(sim, sn, data);
            break;

        case 0x9101:
            proc9101(sim, sn, data);
            break; // 播放实时音视频

        case 0x9102:
            proc9102(sim, sn, data);
            break; // 音视频实时传输控制

        case 0x9105:
            proc9105(sim, sn, data);
            break; // 实时音视频传输状态通知

        case 0x9201:
            proc9201(sim, sn, data);
            break; // 平台下发远程录像回放请求

        case 0x9202:
            proc9202(sim, sn, data);
            break; // 平台下发远程录像回放控制

        case 0x9205:
            proc9205(sim, sn, data);
            break; // 查询资源列表（历史音视频查询）

        case 0x9206:
            proc9206(sim, sn, data);
            break; // 文件上传指令

        case 0x9207:
            proc9207(sim, sn, data);
            break; // 文件上传控制

        case 0x9208:
            proc9208(sim, sn, data);
            break; // 报警附件上传指令

        case 0x9212:
            proc9212(sim, sn, data);
            break; // 文件上传完成消息应答

        case 0x8700:
            proc8700(sim, sn, data);
            break;

        default:
            if (!ext_proc(sim, cmd, sn, data)) {
                // 回复通用应答: 不支持
                reply(sim, cmd, sn, access("/data/notImplRptEC", R_OK) ? 0 : 3);
                logw("[JttTcpClient::proc] unkown message: sim=[%s], cmd=[0x%04x], sn=[%d], data=[%d]\n%s", sim.c_str(), cmd, sn, data.length(), my::hex(data, true).c_str());
            }
    }
}

void JttClient::resetSpecialRecordAlarmRsp()
{
    mSpecialRecordAlarmRsp = 0;
    InputService & is = InputService::getInstance();
    Current * current = is.getCurrent();
    current->setSpecialRecordOverFlow(true);
    RecAlarmManager & ram = RecAlarmManager::getInstance();
    ram.clearAlarmRecProportionRsp();
    logi("resetSpecialRecordAlarmRsp\n");
}

// 通用应答
bool JttClient::proc8001(const my::string& sim, my::ushort sn, const my::constr& data)
{
    InputService & is = InputService::getInstance();
    Current * current = is.getCurrent();
    X8001 rsp;

    if (!rsp.decode(data)) {
        logw("[0x8001] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        return false;
    }

    logd("[0x8001] Server response: cmd=[0x%04x], sn=[%d], res=[%d].", rsp.cmd, rsp.sn, rsp.res);

    JttMsgTimer::Item item =  timer.ack(rsp.cmd, rsp.sn);

    switch (rsp.cmd) {
        case 0x0102: { // 鉴权应答
                if (!rsp.res) {
                    logi("[0x8001] Terminal is authoreized to login.");
                    mProtState |= JttClient::AUTHORIZED;
                    expiries[2] = (my::uint64)my::timestamp::now() + timeout[2] * 1000;
                } else {
                    loge("[0x8001] Terminal is authoreized fail!");
                    mpSrvInfo->auth = "";
                    mProtState = JttClient::INVALID;
                    expiries[0] = 0;
                    expiries[1] = 0;
                    resetAuthed();
                }
            }
            break;

        case 0x0002: { // 心跳应答
                logd("[0x8001] Heart-beat ack: sn=[0x%04x].", rsp.sn);
            }
            break;

        case 0x0200: {
                if (rsp.res) {
                    loge("[0x8001] for 0200, res = %d", rsp.res);
                    if (4 == rsp.res) {
                        /* 部标过检机构的检测软件在收到进出线路人工报警后，下发了0x8001消息 */
                        RecAlarmManager & ram = RecAlarmManager::getInstance();
                        InputService & is = InputService::getInstance();
                        Current * current = is.getCurrent();
                        logd("[0x8001] for 0200, res = %d", rsp.res);
                        if (current->getAlarmPathStatus()) {
                            current->setAlarmPathStatus(false);
                        }
                        /* 已发送特殊报警录像达到阈值报警 */
                        if (!mSpecialRecordAlarmRsp) {
                            loge("recieve SpecialRecordOverFlow alarm resp!\n");
                            mSpecialRecordAlarmRsp = 1;
                            current->setSpecialRecordOverFlow(false);
                            // current->setOtherFailUre(false);
                            ram.alarmRecProportionRsp();
                        }
                    }
                } else {
                    if (current->getAlarmPathStatus()) {
                        /* 部标过检机构的检测软件在收到进出线路人工报警后，下发了0x8001消息 */
                        logd("[0x8001] for 0200, res = %d", rsp.res);
                        current->setAlarmPathStatus(false);
                    }

                    /* 通用应答 */
                    alarmResponse(rsp.sn);
                }
            }
            break;
        case 0x0108:{
                /* 收到升级结果通知删除升级结果标志 */
                std::string flag = "/data/minieye/";
                flag += tag + "_upgrade_ok";
                std::string cmd = "rm -f ";
                cmd += flag;
                system(cmd.c_str());
            }
            break;
        default: {
                return ext_procX8001(rsp);
            }
    }

    return true;
}
bool JttClient::proc8003(const my::string& sim, my::ushort sn, const my::constr& data)
{
    loge("todo .....................................");
    return true;
}
// 注册应答
bool JttClient::proc8100(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8100 rsp;

    if (!rsp.decode(data)) {
        logw("[0x8100] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        return false;
    }

    logd("[0x8100] Register response: sn=[%d], res=[%d], auth=[%s].", rsp.sn, rsp.res, rsp.auth.c_str());

    // 发起登录请求
    if (rsp.res == 0) {
        mProtState |= JttClient::JttREGISTERED;
        expiries[1] = (my::uint64)my::timestamp::now() + timeout[1] * 1000;
        mpSrvInfo->auth = rsp.auth; // 保存鉴权码

        bool stat = false;
        char resp[256] = {0};
        char tmp[256] = {0};
        char cmd[512] = {0};
        snprintf(tmp, sizeof(tmp), "cmd saveconf %s auth %s", this->tag.c_str(), mpSrvInfo->auth.c_str());
        my::gbkToUtf8(tmp, cmd);
        stat = LogCallProxyCmd::sendReq("config", cmd, resp, sizeof(resp), 1);
        LOG_RETURN_IF(stat != true, false, loge, "failed to save %s auth, cmd=[%s]", this->tag.c_str(), cmd);
    }

    return true;
}

// 设置终端参数
bool JttClient::proc8103(const my::string& sim, my::ushort sn, const my::constr& data)
{
    InputService & is = InputService::getInstance();
    ServiceHelper & sh = ServiceHelper::getInstance();
    X8103 req;

    if (!req.decode(data)) {
        logw("[0x8103] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        return false;
    }
    if (!this->mpSrvInfo) {
        loge("[0x8103] num=[%d] %s ! nullptr\n", req.list.size(), my::hex(data).c_str());
        return false;
    }
    logi("[0x8103] num=[%d] %s !\n", req.list.size(), my::hex(data).c_str());

    int ret = 0;
    int mode = 0;
    ServerInfo& si = *this->mpSrvInfo;

    //PROT_MLOG("%s > data = %s\n", __FUNCTION__, my::hex(data).c_str());
    ret = sh.setConfig(this, req.list, si, mode);
    LOG_IF(ret != 0, logw, "[0x8103] failed to set config, ret=[%d]", ret);

    reply(sim, 0x8103, sn, ret == 0 ? 0 : 1);

    if (!ret && mode) {
        std::shared_ptr<struct STAT_DATA> sp = make_shared<struct STAT_DATA>();
        sp->flag = STAT_DATA_FLAG_PROT_RELOAD;
        struct STAT_DATA & s = *sp.get();
        s.ctx.ptr = this->mpSrvInfo;
        is.push(sp);
        logi("[0x8103] reload servcie, tag=[%s]", this->get_tag().c_str());
    }

    return true;
}

// 查询终端参数
bool JttClient::proc8104(const my::string& sim, my::ushort sn, const my::constr& data)
{
    ServiceHelper & sh = ServiceHelper::getInstance();
    X8104 req;

    if (!req.decode(data)) {
        logw("[0x8104] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        return false;
    }

    logd("[0x8104] receive 0x8104 cmd.");
    X0104 rsp;
    rsp.sn = sn;

    int ret = sh.getConfig(this, rsp.list);

    JttMsg msg = rsp.encode(sim, mProtVersion);

    if (!send(msg)) {
        loge("[0x8104] failed to send response, sn=[%d]", sn);
        return false;
    }

    return true;
}

// 升级
bool JttClient::proc8105(const my::string& sim, my::ushort sn, const my::constr& data)
{
    OutputService & os = OutputService::getInstance();
    X8105 req;

    if (prot_subtype == "sichuan") {
        if (!req.decode(prot_subtype, data)) {
            logw("[0x8105] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
            return false;
        }
    } else {
        if (!req.decode(data)) {
            logw("[0x8105] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
            return false;
        }
    }

    char prop[PROP_VALUE_MAX] = {0};
    if (__system_property_get(PROP_RW_MINIEYE_ENABLE_UPGRADE, prop) > 0) {
        if (prop[0] && !!strcmp(prop, prot_subtype.c_str())) {
            logd("disable upgrade! %s", prop);
            reply(sim, 0x8105, sn, 3);
            return false;
        }
    }
    logd("[8105]req.cmd:%d!\n", req.cmd);
    int ret = 0;
    bool reboot = false;
    if (0 == req.cmd) {
        ret = 0; /*for test*/
    } else if (1 == req.cmd) {
        string cmdSz = "curl -v --connect-timeout 10 -m 1800 -L ";
        my::string addr, usrpwd;
        if (parseFtpUrl(req.ctx[0], addr, usrpwd)) {
            cmdSz += " -u '";
            cmdSz += usrpwd;
            cmdSz += "' ";
            cmdSz += addr;
        } else {
            cmdSz += req.ctx[0];
        }
        #if 0
        cmdSz += " -o /data/bsp.mpk 2>&1; [ $? == 0 ] && "
                 "cd /data/ && install_mpk.sh bsp.mpk";
        #else
        cmdSz += " -o /data/bsp.tar.gz 2>&1; [ $? == 0 ] && "
                 "cd / && mount -o rw,remount /system; busybox tar xzvf /data/bsp.tar.gz";
        #endif
        logd("CMD : %s", cmdSz.c_str());
        char oldVer[256];
        snprintf(oldVer, sizeof(oldVer), "%s", getSwVer().c_str());
        logd("oldVer %s\n", oldVer);
        os.ttsGBK("开始升级");
        FILE * fp = popen(cmdSz.c_str(), "r");
        if (fp) {
            char cmdPrint[4 << 10];
            while (fgets(cmdPrint, sizeof(cmdPrint), fp)) {
                logd("ftpUpgrade: %s", cmdPrint);
            }
            pclose(fp);
        }
        char newVer[256] = {0};
        snprintf(newVer, sizeof(newVer), "%s", getSwVer().c_str());
        logd("newVer %s\n", newVer);
        reboot = !!strcmp(oldVer, newVer);
        ret = !reboot;
        os.ttsGBK(reboot ? "升级成功,重启中" : "升级失败");

        X0108 rsp;
        JttMsg msg = rsp.encode(sim, !!ret, mProtVersion);
        if (!send(msg)) {
            loge("[0x8108] failed to send response 0x0108, sn=[%d]", sn);
            return false;
        }
    } else if (2 == req.cmd) {
        ret = 3;
    }else if (3 == req.cmd || 4 == req.cmd || 5 == req.cmd) {
        reboot = true;
        ret = 0;
    } else if (6 == req.cmd || 7 == req.cmd) {
        dummy(true);
        ret = 0;
    } else if (8 == req.cmd) {
        /* 通天星扩展为休眠唤醒 */
        if (prot_subtype == "sichuan") {
            ret = ext_procX8105(req);
        } else {
            ret = 3;
        }
    }else {
        ret = 3;
    }
    reply(sim, 0x8105, sn, ret);

    if (reboot) {
        sleep(3);
        system("reboot");
    }
    return true;
}

// 查询指定终端参数
bool JttClient::proc8106(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8106 req;

    if (!req.decode(data)) {
        logw("[0x8106] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        return false;
    }
    int count = 0;
    for (auto i : req.list) {
        if (i == 0xff00) { //get mac twice for sanbao
            count = 1;
            break;
        }
    }
    logi("[0x8106] query para size=[%d]", req.num);
    do {
        X0104 rsp;
        rsp.sn = sn;
        ServiceHelper & sh = ServiceHelper::getInstance();
        int ret = sh.getConfig(this, req.list, rsp.list);

        JttMsg msg = rsp.encode(sim, mProtVersion);

        if (!send(msg)) {
            loge("[0x8106] failed to send response, sn=[%d]", sn);
            return false;
        }
    } while(count--);
    return true;
}

// 查询版本信息
bool JttClient::proc8107(const my::string& sim, my::ushort sn, const my::constr& data)
{
    InputService & is = InputService::getInstance();
    Config & cfg = is.getConfig();
    X0107 rsp;
    string version = getSwVer();
    my::string imei, iccid;
    getSimInfo(imei, iccid);
    rsp.vendor = cfg.sys.product.vendor;
    rsp.product = cfg.sys.product.model;
    rsp.gnssAttr = cfg.sys.gnss.mode;
    JttMsg msg = rsp.encode(sim, iccid, version, mpSrvInfo->id.c_str(), mProtVersion);

    if (!send(msg)) {
        loge("[0x8107] failed to send response, sn=[%d]", sn);
        return false;
    }

    return true;
}
bool JttClient::proc8108(const my::string& sim, my::ushort sn, const my::constr& data)
{
    OutputService & os = OutputService::getInstance();
    X8108 req;

    if (!req.decode(data, mProtVersion)) {
        logw("[0x8108] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        return false;
    }

    logd("[0x8108] type %d, ver{%d} = %s, pkgLen %d\n", req.type, req.verLen, req.version.c_str(), req.pkgLen);
    char prop[PROP_VALUE_MAX] = {0};
    if (__system_property_get(PROP_RW_MINIEYE_ENABLE_UPGRADE, prop) > 0) {
        if (prop[0] && !!strcmp(prop, prot_subtype.c_str())) {
            logd("disable upgrade! %s", prop);
            reply(sim, 0x8108, sn, 3);
            return false;
        }
    }

    int ret = 0;
    bool reboot = false;
    if (0 == req.type) {
        FILE * fp = fopen("/data/bsp.tar.gz", "w");

        if (fp) {
            if (req.pkgLen != fwrite(req.pkgData, 1, req.pkgLen, fp)) {
                loge("pkg write fail!\n");
            }

            fclose(fp);
        }

        os.ttsGBK("开始升级");
        string cmdSz = "cd / && mount -o rw,remount /system; busybox tar xzvf /data/bsp.tar.gz";
        logd("CMD : %s", cmdSz.c_str());
        char oldVer[256];
        snprintf(oldVer, sizeof(oldVer), "%s", getSwVer().c_str());
        logd("oldVer %s\n", oldVer);
        fp = popen(cmdSz.c_str(), "r");
        if (fp) {
            char cmdPrint[4 << 10];
            while (fgets(cmdPrint, sizeof(cmdPrint), fp)) {
                logd("ftpUpgrade: %s", cmdPrint);
            }
            pclose(fp);
        }
        char newVer[256] = {0};
        snprintf(newVer, sizeof(newVer), "%s", getSwVer().c_str());
        logd("newVer %s\n", newVer);
        reboot = !!strcmp(oldVer, newVer);
        ret = !reboot;
        os.ttsGBK(reboot ? "升级成功,重启中" : "升级失败");
    } else {
        ret = 3;
    }
    reply(sim, 0x8108, sn, ret);
#if 0
    JttMsg msg0(sim, 0x0005, mProtVersion);
    my::string& body = msg0.body;
    my::ushort repeatNum = 0;
    body << my::hton << sn << repeatNum;

    if (!send(msg0)) {
        loge("[0x8108] failed to send response 0x0005, sn=[%d]", sn);
        return false;
    }
#endif
    if (reboot) {
#if 1
        std::string cmd = "touch /data/minieye/";
        cmd += tag + "_upgrade_ok; sync";
        system(cmd.c_str());
#else
        X0108 rsp;
        JttMsg msg = rsp.encode(sim, ret, mProtVersion);
        if (!send(msg)) {
            loge("[0x8108] failed to send response 0x0108, sn=[%d]", sn);
            return false;
        }
#endif
        sleep(3);
        system("reboot");
    }
    return true;
}

// 位置信息查询
bool JttClient::proc8201(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8201 req;

    if (!req.decode(data)) {
        logw("[0x8201] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        return false;
    }

    logi("[0x8201] received.");

    X0201 rsp;
    rsp.sn = sn;
    Current st = ServiceHelper::getInstance().getStatus();
    rsp.loc.lbi = ServiceHelper::getInstance().getLocationBaseInfo(st);
    my::string t;
    bcd2numstr(t, rsp.loc.lbi.time, sizeof(rsp.loc.lbi.time));

    JttMsg msg = rsp.encode(sim, mProtVersion);
    if (!send(msg)) {
        loge("[0x8201] failed to send msg, sn=[%d]", rsp.sn);
        return false;
    }

    return true;
}
// 临时位置跟踪控制
bool JttClient::proc8202(const my::string& sim, my::ushort sn, const my::constr& data)
{
    ServiceHelper & sh = ServiceHelper::getInstance();
    Current st = sh.getStatus();
    X8202 req;

    if (!req.decode(data)) {
        logw("[0x8202] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回复通用应答: 消息有错
        reply(sim, 0x8202, sn, 2);
        return false;
    }

    logi("[0x8202] interval=[%d], expires=[%d]", req.interval, req.expires);
    timeout[5] = req.interval;
    expiries[5] = 0;
    trace_expire = (timeout[5] == 0) ? 0 : (TIMESTAMP_NOW(st) + req.expires * 1000);
    reply(sim, 0x8202, sn, 0);
    return true;
}

// 告警标志位清除
bool JttClient::proc8203(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8203 req;

    if (!req.decode(data)) {
        logw("[0x8203] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回复通用应答: 消息有错
        reply(sim, 0x8203, sn, 2);
        return false;
    }

    logd("[0x8203] sn=[%d], warnBits=[%xh]", req.sn, req.warnBits);

    if (is_main_server()) { // 清除标记位
        InputService & is = InputService::getInstance();
        Current * current = is.getCurrent();

        if (getBit(req.warnBits, 0)) {
            //current->setAlarmEmergency(false);
            mbChkAlarmBits |= 1;
        }

        if (getBit(req.warnBits, 3)) {
            current->setAlarmDangerous(false);
        }

        if (getBit(req.warnBits, 20)) {
            current->setAlarmAreaStatus(false);
        }

        if (getBit(req.warnBits, 21)) {
            current->setAlarmPathStatus(false);
        }

        if (getBit(req.warnBits, 22)) {
            current->setAlarmPathTimeFault(false);
        }

        if (getBit(req.warnBits, 27)) {
            current->setAlarmIllegalLauch(false);
        }

        if (getBit(req.warnBits, 28)) {
            current->setAlarmIllegalMove(false);
        }

        if (getBit(req.warnBits, 31)) {
            current->setAlarmIllegalDoorOpen(false);
        }
    }

    reply(sim, 0x8203, sn, 0);
    return true;
}

// 文本信息下发
bool JttClient::proc8300(const my::string& sim, my::ushort sn, const my::constr& data)
{
    OutputService & os = OutputService::getInstance();
    X8300   req;
    char    ret = 2;

    if (!req.decode(data, mProtVersion)) {
        loge("[0x8300] Failed to decode, msg=\n");
        my::hexdump(data, true);

        // 回复通用应答: 消息有错
        reply(sim, 0x8300, sn, 2);
        return false;
    }

    logi("[0x8300] TTS received: cmd=[%02x], msg=[%s]", req.cmd, my::hex(req.msg).c_str());

    if (req.cmd & 0x08) {
        /* 移除tts文本信息中的空格否则tts语音播报和液晶屏显示会出现信息不全 */
        req.msg = req.msg.remove(' ');
        os.ttsGBK(req.msg);
        reply(sim, 0x8300, sn, 0);

        char propValue[PROP_VALUE_MAX] = {0};

        __system_property_get(PROP_RW_MINIEYE_808TTS_IDX, propValue);
        int propIdx = atoi(propValue);
        propIdx %= 10;
        snprintf(propValue, sizeof(propValue), "%d", propIdx + 1);
        __system_property_set(PROP_RW_MINIEYE_808TTS_IDX, propValue);

        char msgGbk[1024] = {0};
        my::utf8ToGbk((char*)req.msg.c_str(), msgGbk);
        std::string recordTTS = "cmd show_message 0 ";
        vector<char> resp;
        LogCallProxyCmd::sendReq("hostio", recordTTS.c_str(), resp);
        recordTTS = "cmd show_message 8 '";
        recordTTS += msgGbk;
        recordTTS += "'";
        logd("%s", recordTTS.c_str());
        resp.clear();
        LogCallProxyCmd::sendReq("hostio", recordTTS.c_str(), resp);

        std::string msgfileName = "/data/tts/";
        msgfileName += propValue;
        msgfileName += ".gbk";
        my::file msgfile;
        msgfile.mkdir("/data/tts");
        if (msgfile.open(msgfileName.c_str()) >= 0) {
            recordTTS = my::timestamp::YYYYMMDD_HHMMSS() + " : ";
            recordTTS += msgGbk;
            msgfile.puts(recordTTS.c_str(), recordTTS.length());
            msgfile.close();
        }
        ret = 0;
    }

    if (req.cmd & 0x05) {
        //char cmd[256] = {0};
        my::string cmd;
        cmd.append("cmd setUiTips");
        cmd += " ";
        if (req.cmd & 0x1) {
            cmd += "紧急:";
        }
        cmd += req.msg + " ";
        /* 字体颜色 */
        cmd += "16777215 808";
        loge("%s", cmd.c_str());
        vector<char> resp;

        if (!LogCallProxyCmd::sendReq("media", cmd.c_str(), resp)) {
            ret = 0;
        }
        loge("%s", (char*)resp.data());

        ret = 0;
    }
    reply(sim, 0x8300, sn, ret);
    return true;
}

bool JttClient::sendEvent(int id)
{
    X0301 rsp;
    rsp.id = id;
    JttMsg m = rsp.encode(sim, mProtVersion);
    return send(m);
}

// 提问下发
bool JttClient::proc8301(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8301 req;
    if (req.decode(data, mProtVersion)) {
        reply(sim, 0x8301, sn, 0);
        if (!req.op) {
            my::file::rm("/data/.evts/");
        } else {
            my::file::mkdir("/data/.evts/");
            for (auto it = req.evts.begin(); it != req.evts.end(); it++) {
                std::string msgfileName = "/data/.evts/";
                msgfileName += std::to_string((int)it->id);
                if (4 == req.op) {
                    my::file::rm(msgfileName.c_str());
                } else {
                    if (2 == req.op) {
                        my::file msgfile;
                        if (msgfile.open(msgfileName.c_str(), "a") >= 0) {
                            msgfile.puts(it->ctx.c_str(), it->ctx.length());
                            msgfile.close();
                        }
                    } else {
                        my::file::rm(msgfileName.c_str());
                        my::file msgfile;
                        if (msgfile.open(msgfileName.c_str()) >= 0) {
                            msgfile.puts(it->ctx.c_str(), it->ctx.length());
                            msgfile.close();
                        }
                    }
                }
            }
        }
        return true;
    }

    // 回通用应答：消息有错
    reply(sim, 0x8301, sn, 2);

    return true;
}

bool JttClient::sendAnswer(int id, int sn)
{
    X0302 rsp;
    rsp.sn = sn;
    rsp.ans_id = id;
    JttMsg m = rsp.encode(sim, mProtVersion);
    return send(m);
}

// 提问下发
bool JttClient::proc8302(const my::string& sim, my::ushort sn, const my::constr& data)
{
    OutputService & os = OutputService::getInstance();
    X8302 req;

    if (!req.decode(data)) {
        logw("[0x8600] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：消息有错
        reply(sim, 0x8302, sn, 2);
        return false;
    }

    reply(sim, 0x8302, sn, 0);
    if (req.flag & 0x08) {
        /* 移除tts文本信息中的空格否则tts语音播报和液晶屏显示会出现信息不全 */
        req.question = req.question.remove(' ');
        /* 语音播报 */
        os.ttsGBK(req.question);


        char propValue[PROP_VALUE_MAX] = {0};

        __system_property_get(PROP_RW_MINIEYE_808TTS_IDX, propValue);
        int propIdx = atoi(propValue);
        propIdx %= 10;
        snprintf(propValue, sizeof(propValue), "%d", propIdx + 1);
        __system_property_set(PROP_RW_MINIEYE_808TTS_IDX, propValue);

        char msgGbk[1024] = {0};
        my::utf8ToGbk((char*)req.question.c_str(), msgGbk);
        std::string recordTTS = "cmd show_message 0 ";
        vector<char> resp;
        LogCallProxyCmd::sendReq("hostio", recordTTS.c_str(), resp);

        recordTTS = "cmd show_message 8 '";
        recordTTS += msgGbk;
        recordTTS += "'";
        logd("%s", recordTTS.c_str());
        resp.clear();
        LogCallProxyCmd::sendReq("hostio", recordTTS.c_str(), resp);

        std::string msgfileName = "/data/tts/";
        msgfileName += propValue;
        msgfileName += ".gbk";
        my::file msgfile;
        msgfile.mkdir("/data/tts");
        if (msgfile.open(msgfileName.c_str()) >= 0) {
            recordTTS = my::timestamp::YYYYMMDD_HHMMSS() + " : ";
            recordTTS += msgGbk;
            msgfile.puts(recordTTS.c_str(), recordTTS.length());
            msgfile.close();
        }

    }

    if (req.answers.size()) {
        my::file::rm("/tmp/.answers/");
        my::file::mkdir("/tmp/.answers/");
        for (auto it = req.answers.begin(); it != req.answers.end(); it++) {
            std::string msgfileName = "/tmp/.answers/";
            msgfileName += std::to_string((int)it->id);
            my::file msgfile;
            if (msgfile.open(msgfileName.c_str()) >= 0) {
                msgfile.puts(it->ans.c_str(), it->ans.length());
                msgfile.close();
            }
        }

        IpcCmd ipc;
        ipc.reset().add("cmd").add("show_opts").add(tag.c_str()).add("/tmp/.answers/").add(sn);
        LogCallProxyCmd::sendReq("hostio", ipc.cmd().c_str());
    }

    return true;
}


// 信息点播菜单设置
bool JttClient::proc8303(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8303 req;

    if (!req.decode(data)) {
        logw("[0x8303] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8303, sn, 2);
        return false;
    }

    logi("[0x8303] type=[%d]", req.type);
    int ret = 0;

    if (req.type == 0) {
        ret = DbHelper::getInstance().delInfoMenu();
        LOG_IF(ret != 0, logw, "[0x8303] Failed to del info menu.");

    } else {
        ret = DbHelper::getInstance().addInfoMenu(req.info_items);
        LOG_IF(ret != 0, logw, "[0x8303] Failed to add info menu.");
    }

    reply(sim, 0x8303, sn, ((ret == 0) ? 0 : 1));
    return true;
}

// 信息服务
bool JttClient::proc8304(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8304 req;

    if (!req.decode(data)) {
        logw("[0x8304] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8304, sn, 2);
        return false;
    }

    logi("[0x8304] type=[%d]", req.type);
    int ret = DbHelper::getInstance().updateInfoMsg(req.type, req.msg);
    LOG_IF(ret != 0, logw, "[0x8304] Failed to update info msg, type=[%d]", req.type);
    reply(sim, 0x8304, sn, ret == 0 ? 0 : 1);
    return true;
}

// 设置电话本
bool JttClient::proc8401(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8401 req;

    if (!req.decode(data)) {
        logw("[0x8401] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8401, sn, 2);
        return false;
    }

    logi("[0x8401] mode=[%d]", req.mode);
    int ret = ServiceHelper::getInstance().saveContacts(req);
    reply(sim, 0x8401, sn, ret == 0 ? 0 : 1);
    return true;
}
// 车辆控制
bool JttClient::proc8500(const my::string& sim, my::ushort sn, const my::constr& data)
{
    InputService & is = InputService::getInstance();
    Current * current = is.getCurrent();

    X8500 req;

    if (!req.decode(data, mProtVersion)) {
       logw("Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

       // 回通用应答：不支持
       reply(sim, 0x8500, sn, 2);
       return false;
   }
    auto it = req.params.find(0x0001);
    if (it != req.params.end()) {
        my::uchar ctl = 0;
        ctl = it->second[0];
        if (mProtVersion == 2019) {
            if (ctl & 1) {
                logd("open the door!");
                current->setStateDoor1(false);
                current->setStateDoorLocker(false);
            } else {
                logd("lock the door!");
                current->setStateDoor1(true);
                current->setStateDoorLocker(true);
            }
        } else {
            if (ctl & 1) {
                logd("lock the door!");
                current->setStateDoor1(true);
                current->setStateDoorLocker(true);
            } else {
                logd("open the door!");
                current->setStateDoor1(false);
                current->setStateDoorLocker(false);
            }
        }

        X0500 resp;
        resp.sn = sn;
        Current st = ServiceHelper::getInstance().getStatus();
        resp.loc.lbi = ServiceHelper::getInstance().getLocationBaseInfo(st);
        JttMsg msg = resp.encode(sim, mProtVersion);
        send(msg);
        report(st);
    }
    return reply(sim, 0x8500, sn, 0);
}

/* 调整定时上报时间点，避免与进出路线/区域的时间点交叠导致过检平台判定测试不通过 */
void JttClient::refreshLibReportTm(int type)
{
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();
    if (!config.sys.report.mode && (timeout[3] > 1)) {
        /* 定时汇报情况下且汇报间隔大于1s才调整定时汇报时间点 */
        my::uint64 current = (my::uint64)my::timestamp::now();
        struct tm dt;
        time_t s = time(nullptr);
        localtime_r(&s, &dt);
        int diff = (current - expiries[3]) / 1000 + (((current - expiries[3]) % 1000) > 500 ? 1 : 0);
        int lastTmS = (dt.tm_sec > diff) ? (dt.tm_sec - diff) : (dt.tm_sec + 60 - diff);
        if (lastTmS == 60) {
            lastTmS = 0;
        }
        loge("last report current sec:%d tm_sec:%d,current:%" FMT_LLD ", expiries[3]:%" FMT_LLD,
                dt.tm_sec, lastTmS, current, expiries[3]);
        if (type == 4) {
            /* 路段 */
            if ((lastTmS == 0)
                || (lastTmS == 1)
                || (lastTmS == 2)
                || (lastTmS == 3)
                || (lastTmS == 4)) {
                /* 避免由于定时上报时间点与路线偏离的时间点重合造成检测平台判定测试失败 */
                expiries[3] += (7 - lastTmS) * 1000;
            } else if ((lastTmS == 59)   /* 由于上传时间有正负1s的误差 */
                        || (lastTmS == 29)
                        || (lastTmS == 30)
                        || (lastTmS == 31)
                        || (lastTmS == 32)
                        || (lastTmS == 33))   {
                expiries[3] += (37 - lastTmS) * 1000;
            }
        } else {
            /* 区域 */
            if ((lastTmS == 59)
                || (lastTmS == 0)
                || (lastTmS == 1)
                || (lastTmS == 29)
                || (lastTmS == 30)
                || (lastTmS == 31)) {
                /* 避免由于定时上报时间点与进出区域的时间点重合造成检测平台判定测试失败 */
                expiries[3] += 7 * 1000;
            }
        }
    }
}

// 设置圆形区域
bool JttClient::proc8600(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8600 req;

    if (!req.decode(data, mProtVersion)) {
        logw("[0x8600] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8600, sn, 2);
        return false;
    }
    PROT_MLOG("%s > data = %s\n", __FUNCTION__, my::hex(data).c_str());//8600

    logi("[0x8600] set circle area, type=[%d], toal=[%d]", req.type, req.total);
    bool success = true;

    AreaHelper & ah = AreaHelper::getInstance();
    if (req.type == 1) { // 追加区域
        for (auto it = req.circles.begin(); it != req.circles.end(); ++it) {
            bool suc = ah.addArea(it->toAreaPtr()) == 0;
            LOG_IF(!suc, loge, "[0x8600] Failed to add circle, id=[%d]", it->id);
            success = success ? suc : success;
        }

    } else if (req.type == 0 || req.type == 2) { // 更新区域、修改区域
        for (auto it = req.circles.begin(); it != req.circles.end(); ++it) {
            bool suc = ah.updateArea(it->toAreaPtr()) == 0;
            LOG_IF(!suc, loge, "[0x8600] Failed to update circle, id=[%d]", it->id);
            success = success ? suc : success;
        }
    }

    reply(sim, 0x8600, sn, success ? 0 : 1);

    /* 更新位置上报时间 */
    refreshLibReportTm(1);
    extProcArea("电子围栏有更新");

    return true;
}

// 删除圆形区域
bool JttClient::proc8601(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8601 req;

    if (!req.decode(data)) {
        logw("[0x8601] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8601, sn, 2);
        return false;
    }

    logi("[0x8601] delete circle area, total=[%d]", req.total);

    if (req.total == 0) {
        LOG_IF(AreaHelper::getInstance().delArea(Area::Circle, 0) != 0, logw, "[0x8601] Failed to delete all circle");

    } else {
        for (auto it = req.ids.begin(); it != req.ids.end(); ++it) {
            LOG_IF(AreaHelper::getInstance().delArea(Area::Circle, *it) != 0, logw, "[0x8601] Failed to delete circle, id=[%d]", *it);
        }
    }

    reply(sim, 0x8601, sn, 0);

    extProcArea("电子围栏有更新");

    return true;
}

// 设置矩形区域
bool JttClient::proc8602(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8602 req;

    if (!req.decode(data, mProtVersion)) {
        logw("[0x8602] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8602, sn, 2);
        return false;
    }
    PROT_MLOG("%s > data = %s\n", __FUNCTION__, my::hex(data).c_str());//8602

    logi("[0x8602] set rectange area, type=[%d], total=[%d], mProtVersion %d", req.type, req.total, mProtVersion);
    bool success = true;
    AreaHelper & ah = AreaHelper::getInstance();
    if (req.type == 1) { // 追加区域
        for (auto it = req.rects.begin(); it != req.rects.end(); ++it) {
            bool tmp = ah.addArea(it->toAreaPtr()) == 0;
            LOG_IF(!tmp, loge, "[0x8602] Failed to add rectange, id=[%d]", it->id);
            success = success ? tmp : success;
        }

    } else if (req.type == 0 || req.type == 2) { // 更新区域、修改区域
        for (auto it = req.rects.begin(); it != req.rects.end(); ++it) {
            bool tmp = ah.updateArea(it->toAreaPtr()) == 0;
            LOG_IF(!tmp, loge, "[0x8602] Failed to update rectange, id=[%d]", it->id);
            success = success ? tmp : success;
        }
    }

    reply(sim, 0x8602, sn, success ? 0 : 1);

    /* 更新位置上报时间 */
    refreshLibReportTm(2);
    extProcArea("电子围栏有更新");

    return true;
}

// 删除矩形区域
bool JttClient::proc8603(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8603 req;

    if (!req.decode(data)) {
        logw("[0x8603] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8603, sn, 2);
        return false;
    }

    logi("[0x8603] delete rectange area, total=[%d]", req.total);

    if (req.total == 0) {
        LOG_IF(AreaHelper::getInstance().delArea(Area::Rectange, 0) != 0, logw, "[0x8603] Failed to delete all rectange.");

    } else {
        for (auto it = req.ids.begin(); it != req.ids.end(); ++it) {
            LOG_IF(AreaHelper::getInstance().delArea(Area::Rectange, *it) != 0, logw, "[0x8603] Failed to delete rectange, id=[%d]", *it);
        }
    }

    reply(sim, 0x8603, sn, 0);

    extProcArea("电子围栏有更新");

    return true;
}

// 设置多边形区域
bool JttClient::proc8604(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8604 req;

    if (!req.decode(data, mProtVersion)) {
        logw("[0x8604] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8604, sn, 2);
        return false;
    }
    PROT_MLOG("%s > data = %s\n", __FUNCTION__, my::hex(data).c_str());//8604

    logi("[0x8604] set polygon area, id=[%d]", req.id);
    AreaHelper & ah = AreaHelper::getInstance();
    int r = ah.replaceArea(req.toAreaPtr());
    reply(sim, 0x8604, sn, r == 0 ? 0 : 1);

    /* 更新位置上报时间点 */
    refreshLibReportTm(3);
    extProcArea("电子围栏有更新");

    return true;
}

// 删除多边形区域
bool JttClient::proc8605(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8605 req;

    if (!req.decode(data)) {
        logw("[0x8605] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8605, sn, 2);
        return false;
    }

    logi("[0x8605] delete polygon, total=[%d]", req.total);

    if (req.total == 0) {
        LOG_IF(AreaHelper::getInstance().delArea(Area::Ploygon, 0) != 0, logw, "[0x8605] Failed to delete all polygon");

    } else {
        for (auto it = req.ids.begin(); it != req.ids.end(); ++it) {
            LOG_IF(AreaHelper::getInstance().delArea(Area::Ploygon, *it) != 0, logw, "[0x8605] Failed to delete polygon, id=[%d]", *it);
        }
    }

    reply(sim, 0x8605, sn, 0);

    extProcArea("电子围栏有更新");

    return true;
}

// 设置路线
bool JttClient::proc8606(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8606 req;

    if (!req.decode(data, mProtVersion)) {
        logw("[0x8606] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8606, sn, 2);
        return false;
    }
    PROT_MLOG("%s > data = %s\n", __FUNCTION__, my::hex(data).c_str());//8606

    logi("[0x8606] set line area, id=[%d]", req.id);;
    AreaHelper & ah = AreaHelper::getInstance();
    int r = ah.replaceArea(req.toAreaPtr());
    reply(sim, 0x8606, sn, r == 0 ? 0 : 1);

    /* 更改位置上报时间点 */
    refreshLibReportTm(4);
    extProcArea("路线有更新");

    return true;
}

// 删除路线
bool JttClient::proc8607(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8607 req;

    if (!req.decode(data)) {
        logw("[0x8607] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8607, sn, 2);
        return false;
    }

    logi("[0x8607] delete line area, total=[%d]", req.total);

    if (req.total == 0) {
        LOG_IF(AreaHelper::getInstance().delArea(Area::LineArea, 0) != 0, logw, "[0x8607] Failed to delete all line area.");

    } else {
        for (auto it = req.ids.begin(); it != req.ids.end(); ++it) {
            LOG_IF(AreaHelper::getInstance().delArea(Area::LineArea, *it) != 0, logw, "[0x8607] Failed to delete line area, id=[%d]", *it);
        }
    }

    reply(sim, 0x8607, sn, 0);

    extProcArea("路线有更新");

    return true;
}
bool JttClient::proc8608(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8608 req;
    if (!req.decode(data)) {
        logw("Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回通用应答：不支持
        reply(sim, 0x8608, sn, 2);
        return false;
    }
    AreaHelper & ah = AreaHelper::getInstance();
    std :: vector < Area :: Ptr > areas;
    int ret = ah.getAreaList(areas, req.type);
    LOG_RETURN_IF(ret != 0, -1, loge, "failed to get areas.");
    logd("areas size = %d", areas.size());
    for (auto i : areas) {
        if (!req.areaIdNum || (req.idTbl.find(i->id) != req.idTbl.end())) {
            req.idTbl[i->id] = i;
            logd("area id %d", i->id);
        }
    }
    for (auto it = req.idTbl.begin(); it != req.idTbl.end();) {
        if (!it->second.get()) {
            it = req.idTbl.erase(it);
        } else {
            it++;
        }
    }
    X0608 resp;
    resp.type = req.type;
    JttMsg msg = resp.encode(sim, req.idTbl, mProtVersion);

    if (!send(msg)) {
        logw("%s > Failed to send X0608 response.", __FUNCTION__);
    }
    return true;
}

bool JttClient::proc8701(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8701 req;

    if (!req.decode(data)) {
        logw("%s > Failed to decode, msg={\n%s\n}.", __FUNCTION__, my::hex(data, true).c_str());
        // 回复通用应答: 消息有误
        reply(sim, 0x8701, sn, 2);
        return false;
    }
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();
    switch (req.cmd)
    {
        case 0xc3: {
            try {
                json j;
                ifstream jf("/sdcard/run/can_input.json");
                jf >> j;
                j["m4_analog"]["aspeed"]["ratio"] = req.u.c3.pulseCoef;
                ofstream of("/sdcard/run/can_input.json");
                of << j.dump(4);
                system("stop hostio;start hostio");
            } catch (json::exception e) {
                loge("%s\n", e.what());
            }
            break;
        }
        case 0xc4: {
            logd("set systime %02x-%02x-%02x %02x:%02x:%02x",
                    req.u.c4.timeBcd[0], req.u.c4.timeBcd[1], req.u.c4.timeBcd[2],
                    req.u.c4.timeBcd[3], req.u.c4.timeBcd[4], req.u.c4.timeBcd[5]);
            logd("set setup time %02x-%02x-%02x %02x:%02x:%02x",
                    req.u.c4.setupTmBcd[0], req.u.c4.setupTmBcd[1], req.u.c4.setupTmBcd[2],
                    req.u.c4.setupTmBcd[3], req.u.c4.setupTmBcd[4], req.u.c4.setupTmBcd[5]);
#ifdef CHOUJIAN//通天星下发协议有问题，暂默认关闭校时
            struct timeval stime;
            stime.tv_sec = req.u.c4.realDt;
            settimeofday(&stime, nullptr);
            // 设置硬件时钟
            system("qcom_date -R &");
#endif

            char req_resp[64] = {0};
            char req_cmd[128] = {0};
            sprintf(req_cmd, "cmd saveconf base init.date %d", req.u.c4.setupDt);
            int req_stat = LogCallProxyCmd::sendReq("config", req_cmd, req_resp, sizeof(req_resp), 1);
            LOG_RETURN_IF(req_stat != true, -1, loge, "failed to save [base] init.date, value=[%d]", req.u.c4.setupDt);
            config.sys.setup.date = req.u.c4.setupDt;

            sprintf(req_cmd, "cmd saveconf base init.mileage %f", req.u.c4.initMileAge * 10);
            req_stat = LogCallProxyCmd::sendReq("config", req_cmd, req_resp, sizeof(req_resp), 1);
            LOG_RETURN_IF(req_stat != true, -1, loge, "failed to save [base] init.mileage, value=[%f]", req.u.c4.initMileAge * 10);
            config.sys.setup.mileage_x10 = req.u.c4.initMileAge * 10;

            //todo //req.u.c4.totalMileAge;
            break;
        }
        default: {
            loge("invalid cmd 0x%x", req.cmd);
            return false;
        }
    }
    X0700 resp;
    resp.seq = sn;
    resp.cmd = req.cmd;
    JttMsg msg = resp.encode(sim, mProtVersion);

    if (!send(msg)) {
        logw("%s > Failed to send X0700 response.", __FUNCTION__);
    }
    return true;
}


// 上报驾驶员身份信息请求
bool JttClient::proc8702(const my::string& sim, my::ushort sn, const my::constr& data)
{
    Current st = ServiceHelper::getInstance().getStatus();
    X0702 res;
    res.state = st.ic.state;
    my::numstr2bcd(res.time, my::timestamp::YYMMDD_HHMMSS_S(st.ic.time), sizeof(res.time) * 2);
    res.ic_status = st.ic.ic_status;
    char name[128] = {0};
    my::utf8ToGbk((char*)st.ic.name.c_str(), name);
    res.name = name;
    char qc_code[128] = {0};
    my::utf8ToGbk((char*)st.ic.qc_code.c_str(), qc_code);
    res.qc_code = qc_code;
    char ca[128] = {0};
    my::utf8ToGbk((char*)st.ic.ca.c_str(), ca);
    res.ca = ca;
    my::date2bcd(st.ic.validity_date, res.validity_date);

    JttMsg msg = res.encode(sim, mProtVersion);

    if (!send(msg)) {
        loge("[0x8702] failed to send ic driver info to server.");
        return false;
    }

    return true;
}

// 多媒体数据上传应答
bool JttClient::proc8800(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8800 req;

    if (!req.decode(data)) {
        logw("[0x8800] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        return false;
    }

    logi("[0x8800] id=[%d], retry_num=[%d]", req.id, req.retry_num);
    return true;
}

// 多媒体数据上传
bool JttClient::sendMedia(const Media &media)
{
    char * jpg = (char *)malloc(1 << 20);
    if (!jpg) {
        return false;
    }

    int jpgSize = 0;
    struct stat st;

    if (!stat(media.path.c_str(), &st)) {
        jpgSize = (int)st.st_size;
    } else {
        return false;
    }

    if (jpgSize <= 0) {
        return false;
    }

    my::string data;
    FILE * fp = fopen(media.path.c_str(), "r");

    if (!fp) {
        free(jpg);
        return false;
    }
    
    int rdsize = fread(jpg, 1, jpgSize, fp);
    if (jpgSize != rdsize) {
        loge("file %s read fail!%d, total %d\n", media.path.c_str(), rdsize, jpgSize);
    }
    fclose(fp);
        
    data.assign((char*)jpg, (int)jpgSize);
    free(jpg);

    // 多媒体信息上传
    JttMsg msg0 = X0800::encode(sim, media.id, media.type, media.codev, media.event, media.ch, mProtVersion);
    if (!send(msg0)) {
        loge("x0800 Failed to upload media info. %s\n", strerror(errno));
        return false;
    } else {
        logd("x0800 Upload a media info successfully, id=[%d]", media.id);
    }

    // 多媒体数据上传
    LocationBaseInfo loc = LocationBaseInfo::create(media.loc);
    JttMsg msg = X0801::encode(sim, media.id, media.type, media.codev, media.event ,media.ch, loc, 
                                data, mProtVersion);

    if (!send(msg)) {
        loge("x0801 Failed to upload the media data. %s\n", strerror(errno));
        return false;
    } else {
        logd("x0801 Upload a media data successfully, id=[%d]", media.id);
    }
    return true;
}

void JttClient::addMedia2Send(const Media &m)
{
    if (mpMediaMgr) {
        mpMediaMgr->addMedia2Send(m);
    }
}

// 抓取图片
bool JttClient::proc8801(const my::string& sim, my::ushort sn, const my::constr& data)
{
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();
    X8801 req;

    if (!req.decode(data)) {
        logw("[0x8801] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回复通用应答: 消息有误
        reply(sim, 0x8801, sn, 2);
        return false;
    }

    logd("[0x8801] ch=[%d], cmd=[%d], inteval=[%d], save=[%d], resolution=[%d], quality=[%d], brightness=[%d], contrast=[%d], saturability=[%d], chroma=[%d].",
         req.ch, req.cmd, req.inteval, req.save, req.resolution, req.quality,
         req.brightness, req.contrast, req.saturability, req.chroma);

    if ((config.sys.cameras + config.sys.ipcs) < req.ch) {
        logw("[0x8801] Channel '%d' not existed.", req.ch);

        // 回复通用应答: 不支持
        reply(sim, 0x8801, sn, 3);
        return false;
    }

    if (req.cmd != 0) {
        req.lastSnapTm = 0;
        req.curSnapTm = my::timestamp::milliseconds_from_19700101();
        req.reqSn = sn;

        reply(sim, 0x8801, sn, 0);/*不回应通用应答，避免过检平台判断失败*/

        int type = (req.cmd == 0xffff) * 2; // 0:录像，2:jpeg
        int count = (req.cmd == 0xffff) ? 1 : req.cmd ;

        std::vector<my::uint> list;
        for (int32_t i = 0; i < count; i++) {
            list.push_back(req.reqSn + i);
        }

        mpMediaMgr->addSnapCmd(req);
        
        JttMsg msg = X0805::encode(sim, req.reqSn, 0, list, mProtVersion);
        if (!send(msg)) {
            // 发送应答失败
            loge("[0x8801] Failed to send X0805 response.");
        }
    } else {
        reply(sim, 0x8801, sn, 0);
        std::vector<my::uint> list;
        JttMsg msg = X0805::encode(sim, req.reqSn, 0, list, mProtVersion);
        if (!send(msg)) {
            // 发送应答失败
            loge("[0x8801] Failed to send X0805 response.");
        }
    }
    return true;
}

// 存储多媒体数据检索
bool JttClient::proc8802(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8802 req;

    if (!req.decode(data)) {
        logw("[0x8802] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回复通用应答: 消息有误
        reply(sim, 0x8802, sn, 2);
        return false;
    }

    logi("[0x8802] query multi-media data, meida=[%d], ch=[%d], event=[%d]", req.media, req.ch, req.event);

    if (req.media == 0 || req.media == 2) {
        std::vector<Media> list;
        DbHelper & dbh = DbHelper::getInstance();
        int ret = dbh.getMediaList(list, req);

        if (0 != ret) {
            reply(sim, 0x8802, sn, 2);
            return false;
        }

        X0802 rsp;
        rsp.sn = sn;

        for (int i = 0; i < list.size(); ++i) {
            X0802::MediaItem item;
            Media& m = list[i];
            logd("m.id %d", m.id);
            item.id = m.id;
            item.ch = m.ch;
            item.media = m.type;
            item.event = m.event;
            item.location.lbi = LocationBaseInfo::create(m.loc);
            rsp.items.push_back(item);
        }

        JttMsg msg = rsp.encode(sim, mProtVersion);

        if (!send(msg)) {
            logw("[0x8802] Failed to send X0802 response.");
            return false;
        }

        logi("[0x8802] response a media query successfully.");
    } else {
        reply(sim, 0x8802, sn, 3);
    }

    return true;
}

// 存储多媒体数据上传命令
bool JttClient::proc8803(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8803 req;

    if (!req.decode(data)) {
        logw("[0x8803] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回复通用应答: 消息有误
        reply(sim, 0x8803, sn, 2);
        return false;
    }

    logi("[0x8803] query upload data, meida=[%d], ch=[%d], event=[%d]", req.x8802.media, req.x8802.ch, req.x8802.event);
    std::vector<Media> list;
    bool res = false;

    do {
        int ret = DbHelper::getInstance().getMediaList(list, req.x8802);

        if (ret != 0) {
            loge("[0x8803] Failed to get media from db.");
            break;
        }

        auto it = list.begin();

        for (; it != list.end(); ++it) {
            Media& m = *it;

            // 多媒体数据上传
            sendMedia(m);

            if (req.flag == 1) { // 删除
                if (DbHelper::getInstance().delMedia(m.id, m.loc.id) != 0 ) {
                    loge("[0x8803] Failed to delete media, id=[%d]", m.id);
                    if (my::fileUtil::removeFile(m.path.c_str()) == false) {
                        logd("[0x8803] Failed to remove file %s", m.path.c_str());
                    }
                    
                    break;
                }
            }
        }

        res = (it == list.end());
    } while (0);

    reply(sim, 0x8803, sn, res ? 0 : 1);
    return true;
}
// 录音命令
bool JttClient::proc8804(const my::string& sim, my::ushort sn, const my::constr& data)
{
    if (data.length() < 5) {
        reply(sim, 0x8804, sn, 2);
        return false;
    }
    my::uchar recAud;
    my::ushort recSec;
    my::uchar saveFlag, sampleRate;
    my::constr msgData = data;

    msgData >> my::ntoh >> recAud >> recSec >> saveFlag >> sampleRate;
    if (recAud) {//开始录音
        // todo
    } else {
        // todo
    }
    reply(sim, 0x8804, sn, 0);
    return true;
}

// 单条存储多媒体数据检索上传命令
bool JttClient::proc8805(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X8805 req;

    if (!req.decode(data)) {
        logw("[0x8805] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回复通用应答: 消息有误
        reply(sim, 0x8805, sn, 2);
        return false;
    }

    logi("[0x8805] id=[%d] flag=[%d]", req.id, req.flag);
    DbHelper & dh = DbHelper::getInstance();

    std::vector<Media> list;
    X8802 cond;
    bool res = false;

    int ret = dh.getMediaList(list, cond, req.id);

    if (ret != 0 || list.size() != 1) {
        loge("[0x8805] Failed to get media from db, id=[%d]", req.id);
        return res;
    }

    Media& m = list[0];
    sendMedia(m);

    if (req.flag == 1) { // 删除
        if (dh.delMedia(req.id, m.loc.id) != 0 || my::file::rm(m.path.c_str()) != 0) {
            loge("[0x8805] Failed to remove media, id=[%d]", req.id);
            return false;
        }
    }

    return true;
}
bool JttClient::proc8900(const my::string& sim, my::ushort sn, const my::constr& data)
{
    InputService & is = InputService::getInstance();
    Config & cfg = is.getConfig();
    X8900 req;
    if (!req.decode(data)) {
        loge("msg decode error!");
        return false;
    }
    int result = 0;
    switch (req.type) {
        case 0x00: { //GNSS模块详细定位数据
            //todo
            break;
        }
        case 0x41: { // 串口1透传信息
            break;
        }
        case 0x42: { // 串口2透传信息
            break;
        }
        case 0x0B: { //道路运输证IC卡信息
            //break;
        }
        default : {
            if (!proc8900_ext(sim, sn, req)) {
                PROT_MLOG_D("%s > id %#x, data = %s\n",
                    __FUNCTION__, req.type, my::hex(data).c_str());
            }
            //logd("data for ext : %s", (const char*)data);
        }
    }
    reply(sim, 0x8900, sn, result);
    return true;
}
bool JttClient::proc9003(const my::string& sim, my::ushort sn, const my::constr& data)
{
    InputService & is = InputService::getInstance();
    Config & cfg = is.getConfig();
    X1003 rsp;

    rsp.audCodeTyp = 8;
    rsp.audSampleLen = 200;
    char propAudioValue[PROP_VALUE_MAX] = {0};
    memset(propAudioValue, 0, sizeof(propAudioValue));
    __system_property_get(PROP_PERSIST_MINIEYE_AUDIO_ENCODE, propAudioValue);
    if (0 == strcmp(propAudioValue, "g711a")) {
        rsp.audCodeTyp = 6;
    } else if (0 == strcmp(propAudioValue, "g711u")) {
        rsp.audCodeTyp = 7;
    } else if (0 == strcmp(propAudioValue, "g726")) {
        rsp.audCodeTyp = 8;
    } else if (0 == strcmp(propAudioValue, "aac")) {
        rsp.audCodeTyp = 19;
    } else {
        rsp.audCodeTyp = 8;
    }
    rsp.audChNum = 1;
    rsp.audSampleRate = E_AUDIO_SAMPLE_RATE_8K;
    rsp.audSampleBit = E_AUDIO_SAMPLING_DIGITS_16;
    rsp.audOutEn = 1;
    rsp.videoCodeTyp = E_VIDEO_CODE_TYPE__H264;
    rsp.audPhyNum = 4;
    rsp.videoPhyNum = 8;//平台检测支持的路数cfg.sys.cameras;

    JttMsg msg = rsp.encode(sim, mProtVersion);
    if (!send(msg)) {
        loge(" Failed to send audio/video prop.");
        return false;
    }
    return true;
}

void JttClient::registerTcpConnect(my::uchar playType, const my::uchar dataType,const my::uchar streamType, const my::uchar chId)
{
    std::lock_guard<std::mutex> lock(mMMXConnectMutex);
    my::uint64 connectTime = my::getTimestampMs();
    MMXConnect connect;
    connect.dataType = dataType;
    connect.streamType = streamType;
    connect.chId = chId;
    connect.playType = playType;
    mMMXConnectMap[connectTime] = connect;
}

void JttClient::deletTcpConnect(my::uchar playType, const my::uchar chId)
{
    std::lock_guard<std::mutex> lock(mMMXConnectMutex);
    auto it = mMMXConnectMap.begin();
    while (it != mMMXConnectMap.end()) {
        if (it->second.chId == chId && it->second.playType == playType) {
            it = mMMXConnectMap.erase(it);
            continue;
        } else {
            it++;
        }
    }
}

void JttClient::setPauseTcpConnectTm(my::uchar playType, const my::uchar chId, const my::uint64 pauseTm_ms)
{
    int setNum = 0;
    std::lock_guard<std::mutex> lock(mMMXConnectMutex);
    auto it = mMMXConnectMap.begin();
    while (it != mMMXConnectMap.end()) {
        if (it->second.chId == chId && it->second.playType == playType) {
            it->second.pauseTm_ms = pauseTm_ms;
            setNum++;
        }
        it++;
    }
    if (0 == pauseTm_ms && 0 == setNum) {
        registerTcpConnect(playType, 1, 1, chId);
    }
}

// 播放实时音视频
/*命令格式：rtp+*/
bool JttClient::proc9101(const my::string& sim, my::ushort sn, const my::constr& data)
{
    char pv[PROP_VALUE_MAX] = {0};
    __system_property_get(PROP_PERSIST_MINIEYE_TALK_CH_BASE, pv);
    int talkChBase = atoi(pv);
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();
    if (talkChBase < (config.sys.cameras + config.sys.ipcs)) {
        /* 客户定制，语音对讲监听未使用与视频通道一致的通道号，需增加偏移量进行转换 */
        talkChBase = 32;;
    }

    char audio[PROP_VALUE_MAX] = {0};
    memset(audio, 0, sizeof(audio));
    __system_property_get(PROP_PERSIST_JT808_LIVEWITHOUTAUDIO, audio);
    bool liveWithoutAudio = (atoi(audio) > 0);

    X9101 req;

    if (!req.decode(data)) {
        logw("[0x9101] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回复通用应答: 消息有误
        reply(sim, 0x9101, sn, 2);
        return false;
    }

    logi("[0x9101] ch=[%d], meida=[%d], quality=[%d], ip=[%s], port=[%d]", req.ch, req.media, req.quality, req.server.c_str(), req.tcp_port);
    IpcCmd ipc;
    bool v = false, a = false;

    if (req.media == E_TRANSPORT_MEDIA_TYPE_AV || req.media == E_TRANSPORT_MEDIA_TYPE_VIDEO) {
        v = true;
        ipc.reset().add("cmd").add("rtp+").add(req.ch).add(getMedia(req.quality).c_str()).add(this->tag)
                        .add(req.server).add(req.tcp_port).add(sim).add(0);
        int ret = MediaHelper::getInstance().sendIpc(ipc);
        logd("[0x9101] play video, rspOk=[%s]", ipc.rspOkString().c_str());
    }

    if (req.media == E_TRANSPORT_MEDIA_TYPE_TALKING) {
        a = true;
        ipc.reset().add("cmd").add("rtp+").add(req.ch % talkChBase).add("A").add(this->tag)
                        .add(req.server).add(req.tcp_port).add(sim).add(0);
        int ret = MediaHelper::getInstance().sendIpc(ipc);
        logd("[0x9101] paly audio, rspOk=[%s]", ipc.rspOkString().c_str());
    }

    if (req.media == E_TRANSPORT_MEDIA_TYPE_LISTEN || req.media == E_TRANSPORT_MEDIA_TYPE_AV) {
        a = true;
        ipc.reset().add("cmd").add("rtp+").add(req.ch % talkChBase).add("a").add(this->tag)
                        .add(req.server).add(req.tcp_port).add(sim).add(0);
        int ret = MediaHelper::getInstance().sendIpc(ipc);
        logd("[0x9101] paly audio, rspOk=[%s]", ipc.rspOkString().c_str());
    }

    registerTcpConnect(PLAY_REAL, req.media, req.quality, req.ch);

    reply(sim, 0x9101, sn, v || a ? 0 : 3);

    return true;
}

// 音视频实时传输控制
bool JttClient::proc9102(const my::string& sim, my::ushort sn, const my::constr& data)
{
    char pv[PROP_VALUE_MAX] = {0};
    __system_property_get(PROP_PERSIST_MINIEYE_TALK_CH_BASE, pv);
    int talkChBase = atoi(pv);
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();
    if (talkChBase < (config.sys.cameras + config.sys.ipcs)) {
        talkChBase = 32;;
    }
    X9102 req;

    if (!req.decode(data)) {
        logw("[0x9102] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        reply(sim, 0x9102, sn, 2); // 消息有误
        return false;
    }

    logd("[0x9102] ch=[%d], cmd=[%d], option=[%d], code=[%d]", req.ch, req.cmd, req.option, req.code);

    IpcCmd ipc;
    if (req.cmd == 0) { // 关闭音视频
        if (req.option == 0 || req.option == 2) {
            MediaHelper &md = MediaHelper::getInstance();
            ipc.reset().add("cmd").add("rtp-").add(req.ch).add("v").add(this->tag);
            int ret = md.sendIpc(ipc);
            logd("[0x9102] close video v rspOk=[%s]", ipc.rspOkString().c_str());
            ipc.reset().add("cmd").add("rtp-").add(req.ch).add("V").add(this->tag);
            ret = md.sendIpc(ipc);
            logd("[0x9102] close video V rspOk=[%s]", ipc.rspOkString().c_str());
        }

        if (req.option == 0 || req.option == 1) {
            MediaHelper &md = MediaHelper::getInstance();
            ipc.reset().add("cmd").add("rtp-").add(req.ch % talkChBase).add("a").add(this->tag);
            int ret = md.sendIpc(ipc);
            logd("[0x9102] close audio a rspOK=[%s]", ipc.rspOkString().c_str());
            ipc.reset().add("cmd").add("rtp-").add(req.ch % talkChBase).add("A").add(this->tag);
            ret = md.sendIpc(ipc);
            logd("[0x9102] close audio A rspOK=[%s]", ipc.rspOkString().c_str());
        }
        reply(sim, 0x9102, sn, 0);
        deletTcpConnect(PLAY_REAL, req.ch);
    } else if (req.cmd == 1) { // 切换码流
        if (req.code == 0 || req.code == 1) { // 主码流
            // ch 、stream 、w、h
            InputService &inService = InputService::getInstance();
            Config &config = inService.getConfig();

            MediaHelper &md = MediaHelper::getInstance();
            ipc.add("cmd").add("rtpSwitch").add(req.ch).add(req.code).add(this->tag);
            int ret = md.sendIpc(ipc);
            reply(sim, 0x9102, sn, ipc.rspOk() ? 0 : 1);
        } else {
            logw("[0x9102] Bad video stream code: %d.", req.code);
            reply(sim, 0x9102, sn, 3); // 其他参数不支持
            return false;
        }
        setPauseTcpConnectTm(PLAY_REAL, req.ch, 0);
    } else if (req.cmd == 2) {
        MediaHelper &md = MediaHelper::getInstance();
        ipc.reset().add("cmd").add("rtpPause").add(req.ch).add("v").add(this->tag).add(30);
        md.sendIpc(ipc);

        ipc.reset().add("cmd").add("rtpPause").add(req.ch).add("V").add(this->tag).add(30);
        md.sendIpc(ipc);

        ipc.reset().add("cmd").add("rtpPause").add(req.ch).add("a").add(this->tag).add(30);
        md.sendIpc(ipc);

        reply(sim, 0x9102, sn, ipc.rspOk() ? 0 : 1);
        loge("response 9102 2 \n");
        setPauseTcpConnectTm(PLAY_REAL, req.ch, my::timestamp::utc_milliseconds());
    } else if (req.cmd == 3) {
        MediaHelper &md = MediaHelper::getInstance();
        ipc.reset().add("cmd").add("rtpContinue").add(req.ch).add("v").add(this->tag);
        md.sendIpc(ipc);

        ipc.reset().add("cmd").add("rtpContinue").add(req.ch).add("V").add(this->tag);
        md.sendIpc(ipc);

        ipc.reset().add("cmd").add("rtpContinue").add(req.ch).add("a").add(this->tag);
        md.sendIpc(ipc);

        reply(sim, 0x9102, sn, ipc.rspOk() ? 0 : 1);
        loge("response 9102 3 \n");
        setPauseTcpConnectTm(PLAY_REAL, req.ch, 0);
    }else if (req.cmd == 4) { // 关闭语音对讲
        ipc.reset().add("cmd").add("rtp-").add(req.ch % talkChBase).add("A").add(this->tag);
        MediaHelper &md = MediaHelper::getInstance();
        int ret = md.sendIpc(ipc);
        logd("[0x9102] close two-way rradio rspOK=[%s]", ipc.rspOkString().c_str());
        reply(sim, 0x9102, sn, ipc.rspOk() ? 0 : 3);
        deletTcpConnect(PLAY_REAL, req.ch);
    } else {
        logw("[0x9102] Not suppored cmd: %d.", req.cmd);
        reply(sim, 0x9102, sn, 3);
        return false;
    }

    return true;
}

// 实时音视频传输状态通知
bool JttClient::proc9105(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X9105 req;

    if (!req.decode(data)) {
        logw("[0x9105] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());

        // 回复通用应答: 消息有误
        reply(sim, 0x9105, sn, 2);
        return false;
    }

    logd("[0x9105] ch=[%d], loss=[%d]]", req.ch, req.loss);
    reply(sim, 0x9105, sn, 0);
    return true;
}

// 平台下发远程录像回放请求
bool JttClient::proc9201(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X9201 req;

    if (!req.decode(data)) {
        logw("[0x9201] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        // 回复通用应答: 消息有误
        reply(sim, 0x9201, sn, 2);
        return false;
    }

    X9205 cond;
    cond.ch = req.ch;
    memcpy(cond.s_time, req.s_time, sizeof(cond.s_time));
    memcpy(cond.e_time, req.e_time, sizeof(cond.e_time));
    cond.media = req.media;
    cond.code = req.code;
    cond.storage = req.storage;
    cond.alarm_tag = -1;

    my::string s_time, e_time;
    my::bcd2numstr(s_time, req.s_time, sizeof(req.s_time));
    my::bcd2numstr(e_time, req.e_time, sizeof(req.e_time));

    logi("[0x9201] ch=[%d], s_time=[%s], e_time[%s], media=[%d], code=[%d], storage=[%d]",
         req.ch, s_time.c_str(), e_time.c_str(),  req.media, req.code, req.storage);
    std::vector<NewMedia> list;
    my::uint begin = 0, end = 0;
    my::bcd2time(req.s_time, sizeof(req.s_time), begin);
    my::bcd2time(req.e_time, sizeof(req.e_time), end);
    int ret = recordQuery(req.ch, req.code, begin, end, list, cond.alarm_tag, cond.storage);

    if (ret <= 0) {
        logw("[0x9201] Failed to get media list from record, ret=[%d]", ret);
        // 回复通用应答: 消息处理失败
        reply(sim, 0x9201, sn, 1);
        return false;
    }

    X1205 rsp;
    rsp.sn = sn;

    for (int i = 0; i < list.size(); ++i) {
        rsp.list.push_back(X1205::NewMediaItem::create(list[i]));
    }

    JttMsg msg = rsp.encode(sim, mProtVersion);

    if (!send(msg)) {
        logw("[0x9201] Failed to send X1205 response.");
        return false;
    }

    //"start file playback: [ch] [strmIdx] [begin tick] [end tick] [ip] [port] [connection].\n"
    std::string pbCmd = "cmd rtpPb+ ";
    pbCmd += std::to_string((int)req.ch) + " ";
    pbCmd += std::to_string((int)((req.code == 2) ? 1 : 0)) + " ";
    pbCmd += std::to_string(begin) + " ";
    pbCmd += std::to_string(end) + " ";
    pbCmd += req.server_ip + " ";
    pbCmd += std::to_string(req.tcp_port) + " ";
    pbCmd += sim + " ";
    pbCmd += this->tag + " ";
    pbCmd += std::to_string((int)0) + " ";
    pbCmd += std::to_string((int)req.media);

    logd("%s", pbCmd.c_str());
    vector<char> resp;
    const char * ps = "recorder";
    if (2 == cond.storage) {
        ps = "recorder_recovery";
    }

    if (!LogCallProxyCmd::sendReq(ps, pbCmd.c_str(), resp)) {
        loge("pb cmd fail! %s", pbCmd.c_str());
    }

    registerTcpConnect(PLAY_BACK, req.media, req.code, req.ch);
    return true;
}

// 平台下发远程录像回放控制
bool JttClient::proc9202(const my::string& sim, my::ushort sn, const my::constr& data)
{
    int ret = 0;
    X9202 req;

    if (!req.decode(data)) {
        logw("[0x9202] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        // 回复通用应答: 消息有误
        reply(sim, 0x9202, sn, 2);
        return false;
    }

    std::string pbCmd = "";

    switch (req.ctl) {
        case 2: {
                pbCmd = "cmd rtpPb- ";
                pbCmd += std::to_string((int)req.ch) + " 0 ";
                pbCmd += this->tag;
                deletTcpConnect(PLAY_BACK, req.ch);
                break;
            }

        case 5: {
                my::uint seek = 0;
                my::bcd2time((char*)req.time, sizeof(req.time), seek);
                pbCmd = "cmd rtpPbSeek ";
                pbCmd += std::to_string((int)req.ch) + " ";
                pbCmd += std::to_string((int)seek) + " ";
                pbCmd += this->tag;
                setPauseTcpConnectTm(PLAY_BACK, req.ch, 0);
                break;
            }

        case 0:
		case 1:
		case 3:
        case 4:
        case 6:
            if (req.ctl == 1) {
                setPauseTcpConnectTm(PLAY_BACK, req.ch, my::timestamp::utc_milliseconds());
            } else {
                setPauseTcpConnectTm(PLAY_BACK, req.ch, 0);
            }
            pbCmd = "cmd rtpPbMode ";
            pbCmd += std::to_string((int)req.ch) + " ";
            pbCmd += std::to_string((int)req.ctl) + " ";
			pbCmd += std::to_string((int)req.speed) + " ";
            pbCmd += this->tag;
            break;
        default: {
                logd("%s > ctl %d not support!", __FUNCTION__, req.ctl);
                break;
            }
    }

    if (pbCmd != "") {
        logd("%s", pbCmd.c_str());
        vector<char> resp;

        if (!LogCallProxyCmd::sendReq("recorder", pbCmd.c_str(), resp)) {
            loge("pb cmd fail! %s", pbCmd.c_str());
            ret = 1;
        }

    } else {
        ret = 1;
    }

    reply(sim, 0x9202, sn, !!ret);
    return true;
}

int32_t JttClient::recordQuery(int ch, int code, uint32_t begin, uint32_t end, std :: vector < NewMedia > & list,
                                        my::uint64 alarmTg, my::uchar mediaType, my::uchar storage)
{
    int32_t totalRecord = 0;
    int startIdx = 0;

    do {
        int once = 0;
        std::string querycmd = "cmd queryFrame ";
        querycmd += std::to_string(ch) + " ";
        querycmd += std::to_string((int)(code == 2) ? 1 : 0) + " ";
        uint64_t bgn64 = begin;
        bgn64 *= 1000000;
        uint64_t end64 = end;
        end64 *= 1000000;
        querycmd += std::to_string(bgn64) + " ";
        querycmd += std::to_string(end64) + " ";
        querycmd += std::to_string(1000000000) + " ";
        uint64_t eventBits = (alarmTg == 0) ? ((uint64_t) -1) : alarmTg;
        querycmd += std::to_string(eventBits);

        logd("alarmTg:%" FMT_LLD "!%s", alarmTg, querycmd.c_str());
        vector<char> resp;
        const char * ps = "recorder";
        if (2 == storage) {
            ps = "recorder_recovery";
        }
        LogCallProxyCmd::sendReqWithTimeOutEx(ps, querycmd.c_str(), resp, 2000); // 至少5s

        if (resp.size() <= 0) {
            return false;
        }

        const char *data = (const char *) resp.data();

        logd("==> resp: %s", data);
        
        data = strchr(data, '{');

        if (!data) {
            logd("not find {");
            return false;
        }

        jsonUtil::jsonObject_t jsonObject = jsonUtil::stringToJsonObject(data);
        int32_t count = jsonUtil::getIntValue(jsonObject, "count");

        logd("ts file query count: %d", count);
        count = MIN(count, 100000);
        totalRecord = count;

        for (int32_t i = 0; i < count; i ++) {

            jsonUtil::jsonObject_t elemObj = jsonUtil::getJsonObjectValue(jsonObject, my::to_string(i).c_str());
            int bgnSec = jsonUtil::getUnsignedLongLongValue(elemObj, "bgnUsTimestamp") / 1000000, 
                endSec = jsonUtil::getUnsignedLongLongValue(elemObj, "endUsTimestamp") / 1000000;

            eventBits = jsonUtil::getUnsignedLongLongValue(elemObj, "evtBits");
            if (bgnSec == endSec) {
                logd("skip %d: %d ~ %d", i, bgnSec, endSec);
                continue;
            }

            int dataSize = jsonUtil::getIntValue(elemObj, "dataSize");
            NewMedia nm;
            nm.id = (int)jsonUtil::getIntValue(elemObj, "tsIndex");
            nm.ch = (int)jsonUtil::getIntValue(elemObj, "ch") + 1;
            nm.td = {bgnSec, endSec};
            nm.alarm_tag = eventBits;
            nm.meida = 0;
            nm.code = 1;
            nm.storage = 1;
            nm.size = dataSize;
            list.push_back(nm);
            once++;
        }

        jsonUtil::jsonObjectDestroy(jsonObject);

    } while (0);

    return totalRecord;
}


// 查询资源列表
bool JttClient::proc9205(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X9205 req;

    if (!req.decode(data)) {
        logw("[0x9205] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        // 回复通用应答: 消息有误
        reply(sim, 0x9205, sn, 2);
        return false;
    }

    my::string s_time, e_time;
    my::bcd2numstr(s_time, req.s_time, sizeof(req.s_time));
    my::bcd2numstr(e_time, req.e_time, sizeof(req.e_time));

    logi("[0x9205] ch=[%d], s_time=[%s], e_time[%s], alarm=[%" FMT_LLD "], media=[%d], code=[%d], storage=[%d]",
         req.ch, s_time.c_str(), e_time.c_str(), req.alarm_tag, req.media, req.code, req.storage);

    std::vector<NewMedia> list;
    my::uint begin = 0, end = 0;
    my::bcd2time(req.s_time, sizeof(req.s_time), begin);
    my::bcd2time(req.e_time, sizeof(req.e_time), end);
    int ret = recordQuery(req.ch, req.code, begin, end, list, req.alarm_tag, req.media, req.storage);

    X1205 rsp;
    rsp.sn = sn;

    for (int i = 0; i < list.size(); ++i) {
        rsp.list.push_back(X1205::NewMediaItem::create(list[i]));
    }

    JttMsg msg = rsp.encode(sim, mProtVersion);

    if (!send(msg)) {
        logw("[0x9205] Failed to send X1205 response.");
        return false;
    }

    logi("[0x9205] send a media list successfully.");
    return true;
}

int32_t JttClient::SOSDataQuery(int ch, int code, uint32_t begin, uint32_t end, std :: vector < NewMedia > & list)
{
    int total = 0;
    const char * sosPath = "/tmp/media_rw/sdcard2/MNEYE/SOS/";
    DIR * pd = opendir(sosPath);
    if (pd) {
        struct dirent *dp = nullptr;
        struct stat st;
        while ((dp = readdir(pd)) != nullptr) {
            if ('.' == dp->d_name[0]) {
                continue;
            }

            char p[256] = {0};
            snprintf(p, sizeof(p) - 1, "%s/%s/%d.mp4", sosPath, dp->d_name, ch);
            if (-1 == lstat(p, &st)) {
                continue;
            }

            if (!S_ISDIR(st.st_mode)) {
                int Y, M, D, h, m, s;
                int ret = sscanf(dp->d_name, "%04d%02d%02d%02d%02d%02d", &Y, &M, &D, &h, &m, &s);
                time_t         now = time(nullptr);
                struct tm      tm_local;
                struct tm      tm_utc;
                unsigned long  time_local, time_utc;
                gmtime_r(&now, &tm_utc);
                localtime_r(&now, &tm_local);
                tm_local.tm_isdst = -1;
                tm_utc.tm_isdst = -1;

                #define TM_SET(_tm, _Y, _M, _D, _h, _m, _s) do {\
                    _tm.tm_year = _Y - 100; _tm.tm_mon = _M - 1; _tm.tm_mday = _D;\
                    _tm.tm_hour = _h; _tm.tm_min = _m; _tm.tm_sec = _s;\
                } while(0)

                TM_SET(tm_utc, Y, M, D, h, m, s);
                TM_SET(tm_local, Y, M, D, h, m, s);

                time_local = mktime(&tm_local);
                time_utc = mktime(&tm_utc);
                logd("time_utc %d, time_local %d", time_utc, time_local);

                uint32_t evtTm = time_utc;
                uint32_t dataBgn = evtTm - 5;
                uint32_t dataEnd = evtTm + 10;
                if (((!begin) && (!end)) ||
                    ((begin <= dataBgn) && (dataBgn < end)) ||
                    ((dataBgn <= begin) && (begin < dataEnd))) {
                    NewMedia nm;
                    nm.id = (int)evtTm;
                    nm.ch = ch;
                    nm.td = {dataBgn, dataEnd};
                    nm.alarm_tag = 0;
                    nm.meida = 2;
                    int strm = 1;//子流
                    nm.code = strm + 1;
                    nm.storage = 1;
                    nm.size = st.st_size;
                    nm.path = p;
                    list.push_back(nm);
                    total++;
                }
            } else {
                logd("skip dir %s\n", p);
            }
        }
        closedir(pd);
    }
    return total;
}


bool X08FakeDataParse(const char * path, std::vector<my::string> & data, my::uchar bgn[6], my::uchar end[6], int n)
{
    FILE * fp = fopen(path, "r");
    if (!fp) {
        logw("open %s fail!", path);
        return false;
    }
    char line[1024];
    struct X08 param;
    int total = 0;
    while(fgets(line, sizeof(line), fp)) {
        uint32_t Y, M, D, h, m, s, ms, spd, stat;
        int ret = sscanf(line, "%d-%d-%d %d:%d:%d.%d,%d,%x",
            &Y, &M, &D, &h, &m, &s, &ms, &spd, &stat);
        if (9 == ret) {
            my::uchar timeBcd[6];
            BCDTIME(timeBcd, Y - 2000, M, D, h, m, 0);
            if (memcmp(timeBcd, bgn, 5) < 0 ||
                memcmp(timeBcd, end, 5) > 0) {
                //logd("bgn = %" PRIx64 " end = %" PRIx64 ", cur = %" PRIx64 "", *((uint64_t*)bgn), *((uint64_t*)end), *((uint64_t*)timeBcd));
                continue;
            }
            uint64_t key = 0;
            TIMEKEY(((my::uchar*)&key), Y - 2000, M, D, h, m, 0);
            auto it = param.dataBlks.find(key);
            logd("%d-%d-%d %d:%d:%d.%d,%d,%d, ret = %d",
                Y, M, D, h, m, s, ms, spd, stat, ret);
            if (it != param.dataBlks.end()) {
                if (s < 60) {
                    it->second.secondData[s].spd    = spd;
                    it->second.secondData[s].status = stat;
                    total++;
                }
            } else {// if (param.dataBlks.size() <= n){
                MinusDataBlk blk;
                memset(&blk, 0, sizeof(blk));
                BCDTIME(blk.timeBcd, Y - 2000, M, D, h, m, 0);
                blk.secondData[s].spd    = spd;
                blk.secondData[s].status = stat;
                param.dataBlks[key] = blk;
                total++;
            }
        }
    }
    fclose(fp);
    return param.encode(data, n * 60);
}

bool X09FakeDataParse(const char * path, my::string & data, my::uchar bgn[6], my::uchar end[6], int n)
{
    FILE * fp = fopen(path, "r");
    if (!fp) return false;
    char line[1024];
    struct X09 param;
    int total = 0;
    while(fgets(line, sizeof(line), fp)) {
        uint32_t Y, M, D, h, m, s, ms, spd;
        float lng, lat, alt;
        //开始时间    ; 经度信息; 纬度信息; 高度信息; 速度信息
        //2020-05-31 08:01:00.000,115.0,39.0,37.0,38
        int ret = sscanf(line, "%d-%d-%d %d:%d:%d.%d,%f,%f,%f,%d",
            &Y, &M, &D, &h, &m, &s, &ms, &lng, &lat, &alt, &spd);
        //logd("%d-%d-%d %d:%d:%d.%d,%f,%f,%f,%d; ret = %d", Y, M, D, h, m, s, ms, lng, lat, alt, spd, ret);
        if (11 == ret) {
            my::uchar timeBcd[6];
            BCDTIME(timeBcd, Y - 2000, M, D, h, 0, 0);
            if (memcmp(timeBcd, bgn, 4) < 0 ||
                memcmp(timeBcd, end, 4) > 0) {
                //logd("bgn = %" PRIx64 " end = %" PRIx64 ", cur = %" PRIx64 "", *((uint64_t*)bgn), *((uint64_t*)end), *((uint64_t*)timeBcd));
                continue;
            }
            uint64_t key = 0;
            TIMEKEY(((my::uchar*)&key), Y - 2000, M, D, h, 0, 0);
            auto it = param.dataBlks.find(key);
            struct Loc l;
            BCDTIME(l.timeBcd, Y - 2000, M, D, h, m, 0);
            l.lng = (my::uint)(lng * 60 * 10000);
            l.lat = (my::uint)(lat * 60 * 10000);
            l.alt = (my::ushort)alt;
            l.spd = (my::uchar)spd;
            if (it != param.dataBlks.end()) {
                if (m < 60) {
                    it->second.loc[m] = l;
                    //logd("%" PRIx64 " : lng %d, lat %d, alt %d, spd %d", key, l.lng, l.lat, l.alt, l.spd);
                    total++;
                }
            } else {
                LocDataBlk blk;
                blk.loc[m] = l;
                //logd("%" PRIx64 " : lng %d, lat %d, alt %d, spd %d", key, l.lng, l.lat, l.alt, l.spd);
                param.dataBlks[key] = blk;
                total++;
            }
        }
    }
    fclose(fp);
    return param.encode(data, n);
}

bool X10FakeDataParse(const char * path, my::string & data, my::uchar bgn[6], my::uchar end[6], int n)
{
    FILE * fp = fopen(path, "r");
    if (!fp) return false;
    char line[1024];
    struct X10 param;
    int total = 0;
    while(fgets(line, sizeof(line), fp)) {
        uint32_t Y, M, D, h, m, s, ms, spd, stat;
        float lng, lat;
        int alt;
        char cert[128] = {0};
        //结束时间,机动车驾驶证号码,开始时间速度信息,信号状态,开始时间经度信息,开始时间纬度信息,开始时间高度信息
        //2020-06-01 06:59:59.800,430224198812247111,32,9C,116.0,40.0,37
        int ret = sscanf(line, "%d-%d-%d %d:%d:%d.%d,%18s,%d,%02x,%f,%f,%d",
            &Y, &M, &D, &h, &m, &s, &ms, cert, &spd, &stat, &lng, &lat, &alt);
        //logd("%d-%d-%d %d:%d:%d.%d,%s,%d,%02x,%f,%f,%d; ret = %d",
        //    Y, M, D, h, m, s, ms, cert, spd, stat, lng, lat, alt, ret);
        if (13 == ret) {
            my::uchar timeBcd[6];
            BCDTIME(timeBcd, Y - 2000, M, D, h, 0, 0);
            if (memcmp(timeBcd, bgn, 6) < 0 ||
                memcmp(timeBcd, end, 6) > 0) {
                logd("bgn = %" PRIx64 " end = %" PRIx64 ", cur = %lx", *((uint64_t*)bgn), *((uint64_t*)end), *((uint64_t*)timeBcd));
                continue;
            }
            int idx = 100 - ((s * 10 + ms / 100) % 200 / 2 + 1);
            uint64_t key = 0;
            TIMEKEY(((my::uchar*)&key), Y - 2000, M, D, h, m, 0);
            auto it = param.dataBlks.find(key);
            if (it != param.dataBlks.end()) {
                if (0 <= idx && idx < 100) {
                    BCDTIME(it->second.rec[idx].timeBcd, Y - 2000, M, D, h, m, s);
                    it->second.rec[idx].spd = spd;
                    it->second.rec[idx].stat1stByte = stat;
                    it->second.rec[idx].lng = (my::uint)(lng * 60 * 10000);;
                    it->second.rec[idx].lat = (my::uint)(lat * 60 * 10000);;
                    it->second.rec[idx].alt = alt;
                    logd("[%03d] %d-%d-%d %d:%d:%d.%d,%s,%d,%02x,%f,%f,%d", idx,
                        Y, M, D, h, m, s, ms, cert, spd, stat, lng, lat, alt);
                    total++;
                }
            } else {
                AcdDataBlk blk;
                if (0 <= idx && idx < 100) {
                    blk.drvCert = cert;
                    BCDTIME(blk.rec[idx].timeBcd, Y - 2000, M, D, h, m, s);
                    blk.rec[idx].spd = spd;
                    blk.rec[idx].stat1stByte = stat;
                    blk.rec[idx].lng = (my::uint)(lng * 60 * 10000);;
                    blk.rec[idx].lat = (my::uint)(lat * 60 * 10000);;
                    blk.rec[idx].alt = alt;
                    logd("[%03d] %d-%d-%d %d:%d:%d.%d,%s,%d,%02x,%f,%f,%d", idx,
                        Y, M, D, h, m, s, ms, cert, spd, stat, lng, lat, alt);
                    param.dataBlks[key] = blk;
                    total++;
                }
            }
        }
    }
    fclose(fp);
    return param.encode(data, n);
}

bool X11FakeDataParse(const char * path, my::string & data, my::uchar bgn[6], my::uchar end[6], int n)
{
    FILE * fp = fopen(path, "r");
    if (!fp) return false;
    char line[1024];
    struct X11 param;

    while(fgets(line, sizeof(line), fp)) {
        uint32_t bY, bM, bD, bh, bm, bs;
        uint32_t eY, eM, eD, eh, em, es;
        float bLng, bLat, bAlt;
        float eLng, eLat, eAlt;
        char cert[128] = {0};

        //机动车驾驶证号码,开始时间,结束时间,开始时间经度信息,开始时间纬度信息,开始时间高度信息,结束时间经度信息,结束时间纬度信息,结束时间高度信息
        //430224198812247111,2019-08-24 07:46:08,2019-08-24 12:20:13,115.55739666666666,39.92173833333333,37.0,115.52885,39.89319166666667,39.0
        int ret = sscanf(line, "%18s,%d-%d-%d %d:%d:%d,%d-%d-%d %d:%d:%d,%f,%f,%f,%f,%f,%f",
            cert, &bY, &bM, &bD, &bh, &bm, &bs, &eY, &eM, &eD, &eh, &em, &es, &bLng, &bLat, &bAlt, &eLng, &eLat, &eAlt);
        logd("%18s,%d-%d-%d %d:%d:%d,%d-%d-%d %d:%d:%d,%f,%f,%d,%f,%f,%d",
            cert, bY, bM, bD, bh, bm, bs, eY, eM, eD, eh, em, es, bLng, bLat, bAlt, eLng, eLat, eAlt);
        if (19 == ret) {
            OvTmDataBlk blk;
            blk.drvCert = cert;
            BCDTIME(blk.bgnTmBcd, bY - 2000, bM, bD, bh, bm, bs);
            BCDTIME(blk.endTmBcd, eY - 2000, eM, eD, eh, em, es);
            if (memcmp(blk.bgnTmBcd, bgn, 6) < 0 ||
                memcmp(blk.endTmBcd, end, 6) > 0) {
                logd("bgn = %" PRIx64 " end = %" PRIx64 ", cur = %" PRIx64 "", *((uint64_t*)bgn), *((uint64_t*)end), *((uint64_t*)blk.bgnTmBcd));
                continue;
            }
            blk.BELoc[0].lng = (my::uint)(bLng * 60 * 10000);;
            blk.BELoc[0].lat = (my::uint)(bLat * 60 * 10000);;
            blk.BELoc[0].alt = bAlt;
            blk.BELoc[1].lng = (my::uint)(eLng * 60 * 10000);;
            blk.BELoc[1].lat = (my::uint)(eLat * 60 * 10000);;
            blk.BELoc[1].alt = eAlt;
            uint64_t key = 0;
            TIMEKEY(((my::uchar*)&key), bY - 2000, bM, bD, bh, bm, bs);
            param.dataBlks[key] = blk;
        }
    }
    fclose(fp);
    return param.encode(data, n);
}

bool X12FakeDataParse(const char * path, my::string & data, my::uchar bgn[6], my::uchar end[6], int n)
{
    FILE * fp = fopen(path, "r");
    if (!fp) return false;
    char line[1024];
    struct X12 param;

    while(fgets(line, sizeof(line), fp)) {
        uint32_t bY, bM, bD, bh, bm, bs, evtType = 0;
        char cert[128] = {0};

        //发生时间,机动车驾驶证号码,事件类型
        //2020-05-27 23:23:54,430224198812247111,01
        int ret = sscanf(line, "%d-%d-%d %d:%d:%d,%18s,%d",
            &bY, &bM, &bD, &bh, &bm, &bs, cert, &evtType);
        logd("%d-%d-%d %d:%d:%d,%18s,%x",
            bY, bM, bD, bh, bm, bs, cert, evtType);
        if (8 == ret) {
            EvtDataBlk blk;
            BCDTIME(blk.timeBcd, bY - 2000, bM, bD, bh, bm, bs);
            if (memcmp(blk.timeBcd, bgn, 6) < 0 ||
                memcmp(blk.timeBcd, end, 6) > 0) {
                logd("bgn = %" PRIx64 " end = %" PRIx64 ", cur = %" PRIx64 "", *((uint64_t*)bgn), *((uint64_t*)end), *((uint64_t*)blk.timeBcd));
                continue;
            }
            blk.drvCert = cert;
            blk.evtType = evtType;
            uint64_t key = 0;
            TIMEKEY(((my::uchar*)&key), bY - 2000, bM, bD, bh, bm, bs);
            param.dataBlks[key] = blk;
        }
    }
    fclose(fp);
    return param.encode(data, n);
}

bool X13FakeDataParse(const char * path, my::string & data, my::uchar bgn[6], my::uchar end[6], int n)
{
    FILE * fp = fopen(path, "r");
    if (!fp) return false;
    char line[1024];
    struct X13 param;

    while(fgets(line, sizeof(line), fp)) {
        uint32_t bY, bM, bD, bh, bm, bs, evtType = 0;
        char cert[128] = {0};

        //发生时间,事件类型
        //2020-05-27 01:34:46,01
        int ret = sscanf(line, "%d-%d-%d %d:%d:%d,%x",
            &bY, &bM, &bD, &bh, &bm, &bs, &evtType);
        logd("%d-%d-%d %d:%d:%d,%x",
            bY, bM, bD, bh, bm, bs, evtType);
        if (7 == ret) {
            ACCDataBlk blk;
            BCDTIME(blk.timeBcd, bY - 2000, bM, bD, bh, bm, bs);
            if (memcmp(blk.timeBcd, bgn, 6) < 0 ||
                memcmp(blk.timeBcd, end, 6) > 0) {
                logd("bgn = %" PRIx64 " end = %" PRIx64 ", cur = %" PRIx64 "", *((uint64_t*)bgn), *((uint64_t*)end), *((uint64_t*)blk.timeBcd));
                continue;
            }
            blk.acc = evtType;
            uint64_t key = 0;
            TIMEKEY(((my::uchar*)&key), bY - 2000, bM, bD, bh, bm, bs);
            param.dataBlks[key] = blk;
        }
    }
    fclose(fp);
    return param.encode(data, n);
}

bool X15FakeDataParse(const char * path, my::string & data, my::uchar bgn[6], my::uchar end[6], int n)
{
    FILE * fp = fopen(path, "r");
    if (!fp) return false;
    char line[1024];
    struct X15 param;

    while(fgets(line, sizeof(line), fp)) {
        uint32_t stat, bY, bM, bD, bh, bm, bs, bMs;
        uint32_t eY, eM, eD, eh, em, es, eMs, spdRec, spdRef;

        //记录仪的速度状态,开始时间,结束时间,开始时间速度信息,开始时间参考速度
        //01,2020-05-20 07:00:00.000,2020-05-20 07:05:00.000,49,50
        int ret = sscanf(line, "%d,%d-%d-%d %d:%d:%d.%d,%d-%d-%d %d:%d:%d.%d,%d,%d",
            &stat, &bY, &bM, &bD, &bh, &bm, &bs, &bMs, &eY, &eM, &eD, &eh, &em, &es, &eMs, &spdRec, &spdRef);
        logd("%d,%d-%d-%d %d:%d:%d.%d,%d-%d-%d %d:%d:%d.%d,%d,%d",
            stat, bY, bM, bD, bh, bm, bs, bMs, eY, eM, eD, eh, em, es, eMs, spdRec, spdRef);
        if (17 == ret) {
            SpdStatDataBlk blk;
            blk.stat = stat;
            BCDTIME(blk.bgnTmBcd, bY - 2000, bM, bD, bh, bm, bs);
            BCDTIME(blk.endTmBcd, eY - 2000, eM, eD, eh, em, es);
            if (memcmp(blk.bgnTmBcd, bgn, 5) < 0 ||
                memcmp(blk.endTmBcd, end, 5) > 0) {
                logd("bgn = %" PRIx64 " end = %" PRIx64 ", cur = %" PRIx64 "", *((uint64_t*)bgn), *((uint64_t*)end), *((uint64_t*)blk.bgnTmBcd));
                continue;
            }
            uint64_t key = 0;
            TIMEKEY(((my::uchar*)&key), bY - 2000, bM, bD, bh, bm, 0);
            auto it = param.dataBlks.find(key);
            if (it != param.dataBlks.end()) {
                it->second.spd[bs].spdRec = spdRec;
                it->second.spd[bs].spdRef = spdRef;
            } else {
                blk.spd[bs].spdRec = spdRec;
                blk.spd[bs].spdRef = spdRef;
                param.dataBlks[key] = blk;
            }
        }
    }
    fclose(fp);
    return param.encode(data, n);
}

bool JttClient::fake8700(X8700 req, const my::constr data, const my::string& sim, my::ushort sn)
{
    my::datetime tick;
    my::uchar cmd;
    my::uchar bgn[6] = {0x10, 0x1, 0x1,  0x0,  0x0,  0x0};
    my::uchar end[6] = {0x33, 0x1, 0x1, 0x23, 0x59, 0x59};
    my::ushort num = 0;
    const char * p = (const char *)data;

    cmd = p[0];
    p += 7;
    if (req.msg.length() >= 15) {
        // 开始时间
        memcpy(bgn, p, 6);
        p += 6;
        // 结束时间
        memcpy(end, p, 6);
        p += 6;
        // 数量
        num = ((my::ushort)p[0] << 16) | p[1];
        logd("num = %d!", num);
    } else {
        switch (cmd) {
            case 0x08:
            case 0x09:
            case 0x15:
                num = 2880;
                break;
            default:
                num = 100 + cmd;
                break;
        }
        logd("use default param!");
    }
    Current st = ServiceHelper::getInstance().getStatus();
    char timeBCD[6];
    numstr2bcd(timeBCD, my::timestamp::YYMMDD_HHMMSS(), sizeof(timeBCD) * 2);
    X0700 resp;
    resp.seq = sn;
    resp.cmd = req.cmd;
    char path[128];
    snprintf(path, sizeof(path), "/data/%02xH.txt", req.cmd);

    switch(req.cmd)
    {
        case 0x08: {
            std::vector<my::string> data;
            if (!X08FakeDataParse(path, data, bgn, end, num)) {
                return false;
            }
            JttMsg msg = resp.encode(sim, mProtVersion, data);

            if (!send(msg)) {
                logw("%s > Failed to send X0700 response.", __FUNCTION__);
            }
            return true;
        }
        case 0x09: {
            if (!X09FakeDataParse(path, resp.msg, bgn, end, num)) {
                return false;
            }
            break;
        }
        case 0x10: {
            if (!X10FakeDataParse(path, resp.msg, bgn, end, num)) {
                return false;
            }
            break;
        }
        case 0x11: {
            if (!X11FakeDataParse(path, resp.msg, bgn, end, num)) {
                return false;
            }
            break;
        }
        case 0x12: {
            if (!X12FakeDataParse(path, resp.msg, bgn, end, num)) {
                return false;
            }
            break;
        }
        case 0x13:
        case 0x14: {
            if (!X13FakeDataParse(path, resp.msg, bgn, end, num)) {
                return false;
            }
            break;
        }
        case 0x15: {
            if (!X15FakeDataParse(path, resp.msg, bgn, end, num)) {
                return false;
            }
            break;
        }
        default:
            return false;
    }

    JttMsg msg = resp.encode(sim, mProtVersion);

    if (!send(msg)) {
        logw("%s > Failed to send X0700 response.", __FUNCTION__);
    }
    return true;
}

bool JttClient::proc8700(const my::string& sim, my::ushort sn, const my::constr& data)
{
    OutputService & os = OutputService::getInstance();
    X8700 req;

    if (!req.decode(data)) {
        logw("%s > Failed to decode, msg={\n%s\n}.", __FUNCTION__, my::hex(data, true).c_str());
        // 回复通用应答: 消息有误
        reply(sim, 0x8700, sn, 2);
        return false;
    }

    // fake8700
    if (fake8700(req, data, sim, sn)) {
        return true;
    }

    logw("%s > msg={\n%s\n}.", __FUNCTION__, my::hex(req.msg, true).c_str());
    os.fetchGb19056((uint8_t *)((const char*)data), data.length());
    Current st = ServiceHelper::getInstance().getStatus();
    char timeBCD[6];
    numstr2bcd(timeBCD, my::timestamp::YYMMDD_HHMMSS(), sizeof(timeBCD) * 2);
    X0700 resp;
    resp.seq = sn;
    resp.cmd = req.cmd;

    int trycnt = 3;
    char fileName[128];
    snprintf(fileName, sizeof(fileName), "/mnt/obb/.%02xH.dat", resp.cmd);

    while (trycnt-- > 0) {
        usleep(1e5);
        FILE * fp = fopen(fileName, "r");

        if (fp) {
            fseek(fp, 0, SEEK_END);
            int file_size = ftell(fp);
            resp.msg.length(file_size);
            rewind(fp);

            if (file_size != fread((void*)resp.msg.c_str(), 1, file_size, fp)) {
                loge("%s > read %s error!", __FUNCTION__, fileName);
            }

            fclose(fp);
            break;
        }
    }

    JttMsg msg = resp.encode(sim, mProtVersion);

    if (!send(msg)) {
        logw("%s > Failed to send X0700 response.", __FUNCTION__);
        return false;
    }

    return true;
}
// 文件上传指令
bool JttClient::proc9206(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X9206 req;

    if (!req.decode(data)) {
        logw("[0x9206] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        // 回复通用应答: 消息有误
        reply(sim, 0x9206, sn, 2);
        return false;
    }

    reply(sim, 0x9206, sn, 0);

    logd("[0x9206] ftp file a upload media request. ch %d, beginTs %x%x%x%x%x%x, end %x%x%x%x%x%x", req.ch,
            req.s_time[0], req.s_time[1], req.s_time[2], req.s_time[3], req.s_time[4], req.s_time[5],
            req.e_time[0], req.e_time[1], req.e_time[2], req.e_time[3], req.e_time[4], req.e_time[5]);

    logd("ftp param: ip:%s, port:%d, user:%s, passwd:%s, path:%s", req.server_ip.c_str(), req.server_port, req.user.c_str(), req.password.c_str(), req.path.c_str());
    auto it = mFtpTasks.begin();
    while (it != mFtpTasks.end()) {
        shared_ptr<JttFtpUpload> sp = it->second;
        X9206 hr = sp->getReqInfo();
        if ((hr.server_ip == req.server_ip) &&
            (hr.server_port == req.server_port) &&
            (hr.ch == req.ch) &&
            (!memcmp(hr.s_time, req.s_time, sizeof(hr.s_time))) &&
            (!memcmp(hr.e_time, req.e_time, sizeof(hr.e_time))) &&
            (hr.alarm_tag == req.alarm_tag) &&
            (hr.media == req.media) &&
            (hr.code == req.code) &&
            (hr.storage == req.storage)
        ) {
            break;
        }
        it++;
    }
    if (it == mFtpTasks.end()) {
        shared_ptr<JttFtpUpload> sp = make_shared<JttFtpUpload>(this, req, sim, sn);
        if (sp.get()) {
            sp->start();
            mFtpTasks[sn] = sp;
        }
    } else {
        loge("Last same task is runing!");
        return false;
    }
    return true;
}

// 文件上传控制
bool JttClient::proc9207(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X9207 req;
    if (!req.decode(data)) {
        logw("[0x9207] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        // 回复通用应答: 消息有误
        reply(sim, 0x9207, sn, 2);
        return false;
    }

    logd("[0x9207]: set sn %d ftp status %d", req.sn, req.ctl);
    auto it = mFtpTasks.find(req.sn);
    if (it != mFtpTasks.end()) {
        shared_ptr<JttFtpUpload> ftp = it->second;
        ftp->setFtpStatus(req.ctl);
        logd("[0x9207]: find sn %d in ftp tasks", req.sn);
    } else {
        logd("[0x9207]: not find sn %d in ftp tasks", req.sn);
    }
    reply(sim, 0x9207, sn, 0);
    return true;
}


// 报警附件上传指令
bool JttClient::proc9208(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X9208 req;

    if (!req.decode(data, mProtVersion, prot_subtype)) {
        logw("[0x9208] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        // 回复通用应答: 消息有误
        reply(sim, 0x9208, sn, 2);
        return false;
    }

    char res = 0;

    AlarmInfoItem alarm_info;
    alarm_info.protVer = (mpSrvInfo->attSndProtVer < 0) ? mProtVersion : mpSrvInfo->attSndProtVer;
    alarm_info.prot_subtype = prot_subtype;
    alarm_info.server_ip = req.server_ip;
    alarm_info.tcp_port = req.tcp_port;
    alarm_info.udp_port = req.udp_port;
    alarm_info.sim = sim;
    DbHelper & dbh = DbHelper::getInstance();
    // 根据alarm tag从数据库中查找到对应的 alarm info
    int ret = dbh.getAlarmInfoByTag(req.alarm_tag, alarm_info);

    logd("#### 0x9208 server_ip=%s, tcp_port=%d,term_id=%s, cnt=%d,sn=%d,alarm_id=%s",
      alarm_info.server_ip.c_str(), alarm_info.tcp_port,
      alarm_info.alarm_tag.term_id.c_str(), alarm_info.alarm_tag.cnt, alarm_info.alarm_tag.sn, req.alarm_id);

    if (0 == ret) {
        res = 1;
        alarm_info.alarm_id.assign(req.alarm_id, sizeof(req.alarm_id));
        send_attachment(alarm_info);
        // update 数据库中的 alarm info
        dbh.updateAlarmInfo(alarm_info);
    } else {
        logd("not found");
    }

    reply(sim, 0x9208, sn, res);
    return true;
}

// 文件上传完成消息应答
bool JttClient::proc9212(const my::string& sim, my::ushort sn, const my::constr& data)
{
    X9212 req;

    if (!req.decode(data)) {
        logw("[0x9212] Failed to decode, msg={\n%s\n}.", my::hex(data, true).c_str());
        // 回复通用应答: 消息有误
        reply(sim, 0x9212, sn, 2);
        return false;
    }

    logd("[0x9212] file=[%s], res=[%d]", req.path.c_str(), req.res);
    reply(sim, 0x9212, sn, 0);

    return true;
}
void JttClient::onMessageReceived(const std::shared_ptr<minieye::AMessage> &msg) {
    InputService & is = InputService::getInstance();
    Current * current = is.getCurrent();
    if (onMsgRcved(msg)) {
        return;
    }
    switch(msg->what()) {
        case EVT_TYPE_PROT_DB_SAVE_RPT_DATA: {
            if (!access("/data/disable_save_rpt_data", R_OK)) {
                logd("disable save rpt data by /data/disable_save_rpt_data!s");
                break;
            }
            // 盲区数据
            minieye::AString tmp;
            int32_t tmS = 0;
            msg->findString("data", &tmp);
            msg->findInt32("time", &tmS);

            ReportData rd;
            rd.data.assign(tmp.c_str(), tmp.size());

            DbHelper &dbHelper = DbHelper::getInstance();
            LOG_IF(dbHelper.addReportData(rd, this->tag, tmS) != 0, logw, "Failed to add report data.");
            break;
        }
        case EVT_TYPE_PROT_DB_CHECK_RPT_DATA: {
            // 检测盲区补报
            LOG_IF(check_report_data() != 0, logw, "failed to check report data.");
            break;
        }
        case EVT_TYPE_PROT_DB_CHECK_AREA_ALARM: {
            // 检测预警
            int r = check_alarm();
            LOG_IF(r != 0, logw, "Failed to check alarm.");
            break;
        }
        case EVT_TYPE_PROT_CAR_ROLL_OVER: {
            if (mRollOverTs.elapsed() > 5000) {
                ttsPlayRightNow("侧翻危险");
                mRollOverTs = my::timestamp::now();
                logd("roll over!\n");
                /* 使用行人碰撞报警2级上传侧翻报警 */
                my::string cmd;
                cmd.assignf("cmd trigger roll_over 0");
                if (!LogCallProxyCmd::sendReq("prot", cmd.c_str())) {
                    loge("cmd failed, %s", cmd.c_str());
                }
                current->setAlarmRolloverWarning(true);
                report(*current);
            }
            break;
        }
        case EVT_TYPE_PROT_CAR_CRASH: {
            if (mCrashTs.elapsed() > 5000) {
                mCrashTs = my::timestamp::now();
                ttsPlayRightNow("碰撞危险");
                current->setAlarmCollisionWarning(true);
                report(*current);
            }
            break;
        }
        default: {

        }
    }
}
bool JttClient::stopPlaybackStream(const my::uchar chId)
{
    std::string pbCmd = "";
    pbCmd = "cmd rtpPb- ";
    pbCmd += std::to_string((int)chId) + " 0 ";
    pbCmd += this->tag;

    logd("%s", pbCmd.c_str());
    vector<char> resp;
    if (!LogCallProxyCmd::sendReq("recorder", pbCmd.c_str(), resp)) {
        loge("pb cmd fail! %s", pbCmd.c_str());
        return false;
    }
    return true;
}

bool JttClient::stopRealView(const my::uchar chId, const my::uchar streamType, const my::uchar dataType) {
    IpcCmd ipc;
    char pv[PROP_VALUE_MAX] = {0};
    __system_property_get(PROP_PERSIST_MINIEYE_TALK_CH_BASE, pv);
    int talkChBase = atoi(pv);
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();
    if (talkChBase < (config.sys.cameras + config.sys.ipcs)) {
        talkChBase = 32;
    }
    if (dataType == E_TRANSPORT_MEDIA_TYPE_AV || dataType == E_TRANSPORT_MEDIA_TYPE_VIDEO) {
        ipc.reset().add("cmd").add("rtp-").add(chId).add(getMedia(streamType).c_str()).add(this->tag);
        int ret = MediaHelper::getInstance().sendIpc(ipc);
    }

    if (dataType == E_TRANSPORT_MEDIA_TYPE_LISTEN) {
        ipc.reset().add("cmd").add("rtp-").add(chId % talkChBase).add("a").add(this->tag);
        int ret = MediaHelper::getInstance().sendIpc(ipc);
    }

    if (dataType == E_TRANSPORT_MEDIA_TYPE_TALKING) {
        ipc.reset().add("cmd").add("rtp-").add(chId % talkChBase).add("A").add(this->tag);
        int ret = MediaHelper::getInstance().sendIpc(ipc);
    }

    return true;
}


