#ifndef __IDVRCORE_SERVICE_JTT808_1078_MSG_H__
#define __IDVRCORE_SERVICE_JTT808_1078_MSG_H__

#include <vector>
#include <memory>
#include <algorithm>
#include <inttypes.h>
#include "system_properties.h"
#include "mystd.h"
#include "in.service.h"
#include "prot.jtt808-1078.data.h"
#include "prot.jtt808-1078.area.h"

// 追加定长字节
#define APPEND_FIXED_LENGTH(DST, PLACEHOLDER, SRC, LEN) \
    do \
    { \
        int n = SRC.length(); \
        if (n < (LEN)) {\
            DST.append(PLACEHOLDER, (LEN) - n).append(SRC);\
        } \
        else {\
            DST.append(SRC, 0, (LEN)); \
        }\
    } while (0);
#define APPEND_FIXED_BACK(DST, PLACEHOLDER, SRC, LEN) \
    do \
    { \
        int n = SRC.length(); \
        if (n < (LEN)) {\
            DST.append(SRC).append(PLACEHOLDER, (LEN) - n);\
        } \
        else {\
            DST.append(SRC, 0, (LEN)); \
        }\
    } while (0);

// 获取固定长度的字符串, 不足在前面补placeholder
my::string get_fixed_string(my::constr input, int len, char placeholder = '\0');

typedef struct JttPayloadAttr {
    uint16_t    msgDatLen : 10;
    uint16_t    ecrypt : 3;
    uint16_t    pack : 1 ;
    uint16_t    ver  : 1 ;
    uint16_t    rsvd : 1 ;
} __attribute__((packed)) JttPayloadAttr;

typedef struct JttHdr_T {
    uint16_t    cmd;
    uint16_t    payloadAttr;
    union {
        struct {
            uint8_t     regId[6];
            uint16_t    seq;/*msg sequence*/
            uint16_t    totalPack;
            uint16_t    packIdx;
        } __attribute__((packed)) old;
        struct {
            uint8_t     ver;
            uint8_t     regId[10];
            uint16_t    seq;/*msg sequence*/
            uint16_t    totalPack;
            uint16_t    packIdx;
        } __attribute__((packed)) v2019;
    } __attribute__((packed)) u;
} __attribute__((packed)) JttHdr;

// JTT部标消息
class JttMsg
{
    public:
        JttMsg(bool b2019 = false);
        JttMsg(const my::constr& sim, my::ushort cmd, bool b2019);
        JttMsg& optimized(); // 作为右值向左值赋值

    public:
        my::ushort cmd;
        my::ushort sn;
        JttHdr header;
        my::string body; // 消息体
};

// 设备对平台的通用应答
struct X0001 {
    static JttMsg encode(const my::constr& sim, my::ushort cmd, my::ushort sn, char res, bool b2019)
    {
        JttMsg t(sim, 0x0001, b2019);

        my::string& body = t.body;
        body << my::hton << sn << cmd << res;
        return t.optimized();
    }
};

// 设备发起的注册请求
struct X0100 {
    my::ushort state; // 省域ID
    my::ushort city; // 市县域ID
    my::constr vendor; // 制造商ID, 5位, 位数不足, 补空格
    my::constr model; // 终端型号, 8位, 位数不足, 补空格
    my::constr device; // 设备ID, 7位, 位数不足, 补空格
    char vehicle_plate_color; // 车辆颜色
    my::constr vehicle_plate_num; // 车辆标识, gbk
    my::constr vin;

    JttMsg encode(const my::constr& sim, bool bNew = false)
    {
        static char blanks[] = {
            ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ',
            ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ',
            ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ',
            ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ',
        }; // 此处根据需要设置
        static char zeros[32] = {0};

        JttMsg t(sim, 0x0100, bNew);

        my::string& body = t.body;
        body << my::hton << state << city;

        if (bNew) {
            APPEND_FIXED_LENGTH(body, blanks, vendor, 11);
            APPEND_FIXED_LENGTH(body, zeros, model, 30);
            APPEND_FIXED_BACK(body, zeros, device, 30);

        } else {
            int len = vendor.capacity();
            my::constr shortVendor = vendor;
            if (len > 5) {
                shortVendor = &vendor[len - 5];
            }
            APPEND_FIXED_LENGTH(body, blanks, shortVendor, 5);
            APPEND_FIXED_BACK(body, zeros, model, 20);
            APPEND_FIXED_BACK(body, zeros, device, 7);
        }

        if (vehicle_plate_color) {
            body << vehicle_plate_color << vehicle_plate_num;

        } else {
            body << vehicle_plate_color << vin;
        }

        return t.optimized();
    }
};

// 终端注销
struct X0003 {
    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0x0003, b2019);
        return t.optimized();
    }
};
struct X0004 {
    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0x0004, b2019);
        return t.optimized();
    }
};

// 设备向平台发起登录鉴权请求
struct X0102 {
    my::constr auth;
    char imei[15] = {0};
    char version[20] = {0};
    JttMsg encode(const my::constr& sim, bool bNew)
    {
        JttMsg t(sim, 0x0102, bNew);

        my::string& body = t.body;

        if (bNew) {
            body << my::hton << (uint8_t)auth.length() << auth;
            body.append(imei, 15).append(version, 20);

        } else {
            body << my::hton << auth;
        }

        return t.optimized();
    }
};


// 终端参数项数据格式
struct TParam {
    my::uint id;        // 参数ID
    my::string val;     // 参数值

    template<typename T>
    static my::string encode(my::uint id, T val)
    {
        my::string ret;
        my::uchar len = sizeof(val);
        ret << my::hton << id << len << val;
        return ret;
    }

    static my::string encode(my::uint id, const my::string& val)
    {
        my::string ret;
        my::uchar len = val.length();
        ret << my::hton << id << len << val;
        return ret;
    }

    template<typename T>
    static bool decode(my::constr str, my::uint& id, T& val)
    {
        my::uchar len = sizeof(val);

        if (str.length() < 5 + len) {
            return false;
        }

        str >> my::ntoh >> id >> val(1);
        return true;
    }

    static bool decode(my::constr str, my::uint& id, my::string& val)
    {
        my::uchar len = 0;

        if (str.length() < 5 + len) {
            return false;
        }

        str >> my::ntoh >> id >> len;

        if (str.length() < len) {
            return false;
        }

        val.assign((const char*)str, len);
        return true;
    }
};

// 查询终端参数应答
struct X0104 {
    my::ushort sn;  // 应答流水号
    std::vector<my::string> list; // 参数项列表

    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0x0104, b2019);
        my::string& body = t.body;
        my::uchar num = (my::uchar)list.size();
        body << my::hton << sn << num;

        for (int i = 0; i < num; i++) {
            //TParam& tp = list[i];
            //body << tp.id << tp.data(1);
            body << list[i];
        }

        return t.optimized();
    }
};
// 查询终端版本应答
struct X0107 {
    my::uchar attr[2];//0xff
    my::string vendor;//5 bytes
    my::string product;
    my::string devId;

    char ICCID[10];
    my::string hdVer;
    my::string swVer;
    my::uchar gnssAttr;
    my::uchar comAttr;

    JttMsg encode(const my::constr& sim, const my::constr& iccid, std::string version, const char * devid, bool b2019)
    {
        attr[0] = attr[1] = 0xff;
        devId = devid;
        logd("devid %s", devId.c_str());
        my::str2bcd((const char *)iccid, iccid.length(), (uint8_t *)ICCID, sizeof(ICCID), 0xFF);

        hdVer = "v1.0";

        swVer =  version.c_str();

        comAttr = 0x3f;

        JttMsg t(sim, 0x0107, b2019);
        my::string& body = t.body;
        body << my::hton << attr[0] << attr[1];
        my::uchar len = vendor.length();
        char PLACEHOLDER[30];
        memset(PLACEHOLDER, 0, sizeof(PLACEHOLDER));
        if (b2019) {
            if (!access("/data/short_vendor", R_OK)) {
                if (len >= 5) {
                    len -= 5;
                    body.append(&((const char *)vendor)[len], 5);
                } else {
                    APPEND_FIXED_BACK(body, PLACEHOLDER, vendor, 5);
                }
            } else {
                if (len >= 11) {
                    len -= 11;
                    body.append(&((const char *)vendor)[len], 11);
                } else {
                    APPEND_FIXED_BACK(body, PLACEHOLDER, vendor, 11);
                }
            }
            APPEND_FIXED_BACK(body, PLACEHOLDER, product, 30);
            APPEND_FIXED_BACK(body, PLACEHOLDER, devId, 30);
        } else {
            if (len >= 5) {
                len -= 5;
                body.append(&((const char *)vendor)[len], 5);
            } else {
                APPEND_FIXED_BACK(body, PLACEHOLDER, vendor, 5);
            }
            APPEND_FIXED_BACK(body, PLACEHOLDER, product, 20);
            len = devId.length();
            if (len > 7) {
                my::string tmp = &(((const char *)devId)[len - 7]);
                APPEND_FIXED_BACK(body, PLACEHOLDER, tmp, 7);
            } else {
                APPEND_FIXED_BACK(body, PLACEHOLDER, devId, 7);
            }
        }
        body.append((const char *)ICCID, sizeof(ICCID));

        len = hdVer.length();
        body << len;
        body.append(hdVer);

        len = swVer.length();
        body << len;
        body.append(swVer);

        body << gnssAttr;
        body << comAttr;
        return t.optimized();
    }
};

// 升级应答
struct X0108 {
    uint8_t type;//0
    uint8_t result;// 0 ok
    JttMsg encode(const my::constr& sim, uint8_t r, bool b2019)
    {
        JttMsg t(sim, 0x0108, b2019);
        my::string& body = t.body;
        type = 0;
        result = r;
        body << my::hton << type << result;
        return t.optimized();
    }
} __attribute__((packed));

// 位置基本信息
struct LocationBaseInfo {
    my::uint alarm_tag = 0;             // 报警标志
    my::uint status = 0;                // 状态
    my::uint latitude = 0;              // 纬度
    my::uint longitude = 0;             // 经度
    short    height = 0;                // 高程
    my::ushort speed = 0;               // 速度
    my::ushort direction = 0;           // 方向
    char time[6];                   // 时间

    LocationBaseInfo()
    {
        memset(time, 0, sizeof(time));
    }

    static LocationBaseInfo create(const IdvrLocation& loc)
    {
        return create(loc.alarm_tag, loc.status, loc.latitude, loc.longitude,
                      loc.height, loc.speed, loc.direction, my::timestamp::YYMMDD_HHMMSS(loc.time * 1000));
    }

    static LocationBaseInfo create(my::uint alarm, my::uint status, double lat, double lng,
                                   double height, double speed, double dir, my::string time)
    {
        LocationBaseInfo lbi;
        lbi.alarm_tag = alarm;// & ~((speed < 20.0) ? (1<<2) : 0);
        lbi.status = status;
        lbi.latitude = (my::uint)((int)(lat * 1000000));
        lbi.longitude = (my::uint)((int)(lng * 1000000));
        lbi.height = (my::ushort)height;
        lbi.speed = (my::ushort)(speed * 10);//(status & 2) ? (my::ushort)(speed * 10) : 0;
        lbi.direction = (my::ushort)dir;
        my::numstr2bcd(lbi.time, time, sizeof(lbi.time) * 2);
        return lbi;
    }

    my::string str() const
    {
        my::string str;
        str << my::hton << alarm_tag << status << latitude << longitude << height << speed << direction;
        str.append(time, sizeof(time));
        return str;
    }
};

struct X0200 {
    LocationBaseInfo lbi;
    std::map<my::uchar, my::string> lai_list;
    my::uint         libTmS;
    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0x0200, b2019);

        t.body = str();
        return t.optimized();
    }

    my::string str()
    {
        my::string body;
        body << my::hton << lbi.str();
        for (auto it = lai_list.begin(); it != lai_list.end(); ++it) {
            body << it->second;
        }

        return body;
    }

    X0200& add_mileage(my::uint m)
    {
        lai_list[0x01] = getAddition(0x01, 4, m);
        return *this;
    }

    X0200& add_fuel(my::ushort fuel)
    {
        lai_list[0x02] = getAddition(0x02, 2, fuel);
        return *this;
    }

    X0200& add_spd(my::ushort spd)
    {
        lai_list[0x03] = getAddition(0x03, 2, spd);
        return *this;
    }

    X0200& add_storage_status(my::ushort status)
    {
        lai_list[0x17] = getAddition(0x17, 2, status);
        return *this;
    }

    X0200& add_extern_io(my::uint io)
    {
        lai_list[0x25] = getAddition(0x25, 4, io);
        return *this;
    }

    X0200& add_analogs(my::uint ad1, my::uint ad2)
    {
        my::uint ad = (ad2 << 16) | (ad1 & 0xffff);
        lai_list[0x2B] = getAddition(0x2B, 4, ad);
        return *this;
    }

    X0200& add_wireless_intensity(my::uchar n)
    {
        lai_list[0x30] = getAddition(0x30, 1, n);
        return *this;
    }

    X0200& add_gnss_stars(my::uchar n)
    {
        lai_list[0x31] = getAddition(0x31, 1, n);
        return *this;
    }

    X0200& add_extended_status(my::uint status)
    {
        lai_list[0x14] = getAddition(0x14, 4, status);
        return *this;
    }

    X0200& add_cam_signal_status(my::uint status)
    {
        lai_list[0x15] = getAddition(0x15, 4, status);
        return *this;
    }

    /* 摄像头遮挡报警 */
    X0200& add_cam_occlusion_status(my::uint status)
    {
        lai_list[0x16] = getAddition(0x16, 4, status);
        return *this;
    }

    X0200& add_spd_limit_info(my::uchar type, my::uchar spd)
    {
        my::string data;
        my::uchar id = 0x6a;
        my::uchar len = 2;
        data << my::hton << id << len << type << spd;
        lai_list[id] = data;
        return *this;
    }
    X0200& add_spd_limit_info2(my::uchar spd, my::uchar id = 0x32)
    {
        my::string data;
        my::uchar len = 1;
        data << my::hton << id << len << spd;
        lai_list[id] = data;
        return *this;
    }
    //夜间停车休息告警
    X0200& add_NSR_info(bool bPulseSpd, my::uchar sensor_sig)
    {
        my::string data;
        my::uchar id = 0xEF;
        my::uchar len = 4;
        my::uchar warn_bits = (1 << 4);
        my::uchar pad = 0;
        my::uchar stat = bPulseSpd ? (1 << 7) : 0;
        my::uchar ext_sig = sensor_sig;
        data << my::hton << id << len << warn_bits << pad << stat << ext_sig;

        lai_list[id] = data;
        return *this;
    }

    X0200& add_ztc_status(my::ushort ztcStat)
    {
        //lai_list[0xEE] = getAddition(0xEE, 2, ztcStat);
        return *this;
    }
    // 有为DCR/DVR扩展状态
    X0200& add_dvr_status(bool bInRec, my::uchar diskStatus, bool bLock)
    {
        my::string data;
        my::uchar id = 0xEA;
        my::uchar len = 4;
        my::uchar dvrType = 2;
        my::uchar recStatus = (bInRec ? 0b1101 : 0b0);
        my::uchar sdStatus = diskStatus | (bLock ? 0b11000000 : 0b01000000);
        my::uchar camStatus = 0; // 暂时不实现，标准协议有
        data << my::hton << id << len << dvrType << recStatus << sdStatus << camStatus;

        lai_list[id] = data;
        return *this;
    }

    X0200& add_weight(my::ushort weight)
    {
        lai_list[0xF3] = getAddition(0xF3, 2, weight);
        return *this;
    }

    X0200& add_extdev_status(my::uint status)
    {
        lai_list[0xE5] = getAddition(0xE5, 4, status);
        return *this;
    }

    X0200& add_can_info(const IdvrCarInfo& carInfo)
    {
        //logd("totalMile %10f, totalFuel %10f, avgFuel %10f, speed %10f, turnSpeed %10f", carInfo.canMileTotal, carInfo.canFuelTotal, carInfo.canFuelAvg, carInfo.canSpeed, carInfo.canTurnSpeed);
        my::string data;
        my::uchar id = 0xF6;
        my::uchar len = 23;
        my::uint mileage = (my::uint)carInfo.canMileTotal;//总里程
        my::uint fuel = (my::uint)carInfo.canFuelTotal;//总油耗
        my::uchar fuel_remain = 0;//剩余油量
        my::ushort fuel_averge = (my::ushort)carInfo.canFuelAvg;//瞬时油耗
        my::ushort car_speed = (my::ushort)carInfo.canSpeed;//车速
        my::ushort rota_speed = (my::ushort)carInfo.canTurnSpeed;//转速
        my::uint  torque = 0;//扭矩
        my::ushort battery_voltage = 0;//蓄电池电压
        my::uchar park_switch = 0;
        my::uchar tempature = 0;//低精度发动机冷却液温度
        data << my::hton << id << len << mileage << fuel << fuel_remain
            << fuel_averge << car_speed << rota_speed << torque
            << battery_voltage << park_switch << tempature;
        //logd("totalMile %d, totalFuel %d, avgFuel %d, speed %d, turnSpeed %d", mileage, fuel, fuel_averge, car_speed, rota_speed);
        lai_list[id] = data;
        return *this;
    }

    X0200& add_car_vin(void)
    {
        static char zeros[32] = {0};
        my::string data;
        my::uchar id = 0xF5;
        my::uchar len = 20;
        InputService & is = InputService::getInstance();
        Config & config = is.getConfig();
        my::string vin = config.sys.vehicle.vin;
        data << my::hton << id << len;
        APPEND_FIXED_BACK(data, zeros, vin, 20);
        lai_list[id] = data;
        return *this;
    }

    X0200& add_emergency_status(my::uchar status)
    {
        my::string data;
        my::uchar id = 0xF7;
        my::uchar len = 7;
        my::uchar bcdTime[6] = {0};
        my::string timeBCD;
        static char zeros[32] = {0};
        my::time2bcd((my::uint)time(NULL),  timeBCD);

        data << my::hton << id << len << status;
        APPEND_FIXED_BACK(data, zeros, timeBCD, 6);
        lai_list[id] = data;
        return *this;
    }

    X0200& add_ext_alarm(my::uint alarm)
    {
        lai_list[0xE1] = getAddition(0xE1, 4, alarm);
        return *this;
    }

    X0200& add_io_status(my::ushort status)
    {
        lai_list[0x2A] = getAddition(0x2A, 2, status);
        return *this;
    }
    X0200& add_location_is_abnormal(uint8_t abnormal)
    {
        my::string data;
        my::uchar id = 0xb8;
        my::uchar len = sizeof(abnormal);
        data << my::hton << id << len << abnormal;
        lai_list[id] = data;
        return *this;
    }
    X0200& add_night_abnormal_mv(uint32_t abnormal)
    {
        my::string data;
        my::uchar id = 0xb7;
        my::uchar len = sizeof(abnormal);
        data << my::hton << id << len << abnormal;
        lai_list[id] = data;
        return *this;
    }
    X0200& add_day_drive_time(uint32_t continueTime)
    {
        my::string data;
        my::uchar id = 0xc0;
        my::uchar len = 4;
        data << my::hton << id << len << continueTime;
        lai_list[id] = data;
        return *this;
    }
    X0200& add_stop_time(uint32_t stopTime)
    {
        my::string data;
        my::uchar id = 0xc1;
        my::uchar len = 4;
        data << my::hton << id << len << stopTime;
        lai_list[id] = data;
        return *this;
    }

    X0200& add_continue_drive_time(uint32_t continueTime)
    {
        my::string data;
        my::uchar id = 0xc2;
        my::uchar len = 4;
        data << my::hton << id << len << continueTime;
        lai_list[id] = data;
        return *this;
    }

private:
    template<typename T>
    my::string getAddition(my::uchar id, my::uchar len, const T& val)
    {
        my::string body;
        body << my::hton << id << len << val;
        return body;
    }
};

// 位置信息查询应答
struct X0201 {
    my::ushort sn;
    X0200 loc;

    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0x0201, b2019);

        my::string& body = t.body;
        body << my::hton << sn << loc.str();
        return t.optimized();
    }
};

struct SATELLITE_STAT {
    my::uchar  idx;
    my::uchar  elevation ;
    my::ushort bearing;
    my::uchar  cnr;
    my::string str() const {
        my::string s;
        s << my::hton << idx << elevation << bearing << cnr;
        return s;
    }
};
struct X0205 {
    std::vector<SATELLITE_STAT> bds;
    std::vector<SATELLITE_STAT> gps;
    std::vector<SATELLITE_STAT> glonass;
    std::vector<SATELLITE_STAT> galileo;
    LocationBaseInfo lbi;

    JttMsg encode(const my::constr & sim, bool b2019)
    {
        JttMsg t(sim, 0x0205, b2019);

        my::string& body = t.body;
        body << my::hton;
        body << (my::uchar)bds.size();
        if (bds.size()) {
            logd("bds.size %d", bds.size());
            my::string tmp;
            for (auto i : bds) {
                tmp += i.str();;
            }
            body << tmp;
        }
        body << (my::uchar)gps.size();
        if (gps.size()) {
            logd("gps.size %d", gps.size());
            my::string tmp;
            for (auto i : gps) {
                tmp += i.str();;
            }
            body << tmp;
        }
        body << (my::uchar)glonass.size();
        if (glonass.size()) {
            logd("glonass.size %d", glonass.size());
            my::string tmp;
            for (auto i : glonass) {
                tmp += i.str();;
            }
            body << tmp;
        }
        body << (my::uchar)glonass.size();
        if (galileo.size()) {
            logd("galileo.size %d", galileo.size());
            my::string tmp;
            for (auto i : galileo) {
                tmp += i.str();;
            }
            body << tmp;
        }
        body << lbi.str();
        return t.optimized();
    }
};

struct X0301 {
    my::uchar id;
    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0x0301, b2019);

        my::string& body = t.body;
        body << my::hton << id;
        return t.optimized();
    }
};
struct X0302 {
    my::ushort sn;              // 应答流水号
    my::uchar ans_id;           // 答案ID

    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0x0302, b2019);

        my::string& body = t.body;
        body << my::hton << sn << ans_id;
        return t.optimized();
    }
};
struct X0500 {
    my::ushort sn;
    X0200 loc;

    JttMsg encode(const my::constr& sim, int b2019) {
        JttMsg t(sim, 0x0500, b2019);

        my::string& body = t.body;
        body << my::hton << sn << loc.str();
        return t.optimized();
    }
};

struct X0701 {
    my::uint   ebillLen;
    my::string ebill;

    JttMsg encode(const my::constr& sim, bool bNew)
    {
        JttMsg t(sim, 0x0701, bNew);

        my::string& body = t.body;
        body << my::hton << ebillLen << ebill;
        return t.optimized();
    }
};

// 驾驶员身份信息采集上报
struct X0702 {
    my::uchar state;                // 状态
    char time[6];                   // 时间
    my::uchar ic_status;            // IC卡读取结果
    my::string name;                // 驾驶员姓名
    my::string qc_code;             // 从业资格证编码
    my::string ca;                  // 发证机构名称
    char validity_date[4];          // 证件有效期

    JttMsg encode(const my::constr& sim, bool bNew)
    {
        JttMsg t(sim, 0x0702, bNew);

        my::string& body = t.body;
        body << my::hton << state;
        body.append(time, sizeof(time));
        body << my::hton << ic_status << name(1)
             << get_fixed_string(qc_code, 20) << ca(1);
        body.append(validity_date, sizeof(validity_date));
        if (bNew) {
            body << get_fixed_string(qc_code, 20);
        }
        return t.optimized();
    }
};

// 定位数据批量上传
struct X0704 {
    my::ushort num;     // 数据项个数
    my::uchar type;     // 位置数据类型, 0正常位置批量汇报, 1盲区补报
    my::string data;    // 位置汇报数据体

    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0x0704, b2019);
        my::ushort len = (my::ushort)data.length();
        my::string& body = t.body;
        body << my::hton << num << type;
        body.append(data, 0);
        return t.optimized();
    }
};

struct X0705 {
    char time[5] = {0};
    std::vector<CanData> list;
    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0X0705, b2019);
        my::string& body = t.body;
        my::ushort num = list.size();
        body << my::hton << num;
        body.append(time, sizeof(time));
        for (int i = 0; i < list.size(); i++) {
            body << list[i].canID << list[i].canData;
        }
        return t.optimized();
    }

};
/*多媒体事件消息*/
struct X0800
{
    static JttMsg encode(const my::constr& sim, my::uint id, my::uchar type, my::uchar codev,
            my::uchar event_id, my::uchar ch, bool b2019)
    {
        JttMsg t(sim, 0x0800, b2019);

        my::string& body = t.body;
        body << my::hton << id << type << codev << event_id << ch;
        return t.optimized();
    }

};

// 向平台上传多媒体数据
struct X0801 {
    static JttMsg encode(const my::constr& sim, my::uint id, my::uchar type, my::uchar codev,
                         my::uchar event_id, my::uchar ch, const LocationBaseInfo& loc, const my::constr& data, bool b2019)
    {
        JttMsg t(sim, 0x0801, b2019);

        my::string& body = t.body;
        body << my::hton << id << type << codev << event_id << ch
             << loc.str() << data;
        return t.optimized();
    }
};

// 存储多媒体数据检索应答
struct X0802 {
    struct MediaItem {      // 检索项数据
        my::uint id = 0;        // 多媒体ID
        my::uchar media = 0;    // 多媒体类型
        my::uchar ch = 0;       // 通道ID
        my::uchar event = 0;    // 事件项编码
        X0200 location;         // 位置信息汇报

        my::string str()
        {
            my::string body;
            body << my::hton << id << media << ch << event << location.str();
            return body;
        }
    };

    my::ushort sn = 0;              // 应答流水号
    std::vector<MediaItem> items;   // 检索项

    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0x0802, b2019);

        my::string& body = t.body;
        my::ushort num = (my::ushort)items.size();
        body << my::hton << sn << num;

        for (int i = 0; i < num; ++i) {
            body << items[i].str();
        }

        return t.optimized();
    }
};

// 向平台发送摄像头立即拍摄命令应答
struct X0805 {
    static JttMsg encode(const my::constr& sim, my::ushort sn, my::uchar res,
                         std::vector<my::uint>& listId, bool b2019)
    {
        JttMsg t(sim, 0x0805, b2019);
        my::ushort total = (my::ushort)listId.size();
        my::string& body = t.body;
        body << my::hton << sn << res << total;

        for (my::ushort i = 0; i < total; i++) {
            body << listId[i];
        }

        return t.optimized();
    }
};

// 终端上传音视频资源列表
struct X1205 {
    struct NewMediaItem {
        my::uchar ch = 0;           // 逻辑通道号
        char s_time[6];             // 开始时间
        char e_time[6];             // 结束时间
        my::uint64 alarm_tag = 0;   // 报警标志
        my::uchar media = 0;        // 音视频资源类型, 0音视频, 1音频, 2视频
        my::uchar code = 0;     // 码流类型, 1主码流, 2子码流
        my::uchar storage = 0;      // 存储器类型, 1主, 2灾备
        my::uint size = 0;          // 文件大小, 字节(Byte)
        NewMediaItem()
        {
            memset(s_time, 0, sizeof(s_time));
            memset(e_time, 0, sizeof(e_time));
        }
        my::string str()
        {
            my::string body;
            body << my::hton << ch;
            body.append(s_time, sizeof(s_time));
            body.append(e_time, sizeof(e_time));
            body << alarm_tag << media << code << storage << size;
            return body;
        }

        static NewMediaItem create(const NewMedia& nm)
        {
            return create(nm.ch, nm.td.first, nm.td.second, nm.alarm_tag, nm.meida, nm.code, nm.storage, nm.size);
        }

        static NewMediaItem create(my::uchar ch, my::uint64 st, my::uint64 et, my::uint64 alarm,
                                   my::uchar media, my::uchar code, my::uchar storage, my::uint size)
        {
            NewMediaItem m;
            m.ch = ch;
            my::numstr2bcd(m.s_time, my::timestamp::YYMMDD_HHMMSS(st * 1000), sizeof(m.s_time) * 2);
            my::numstr2bcd(m.e_time, my::timestamp::YYMMDD_HHMMSS(et * 1000), sizeof(m.e_time) * 2);
            m.alarm_tag = alarm;
            m.media = media;
            m.code = code;
            m.storage = storage;
            m.size = size;
            return m;
        }
    };

    my::ushort sn = 0;              // 流水号
    std::vector<NewMediaItem> list; // 音视频资源列表
    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0x1205, b2019);
        my::uint total = (my::uint)list.size();
        my::string& body = t.body;
        body << my::hton << sn <<  total;

        for (my::uint i = 0; i != total; i++) {
            body << list[i].str();
        }

        return t.optimized();
    }
};

// 文件上传完成通知
struct X1206 {
    my::ushort sn;              // 应答流水号
    my::uchar res;              // 结果, 0成功, 1失败

    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0x1206, b2019);
        my::string& body = t.body;
        body << my::hton << sn << res;
        logd("X1206 sn = %d, %s", sn, my::hex(body).c_str());
        return t.optimized();
    }
};

typedef enum {
    MM_PHOTO = 0,
    MM_AUDIO = 1,
    MM_VIDEO = 2,
    MM_BIN   = 3,
} MM_TYPE_E;
// 报警附件信息消息
struct X1210 {
    struct Attachment {
        my::string file;    // 协议文件名称字符串
        my::string path;    // 实际文件路径
        my::uint size = 0;  // 当前文件的大小
        MM_TYPE_E type;

        my::string str()
        {
            my::string body;
            body << my::hton << file(1) << size;
            return body;
        }
    };

    my::string terminal_id; // 终端ID
    AlarmTag alarm_tag;     // 报警标识号
    my::string alarm_id;   // 平台给报警分配的唯一编号
    my::uchar type = 0;     // 信息类型, 0x00正常报警文件信息, 0x01补传报警文件信息
    std::vector<Attachment> attachment; // 附件信息列表

    JttMsg encode(const my::constr& sim, int jt808_new, const  my::string & prot_subtype)
    {
        JttMsg t(sim, 0x1210, jt808_new);
        my::string& body = t.body;
        body << my::hton;

        if ((prot_subtype == "guangdong") || (prot_subtype == "sichuan")) {
            if (terminal_id.length() <= 30) {
                body.append(terminal_id.c_str(), terminal_id.length());
                my::uchar na = 0;
                for (int i = 0; i < (30 - terminal_id.length()); i++) {
                    body << na;
                }
            } else {
                body.append(terminal_id.c_str(), 30);
            }
        } else {

            if (terminal_id.length() <= 7) {
                body.append(terminal_id.c_str(), terminal_id.length());
                my::uchar na = 0;
                for (int i = 0; i < (7 - terminal_id.length()); i++) {
                    body << na;
                }
            } else {
                body.append(terminal_id.c_str(), 7);
            }
        }

        body << alarm_tag.str(jt808_new, prot_subtype);
        if (alarm_id.length() <= 32) {
            body.append(alarm_id.c_str(), alarm_id.length());
            my::uchar na = 0;
            for (int i = 0; i < (32 - alarm_id.length()); i++) {
                body << na;
            }
        } else {
            body.append(alarm_id.c_str(), 32);
        }
        my::uchar num = (my::uchar)attachment.size();
        body << type << num;

        for (int i = 0; i < num; ++i) {
            body << attachment[i].str();
        }

        return t.optimized();
    }

};

// 文件信息上传
struct X1211 {
    my::string path;            // 文件名称
    my::uchar type = 0;         // 文件类型, 0图片, 1音频, 2视频, 3文本, 4其它
    my::uint size = 0;          // 文件大小
    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0x1211, b2019);
        my::string& body = t.body;
        body << my::hton << path(1) << type << size;
        return t.optimized();
    }
};

// 文件上传完成消息
struct X1212 {
    my::string path;        // 文件名称
    my::uchar type = 0;     // 文件类型
    my::uint size = 0;      // 文件大小
    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0x1212, b2019);
        my::string& body = t.body;
        body << my::hton << path(1) << type << size;
        return t.optimized();
    }
};


// 设备收到来自平台的通用应答
struct X8001 {
    my::uchar res;
    my::ushort sn;
    my::ushort cmd;

    bool decode(my::constr data)
    {
        if (data.length() != 5) {
            return false;
        }

        data >> my::ntoh >> sn >> cmd >> res;
        return true;
    }
    enum RES {
        RES_OK = 0,
        RES_FAIL = 1,
        RES_ERROR = 2,
        RES_UNKNOWN = 3,
        RES_ALARM = 4
    };
};

// 补传分包请求, todo
struct X0005 {
    my::ushort sn;
    my::ushort num;
    std::vector<my::ushort> idxs;
    bool decode(my::constr data, bool bNew)
    {
        if (data.length() < 3) {
            return false;
        }

        data >> my::ntoh >> sn >> num;
        if (data.length() < 2 * num) {
            return false;
        }
        for (int i = 0; i < num; i++) {
            my::ushort tmp;
            data >> tmp;
            idxs.push_back(tmp);
        }
        return true;
    }
    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0x0005, b2019);
        num = idxs.size();
        t.body << my::hton << sn << num;
        for (my::ushort & idx : idxs) {
            t.body << idx;
        }
        return t.optimized();
    }

};

// 文本信息下发
struct X8300 {
    my::uchar cmd;  // 标志
    my::uchar type; // 文本类型， 1通知 2服务
    my::string msg; // 文本信息

    bool decode(my::constr data, bool bNew)
    {
        if (data.length() < 1) {
            return false;
        }

        my::string gbkMsg;
        data >> my::ntoh >> cmd;
        if (bNew && !!access("/data/8300_ignore_type", R_OK)) {
            data >> type;
        }
        data >> gbkMsg;
        char utf8Msg[4096];
#if 1
        my::gbkToUtf8((char*)gbkMsg.c_str(), utf8Msg);
#else
        my::conf::ini::CCConvert("UTF-8", utf8Msg, sizeof(utf8Msg), "gbk", gbkMsg.c_str(), gbkMsg.length());
#endif
        msg = utf8Msg;
        return true;
    }
};

struct Answer {
    my::uchar id = 0;               // 答案ID
    my::string ans;             // 答案内容

    bool decode(my::constr& data)
    {
        if (data.length() < 3) {
            return false;
        }

        data >> my::ntoh >> id >> ans(2);
        return true;
    }
};
struct EvtCtx{
    my::uchar id;
    my::string ctx;
    bool decode(my::constr & data, bool bNew)
    {
        if (data.length() < 2) {
            return false;
        }
        data >> id >> ctx(1);
        return true;
    }
};

struct X8301 {
    my::uchar op; /*0删除所有; 1 更新; 2 追加; 3 修改; 4删除几个*/
    my::uchar count;
    std::vector<EvtCtx> evts;
    bool decode(my::constr data, bool bNew)
    {
        if (data.length() < 2) {
            return false;
        }
        data >> op >> count;
        bool ret = false;

        do {
            EvtCtx ctx;
            ret = ctx.decode(data, bNew);
            if (ret) {
                logd("id %d", ctx.id);
                evts.push_back(ctx);
            } else {
                break;
            }
        } while (data.length()  && ret && (evts.size() < count));

        return count == evts.size();
    }
};
// 提问下发
struct X8302 {
    my::uchar flag;             // 标志
    my::string question;        // 问题
    std::vector<Answer> answers;    // 候选答案列表

    bool decode(my::constr data)
    {
        if (data.length() < 2) {
            return false;
        }

        data >> my::ntoh >> flag >> question(1);

        while (!data.empty()) {
            Answer ans;

            if (!ans.decode(data)) {
                return false;
            }

            answers.push_back(ans);
        }

        return true;
    }
};

struct InfoItem {
    my::uchar type = 0;     // 信息类型
    my::string name;        // 信息名称
    my::uchar status = 0;   // 点播状态：0取消, 1点播
    my::string msg;         // 信息服务消息
};

// 信息点播菜单设置
struct X8303 {
    my::uchar type = 0;         // 设置类型：0删除, 1更新, 2追加, 3修改
    std::vector<InfoItem> info_items;       // 信息项

    bool decode(my::constr data)
    {
        if (data.length() < 1) {
            return false;
        }

        my::uchar len;
        data >> my::ntoh >> type >> len;

        for (int i = 0; i < len; ++i) {
            InfoItem item;
            data >> item.type >> item.name(2);
            info_items.push_back(item);
        }

        return true;
    }
};

// 信息服务
struct X8304 {
    my::uchar type;         // 信息类型
    my::string msg;         // 信息内容
    bool decode(my::constr data)
    {
        if (data.length() < 1) {
            return false;
        }

        data >> my::ntoh >> type >> msg(2);
        return true;
    }
};

// 设置电话本
struct X8401 {
    my::uchar mode;     // 设置类型
    std::vector<Contacts> contacts; // 联系人项

    bool decode(my::constr data)
    {
        if (data.length() < 2) {
            return false;
        }

        my::uchar num;
        data >> my::ntoh >> mode >> num;

        for (int i = 0; i < num && !data.empty(); i++) {
            Contacts con;
            data >> con.type >> con.number(1) >> con.name(1);
            contacts.push_back(con);
        }

        return contacts.size() == num;
    }



};

struct X8500 {
    my::ushort count;
    std::map<my::ushort, my::string> params;
    bool decode(my::constr data, bool b2019)
    {
        if (data.length() < (1 + b2019)) {
            return false;
        }
        if (!b2019) {
            my::ushort id = 0x0001;
            my::uchar param;
            data >> my::ntoh >> param;
            params[id] << param;
            return true;
        }

        data >> my::ntoh >> count;

        int num = 0;
        for (int i = 0; i < count && !data.empty(); i++) {
            my::ushort id;
            my::string param;
            data >> id;
            switch(id) {
                case 0x0001: {
                    my::uchar ctl;
                    data >> ctl;
                    param << ctl;
                    num++;
                    break;
                }
                default:{
                    data >> param;
                    loge("not support id %d", id);
                    break;
                }
            }
            params[id] = param;
        }

        return params.size() == num;
    }
};
// 设备收到来自平台的摄像头立即拍摄命令
struct X8801 {
    my::uchar ch; // 通道ID
    my::ushort cmd; // 拍摄命令, 0 表示停止拍摄；0xFFFF 表示录像；其它表示拍照张数
    my::ushort inteval; // 拍照间隔/录像时间
    my::uchar save; // 保存标志, 1 保存，0 实时上传
    my::uchar resolution; // 分辨率
    my::uchar quality; // 图像/视频质量
    my::uchar brightness; // 亮度
    my::uchar contrast; // 对比度
    my::uchar saturability; // 饱和度
    my::uchar chroma; // 色度
    
    my::uint64 lastSnapTm;
    my::uint64 curSnapTm;
    my::ushort reqSn;
    my::ushort count;

    bool decode(my::constr data)
    {
        if (data.length() != 12) {
            return false;
        }

        data >> my::ntoh >> ch >> cmd >> inteval >> save
             >> resolution >> quality >> brightness >> contrast >> saturability >> chroma;
        resolution -= 1;
        count = 0;
        return true;
    }
};

// 存储多媒体数据检索
struct X8802 {
    my::uchar media = 0;            // 多媒体类型：0图像, 1音频, 2视频
    my::uchar ch = 0;               // 通道ID, 0表示所有通道
    my::uchar event = 0;            // 事件项编码：0平台下发, 1定时, 2抢劫, 3碰撞
    char s_time[6];             // 起始时间, YYMMDDhhmmss
    char e_time[6];             // 结束时间, YYMMDDhhmmss

    X8802()
    {
        memset(s_time, 0, sizeof(s_time));
        memset(e_time, 0, sizeof(e_time));
    }

    bool decode(my::constr data)
    {
        if (data.length() != 15) {
            return false;
        }

        data >> my::ntoh >> media >> ch >> event;
        if (data.length() >= 12) {
            memcpy(s_time, (const char*)data, sizeof(s_time));
            data += sizeof(s_time);
            memcpy(e_time, (const char*)data, sizeof(e_time));
            data += sizeof(e_time);
        }
        return true;
    }
};

// 存储多媒体数据上传命令
struct X8803 {
    X8802 x8802;
    my::uchar flag = 0;         // 删除标志：0保留, 1删除
    bool decode(my::constr data)
    {
        my::constr d(data, 0, 15);

        if (data.length() != 16 || !x8802.decode(d)) {
            return false;
        }

        data += 15;
        data >> my::ntoh >> flag;
        return true;
    }
};

// 单条存储多媒体数据检索上传命令
struct X8805 {
    my::uint id = 0;            // 多媒体ID, >0
    my::uchar flag = 0;         // 删除标志：0保留, 1删除
    bool decode(my::constr data)
    {
        if (data.length() != 5) {
            return false;
        }

        data >> my::ntoh >> id >> flag;
        return id > 0 ? true : false;
    }
};
typedef struct AreaAttr {
    uint16_t enBETime       : 1;
    uint16_t enSpdMaxSpding : 1;//reserved for line
    uint16_t enAlarmIn2drv  : 1;
    uint16_t enAlarmIn2srv  : 1;
    uint16_t enAlarmOut2drv : 1;
    uint16_t enAlarmOut2srv : 1;
    uint16_t latitude       : 1;//0 north,1 south //reserved for line
    uint16_t longitude      : 1;//0 east, 1 west//reserved for line
    uint16_t banOpenDr      : 1;//reserved for line
    uint16_t reserved       : 5;//reserved for line
    uint16_t in2CloseCom    : 1;//reserved for line
    uint16_t in2CollectGNSS : 1;//reserved for line
}__attribute__((packed)) AreaAttrT;


// 设置圆形区域
struct X8600 {
    struct Circle    { // 圆形区域
        my::uint id = 0;        // 区域ID
        my::ushort type = 0;    // 区域属性
        my::uint c_lat = 0; // 中心点纬度,10的6次方
        my::uint c_lng = 0; // 中心点经度, 10的6次方
        my::uint r = 0;     // 半径
        char s_time[6] = {0}; // 起始时间, YYMMDDhhmmss
        char e_time[6] = {0}; // 结束时间, YYMMDDhhmmss
        my::ushort h_speed = 0;     // 最高速度, Km/h
        my::uchar duration = 0;         // 持续时间
        my::ushort h_spd_night = 0;
        my::string name;
        Circle()
        {
            memset(s_time, 0, sizeof(s_time));
            memset(e_time, 0, sizeof(e_time));
        }
        Circle(Area::Ptr ap)
        {
            ::Circle& c = *(::Circle*)(&*ap);
            id = c.id;
            type = c.type;
            c_lat = c.c_lat * 1000 * 1000;
            c_lng = c.c_lng * 1000 * 1000;
            r = c.r;
            AreaAttrT * pAttr = (AreaAttrT*)&type;
            if (pAttr->enBETime) {
                my::time2bcd(c.st, s_time);
                my::time2bcd(c.et, e_time);
            }
            h_speed = c.h_speed;
            duration = c.duration;
            h_spd_night = c.h_spd_night;
            name = c.name;
        }
        Area::Ptr toAreaPtr()
        {
            Area::Ptr ap(new ::Circle());
            ::Circle& c = *(::Circle*)(&*ap);
            c.id = id;
            c.type = type;
            AreaAttrT * pAttr = (AreaAttrT*)&type;
            if (pAttr->enBETime) {
                my::string st, et;
                my::bcd2numstr(st, s_time, sizeof(s_time));
                my::bcd2numstr(et, e_time, sizeof(e_time));
                c.st = my::timestamp::seconds_from_19700101(st);
                c.et = my::timestamp::seconds_from_19700101(et);
            }
            c.area_type = Area::TYPE::Circle;
            c.c_lat = c_lat / 1000000.0;
            c.c_lng = c_lng / 1000000.0;
            c.r = r;
            c.h_speed = h_speed;
            c.duration = duration;
            c.h_spd_night = h_spd_night;
            c.name = name;
            return ap;
        }

        bool decode(my::constr & data, bool bNew)
        {
            if (data.length() < 18) {
                loge("decode error!");
                return false;
            }
            data >> my::ntoh;
            try {
                data >> id >> type >> c_lat >> c_lng >> r;
                AreaAttrT * pAttr = (AreaAttrT*)&type;
                if (pAttr->enBETime) {
                    memcpy(s_time, (const char*)data, sizeof(s_time));
                    data += sizeof(s_time);
                    memcpy(e_time, (const char*)data, sizeof(e_time));
                    data += sizeof(e_time);
                }

                if (pAttr->enSpdMaxSpding) {
                    data >> h_speed >> duration;
                    if (bNew && (data.length() >= 2)) {
                        data >> h_spd_night;
                    }
                }
                if (bNew && (data.length() >= 2)) {
                    data >> name(2);
                }
            } catch (std::exception & e) {
                logw("%s", e.what());
            }
            return true;
        }

        my::string encode(bool bNew)
        {
            my::string data;

            data << my::hton << type << c_lat << c_lng << r;
            AreaAttrT * pAttr = (AreaAttrT*)&type;
            if (pAttr->enBETime) {
                data.append(s_time, sizeof(s_time));
                data.append(e_time, sizeof(e_time));
            }
            if (pAttr->enSpdMaxSpding) {
                data << h_speed << duration;
                if (bNew) {
                    data << h_spd_night;
                }
            }
            if (bNew) {
                data << name(2);
            }
            return data;
        }
    };

    my::uchar type = 0;             // 设置属性：0更新, 1追加, 2修改
    my::uchar total = 0;            // 总数
    std::vector<Circle> circles;                // 圆形区域项

    bool decode(my::constr data, bool bNew)
    {
        if (data.length() < 2) {
            return false;
        }

        data >> my::ntoh >> type >> total;
        for (int i = 0; i < total; ++i) {
            Circle c;
            if (c.decode(data, bNew)) {
                circles.push_back(c);
            }
        }
        return true;
    }
};

// 删除圆形区域
struct X8601 {
    my::uchar total = 0;                // 区域数：0删除所有, 不超过125个
    std::vector<my::uint> ids;          //  待删除的区域

    bool decode(my::constr data)
    {
        if (data.length() < 1) {
            return false;
        }

        data >> my::ntoh >> total;

        for (int i = 0; i < total; ++i) {
            my::uint id;
            data >> id;
            ids.push_back(id);
        }

        return true;
    }
};

// 设置矩形区域
struct X8602 {
    struct Rectange { // 矩形区域
        my::uint id = 0;    // 区域ID
        my::ushort type = 0;    // 区域属性
        my::uint lt_lat = 0;    // 左上纬度, 10的6次方分之一
        my::uint lt_lng = 0;    // 左上经度, 10的6次方分之一
        my::uint rb_lat = 0;    // 右下纬度, 10的6次方分之一
        my::uint rb_lng = 0;    // 右下经度, 10的6次方分之一
        char s_time[6] = {0};         // 起始时间
        char e_time[6] = {0};         // 结束时间
        my::ushort h_speed = 0; // 最高时速, km/h
        my::uchar  duration = 0; // 超速持续时间, 秒
        my::ushort h_spd_night = 0;
        my::string name;

        Rectange()
        {
            memset(s_time, 0, sizeof(s_time));
            memset(e_time, 0, sizeof(e_time));
        }
        Rectange(Area::Ptr ap)
        {
            ::Rectange& a = *(::Rectange*)(&*ap);
            id = a.id;
            type = a.type;
            lt_lat = a.lt_lat * 1000 * 1000;
            lt_lng = a.lt_lng * 1000 * 1000;
            rb_lat = a.rb_lat * 1000 * 1000;
            rb_lng = a.rb_lng * 1000 * 1000;
            AreaAttrT * pAttr = (AreaAttrT*)&type;
            if (pAttr->enBETime) {
                my::time2bcd(a.st, s_time);
                my::time2bcd(a.et, e_time);
            }
            h_speed = a.h_speed;
            duration = a.duration;
            h_spd_night = a.h_spd_night;
            name = a.name;
        }
        Area::Ptr toAreaPtr()
        {
            Area::Ptr ap(new ::Rectange());
            ::Rectange& r = *(::Rectange*)(&*ap);
            r.id = id;
            r.type = type;
            AreaAttrT * pAttr = (AreaAttrT*)&type;
            if (pAttr->enBETime) {
                my::string st, et;
                my::bcd2numstr(st, s_time, sizeof(s_time));
                my::bcd2numstr(et, e_time, sizeof(e_time));
                r.st = my::timestamp::seconds_from_19700101(st);
                r.et = my::timestamp::seconds_from_19700101(et);
            }
            r.area_type = Area::TYPE::Rectange;
            r.lt_lat = lt_lat / 1000000.0;
            r.lt_lng = lt_lng / 1000000.0;
            r.rb_lat = rb_lat / 1000000.0;
            r.rb_lng = rb_lng / 1000000.0;
            r.h_speed = h_speed;
            r.duration = duration;
            r.h_spd_night = h_spd_night;
            r.name = name;
            return ap;
        }

        bool decode(my::constr & data, bool bNew)
        {
            if (data.length() < 22) {
                loge("rectAngle fail!");
                return false;
            }
            data >> my::ntoh;

            try {
                data >> id >> type >> lt_lat >> lt_lng
                     >> rb_lat >> rb_lng;

                AreaAttrT * pAttr = (AreaAttrT*)&type;
                if (pAttr->enBETime) {
                    memcpy(s_time, (const char*)data, sizeof(s_time));
                    data += sizeof(s_time);
                    memcpy(e_time, (const char*)data, sizeof(e_time));
                    data += sizeof(e_time);
                }

                if (pAttr->enSpdMaxSpding) {
                    data >> h_speed >> duration;
                    if (bNew && (data.length() >= 2)) {
                        data >> h_spd_night;
                    }
                }
                if (bNew && (data.length() >= 2)) {
                    data >> name(2);
                }
            } catch (std::exception & e) {
                logw("%s", e.what());
            }

            return true;
        }
        my::string encode(bool bNew)
        {
            my::string data;

            data << my::hton << id << type << lt_lat << lt_lng << rb_lat << rb_lng;
            AreaAttrT * pAttr = (AreaAttrT*)&type;
            if (pAttr->enBETime) {
                data.append(s_time, sizeof(s_time));
                data.append(e_time, sizeof(e_time));
            }
            if (pAttr->enSpdMaxSpding) {
                data << h_speed << duration;
                if (bNew) {
                    data << h_spd_night;
                }
            }
            if (bNew) {
                data << name(2);
            }
            return data;
        }

    };

    my::uchar type = 0;             // 设置属性：0更新, 1追加, 2修改
    my::uchar total = 0;            // 区域总数
    std::vector<Rectange> rects;    // 区域项

    bool decode(my::constr data, bool bNew)
    {
        if (data.length() < 2) {
            return false;
        }

        data >> my::ntoh >> type >> total;
        for (int i = 0; i < total; ++i) {
            Rectange r;
            if (r.decode(data, bNew)) {
                rects.push_back(r);
            }
        }

        return true;
    }

};

// 删除矩形区域
struct X8603 {
    my::uchar total = 0;                // 区域数：0删除所有, 不超过125个
    std::vector<my::uint> ids;          //  待删除的区域

    bool decode(my::constr data)
    {
        if (data.length() < 1) {
            return false;
        }

        data >> my::ntoh >> total;

        for (int i = 0; i < total; ++i) {
            my::uint id;
            data >> id;
            ids.push_back(id);
        }

        return true;
    }
};


// 设置多边形区域
struct X8604 {
    struct Vertex { // 顶点
        my::uint lat = 0;       // 顶点纬度, 10的6次方分之一
        my::uint lng = 0;       // 顶点经度, 10的6次方分之一
    };
    my::uint id = 0;                // 区域ID
    my::ushort type = 0;            // 区域属性
    char s_time[6] = {0};             // 起始时间
    char e_time[6] = {0};             // 结束时间
    my::ushort h_speed = 0;     // 最高速度, km/h
    my::uchar duration = 0;     // 超速持续时间
    my::ushort v_total = 0;     // 区域顶点数
    std::vector<Vertex> v_list; // 顶点项
    my::ushort h_spd_night = 0;
    my::string name;

    X8604()
    {
        memset(s_time, 0, sizeof(s_time));
        memset(e_time, 0, sizeof(e_time));
    }
    X8604(Area::Ptr ap)
    {
        ::PolygonA& a = *(::PolygonA*)(&*ap);
        id = a.id;
        type = a.type;
        AreaAttrT * pAttr = (AreaAttrT*)&type;
        if (pAttr->enBETime) {
            my::time2bcd(a.st, s_time);
            my::time2bcd(a.et, e_time);
        }
        h_speed = a.h_speed;
        duration = a.duration;
        v_total = a.points.size();
        for (auto p : a.points) {
            Vertex v;
            v.lat = p.lat * 1000000;
            v.lng = p.lng * 1000000;
            v_list.push_back(v);
        }
        h_spd_night = a.h_spd_night;
        name = a.name;
    }
    Area::Ptr toAreaPtr()
    {
        Area::Ptr ap(new PolygonA());
        PolygonA& p = *(PolygonA*)(&*ap);
        p.id = id;
        p.type = type;
        AreaAttrT * pAttr = (AreaAttrT*)&type;
        if (pAttr->enBETime) {
            my::string st, et;
            my::bcd2numstr(st, s_time, sizeof(s_time));
            my::bcd2numstr(et, e_time, sizeof(e_time));
            p.st = my::timestamp::seconds_from_19700101(st);
            p.et = my::timestamp::seconds_from_19700101(et);
        }
        p.area_type = Area::TYPE::Ploygon;
        p.h_speed = h_speed;
        p.duration = duration;

        for (auto it = v_list.begin(); it != v_list.end(); ++it) {
            p.points.push_back(LocPoint::create(it->lat / 1000000.0, it->lng / 1000000.0));
        }
        p.h_spd_night = h_spd_night;
        p.name = name;

        return ap;
    }

    bool decode(my::constr data, bool bNew)
    {
        if (data.length() < 32) { // 至少3个顶点
            return false;
        }
        try {
            data >> my::ntoh >> id >> type;

            AreaAttrT * pAttr = (AreaAttrT*)&type;
            if (pAttr->enBETime) {
                memcpy(s_time, (const char*)data, sizeof(s_time));
                data += sizeof(s_time);
                memcpy(e_time, (const char*)data, sizeof(e_time));
                data += sizeof(e_time);
            }

            if (pAttr->enSpdMaxSpding) {
                data >> h_speed >> duration;
            }

            data >> v_total;

            for (int i = 0; i < v_total; ++i) {
                Vertex v;
                data >> v.lat >> v.lng;
                v_list.push_back(v);
            }
            if (pAttr->enSpdMaxSpding && (bNew && (data.length() >= 2))) {
                data >> h_spd_night;
            }
            if (bNew && (data.length() >= 2)) {
                data >> name(2);
            }
        } catch (std::exception & e) {
            logw("%s", e.what());
        }
        return true;
    }

    my::string encode(bool bNew)
    {
        my::string data;

        data << my::hton << id << type;
        AreaAttrT * pAttr = (AreaAttrT*)&type;
        if (pAttr->enBETime) {
            data.append(s_time, sizeof(s_time));
            data.append(e_time, sizeof(e_time));
        }
        if (pAttr->enSpdMaxSpding) {
            data << h_speed << duration;
        }
        data << v_total;
        for (auto v : v_list) {
            data << v.lat;
            data << v.lng;
        }
        if (pAttr->enSpdMaxSpding) {
            if (bNew) {
                data << h_spd_night;
            }
        }
        if (bNew) {
            data << name(2);
        }
        return data;
    }
};

// 删除多边形区域
struct X8605 {
    my::uchar total = 0;        // 区域数
    std::vector<my::uint> ids;  // 区域项

    bool decode(my::constr data)
    {
        if (data.length() < 1) {
            return false;
        }

        data >> my::ntoh >> total;

        for (int i = 0; i < total; ++i) {
            my::uint id;
            data >> id;
            ids.push_back(id);
        }

        return true;
    }
};
typedef struct PathAttr {
    uint8_t enTime       : 1;
    uint8_t enSpdMaxSpding : 1;//reserved for line
    uint8_t latitude       : 1;//0 north,1 south //reserved for line
    uint8_t longitude      : 1;//0 east, 1 west//reserved for line
    uint8_t reserved       : 4;//reserved for line
}__attribute__((packed)) PathAttrT;

// 设置路线
struct X8606 {
    struct Point {      // 拐点项
        my::uint point_id = 0;      // 拐点ID
        my::uint path_id = 0;       // 线路ID
        my::uint p_lat = 0;         // 拐点纬度, 10的6次方分之一
        my::uint p_lng = 0;         // 拐点经度, 10的6次方分之一
        my::uchar width = 0;        // 路段宽度
        my::uchar type = 0;         // 路段属性
        my::ushort max_time = 0;        // 行驶过长阈值
        my::ushort min_time = 0;        // 行驶不足阈值
        my::ushort h_speed = 0;         // 最高速度
        my::uchar duration = 0;         // 超速持续时间, 秒
        my::ushort h_spd_night = 0;
        bool decode(my::constr & data, bool bNew)
        {
            if (data.length() < 18) {
                return false;
            }
            data >> my::ntoh >> point_id >> path_id >> p_lat >> p_lng >> width >> type;

            PathAttr * pPAttr = (PathAttr*)&type;
            if (pPAttr->enTime && data.length() >= 4) {
                data >> max_time >> min_time;
            }

            if (pPAttr->enSpdMaxSpding && data.length() >= 3) {
                data >> h_speed >> duration;
                if (bNew && (data.length() >= 2)) {
                    data >> h_spd_night;
                }
            }
            return true;
        }
        my::string encode(bool bNew)
        {
            my::string data;
            data << my::hton << point_id << path_id << p_lat << p_lng << width << type;
            PathAttr * pPAttr = (PathAttr*)&type;
            if (pPAttr->enTime) {
                data << max_time << min_time;
            }

            if (pPAttr->enSpdMaxSpding) {
                data << h_speed << duration;
                if (bNew) {
                    data << h_spd_night;
                }
            }

            return data;
        }
    };

    my::uint id = 0;                // 路线ID
    my::ushort type = 0;            // 路线属性
    char s_time[6] = {0};             // 起始时间
    char e_time[6] = {0};             // 结束时间
    my::ushort p_total = 0;       // 路线总拐点数
    std::vector<Point> points;      // 拐点项
    my::string name;

    X8606()
    {
        memset(s_time, 0, sizeof(s_time));
        memset(e_time, 0, sizeof(e_time));
    }
    X8606(Area::Ptr ap)
    {
        ::LineArea & a = *(::LineArea*)(&*ap);
        id = a.id;
        type = a.type;
        AreaAttrT * pAttr = (AreaAttrT*)&type;
        if (pAttr->enBETime) {
            my::time2bcd(a.st, s_time);
            my::time2bcd(a.et, e_time);
        }
        p_total = a.points.size();
        for (auto p : a.points) {
            Point v;
            v.point_id = p.point_id;
            v.path_id  = p.path_id;
            v.p_lat    = p.p_lat * 1000000;
            v.p_lng    = p.p_lng * 1000000;
            v.width    = p.width;
            v.type     = p.type;
            v.max_time = p.max_time;
            v.min_time = p.min_time;
            v.h_speed  = p.h_speed;
            v.duration = p.duration;
            v.h_spd_night = p.h_spd_night;
            points.push_back(v);
        }
        name = a.name;
    }

    bool decode(my::constr data, bool bNew)
    {
        if (data.length() < 8) {
            return false;
        }

        data >> my::ntoh >> id >> type;
        try {
            AreaAttrT * pAttr = (AreaAttrT*)&type;
            if (pAttr->enBETime) {
                memcpy(s_time, (const char*)data, sizeof(s_time));
                data += sizeof(s_time);
                memcpy(e_time, (const char*)data, sizeof(e_time));
                data += sizeof(e_time);
            }

            data >> p_total;

            for (int i = 0; i < p_total; ++i) {
                Point p;
                if (p.decode(data, bNew)) {
                    points.push_back(p);
                }
            }
            if (bNew && (data.length() >= 2)) {
                data >> name(2);
            }
        } catch (std::exception & e) {
            logw("%s", e.what());
        }

        return true;
    }

    Area::Ptr toAreaPtr()
    {
        Area::Ptr ap(new LineArea());
        LineArea& la = *(LineArea*)(&*ap);
        la.id = id;
        la.type = type;
        AreaAttrT * pAttr = (AreaAttrT*)&type;
        if (pAttr->enBETime) {
            my::string st, et;
            my::bcd2numstr(st, s_time, sizeof(s_time));
            my::bcd2numstr(et, e_time, sizeof(e_time));
            la.st = my::timestamp::seconds_from_19700101(st);
            la.et = my::timestamp::seconds_from_19700101(et);
        }
        la.area_type = Area::TYPE::LineArea;

        for (auto it = points.begin(); it != points.end(); ++it) {
            LineArea::LinePoint lp;
            lp.point_id = it->point_id;     // 拐点ID
            lp.path_id = it->path_id;       // 线路ID
            lp.p_lat = it->p_lat / 1000000.0;           // 拐点纬度
            lp.p_lng = it->p_lng / 1000000.0;           // 拐点经度
            lp.width = it->width;       // 路段宽度
            lp.type = it->type;         // 路段属性
            lp.max_time = it->max_time;     // 行驶过长阈值
            lp.min_time = it->min_time;     // 行驶不足阈值
            lp.h_speed = it->h_speed;           // 最高速度
            lp.duration = it->duration;         // 超速持续时间, 秒
            lp.h_spd_night = it->h_spd_night;
            la.points.push_back(lp);
        }
        la.name = name;
        return ap;
    }

    my::string encode(bool bNew)
    {
        my::string data;
        data << my::hton << id << type;
        AreaAttrT * pAttr = (AreaAttrT*)&type;
        if (pAttr->enBETime) {
            data.append(s_time, sizeof(s_time));
            data.append(e_time, sizeof(e_time));
        }

        data << p_total;

        for (auto p : points) {
            data << p.encode(bNew);
        }
        if (bNew) {
            data << name(2);
        }

        return data;
    }
};

// 删除路线区域
struct X8607 {
    my::uchar total = 0;        // 区域数
    std::vector<my::uint> ids;  // 区域项

    bool decode(my::constr data)
    {
        if (data.length() < 1) {
            return false;
        }

        data >> my::ntoh >> total;

        for (int i = 0; i < total; ++i) {
            my::uint id;
            data >> id;
            ids.push_back(id);
        }

        return true;
    }
};

struct X8608 {
    my::uchar type;
    my::uint  areaIdNum;
    std::map<my::uint, Area::Ptr> idTbl;
    bool decode(my::constr data)
    {
        data >> my::ntoh >> type >> areaIdNum;
        for (int i = 0; i < areaIdNum; i++)
        {
            my::uint id;
            data >> id;
            idTbl.insert(std::pair<my::uint, Area::Ptr>(id, NULL));
        }
        return true;
    }
};
struct X0608 {
    my::uchar type;
    my::uint  num;
    JttMsg encode(const my::constr& sim, std::map<my::uint, Area::Ptr> & areaMap, bool b2019)
    {
        JttMsg t(sim, 0x0608, b2019);
        my::string& body = t.body;
        num = areaMap.size();
        if (type == Area::TYPE::Circle || type == Area::TYPE::Rectange) {
            my::uchar n = num;
            my::uchar attr = 0;
            body << my::hton << type << num << attr << n;
        } else {
            body << my::hton << type << num;
        }
        for (auto i : areaMap) {
            if (i.second) {
                switch(type) {
                    case Area::TYPE::Circle: {
                        X8600::Circle c(i.second);
                        body << c.encode(b2019);
                        break;
                    }
                    case Area::TYPE::Rectange: {
                        X8602::Rectange r(i.second);
                        body << r.encode(b2019);
                        break;
                    }
                    case Area::TYPE::Ploygon: {
                        X8604 p(i.second);
                        body << p.encode(b2019);
                        break;
                    }
                    case Area::TYPE::LineArea: {
                        X8606 p(i.second);
                        body << p.encode(b2019);
                        break;
                    }
                    default: {
                        loge("invalid type %d", type);
                        break;
                    }
                }
            }
        }
        return t.optimized();
    }
};
// 收到平台发来的多媒体数据上传应答
struct X8800 {
    my::uint id;
    my::uchar retry_num;
    my::string retry_list;

    bool decode(my::constr data)
    {
        if (data.length() < 5) {
            return false;
        }

        data >> my::ntoh >> id >> retry_num >> retry_list;
        return true;
    }
};

// 设备收到来自平台的注册应答
struct X8100 {
    my::uchar res;
    my::ushort sn;
    my::string auth; // 鉴权码

    bool decode(my::constr data)
    {
        if (data.length() < 3) {
            return false;
        }

        data >> my::ntoh >> sn >> res >> auth;
        return true;
    }
    enum RES {
        RES_OK = 0,
        RES_VEHICLE_REG = 1,
        RES_NO_VEHICLE = 2,
        REG_CLIENT_REG = 3,
        REG_NO_CLIENT = 4
    };
};

// 设置终端参数
struct X8103 {
    std::vector<TParam> list; // 参数项列表

    bool decode(my::constr data)
    {
        if (data.length() < 1) {
            return false;
        }

        my::uchar num;
        data >> my::ntoh >> num;

        for (int i = 0; i < num && !data.empty(); i++) {
            TParam tp;
            data >> my::ntoh >> tp.id >> tp.val(1);
            list.push_back(tp);
        }

        return (my::uchar)list.size() == num;
    }
};

// 查询终端参数
struct X8104 {
    bool decode(my::constr data)
    {
        return data.length() == 0;
    }
};

// 查询指定参数
struct X8105 {
    my::uchar cmd;  // 控制指令 1 - 升级，4 - reboot, 其他不支持
    std::vector<my::string> ctx;  // 参数列表
    /*
        URL 地址;
        拨号点名称;
        拨号用户名;
        拨号密码;
        地址;
        TCP 端口;
        UDP 端口;
        制造商ID;
        硬件版本;
        固件版本;
        连接到指定服务器时限
    */

    bool decode(my::constr data)
    {
        if (data.length() < 1) {
            return false;
        }

        data >> my::ntoh >> cmd;
        my::string tmp;
        if (4 == cmd) {
            system("sync;reboot;");
        }
        while (!data.empty()) {
            char c;
            data >> c;

            if (c == ';') {
                ctx.push_back(tmp);
                tmp.clear();

            } else {
                tmp << c;
            }
        }

        return !!ctx.size();
    }

    bool decode(const my::string prot_subtype, my::constr data)
    {
        logd("data.length(%d)\n", data.length());
        if (data.length() < 1) {
            return false;
        }

        data >> my::ntoh >> cmd;
        my::string tmp;
        if (4 == cmd) {
            system("sync;reboot;");
        } else if (8 == cmd) {
            /* 通天星扩展为终端关机 */
            if (prot_subtype == "sichuan") {
                my::ushort time = 0;
                if (data.empty()) {
                    ctx.push_back(tmp);
                    return true;
                }

                if (!data.empty() && 2 == data.length()) {
                    my::ushort c;
                    data >> c;
                    tmp << c;
                    ctx.push_back(tmp);
                    return true;
                }
            }
        }
        while (!data.empty()) {
            char c;
            data >> c;

            if (c == ';') {
                ctx.push_back(tmp);
                tmp.clear();

            } else {
                tmp << c;
            }
        }

        return !!ctx.size();
    }
};

// 查询指定参数
struct X8106 {
    my::uchar num;  // 参数总数
    std::vector<my::uint> list;  // 参数ID列表

    bool decode(my::constr data)
    {
        if (data.length() < 1) {
            return false;
        }

        my::uint id;
        data >> my::ntoh >> num;

        for (int i = 0; i < num && !data.empty(); i++) {
            data >> id;
            list.push_back(id);
        }

        return (my::uchar)list.size() == num;
    }
};

// 查询指定参数
struct X8108 {
    my::uchar type;  // 升级类型
    my::uchar venderLen;
    my::uchar vendor[11];
    my::uchar verLen;
    my::string version;
    int32_t    pkgLen;
    const char *  pkgData;
    bool decode(my::constr data, bool b2019)
    {
        if (data.length() < 1) {
            loge("datalen %d error", data.length());
            return false;
        }

        data >> my::ntoh >> type;
        venderLen = b2019 ? 11 : 5;
        if (!access("/data/short_vendor", R_OK)) {
            venderLen = 5;
        } else {
            venderLen = b2019 ? 11 : 5;
        }
        for (int i = 0; i < venderLen; i++) {
            data >> vendor[i];
        }
        data >> version(1);
        verLen = version.length();
        data >> pkgLen;
        pkgData = (const char*)data;
        logd("pkgData %p", pkgData);
        return !!pkgData;
    }
};

// 位置信息查询
struct X8201 {
    bool decode(my::constr data)
    {
        return data.length() == 0;
    }
};

// 临时位置跟踪控制
struct X8202 {
    my::ushort interval;        // 时间间隔
    my::uint expires;           // 位置跟踪有效期

    bool decode(my::constr data)
    {
        if (data.length() != 6) {
            return false;
        }

        data >> my::ntoh >> interval >> expires;
        return true;
    }
};
// 清除告警标志位
struct X8203 {
    my::ushort sn;
    my::uint   warnBits;

    bool decode(my::constr data)
    {
        if (data.length() != 6) {
            return false;
        }

        data >> my::ntoh >> sn >> warnBits;
        return true;
    }
};

struct X1003 {
    uint8_t  audCodeTyp;//ADPCM 26
    uint8_t  audChNum;//2
    uint8_t  audSampleRate;//8k
    uint8_t  audSampleBit;//16
    uint16_t audSampleLen;//320
    uint8_t  audOutEn;//1
    uint8_t  videoCodeTyp;//98
    uint8_t  audPhyNum;//1
    uint8_t  videoPhyNum;//5

    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0x1003, b2019);
        my::string& body = t.body;
        body << my::hton << audCodeTyp << audChNum << audSampleRate;
        body << audSampleBit << audSampleLen << audOutEn;
        body << videoCodeTyp << audPhyNum << videoPhyNum;
        return t.optimized();
    }
};

// 实时音视频请求
struct X9101 {
    my::string server; // 服务器IP
    my::ushort tcp_port; // tcp端口
    my::ushort udp_port; // udp端口
    my::uchar ch; // 逻辑通道号
    my::uchar media; // 数据类型
    my::uchar quality; // 码流类型

    bool decode(my::constr data)
    {
        if (data.length() < 8) {
            return false;
        }

        data >> my::ntoh >> server(1)
             >> tcp_port >> udp_port
             >> ch >> media >> quality;
#if 0
        if (media > 1 && ch > 32) { /* 适配某些平台对讲音频通道从33开始 */
            ch -= 32;
        }
#endif
        int len = strlen(server.c_str());
        server.length(len);
        return true;
    }
};

// 音视频实时传输控制
struct X9102 {
    my::uchar ch; // 逻辑通道号
    my::uchar cmd; // 控制命令
    my::uchar option; // 音视频选项
    my::uchar code; // 码流类型

    bool decode(my::constr data)
    {
        if (data.length() != 4) {
            return false;
        }

        data >> my::ntoh >> ch >> cmd >> option >> code;
        return true;
    }
};

// 实时音视频传输状态通知
struct X9105 {
    my::uchar ch; // 通道
    my::uchar loss; // 丢包率

    bool decode(my::constr data)
    {
        if (data.length() != 2) {
            return false;
        }

        data >> my::ntoh >> ch >> loss;
        return true;
    }
};

// 平台下发远程录像回放请求
struct X9201 {
    my::string server_ip;           // 服务器IP地址
    my::ushort tcp_port = 0;        // 音视频通道TCP监听端口
    my::ushort udp_port = 0;        // 音视频通道UDP监听端口
    my::uchar ch = 0;               // 逻辑通道号
    my::uchar media = 0;            // 音视频类型, 0音视频, 1音频, 2视频, 3视频或音频
    my::uchar code = 0;             // 码流类型, 0主或子码流, 1主码流, 2子码流
    my::uchar storage = 0;          // 存储器类型, 0主或灾备, 1主, 2灾备
    my::uchar mode = 0;             // 回放方式, 0正常, 1快进, 2关键帧快退回放, 3关键帧播放, 4单帧上传
    my::uchar speed = 0;            // 快进或回放倍数, 0无效, 1-1倍, 1-2倍, 3-4倍, 4-8倍, 5-16倍
    char s_time[6];             // 开始时间
    char e_time[6];             // 结束时间
    X9201()
    {
        memset(s_time, 0, sizeof(s_time));
        memset(e_time, 0, sizeof(e_time));
    }

    bool decode(my::constr data)
    {
        if (data.length() < 23) {
            return false;
        }

        data >> my::ntoh >> server_ip(1) >> tcp_port >> udp_port >> ch >> media
             >> code >> storage >> mode >> speed;
        int len = strlen(server_ip.c_str());
        server_ip.length(len);
        memcpy(s_time, (const char*)data, sizeof(s_time));
        data += sizeof(s_time);
        memcpy(e_time, (const char*)data, sizeof(e_time));
        data += sizeof(e_time);
        return true;
    }
};

// 平台下发远程录像回放控制
struct X9202 {
    my::uchar ch = 0;           // 音视频通道号
    my::uchar ctl = 0;          // 回放控制, 0开始, 1暂停, 2结束, 3快进, 4关键帧快退, 5拖动, 6关键帧
    my::uchar speed = 0;        // 快进或者已退倍数
    my::uchar time[6];          // 手动回放位置, ctl为5有效
    X9202()
    {
        memset(time, 0, sizeof(time));
    }
    bool decode(my::constr data)
    {
        if (data.length() != 9) {
            return false;
        }

        data >> my::ntoh >> ch >> ctl >> speed;

        if (5 == ctl) {
            memcpy(time, (const char*)data, sizeof(time));
        }

        return true;
    }
};

// 查询资源列表（历史音视频查询）
struct X9205 {
    my::uchar ch = 0;           // 逻辑通道号
    char s_time[6];         // 开始时间
    char e_time[6];         // 结束时间
    my::uint64 alarm_tag = 0;   // 报警标志
    my::uchar media = 0;        // 音视频资源类型, 0音视频, 1音频, 2视频, 3视频或音频
    my::uchar code = 0;     // 码流类型, 0所有码流, 1主码流, 2子码流
    my::uchar storage = 0;      // 存储器类型, 0所有存储器, 1主存储器, 2灾备存储器
    X9205()
    {
        memset(s_time, 0, sizeof(s_time));
        memset(e_time, 0, sizeof(e_time));
    }
    bool decode(my::constr data)
    {
        if (data.length() != 24) {
            return false;
        }

        data >> my::ntoh >> ch;
        memcpy(s_time, (const char*)data, sizeof(s_time));
        data += sizeof(s_time);
        memcpy(e_time, (const char*)data, sizeof(e_time));
        data += sizeof(e_time);
        data >> alarm_tag >> media >> code >> storage;
        if (!alarm_tag) {
            alarm_tag = -1;
        }
        return true;
    }
};

// 文件上传指令
struct X9206 {
    my::string server_ip;           // 服务器地址
    my::ushort server_port;         // 端口
    my::string user;                // 用户名
    my::string password;            // 密码
    my::string path;                // 文件上传路径
    my::uchar ch = 0;               // 逻辑通道号
    char s_time[6];             // 开始时间
    char e_time[6];             // 结束时间
    my::uint64 alarm_tag = 0;       // 报警标志
    my::uchar media = 0;            // 音视频资源类型
    my::uchar code = 0;             // 码流类型
    my::uchar storage = 0;          // 存储位置
    my::uchar cond = 0;         // bit0为1:Wi-fi可下载, bit1为1, Lan可下载, bit2为1：3G/4G可下载
    X9206()
    {
        memset(s_time, 0, sizeof(s_time));
        memset(e_time, 0, sizeof(e_time));
    }
    bool decode(my::constr data)
    {
        if (data.length() < 31) {
            return false;
        }

        data >> my::ntoh >> server_ip(1) >> server_port >> user(1) >> password(1) >> path(1) >> ch;

        if (!path.prefix("/")) {
            my::string tmp = path;
            path = "/";
            path = path + tmp.c_str();
        }
        if (!path.suffix("/")) {
             path.append("/");
        }
        int len = strlen(server_ip.c_str());
        server_ip.length(len);

        memcpy(s_time, (const char*)data, sizeof(s_time));
        data += sizeof(s_time);
        memcpy(e_time, (const char*)data, sizeof(e_time));
        data += sizeof(e_time);
        data >> alarm_tag >> media >> code >> storage >> cond;
        return true;
    }
};

// 文件上传控制
struct X9207 {
    my::ushort sn = 0;      // 应答流水号
    my::uchar ctl = 0;      // 上传控制, 0暂停, 1继续, 2取消
    bool decode(my::constr data)
    {
        if (data.length() != 3) {
            return false;
        }

        data >> my::ntoh >> sn >> ctl;
        return true;
    }
};

// 报警附件上传指令
struct X9208 {
    my::string server_ip;       // 服务器IP地址
    my::ushort tcp_port = 0;    // TCP端口
    my::ushort udp_port = 0;    // udp端口
    AlarmTag alarm_tag;         // 报警标识号
    char alarm_id[32];          // 报警编号
    char reserved[16];          // 预留
    X9208()
    {
        memset(alarm_id, 0, sizeof(alarm_id));
        memset(reserved, 0, sizeof(reserved));
    }
    bool decode(my::constr data, bool jt808_new, const my::string prot_subtype)
    {
        if (data.length() < 52) {
            loge("data.length = %d", data.length());
            return false;
        }

        data >> my::ntoh >> server_ip(1) >> tcp_port >> udp_port;
        int len = strlen(server_ip.c_str());
        server_ip.length(len);

        if (!alarm_tag.decode(data, jt808_new, prot_subtype)) {
            return false;
        }

        memcpy(alarm_id, (const char*)data, sizeof(alarm_id));
        data += sizeof(alarm_id);
        if (data.length() >= sizeof(reserved)) {
            memcpy(reserved, (const char*)data, sizeof(reserved));
            data += sizeof(reserved);
        }
        return true;
    }
};

struct alarmAttInfo {
    my::uchar fileType;         // 文件类型
    my::uchar strmType;         // 码流类型
    my::uchar fileFormate;      // 文件格式
    my::uchar storageType;      // 存储器类型
    my::uchar ch;               // 逻辑通道号
    char s_time[6];             // 开始时间
    char e_time[6];             // 结束时间
    my::uchar picCnt;           // 图片张数

    my::uint startTs;           // 时间
    my::uint endTs;             // 时间


    bool decode(my::constr& data, bool jt808_new, const my::string & prot_subtype)
    {
        data >> my::ntoh  >> fileType >> strmType >> fileFormate >> storageType >> ch;
        
        memcpy(s_time, (const char*)data, sizeof(s_time));
        my::bcd2time(s_time, sizeof(s_time), startTs);
        data += sizeof(s_time);

        memcpy(e_time, (const char*)data, sizeof(e_time));
        my::bcd2time(e_time, sizeof(e_time), endTs);
        data += sizeof(e_time);

        data >> picCnt;

        return true;
    }

};

struct X9218 {
    my::string server_ip;       // 服务器IP地址
    my::ushort tcp_port = 0;    // TCP端口
    my::ushort udp_port = 0;    // udp端口
    AlarmTag alarm_tag;         // 报警标识号
    char alarm_id[32];          // 报警编号
    char reserved[16];          // 预留

    my::uchar cnt;              // 附件总数
    std::vector<alarmAttInfo> attInfos;       // 附件列表

    bool decode(my::constr data, bool jt808_new, const my::string prot_subtype)
    {
        if (data.length() < 70) {
            loge("data.length = %d", data.length());
            return false;
        }

        data >> my::ntoh >> server_ip(1) >> tcp_port >> udp_port;
        int len = strlen(server_ip.c_str());
        server_ip.length(len);

        if (!alarm_tag.decode(data, jt808_new, prot_subtype)) {
            return false;
        }

        memcpy(alarm_id, (const char*)data, sizeof(alarm_id));
        data += sizeof(alarm_id);
        if (data.length() >= sizeof(reserved)) {
            memcpy(reserved, (const char*)data, sizeof(reserved));
            data += sizeof(reserved);
        }

        data >> cnt;

        for (int32_t i = 0; i < cnt; i++) {
            alarmAttInfo ai;
            ai.decode(data, jt808_new, prot_subtype);
            attInfos.push_back(ai);
        }

        return true;
    }

};

struct X9508 {
    my::uchar queryAtt; // 查询属性
    my::uchar strmId;   // 码流类型
    my::uchar deviceId; // 存储设备编号
    my::uchar queryMode;// 查询模式
    char beginDate[2];  // 开始时间 YY-MM
    char endDate[2];    // 结束时间 YY-MM

    bool decode(my::constr data)
    {
        if (data.length() < 8) {
            loge("data.length = %d", data.length());
            return false;
        }

        data >> my::ntoh >> queryAtt >> strmId >> deviceId >> queryMode;

        memcpy(beginDate, (const char*)data, sizeof(beginDate));
        data += sizeof(beginDate);

        memcpy(endDate, (const char*)data, sizeof(endDate));
        data += sizeof(endDate);

        return true;
    }
};

struct searchList {
    char date[2];   // 年月 YY-MM
    my::uint recBit; // 录像，bit0 - bit31 分别代表月份1日 - 31日
};

struct X1508 {
    my::ushort sn; // 流水号，对应查询音视频资源列表指令的流水号
    my::uchar respRes; // 应答结果
    my::uchar searchRes; // 搜索结果
    my::ushort count; // 搜索总个数
    std::vector<searchList> lists; // 搜索列表
    
    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0x1508, b2019);
        my::string& body = t.body;

        body << my::hton << sn << respRes << searchRes << count;

        for (int i = 0; i < count; i++) {
            body.append(lists[i].date, sizeof(lists[i].date));
            body << lists[i].recBit;
        }

        return t.optimized();
    }
};

struct X9261 {
    my::uchar serverIpLen;
    my::string serverIp;
    my::ushort tcpPort;
    my::ushort udpPort;
    char ch[8];
    //my::uint64 ch;
    my::uchar mediaType;
    my::uchar strmType;
    my::uchar storageType;
    my::uchar playBackType;
    my::uchar speed;
    char beginTs[6];
    char endTs[6];

    bool decode(my::constr data)
    {
        if (data.length() < 30) {
            return false;
        }

        data >> my::ntoh >> serverIp(1) >> tcpPort >> udpPort; // >> ch >> mediaType >> strmType >> storageType >> playBackType >> speed;

        memcpy(ch, (const char*)data, sizeof(ch));
        data += sizeof(ch);

        data >> mediaType >> strmType >> storageType >> playBackType >> speed;

        memcpy(beginTs, (const char*)data, sizeof(beginTs));
        data += sizeof(beginTs);

        memcpy(endTs, (const char*)data, sizeof(endTs));
        data += sizeof(endTs);

        return true;
    }
};

struct X9262 {
    //my::uint64 ch;
    char ch[8];
    my::uchar playBackCtl;
    my::uchar speed;
    char playBackTs[6];

    bool decode(my::constr data) 
    {
        if (data.length() < 16) {
            return false;
        }

        memcpy(ch, (const char*)data, sizeof(ch));
        data += sizeof(ch);

        data >> my::ntoh >> playBackCtl >> speed;

        memcpy(playBackTs, (const char*)data, sizeof(playBackTs));
        data += sizeof(playBackTs);
        return true;
    }
};


// 文件上传完成消息应答
struct X9212 {
    struct DataInfo {
        my::uint offset = 0;    // 数据偏移量
        my::uint len = 0;       // 数据长度
    };

    my::string path;            // 文件名称
    my::uchar type = 0;         // 文件类型
    my::uchar res = 0;          // 上传结果, 0x00完成, 0x01需要补传
    my::uchar num = 0;          // 补传数据包数量
    std::vector<DataInfo> data_list;    // 补传数据
    bool decode(my::constr data)
    {
        if (data.length() < 4) {
            return false;
        }

        data >> my::ntoh >> path(1) >> type >> res >> num;

        for (int i = 0; i < num; ++i) {
            DataInfo di;
            data >> di.offset >> di.len;
            data_list.push_back(di);
        }

        return true;
    }
};

// 文件上传控制
struct X8700 {
    my::uchar cmd;
    my::string msg;
    bool decode(my::constr data)
    {
        if (data.length() < 7) {
            loge("X8700 decode fail : too short data! %d", data.length());
            return false;
        }

        my::ushort magic;
        my::uchar cmd2;
        my::ushort msgLen;
        my::uchar pad;
        data >> my::ntoh >> cmd >> magic >> cmd2 >> msgLen >> pad;
        msg = data;

        if (msg.length() != (msgLen + 1)) {
            loge("X8700 msg parse fail!");
        }

        return true;
    }
};
struct X0700 {
    my::ushort seq;
    my::uchar  cmd;
    my::string msg;
    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0x0700, b2019);
        t.body << my::hton << seq << cmd;
        my::string body;
        my::ushort magic = 0x557a;
        my::ushort msgLen = msg.length();
        my::uchar pad = 0;
        body << my::hton << magic << cmd << msgLen << pad << msg;
        my::uchar chksum = 0;

        for (int i = 0; i < body.length(); i++) {
            chksum += body.c_str()[i];
        }

        body << chksum;
        t.body.append(body);
        return t.optimized();
    }
    JttMsg encode(const my::constr & sim, bool b2019, std::vector<my::string> & msgSet)
    {
        JttMsg t(sim, 0x0700, b2019);
        t.body << my::hton << seq << cmd;
        for (auto m : msgSet) {
            my::string body;
            my::ushort magic = 0x557a;
            my::ushort msgLen = m.length();
            my::uchar pad = 0;
            body << my::hton << magic << cmd << msgLen << pad << m;
            my::uchar chksum = 0;

            for (int i = 0; i < body.length(); i++) {
                chksum += body.c_str()[i];
            }

            body << chksum;
            t.body.append(body);
        }
        return t.optimized();
    }
};

struct XC3 {
    my::uchar  timeBcd[6];
    my::ushort pulseCoef;
};
struct XC4 {
    my::uchar timeBcd[6];
    my::uchar setupTmBcd[6];
    my::uchar initMileAgeBcd[4];//0.1km
    my::uchar totalMileAgeBcd[4];//0.1 km
    my::uint realDt;
    my::uint setupDt;
    float initMileAge;
    float totalMileAge;
};
// 行驶记录参数下发
struct X8701 {
    my::uchar cmd;
    my::string msg;
    union {
        XC3 c3;
        XC4 c4;
    } u;
    bool decode(my::constr data)
    {
        if (data.length() < 1) {
            return false;
        }

        data >> my::ntoh >> cmd;
        msg = data;
        int msgLen = msg.length();
        switch(cmd)
        {
            case 0xc3: {
                data >> u.c3.timeBcd[0] >> u.c3.timeBcd[1] >> u.c3.timeBcd[2] >> u.c3.timeBcd[3] >> u.c3.timeBcd[4] >> u.c3.timeBcd[5];
                data >> u.c3.pulseCoef;
                break;
            }
            case 0xc4: {
                data >> u.c4.timeBcd[0] >> u.c4.timeBcd[1] >> u.c4.timeBcd[2] >> u.c4.timeBcd[3] >> u.c4.timeBcd[4] >> u.c4.timeBcd[5];
                data >> u.c4.setupTmBcd[0] >> u.c4.setupTmBcd[1] >> u.c4.setupTmBcd[2] >> u.c4.setupTmBcd[3] >> u.c4.setupTmBcd[4] >> u.c4.setupTmBcd[5];
                data >> u.c4.initMileAgeBcd[0] >> u.c4.initMileAgeBcd[1] >> u.c4.initMileAgeBcd[2] >> u.c4.initMileAgeBcd[3];
                data >> u.c4.totalMileAgeBcd[0] >> u.c4.totalMileAgeBcd[1] >> u.c4.totalMileAgeBcd[2] >> u.c4.totalMileAgeBcd[3];
                my::bcd2time((const char*)u.c4.timeBcd, sizeof(u.c4.timeBcd), u.c4.realDt);
                my::bcd2time((const char*)u.c4.setupTmBcd, sizeof(u.c4.setupTmBcd), u.c4.setupDt);
                my::string initMileage, totalMileage;
                my::bcd2numstr(initMileage, (const char*)u.c4.initMileAgeBcd, sizeof(u.c4.initMileAgeBcd));
                my::bcd2numstr(totalMileage, (const char*)u.c4.totalMileAgeBcd, sizeof(u.c4.totalMileAgeBcd));
                u.c4.initMileAge = atoi(initMileage.c_str()) / 10;
                u.c4.totalMileAge = atoi(totalMileage.c_str()) / 10;
                break;
            }
            default: {
                loge("0X8701 cmd 0x%x NOT impl!!!", cmd);
                break;
            }
        }
        return true;
    }
};

struct X8900 {
    my::uchar type;//0xf7 状态查询;   0xf8 信息查询
    my::uchar idCount;
    std::vector<my::uchar> idTbl;/*0xf8*/
    my::string msg;
    bool decode(my::constr data)
    {
        if (data.length() < 1) {
            return false;
        }
        switch (type) {
            case 0xf8: {
                data >> my::ntoh >> type >> idCount;
                my::uchar id;
                for (int i = 0; i < idCount; i++) {
                    data >> id;
                    idTbl.push_back(id);
                }
                break;
            }
            default: {
                logd("type = 0x%02xh", type);
                msg = data;
            }
        }
        return true;
    }
};
struct X0900 {
    my::uchar type;//0 GNSS; 0X0B ic卡; 0x41 串口1;0x42 串口2; 0xf7 状态查询;   0xf8 信息查询
    my::uchar count;
    std::map<my::uchar/*device type*/, my::string> data;

    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0x0900, b2019);
        switch(type) {
            case 0xf8: {
                count = data.size();
                t.body << my::hton << type << count;
                for (auto msg : data) {
                    my::uchar len = msg.second.length();
                    t.body << msg.first;
                    t.body << len;
                    t.body.append(msg.second);
                }
                break;
            }
            default: {
                t.body << my::hton << type;
                if (data.size()) {
                    for (auto msg : data) {
                        t.body.append(msg.second);
                    }
                }
                break;
            }
        }
        return t.optimized();
    }
};
#ifndef DISABLE_CHONGQING_PROT_EXT
// 报警附件信息消息
struct X6034 {
    typedef struct {
        my::string  bcdtime;
        my::ushort speed;
        my::ushort direct;
        my::uint   lng;
        my::uint   lat;
        my::uint   alarm;
        my::uint   state;

        my::string str()
        {
            my::string body;
            body << my::hton;
            body.append(bcdtime);
            body << speed << direct;
            body << lng << lat;
            body << alarm << state;
            return body;
        }
    } location;

    my::uchar count;
    std::vector<location> ls;

    JttMsg encode(const my::constr& sim, bool b2019)
    {
        JttMsg t(sim, 0x6034, b2019);
        my::string& body = t.body;
        body << my::hton << count;

        for (int i = 0; i < count; ++i) {
            body << ls[i].str();
        }

        return t.optimized();
    }

};
#endif


#define BCDBYTE(_V) (((((_V) / 10) << 4) | (((_V) % 10) << 0)) & 0xff)
#define BCDTIME(_bcd, _Y, _M, _D, _h, _m, _s) do {\
    _bcd[0] = BCDBYTE(_Y);\
    _bcd[1] = BCDBYTE(_M);\
    _bcd[2] = BCDBYTE(_D);\
    _bcd[3] = BCDBYTE(_h);\
    _bcd[4] = BCDBYTE(_m);\
    _bcd[5] = BCDBYTE(_s);\
} while(0)
#define TIMEKEY(_key, _Y, _M, _D, _h, _m, _s) do {\
    _key[0] = BCDBYTE(_s);\
    _key[1] = BCDBYTE(_m);\
    _key[2] = BCDBYTE(_h);\
    _key[3] = BCDBYTE(_D);\
    _key[4] = BCDBYTE(_M);\
    _key[5] = BCDBYTE(_Y);\
} while(0)

struct MinusDataBlk
{
    my::uchar  timeBcd[6];//the byte for second : [5] = 0
    struct {
        my::uchar  spd;
        my::uchar  status;
    } __attribute__((packed))  secondData[60];
}__attribute__((packed));

struct X08 {
    std::map<uint64_t, MinusDataBlk, std::greater<uint64_t>> dataBlks;
    bool encode(std::vector<my::string> & data, int maxNum)
    {
        if (!dataBlks.size()) {
            return false;
        }
        int secNum = 0, minNum = 0;

        for (auto it : dataBlks) {
            if (secNum >= maxNum){
                break;
            }
            int idx = minNum / 7;
            if (data.size() < (idx + 1)) {
                my::string tmp;
                data.push_back(tmp);
                data[idx] << my::hton;
            }
            my::string & msg = data[idx];
            msg.append((const char *)it.second.timeBcd, sizeof(it.second.timeBcd));
            for (int i = 0; i < ARRAY_SIZE(it.second.secondData); i++) {
                msg << it.second.secondData[i].spd;
                msg << it.second.secondData[i].status;
                secNum++;
            }
            minNum++;
            logd("[%d] %012lx", secNum, it.first);
        }
        return true;
    }
};


struct Loc {
    my::uchar  timeBcd[6];
    my::uint   lng;
    my::uint   lat;
    my::ushort alt;
    my::uchar  spd;
} __attribute__((packed));
struct LocDataBlk {
    std::map<int, Loc> loc;
};
struct X09 {
    std::map<uint64_t, LocDataBlk, std::greater<uint64_t>> dataBlks;
    bool encode(my::string & msg, int maxNum)
    {
        if (!dataBlks.size()) {
            return false;
        }

        msg << my::hton;
        for (auto it : dataBlks) {
            if (!maxNum){
                break;
            }
            maxNum--;
            msg.append((const char *)it.second.loc.begin()->second.timeBcd, 6);
            int count = 60;
            for (auto l : it.second.loc) {
                msg << l.second.lng;
                msg << l.second.lat;
                msg << l.second.alt;
                msg << l.second.spd;
                logd("%" PRIx64 " : lng %d, lat %d, alt %d, spd %d", it.first, l.second.lng, l.second.lat, l.second.alt, l.second.spd);
                count--;
            }
            if (count) {
                struct Loc pad;
                memset(&pad, 0, sizeof(pad));
                while (count--) {
                    msg << pad.lng;
                    msg << pad.lat;
                    msg << pad.alt;
                    msg << pad.spd;
                }
            }
        }
        return true;
    }
};

struct AcdDataBlk {
    //my::uchar timeBcd[6]; using first rec
    my::string drvCert;//驾驶证号码
    struct {
        my::uchar timeBcd[6];
        my::uint   lng;
        my::uint   lat;
        my::ushort alt;
        my::uchar spd;
        my::uchar stat1stByte;
    } __attribute__((packed)) rec[100];
    /*using first rec
    my::uint   lng;
    my::uint   lat;
    my::ushort alt;*/
};

struct X10 {
    std::map<uint64_t, AcdDataBlk, std::greater<uint64_t>> dataBlks;
    bool encode(my::string & msg, int maxNum)
    {
        if (!dataBlks.size()) {
            return false;
        }

        msg << my::hton;
        for (auto it : dataBlks) {
            if (!maxNum){
                break;
            }
            msg.append((const char *)it.second.rec[0].timeBcd, sizeof(it.second.rec[0].timeBcd));
            msg.append(it.second.drvCert);
            maxNum--;
            for (int i = 0; i < ARRAY_SIZE(it.second.rec); i++) {
                msg << it.second.rec[i].spd;
                msg << it.second.rec[i].stat1stByte;
            }
            msg << it.second.rec[0].lng;
            msg << it.second.rec[0].lat;
            msg << it.second.rec[0].alt;
        }
        return true;
    }
};

struct OvTmDataBlk {
    my::string drvCert;//驾驶证号码
    my::uchar bgnTmBcd[6];
    my::uchar endTmBcd[6];
    struct {
        my::uint   lng;
        my::uint   lat;
        my::ushort alt;
    } __attribute__((packed)) BELoc[2];
};

struct X11 {
    std::map<uint64_t, OvTmDataBlk, std::greater<uint64_t>> dataBlks;
    bool encode(my::string & msg, int maxNum)
    {
        if (!dataBlks.size()) {
            return false;
        }

        msg << my::hton;
        for (auto it : dataBlks) {
            if (!maxNum){
                break;
            }
            maxNum--;
            msg.append(it.second.drvCert);
            msg.append((const char *)it.second.bgnTmBcd, 6);
            msg.append((const char *)it.second.endTmBcd, 6);
            for (int i = 0; i < ARRAY_SIZE(it.second.BELoc); i++) {
                msg << it.second.BELoc[i].lng;
                msg << it.second.BELoc[i].lat;
                msg << it.second.BELoc[i].alt;
            }
        }
        return true;
    }
};

struct EvtDataBlk {
    my::uchar  timeBcd[6];
    my::string drvCert;//驾驶证号码
    my::uchar  evtType;//1 登录， 2 退出，其他保留
};

struct X12 {
    std::map<uint64_t, EvtDataBlk, std::greater<uint64_t>> dataBlks;
    bool encode(my::string & msg, int maxNum)
    {
        if (!dataBlks.size()) {
            return false;
        }

        msg << my::hton;
        for (auto it : dataBlks) {
            if (!maxNum){
                break;
            }
            maxNum--;
            msg.append((const char *)it.second.timeBcd, 6);
            msg.append(it.second.drvCert);
            msg << it.second.evtType;
        }
        return true;
    }
};

struct ACCDataBlk {
    my::uchar  timeBcd[6];
    my::uchar  acc;//1 通电 2 断电
} __attribute__((packed));

struct X13 {
    std::map<uint64_t, ACCDataBlk, std::greater<uint64_t>> dataBlks;
    bool encode(my::string & msg, int maxNum)
    {
        if (!dataBlks.size()) {
            return false;
        }

        msg << my::hton;
        for (auto it : dataBlks) {
            if (!maxNum){
                break;
            }
            maxNum--;
            msg.append((const char *)it.second.timeBcd, 6);
            msg << it.second.acc;
        }
        return true;
    }
};

struct SpdStatDataBlk {
    my::uchar stat;//1 正常 2异常
    my::uchar bgnTmBcd[6];
    my::uchar endTmBcd[6];
    struct {
        my::uchar spdRec;
        my::uchar spdRef;
    } __attribute__((packed)) spd[60];
} __attribute__((packed));

struct X15 {
    std::map<uint64_t, SpdStatDataBlk, std::greater<uint64_t>> dataBlks;
    bool encode(my::string & msg, int maxNum)
    {
        if (!dataBlks.size()) {
            return false;
        }

        msg << my::hton;
        for (auto it : dataBlks) {
            if (!maxNum){
                break;
            }
            maxNum--;
            msg << it.second.stat;
            msg.append((const char *)it.second.bgnTmBcd, 6);
            msg.append((const char *)it.second.endTmBcd, 6);
            for (int i = 0; i < ARRAY_SIZE(it.second.spd); i++) {
                msg << it.second.spd[i].spdRec;
                msg << it.second.spd[i].spdRef;
            }
        }
        return true;
    }
};

#endif
