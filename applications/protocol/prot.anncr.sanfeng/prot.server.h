#ifndef __PROT_SERVER_H__
#define __PROT_SERVER_H__
#include <map>
#define HEART_BEAT_THRESHOLD 120
template <class T>
class ProtServer
    : public my::thread
{
    public:
        ProtServer(int port)
            : mListenFd(-1)
        {
            mListenFd = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
            int yes = 1;

            if (-1 == setsockopt(mListenFd, SOL_SOCKET, SO_REUSEADDR, &yes, sizeof(yes))) {
                loge("Reusing ADDR failed\n");
            }

            struct sockaddr_in local_addr;

            local_addr.sin_family = AF_INET;

            local_addr.sin_port = htons(port);

            local_addr.sin_addr.s_addr = htonl(INADDR_ANY);

            if (-1 == ::bind(mListenFd, (struct sockaddr *)&local_addr, sizeof(local_addr))) {
                loge("bind %d failed\n", port);
                close(mListenFd);
                mListenFd = -1;
            }

            if (listen(mListenFd, 128) == -1) {
                loge("listen FAIL %s\n", strerror(errno));
                exit(255);
            }

            logd("listen fd %d\n", mListenFd);
        }
        ~ProtServer()
        {
        }
        virtual int onAccepted(int newSock, std::string ipAddr)
        {
            T * p = new T(this, newSock, ipAddr);

            if (p) {
                {
                    MY_SPINLOCK_X(lock);
                    mCliFdTbl.insert(std::pair<int, T*>(newSock, p));
                }
                p->start();
            }

            return 0;
        }

        int onDisconnect(int socket)
        {
            MY_SPINLOCK_X(lock);
            auto it = mCliFdTbl.find(socket);

            if (it != mCliFdTbl.end()) {
                logd("+sock%d thrd stop", socket);
                it->second->mark_stop();
                logd("-sock%d thrd stop", socket);
            }

            return 0;
        }
    protected:
        void run()
        {
            prctl(PR_SET_NAME, "protServer");

            while (!exiting()) {
                struct timeval timeout = {0, 30000};
                fd_set rfds;
                FD_ZERO(&rfds);
                FD_SET(mListenFd, &rfds);
                int fdMax = mListenFd;

                int32_t ret = select(fdMax + 1, &rfds, NULL, NULL, &timeout);

                if (ret < 0) {
                    loge("selected failed %s", strerror(errno));
                    return ;

                } else if (ret == 0) {
                    //logd("select timetout\n");
                } else {
                    if (FD_ISSET(mListenFd, &rfds)) {
                        struct sockaddr_in skaddr;
                        socklen_t len = sizeof(struct sockaddr_in);

                        logd("SERVER FD = %d\n", mListenFd);
                        int newFd = accept(mListenFd, (struct sockaddr *)&skaddr, &len);

                        if (newFd < 0) {
                            logd("accept() err\n");

                        } else {
                            char ip[INET6_ADDRSTRLEN] = {0};
                            int32_t flag = fcntl(newFd, F_GETFL);
                            flag |= O_NONBLOCK;
                            fcntl(newFd, F_SETFL, flag);

                            if (NULL == inet_ntop(AF_INET, &skaddr.sin_addr, ip, sizeof(ip))) {
                                loge("inet_ntop fail!");
                            }

                            logd("NEW CONNECT FROM %s, fd is %d\n", ip, newFd);
                            onAccepted(newFd, ip);
                        }
                    }
                }

                {
                    MY_SPINLOCK_X(lock);
                    auto it = mCliFdTbl.begin();

                    while (it != mCliFdTbl.end()) {
                        auto pthrd = it->second;

                        if (!pthrd || !pthrd->working()) {
                            if (pthrd) {
                                logd("+sock %d thrd delete", it->first);
                                pthrd->stop();
                                delete pthrd;
                                logd("-sock %d thrd delete", it->first);
                            }

                            close(it->first);
                            it = mCliFdTbl.erase(it);

                        } else {
                            it++;
                        }
                    }
                }
            }
        }
    private:
        int mListenFd;

        my::spinlock lock;
        std::map<int, T*> mCliFdTbl;
};
template<class T>
class OnProtConnect
    : public my::thread
{
    public:
        OnProtConnect(ProtServer<T> * s, int fd, std::string ipAddr)
            : mServer(s)
            , mFd(fd)
            , mIpAddr(ipAddr)
            , mLastHbTime(time(NULL))
            , mbExit(false)
        {

        }
        ~OnProtConnect()
        {
        }

        void mark_stop()
        {
            mbExit = true;
        }
        void update_heartbeat()
        {
            mLastHbTime = time(NULL);
        }
        bool heartbeat_timeout()
        {
            return abs(time(NULL) - mLastHbTime) > HEART_BEAT_THRESHOLD;
        }
    protected:
        virtual void recvMsg() = 0;
        virtual void ext_run(){}
        void run()
        {
            char name[32];
            snprintf(name, sizeof(name), "onSock%d", mFd);
            prctl(PR_SET_NAME, name);

            while (!exiting()) {
                struct timeval timeout = {0, 30000};
                fd_set rfds;
                FD_ZERO(&rfds);
                FD_SET(mFd, &rfds);
                int fdMax = mFd;

                int32_t ret = select(fdMax + 1, &rfds, NULL, NULL, &timeout);

                if (ret < 0) {
                    loge("selected failed %s", strerror(errno));
                    return ;

                } else if (ret == 0) {
                    //logd("select timetout\n");
                } else {
                    if (FD_ISSET(mFd, &rfds)) {
                        recvMsg();
                    }
                }
                ext_run();

                if (heartbeat_timeout()) {
                    loge("heart beat timeout!");
                    break;
                }

                if (mbExit) {
                    break;
                }
            }
        }
    protected:
        int mFd;
        ProtServer<T> * mServer;
    private:
        bool mbExit;
        std::string mIpAddr;
        time_t      mLastHbTime;
};

#endif
