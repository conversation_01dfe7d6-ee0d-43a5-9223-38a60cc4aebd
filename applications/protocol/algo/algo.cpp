#undef LOG_TAG_STR
#define LOG_TAG_STR algo

#include <inttypes.h>
#include <sys/prctl.h>
#include "system_properties.h"
#include "opencv2/imgcodecs.hpp"
#include "opencv2/imgproc.hpp"
#include "msgpack.h"
#include "msgpack.hpp"
#include "json.hpp"

#include "algo.h"
#include "algo.prot.h"
#include "algo.prot.adas.h"
#include "algo.prot.bsd.h"
#include "algo.prot.cam.occlusion.h"
#include "algo.prot.dms.h"
#include "algo.prot.dumper.h"
#include "algo.prot.dms.faceid.h"
#include "algo.prot.dms.faceid.v2.h"
#include "algo.prot.hod.h"
#include "algo.prot.ivs.h"
#include "algo.prot.objp.h"
#include "algo.prot.roadcheck.h"

SINGLETON_STATIC_INSTANCE(AlgoManager);


// 创建算法协议对象
template<class _type_>
static Algo * algoCreator(struct MediaParam & param)
{
    return new _type_(param);
}

AlgoManager::AlgoManager()
{
    const std::unordered_map<std::string, std::pair<std::function<Algo* (struct MediaParam &)>, struct MediaParam>> algoSet = {
        {"adas",            {algoCreator<AdasProt>,         {"bgr888",  1280, 720, 15} }},
        {"bsd",             {algoCreator<BsdProt>,          {"y",       1280, 720, 15} }},
        {"cam_occlusion",   {algoCreator<CamOcclusionProt>, {"y",       1280, 720, 15} }},
        {"dms",             {algoCreator<DmsProt>,          {"y",       1280, 720, 15} }},
        {"dumper_camera",   {algoCreator<DumperCamProt>,    {"y",       1280, 720, 15} }},
        {"dumper_imu",      {algoCreator<DumperImuProt>,    {"y",       1280, 720, 15} }},
        {"dumper_tof",      {algoCreator<DumperTofProt>,    {"y",       1280, 720, 15} }},
        {"faceid",          {algoCreator<DmsFaceIDProt>,    {"y",       1280, 720, 15} }},
        {"faceid_v2",       {algoCreator<DmsFaceIDProt_V2>, {"y",       1280, 720, 15} }},
        {"fbsd",            {algoCreator<FBsdProt>,         {"y",       1280, 720, 15} }},
        {"hod",             {algoCreator<HodProt>,          {"y",       1280, 720, 15} }},
        {"ivs",             {algoCreator<IVSProt>,          {"y",       1280, 720, 15} }},
        {"objp",            {algoCreator<ObjpProt>,         {"y",       1280, 720, 15} }},
        {"road_check",      {algoCreator<INSPProt>,         {"bgr888",  1280, 720, 15} }},
        {"adas_crosswalk",  {algoCreator<AdasCrossWalk>,    {"bgr888",  1280, 720, 15} }},
    };

    for (auto algo : algoSet) {
        Algo * pa = algo.second.first(algo.second.second);
        if (pa) {
            mAlgo[algo.first] = pa;
        }
    }
}

bool AlgoManager::start()
{
    // 启动线程喂数据
    if (!mbStarted) {
        my::thread::start();
        mbStarted = true;
    }

    for (auto algo : mAlgo) {
        if (algo.second) {
            algo.second->start();
        }
    }

    return true;
}

bool AlgoManager::start(std::vector<std::string> aiFuncList)
{
    // 启动线程喂数据
    if (!mbStarted) {
        my::thread::start();
        mbStarted = true;
    }

    for (auto func : aiFuncList) {
        auto algo = mAlgo.find(func);
        if (algo != mAlgo.end()) {
            algo->second->start();
        }
    }
    auto algo = mAlgo.find("cam_occlusion");
    if (algo != mAlgo.end()) {
        algo->second->start();
    }
    return true;
}


void AlgoManager::addObserver(std::string name, IALGO_OBSERVER * observer)
{
    std::lock_guard<std::mutex> lock(mMtx);
    if (mIObservers.find(name) == mIObservers.end()) {
        mIObservers[name] = observer;
    } else {
        loge("name %s is exist already!");
    }
}

void AlgoManager::delObserver(std::string name)
{
    std::lock_guard<std::mutex> lock(mMtx);
    if (mIObservers.find(name) != mIObservers.end()) {
        mIObservers.erase(name);
    }
}

void AlgoManager::disableDealAlarm()
{
    mbDisableDealAlarm = true;
}

void AlgoManager::enableDealAlarm()
{
    mbDisableDealAlarm = false;
}
void AlgoManager::disablePlayAudio()
{
    mbDisablePlayAudio = true;
}

void AlgoManager::enablePlayAudio()
{
    mbDisablePlayAudio = false;
}
int32_t AlgoManager::playAudio(const char * path, int32_t priority)
{
    if (mbDisablePlayAudio) {
        return -1;
    }
    return cmd2voice(path, priority);
}
bool AlgoManager::triggerEvent(std::string name, std::string eventName, double speed)
{
    std::lock_guard<std::mutex> lock(mMtx);
    EVT_TYPE et = name2value(name.c_str(), eventName.c_str());
    std::shared_ptr<Event> e = make_shared<Event>(name.c_str(), et);
    e->c.ts = my::timestamp::milliseconds_from_19700101();
    e->c.speed = speed;
    e->c.event = eventName;
    if (name == "bsd") {
        auto level = eventName.back() - '0';
        if (level > 0 && level <= 3) {
            e->c.level = level;
        }
    }
    logd("%s, et = %d", eventName.c_str(), et);
    for (auto cb : mIObservers) {
        cb.second->onAlgoEvent(e);
    }
    return true;
}

void AlgoManager::run()
{
    prctl(PR_SET_NAME, "algoMngrEvt");

    while (!exiting()) {
        std::shared_ptr<Event> e;
        bool bRet = wait(e, std::chrono::microseconds(60000000));
        if (!bRet) {
            logd("Wait timeout, no event!");
            continue;
        } else if (!mbDisableDealAlarm) {
            std::lock_guard<std::mutex> lock(mMtx);
            for (auto cb : mIObservers) {
                cb.second->onAlgoEvent(e);
            }
            if (EVT_TYPE_DUMPER_DETAIL != e->type()) {
                if (!access("/tmp/dump_algo_msg", R_OK)) {
                    logd("Got event %s", e->to_json().c_str());
                }
            } 
        }

    }
}




int AlgoManager::faceDetect()
{
    Algo * pa = mAlgo["faceid"];

    if (pa) {
        DmsFaceIDProt * pf = dynamic_cast<DmsFaceIDProt *>(pa);
        return pf->faceDetect();
    }

    return -1;
}

int AlgoManager::faceDetectedGet(const std::string & imgPath)
{
    Algo * pa = mAlgo["faceid"];

    if (pa) {
        DmsFaceIDProt * pf = dynamic_cast<DmsFaceIDProt *>(pa);
        return pf->faceDetectedGet(imgPath);
    }

    return -1;
}

int AlgoManager::faceFeatureReq(std::string & imgId, std::string & imgPath)
{
    Algo * pa = mAlgo["faceid"];

    if (pa) {
        DmsFaceIDProt * pf = dynamic_cast<DmsFaceIDProt *>(pa);
        return pf->faceFeatureReq(imgId, imgPath);
    }

    return -1;
}

int AlgoManager::faceFeatureGet(std::string & imgId, std::vector<uint8_t> & faceFeature)
{
    Algo * pa = mAlgo["faceid"];

    if (pa) {
        DmsFaceIDProt * pf = dynamic_cast<DmsFaceIDProt *>(pa);
        return pf->faceFeatureGet(imgId, faceFeature);
    }

    return -1;
}

int AlgoManager::faceFeatureFindMatchReq(std::vector<uint8_t> & curFeature,
        std::vector<std::pair<std::string/*imgId*/, std::vector<uint8_t>/*feature*/>> & featureList)
{
    Algo * pa = mAlgo["faceid"];

    if (pa) {
        DmsFaceIDProt * pf = dynamic_cast<DmsFaceIDProt *>(pa);
        return pf->faceFeatureFindMatchReq(curFeature, featureList);
    }

    return -1;
}
int AlgoManager::faceFeatureFindMatchGet(
    std::unordered_map<std::string  /*imgId*/, struct FaceIdMatchRes > & result)
{
    Algo * pa = mAlgo["faceid"];

    if (pa) {
        DmsFaceIDProt * pf = dynamic_cast<DmsFaceIDProt *>(pa);
        return pf->faceFeatureFindMatchGet(result);
    }

    return -1;
}

int AlgoManager::faceRegister_V2(std::string imgId, std::string& action, std::string &mode)
{
    Algo * pa = mAlgo["faceid_v2"];

    if (pa) {
        DmsFaceIDProt_V2 * pf = dynamic_cast<DmsFaceIDProt_V2 *>(pa);
        return pf->faceRegisterReq(imgId, action, mode);
    }

    return -1;
}

int AlgoManager::faceRegister_V2(std::string imgId, std::string& action, std::string &mode, std::string &param)
{
    Algo * pa = mAlgo["faceid_v2"];

    if (pa) {
        DmsFaceIDProt_V2 * pf = dynamic_cast<DmsFaceIDProt_V2 *>(pa);
        return pf->faceRegisterReq(imgId, action, mode, param);
    }

    return -1;
}

int AlgoManager::faceMatch_V2()
{
    Algo * pa = mAlgo["faceid_v2"];

    if (pa) {
        DmsFaceIDProt_V2 * pf = dynamic_cast<DmsFaceIDProt_V2 *>(pa);
        return pf->faceMatchReq();
    }

    return -1;
}


