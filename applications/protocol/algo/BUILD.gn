config("algo_cfg") {
    include_dirs = [
        ".",
        "//applications/vendor/8838_sdk4/include/",
        "//foundation/base/core/filelog",
        "//foundation/base/core/mystd/include/",
        "//foundation/base/service/imu/libimu",
        "//foundation/communication/ipcAgent/include/",
        "//foundation/communication/libflow/include/",
        "//foundation/communication/libflow/flowWrap/",
        "//foundation/communication/message/",
        "//foundation/communication/property/",
        "//third_party/nlohmann_json/",
        "//third_party/msgpack-c/include/",
    ]
}

this_srcs = [
        "algo.cpp",
        "algo.h",
        "algo.prot.adas.cpp",
        "algo.prot.adas.h",
        "algo.prot.bsd.cpp",
        "algo.prot.bsd.h",
        "algo.prot.cpp",
        "algo.prot.cam.occlusion.cpp",
        "algo.prot.dms.cpp",
        "algo.prot.dms.faceid.cpp",
        "algo.prot.dms.faceid.h",
        "algo.prot.dms.faceid.v2.cpp",
        "algo.prot.dms.faceid.v2.h",
        "algo.prot.dms.h",
        "algo.prot.dumper.cpp",
        "algo.prot.dumper.h",
        "algo.prot.h",
        "algo.prot.objp.cpp",
        "algo.prot.roadcheck.cpp",
        "algo.prot.ivs.cpp",
        "algo.prot.hod.h",
        "event.h",
        "drivingBeHavior.cpp",
]
cflags_cc = [
        "-Wno-psabi",
        "-Wno-unused-parameter",
]

shared_library("algo") {
    sources = this_srcs
    public_configs = [
       ":algo_cfg",
    ]
    deps = [
        "//foundation/base/core/mystd",
        "//foundation/base/core/filelog",
        "//foundation/base/service/tts/libtts",
        "//foundation/base/service/imu/libimu:imu",
        "//foundation/base/core/json-c-util:jsonUtil",
        "//foundation/communication/ipcAgent",
        "//foundation/communication/socketcmd",
        "//foundation/communication/message:message",
        "//foundation/communication/ringbuf:ringbuf",
        "//foundation/communication/libflow:flowWrap",
        "//foundation/communication/property:property",
    ]
}
