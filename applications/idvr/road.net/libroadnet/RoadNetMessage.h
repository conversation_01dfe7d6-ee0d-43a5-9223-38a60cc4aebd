#ifndef __MINIEYE_ROAD_NET_MESSAGE_H__
#define __MINIEYE_ROAD_NET_MESSAGE_H__

#include <stdio.h>
#include <unistd.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>

#include "ipcMessage.h"
#include "ipcAgent.h"
#include "roadnet_nature.h"

#define SH_NAME_ROADNET "/mnt/obb/roadnet"


typedef enum
{
    ROAD_MSG_TYPE_INVALID,
    ROAD_MSG_TYPE_SWVER,
    ROAD_MSG_TYPE_SET_LIMIT, /* 设置限速值       */
    ROAD_MSG_TYPE_SET_CAR_TYPE, /*  设置车辆类型   */
    ROAD_MSG_TYPE_SPL_RLT,  /* 当前道路限速信息及告警 */
    ROAD_MSG_TYPE_RESTRICT_RLT, /*  前方道路限高限重提示 */
    ROAD_MSG_TYPE_ROAD_NAME,
    ROAD_MSG_TYPE_PLAYBACK_FILE_NAME, /* gps回放文件名   */
    ROAD_MSG_TYPE_LICENCE_VALID,        /* 路网地图是否注册成功 */
    ROAD_MSG_TYPE_REGISTER,             /* 向路网地图注册 */
    ROAD_MSG_TYPE_MAX,
} RoadMsgTypeE;

typedef struct LimitSpeed
{
    char     speedLimitMax;
    char     speedLimitlow;
}LimitSpeed;

struct gps_playback_parse
{
    char fileName[64];                         /* 解析文件名 */
    char deviceId[20];                         /* 设备ID */
}; 

struct registerParam_t
{
    char deviceId[64];                         /* 设备编号 */
    char plateNum[64];                         /* 车牌号码 */
}; 

#if 0
typedef struct
{
    RoadMsgTypeE     type;
    uint16_t         len;
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wextern-c-compat"
    union
    {
        uint8_t             u8Array[0];
        char                schar[0];
        int32_t             int32[0];
        road_net_spl_result splInfo[0]; 
        LimitSpeed          splLimit[0];
        road_net_restrict_result restrictInfo[0]; 
        gps_playback_parse parseInfo[0];
        registerParam_t registerParam[0];
    } __attribute__((packed)) u;
#pragma clang diagnostic pop
} __attribute__((packed)) RoadMessage;
#else
typedef struct
{
    IpcMessage ipcMsg;
    union
    {
        uint8_t                 u8Array[0];
        char                    schar[0];
        uint8_t                 uchar[0];
        int32_t                 int32[0];        
        LimitSpeed              splLimit[0];
        registerParam_t         registerParam[0];
        road_net_spl_result     splInfo[0];
        gps_playback_parse      parseInfo[0];
        road_net_restrict_result restrictInfo[0];
    } u;
} RoadMessage;
#endif

#define ROAD_MSG_MAX_SIZE        (sizeof(RoadMessage) + 512)
#define ROAD_MSG_HEADER_SIZE     (sizeof(RoadMessage))
#define ROAD_MSG_SIZE(_s)        (ROAD_MSG_HEADER_SIZE + sizeof(_s))

#define ROAD_MSG_SPL_SIZE       (sizeof(road_net_spl_result))
#define ROAD_MSG_RESTRICT_SIZE (sizeof(road_net_restrict_result))

#endif

