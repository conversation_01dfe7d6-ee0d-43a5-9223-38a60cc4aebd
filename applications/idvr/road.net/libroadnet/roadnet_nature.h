#ifndef ROADNET_NATURE_H
#define ROADNET_NATURE_H

/**  
 * @brief road_net状态 
 */
enum road_net_status {
    road_net_status_license_no_module_enabled = -7, // license校验失败，没有开启任何模块
    road_net_status_license_times_up = -6,          // license校验失败，使用次数已满 
    road_net_status_license_expired = -5,           // license校验失败：过期
    road_net_status_license_id_not_match = -4,      // license校验失败：ID号不匹配
    road_net_status_license_decrypt_failed = -3,    // license校验失败：解析失败
    road_net_status_license_file_corrupt = -2,      // license校验失败：文件损坏
    road_net_status_no_license = -1,                // license校验失败：没有license文件
    road_net_status_success = 0,                    // 成功
    road_net_status_initialization_failed,          // 初始化失败
    road_net_status_uninitialized,                  // 未初始化
    road_net_status_running_hp_failed,              // 运行失败
    road_net_status_running_vp_failed,              // VP启动失败
    road_net_status_param_error                     // 参数错误
};

/**
* @brief road_net车辆类型
*/
enum road_net_vehicle_type{
    road_net_vehicle_type_car = 0,    // 小车
    road_net_vehicle_type_truck,      // 卡车
    road_net_vehicle_type_bus         // 大客车
};


/**
* @brief road_net高低限速分界类型
*/
enum road_net_boundary_type {
    road_net_boundary_type_50 = 0,        // 分界值为50km/h
    road_net_boundary_type_40,            // 分界值为40km/h
    road_net_boundary_type_30,            // 分界值为30km/h
    road_net_boundary_type_20,            // 分界值为20km/h
    road_net_boundary_type_10             // 分界值为10km/h
};

/**
* @brief 坐标系类型
*/
enum road_net_coordinate_kind {
    road_net_coordinate_wgs84 = 0, // 84坐标
    road_net_coordinate_gcj02      // 02坐标
};

/**
* @brief road_net定位状态
*/
enum road_net_location_status
{
    road_net_location_status_weak_gps = 0,      // gps信号弱
    road_net_location_status_off_road = 1,      // 不在道路上
    road_net_location_status_on_road = 2        // 在道路上
};

/**
* @brief road_net道路等级
*/
enum road_net_link_level
{
    road_net_link_level_highway = 0,            // 高速路
    road_net_link_level_expway = 1,             // 城市快速路
    road_net_link_level_national_road = 2,      // 国道
    road_net_link_level_provincial_road = 3,    // 省道
    road_net_link_level_county_road = 4,        // 县道
    road_net_link_level_village_road = 5,       // 乡镇村路
    road_net_link_level_other = 6               // 其它
};

/**
* @brief road_net限速值类型
*/
enum road_net_speed_type {
    road_net_speed_type_none = 0,    // 不区分类型
    road_net_speed_type_basic,       // 基础限速
    road_net_speed_type_actual       // 实际道路限速
};

/**
* @brief road_net新路段预警类型
*/
enum road_net_new_road_pre_alarm_type {
    road_net_new_road_pre_alarm_none = 0,           // 无警报
    road_net_new_road_pre_alarm_low = 1,            // 低限速新路段预警，播报内容:"您即将进入低限速路段"
    road_net_new_road_pre_alarm_normal = 2,         // 普通限速新路段预警，播报内容:"您即将进入限速XX km/h的路段"
    road_net_new_road_pre_alarm_tunnel_low = 3,     // 低限速隧道预警，播报内容:"您即将进入低限速隧道"
    road_net_new_road_pre_alarm_tunnel_normal = 4,  // 普通限速隧道预警，播报内容:"您即将进入限速值为XX km/h的隧道"
    road_net_new_road_pre_alarm_tunnel = 5          // 隧道预警，播报内容"您即将进入隧道", 适应于无法获取限速值的隧道场景
};

/**
* @brief road_net新路段警报类型
*/
enum road_net_new_road_alarm_type {
    road_net_new_road_alarm_none = 0,     // 无警报
    road_net_new_road_alarm_low = 1,      // 低限速新路段播报,播报内容:"此路段为低限速路段，请以实际交规行驶"
    road_net_new_road_alarm_normal = 2    // 普通限速新路段播报,播报内容:"此路段限速值为XX km/h，请谨慎驾驶"
};

/**
* @brief road_net超速报警类型
*/
enum road_net_exceed_speed_alarm_type {
    road_net_exceed_speed_alarm_none = 0,     // 无警报
    road_net_exceed_speed_alarm_pre = 1,      // 超速预警，播报内容:"您即将超速，当前路段限速值为XX km/h"
    road_net_exceed_speed_alarm_low = 2,      // 低限速路段超速报警,播报内容:"您已超速，请减速，当前路段为低限速路段，请按实际交规行驶"
    road_net_exceed_speed_alarm_normal = 3    // 普通限速路段超速报警,播报内容:"您已超速，请减速，当前路段限速值为XX km/h"
};

/**
* @brief road_net风险源类型
*/
enum road_net_disaster_type
{
    road_net_disaster_type_unstable_slope = 1,    // 不稳定斜坡
    road_net_disaster_type_collapse,              // 崩塌
    road_net_disaster_type_mud_rock_flow,         // 泥石流
    road_net_disaster_type_landslips,             // 滑坡
    road_net_disaster_type_unknown                // 未知
};

/**
* @brief road_net风险源程度
*/
enum road_net_disaster_level
{
    road_net_disaster_level_ordinary = 1,       // 一般
    road_net_disaster_level_serious             // 严重
};

/**
* @brief road_net冰雪路段类型
*/
enum road_net_frozen_type
{
    road_net_frozen_type_ice_snow = 1,          // 冰雪
    road_net_frozen_type_freezing_rain,         // 冻雨
    road_net_frozen_type_snow_cover             // 积雪、结冰
};

/**
* @brief road_net升级包安装结果
*/
enum road_net_install_upgrade_package_result {
    road_net_install_upgrade_package_result_success = 0,     // 安装成功
    road_net_install_upgrade_package_result_no_need_install, // 没有升级包，不需要安装
    road_net_install_upgrade_package_result_failed           // 安装失败
};

/**
* @brief road_net城区状态
*/
enum road_net_urban_area_status
{
    road_net_urban_area_status_negative = 0,        // 道路不处于城区，即不属于城市道路
    road_net_urban_area_status_positive,            // 道路处于城区，即属于城市道路
    road_net_urban_area_status_unknown,             // 未知
    road_net_urban_area_status_na                   // 无效
};


/**
* @brief road_net道路限制类型
*/
enum road_net_restrict_type
{
    road_net_restrict_type_unlimited = 0,        // 道路对于车高或车重无限制
    road_net_restrict_type_limited,              // 道路对于车高或车重有限制
    road_net_restrict_type_unknown,              // 未知
    road_net_restrict_type_na                    // 无效
};

/**
* @brief road_net时间戳
*/
struct road_net_date_time {
    short year;        // 年, 公元0年开始, 例如2020
    short month;       // 月, [1-12]
    short day;         // 日, [1-31]
    short hour;        // 时, [0-23]
    short minute;      // 分, [0-59]
    short second;      // 秒, [0-59]
    short millisecond; // 毫秒, [0-999]
};


/**
* @brief road_net坐标
*/
struct road_net_coordinate {
    road_net_coordinate_kind kind; // 坐标系类型
    double lon;                // 经度, 单位:degree
    double lat;                // 纬度, 单位:degree
    double alt;                // 高度, 单位:m
};

/**
* @brief gps信息
*/
struct road_net_gps_info {
    char gprmc_status;         /* 必填, 若使用nmea, GPRMC字段2:状态, 'A'=定位，'V'=未定位
                               若使用自定义协议, 根据数据是否有效填写'A'/'V' */
    char gprmc_mode;           /* 必填, 若使用nmea, GPRMC字段12:模式, 'A'=自动，'D'=差分，'E'=估测，'N'=数据无效,
                               若使用自定义协议, 根据数据是否有效填写'A'/'N' */
    unsigned short mode;       /* 必填, 若使用nmea协议, GPGGA字段6:GPS状态, 0=不可用, 1=单点定位, 2=差分定位, 3=无效PPS, 4=实时差分定位, 5=RTK FLOAT, 6=正在估算
                               若使用自定义协议, 0=不可用/未初始化, 1=单点定位, 2=差分定位, 3=无效PPS, 4=实时差分定位, 5=RTK FLOAT, 7=失锁递推状态 */
    int sat_num;               /* 必填, 若使用nmea协议, GPGGA字段7:正在使用的卫星数量
                               若使用自定义协议, 填写正在使用的卫星数量 */
    road_net_date_time date_time;  /* 必填, 若只使用GPS，填GPS报文里面的时间
                               若使用GPS+IMU，填收到GPS报文的系统时间 */
    road_net_coordinate pos;       /* 必填, 位置 */
    double orient;             /* 必填, 方向, 单位:degree, [0.0 ~ 360.0) */
    double speed;              /* 必填, 速度, 单位:m/s */
    double hdop;               /* 选填, GPGGA字段8:水平精度因子 */
    double vdop;               /* 选填, GPGGA字段17:垂直精度因子 */
    double pdop;               /* 选填, GPGSA字段15:综合位置精度因子 */
    double speed_up;           /* 选填, 天向速度, 单位:m/s */
};

/**
* @brief 限速值
*/
struct road_net_limited_speed
{
    short limited_speed;                   // 限速值(-2表示<=高低限速分界值, -1表示无法获取, 0x7FFF表示无效值)
    road_net_speed_type speed_type;        // 限速值类型 
};

/**
* @brief road_net限速警报
*/
struct road_net_spl_alarm
{
    road_net_new_road_pre_alarm_type new_road_pre_alarm;   // 新路段预警
    road_net_new_road_alarm_type new_road_alarm;           // 新限速路段警报
    road_net_exceed_speed_alarm_type exceed_speed_alarm;   // 超速警报
    unsigned short exceed_speed_voice;                     // 超速警报语音提醒标志，0表示不进行语音提醒，1表示进行语音播报
};
/**
* @brief 限速信息结果
*/
struct road_net_spl_result
{
    char roadName[64];                               // 道路名称
    unsigned long long linkId;                       // 道路id
    road_net_link_level linkLevel;                   // 道路等级
    road_net_limited_speed limited_speed_current;    // 当前路段限速值
    road_net_limited_speed limited_speed_ahead;      // 前方路段限速值
    road_net_spl_alarm spl_alarm;                    // 限速警报
};

/**
* @brief 风险源信息结果
*/
struct road_net_disaster_result
{
    road_net_disaster_type type[3];        // 风险源类型
    road_net_disaster_level level[3];      // 风险源程度
    unsigned short count;                  // 风险源个数，最多3个
};

/**
* @brief 冰雪路段信息结果
*/
struct road_net_frozen_result
{
    char roadName[3][64];                // 冰雪路段名称
    road_net_frozen_type type[3];        // 冰雪路段类型
    unsigned int length[3];              // 冰雪路段长度 单位:米
    unsigned short count;                // 冰雪路段个数，最多3个
    unsigned short leave_flag;           // 驶离冰雪路段标识，1表示刚刚驶离， 0表示否
};

/**
* @brief 道路限高或限重结果
*/
struct road_net_restrict_result
{
    char roadName[64];                         // 路段名称
    road_net_restrict_type height_type;        // 限高类型
    road_net_restrict_type weight_type;        // 限重类型
    unsigned short height_value;               // 限高值，单位为0.01米  
    unsigned short weight_value;               // 限重值，单位为0.01吨
}; 

/**
* @brief 道路详细名称
*/
struct road_net_detail_name
{
    char* detail_name;       // 详细名称
    unsigned short length;   // 名称长度
};

/**
* @brief 数据版本信息
*/
struct road_net_data_info
{
    char* version;                      // 版本信息
    unsigned int length;                // 版本信息长度
};

#endif // ROAD_NET_H