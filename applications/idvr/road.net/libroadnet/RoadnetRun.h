#ifndef __ROADNET_RUN_H__
#define __ROADNET_RUN_H__
#include "RoadNetMessage.h"
#include "ipcMessage.h"
#include "ipcAgent.h"

#include "mystd.h"


typedef void (*CallbackStartPlayBackGps)(void);
typedef bool (*CallbackSetSplInfo)(road_net_spl_result *);
typedef bool (*CallbackSetRestrictInfo)(road_net_restrict_result *);

class RoadnetRun : public my::thread
{
public:
    RoadnetRun()
    {
        mClient = nullptr;
        mPlayBackGps = nullptr;
        mSetSplInfo = nullptr;
        mSetRestrictInfo = nullptr;
        mRoadNetInit = false;
    }
    ~RoadnetRun()
    {
        if (mClient) {
            delete mClient;
        }
    }
    bool init(const char *user);
    void start()
    {
        my::thread::start();
    }
    bool comm_send(RoadMsgTypeE cmd, uint8_t *data, int32_t len);
    bool setPlayBackGpsCallBack(CallbackStartPlayBackGps callFun)
    {
        if (callFun == nullptr) {
            loge("input funtion is null!\n");
            return false;
        }
        mPlayBackGps = callFun;
        return true;
    }
     bool setSplInfoCallBack(CallbackSetSplInfo callFun)
    {
        if (callFun == nullptr) {
            loge("input funtion is null!\n");
            return false;
        }
        mSetSplInfo = callFun;
        return true;
    }
    bool setRestrictInfoCallBack(CallbackSetRestrictInfo callFun)
    {
        if (callFun == nullptr) {
            loge("input funtion is null!\n");
            return false;
        }
        mSetRestrictInfo = callFun;
        return true;
    }
    bool getRoadnetInitFlg() { return mRoadNetInit; }
    bool setSplParam(int limit, int boundaryType);
    bool setCarType(const char *utf8CarType);
    bool registerRoadnet(const my::string &deviceId, const my::string &plateNum);
    bool sendPlaybackParam(const my::string & fileName, const my::string &phoneId);
    bool requestUpdateRoadName();
protected:
    bool on_recv(RoadMessage * msg);
    void run();
private:
    IpcClient                   *mClient = nullptr;
    CallbackStartPlayBackGps    mPlayBackGps;
    CallbackSetSplInfo          mSetSplInfo;
    CallbackSetRestrictInfo     mSetRestrictInfo;
    bool                        mRoadNetInit = false;
};


#endif
