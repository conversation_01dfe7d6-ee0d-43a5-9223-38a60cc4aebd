#include <string>

#include <stdio.h>
#include <unistd.h>

#include "RoadnetRun.h"

bool RoadnetRun::init(const char *user)
{
    mClient = new  IpcClient(user, SH_NAME_ROADNET);
    return true;
}

bool RoadnetRun::comm_send(RoadMsgTypeE cmd, uint8_t *data, int32_t len)
{
    uint8_t buf[ROAD_MSG_MAX_SIZE] = {0};
    RoadMessage *pMsg = (RoadMessage *)&buf[0];
    pMsg->ipcMsg.len = len;
    pMsg->ipcMsg.type = cmd;
    if (len > 0) {
        memcpy(pMsg->ipcMsg.u.u8Array, data, len);
    }
    return mClient->send(&(pMsg->ipcMsg));
}


bool RoadnetRun::on_recv(RoadMessage * msg)
{
    switch(msg->ipcMsg.type)
    {
        case ROAD_MSG_TYPE_SWVER: {
            my::string version(msg->u.schar, msg->ipcMsg.len);
            logd("road net soft version:%s!\n", version.c_str());
            break;
        }
        case ROAD_MSG_TYPE_SPL_RLT: {
            //logd("recieve ROAD_MSG_TYPE_SPL_RLT,msg->len:%d, ROAD_MSG_SPL_SIZE:%d!\n", msg->len, ROAD_MSG_SPL_SIZE);
            #if 0
            if (ROAD_MSG_SPL_SIZE == msg->len) {
                /* 更新道路等级限速情况 */        
                logd("recieve ROAD_MSG_TYPE_SPL_RLT!\n");
                road_net_spl_result *pSpl = msg->u.splInfo;
                setSplInfo(pSpl);
            }
            #else
            if (ROAD_MSG_SPL_SIZE == msg->ipcMsg.len) {
                /* 更新道路等级限速情况 */        
                logd("recieve ROAD_MSG_TYPE_SPL_RLT!\n");
                road_net_spl_result *pSpl = msg->u.splInfo;
                if (mSetSplInfo != nullptr) {
                    mSetSplInfo(pSpl);
                }
            }            
            #endif
            break;
        }
        case ROAD_MSG_TYPE_RESTRICT_RLT: {
            #if 0
            if (ROAD_MSG_RESTRICT_SIZE == msg->len) {
                logd("recieve ROAD_MSG_TYPE_RESTRICT_RLT!\n");
                road_net_restrict_result *pRestrict = msg->u.restrictInfo;
                setRestrictInfo(pRestrict);
            }
            #else
            if (ROAD_MSG_RESTRICT_SIZE == msg->ipcMsg.len) {
                logd("recieve ROAD_MSG_TYPE_RESTRICT_RLT!\n");
                road_net_restrict_result *pRestrict = msg->u.restrictInfo;
                if (mSetRestrictInfo != nullptr) {
                    mSetRestrictInfo(pRestrict);
                }
            }
            #endif
            break;
        }
        case ROAD_MSG_TYPE_PLAYBACK_FILE_NAME: {
            /* 启动回放 */
            #if 0
            my::string attCmd;
            if (!access("/data/app/weightZhf03Test", F_OK)) {
                attCmd.assignf("cmd gps_playback %s %d %d", mPlaybackGpsFile.c_str(), 1000, 1);
            } else {
                attCmd.assignf("cmd gps_playback %s %d %d", mPlaybackGpsFile.c_str(), 1000, 0);
            }
            
            if (!LogCallProxyCmd::sendReq("hostio", attCmd.c_str())) {//ask media to product media files
                logd("[gps playback] cmd failed %s\n", attCmd.c_str());
            } else {
                logd("[gps playback] cmd success %s\n", attCmd.c_str());
                setPlaybackStatus(true);
                return true;
            }
            #else
            if (mPlayBackGps != nullptr) {
                mPlayBackGps();
            }
            #endif
            break;
        }
        case ROAD_MSG_TYPE_LICENCE_VALID: {
            #if 1
            char licenceCheck = msg->u.schar[0];
            loge("roadNet licence %s!\n", licenceCheck == 1 ? "invalid" : "valid");
            mRoadNetInit = (licenceCheck == 1) ? false : true;
            #endif
            break;
        }
        default:{
            break;
        }
    }

    return true;
}

void RoadnetRun::run()
{
    prctl(PR_SET_NAME, "RoadnetRunRecieve");
    while (!exiting()) {
        uint8_t buf[ROAD_MSG_MAX_SIZE];
        RoadMessage *pMsg = (RoadMessage *)&buf[0];
        if (mClient->recv(&(pMsg->ipcMsg))) { 
            on_recv(pMsg);
        }
        else {
            loge("RoadnetRun client read fail!\n");
        }
    }
}

bool RoadnetRun::setSplParam(int limit, int boundaryType)
{
    LimitSpeed lSpeed;
    lSpeed.speedLimitlow = static_cast<char>(boundaryType);
    lSpeed.speedLimitMax = static_cast<char>(limit);
    return comm_send(ROAD_MSG_TYPE_SET_LIMIT, (uint8_t *)&lSpeed, sizeof(LimitSpeed));
}

bool RoadnetRun::setCarType(const char *utf8CarType)
{
    int carType = road_net_vehicle_type_truck;
    if (!strcmp(utf8CarType, "小车")) {
        logd("**** car !\n");
        carType = road_net_vehicle_type_car;
    } else if (!strcmp(utf8CarType, "货车")) {
        logd("**** truck !\n");
        carType = road_net_vehicle_type_truck;
    } else {
        logd("**** bus !\n");
        carType = road_net_vehicle_type_bus;
    }
    return comm_send(ROAD_MSG_TYPE_SET_CAR_TYPE, (uint8_t *)&carType, sizeof(carType));
}

bool RoadnetRun::registerRoadnet(const my::string &deviceId, const my::string &plateNum)
{
    if (deviceId.length() < 1 || plateNum.length() < 1) {
        loge("registerRoadnet failed, deviceId len %d, plateNum len %d\n", deviceId.length(), plateNum.length());
        return false;
    }

    loge("registerRoadnet , deviceId %s, plateNum %s\n", deviceId.c_str(), plateNum.c_str());
    registerParam_t registerParam;
    memset(&registerParam, 0, sizeof(registerParam));
    snprintf(registerParam.plateNum, 64, "%s", plateNum.c_str());
    snprintf(registerParam.deviceId, 64, "%s", deviceId.c_str());
    loge("send ROAD_MSG_TYPE_REGISTER\n");
    return comm_send(ROAD_MSG_TYPE_REGISTER, (uint8_t *)&registerParam, sizeof(registerParam));
}

/* phoneId:部标平台设备号 */
bool RoadnetRun::sendPlaybackParam(const my::string & fileName, const my::string &phoneId)
{
    if (fileName.length() < 1 || phoneId.length() < 1) {
        loge("registerRoadnet failed, fileName len %d, phoneId len %d\n", fileName.length(), phoneId.length());
        return false;
    }
    gps_playback_parse parseInfo;
    memset(&parseInfo, 0, sizeof(parseInfo));
    snprintf(parseInfo.fileName, 64, "%s", fileName.c_str());
    snprintf(parseInfo.deviceId, 20, "%s", phoneId.c_str());    
    return comm_send(ROAD_MSG_TYPE_PLAYBACK_FILE_NAME, (uint8_t *)&parseInfo, sizeof(parseInfo));
}

bool RoadnetRun::requestUpdateRoadName()
{
    return comm_send(ROAD_MSG_TYPE_ROAD_NAME, NULL, 0);    
}

