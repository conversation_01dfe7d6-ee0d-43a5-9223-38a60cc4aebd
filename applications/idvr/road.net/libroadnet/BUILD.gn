
config("roadnet_config") {
    include_dirs = [ "." ]
}

shared_library("roadnet") {

    sources = [
        "RoadnetRun.cpp",
    ]

    deps = [
        "//foundation/base/core/mystd",
        "//foundation/communication/ipcAgent",
    ]

    defines = [
        "LOG_TAG_STR=${target_name}",
    ]

    include_dirs = [
             ".",
           "//third_party/msgpack-c/include/", 
    ]

    public_configs = [ ":roadnet_config" ]
    
    cflags_cc = [
        "-Wno-psabi",
        "-Wno-unused-parameter",
    ]
}
