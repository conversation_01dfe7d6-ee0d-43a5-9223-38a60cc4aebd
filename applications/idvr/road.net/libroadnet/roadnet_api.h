#ifndef ROADNET_API_H
#define ROADNET_API_H

#include "roadnet_nature.h"
#ifdef _WIN32
#define DLL_EXPORT __declspec(dllexport)
#else
#define DLL_EXPORT __attribute__((visibility("default")))
#endif

#ifdef __cplusplus
extern "C" {
#endif
/**
* @brief 初始化接口函数, config文件可放在exe同级目录（也可以配置到其他目录），地图数据及风险源数据的绝对目录部分和.ini相同配置。
* @param config_path 配置文件位置
* @param deviceId 设备id
* @param mac_address 车机mac地址
* @param plate_number 车牌号
* return road_net_status road_net_status_success：成功, 其他：失败
*/
DLL_EXPORT road_net_status road_net_init(const char* config_path, const char* device_id, const char* mac_address, const char* plate_number);

/**
* @brief 退出接口函数, 此函数通常在系统退出的时候调用
* @return road_net_status road_net_status_success：成功，其他：失败
*/
DLL_EXPORT road_net_status road_net_destory();

/**
* @brief 设置车辆类型
* @param vehicle_type 车辆类型
* return road_net_status road_net_status_success：成功, 其他：失败
*/
DLL_EXPORT road_net_status road_net_set_vehicle_type(const road_net_vehicle_type vehicle_type);

/**
* @brief 设置基础限速值
* @param speed 基础限速值
* return road_net_status road_net_status_success：成功, 其他：失败
*/
DLL_EXPORT road_net_status road_net_set_basic_speed(unsigned short speed);

/**
* @brief 设置高低限速分界值,当限速值不高于该值时则限速信息结果的限速值返回-2
* @param boundary_type 高低限速分界值类型
* return road_net_status road_net_status_success：成功, 其他：失败
*/
DLL_EXPORT road_net_status road_net_set_boundary_speed_limit(const road_net_boundary_type boundary_type);

/**
* @brief 获取定位结果
* @param gps gps信息指针
* @param location_status 定位状态
* @param spl_result 限速结果指针
* @param disaster_result 风险源结果指针
* @param frozen_result 冰雪路段结果指针
* @return road_net_status road_net_status_success：成功，其他：失败
*/
DLL_EXPORT road_net_status road_net_get_location_result(road_net_gps_info* gps, road_net_location_status* location_status, road_net_spl_result* spl_result, road_net_disaster_result* disaster_result, road_net_frozen_result* frozen_result);

/**
* @brief 获取城区状态结果
* @param urban_status 城区状态指针
* @return road_net_status road_net_status_success：成功，其他：失败
*/
DLL_EXPORT road_net_status road_net_get_urban_area_status(road_net_urban_area_status* urban_status);

/**
* @brief 获取道路前方200米内的新限高、限重道路信息
* @param restrict_result 限高、限重结果指针
* @return road_net_status road_net_status_success：成功，其他：失败
*/
DLL_EXPORT road_net_status road_net_get_restrict_result(road_net_restrict_result* restrict_result);

/**
* @brief 获取道路详细名称
* @param detail_name 道路详细名称指针
* @return road_net_status road_net_status_success：成功，其他：失败
*/
DLL_EXPORT road_net_status road_net_get_detail_name(road_net_detail_name* detail_name);

/**
* @brief 获取软件版本号,软件版本格式：1.0.0
* @param version 软件版本号
* @param length version的长度, 建议不小于15
* @return void
*/
DLL_EXPORT void road_net_get_soft_version(char* version, int length);

/**
* @brief 获取限速地图、风险源、冰雪路段数据版本号
* @param version_info 数据版本号（动态数组依次存储限速地图、风险源、冰雪路段数据版本）
* @param num version_info的数量, 即是入参：数组个数，也是出参：地图数据种类个数（目前为限速地图、风险源、冰雪路段三个）
* @return road_net_status road_net_status_success：成功，其他：失败
*/
DLL_EXPORT road_net_status road_net_get_data_version(road_net_data_info* version_info, int* num);

/**
* @brief 获取当前软件license的校验结果
* @return road_net_status 校验通过返回road_net_status_success，其他：失败
*/
DLL_EXPORT road_net_status road_net_get_soft_valid();

/**
* @brief 安装升级包
* @param config_path 配置文件的位置
* @return road_net_install_upgrade_package_result
*/
DLL_EXPORT road_net_install_upgrade_package_result road_net_install_upgrade_package(const char* config_path);

#ifdef __cplusplus
}
#endif

#endif // ROAD_NET_H