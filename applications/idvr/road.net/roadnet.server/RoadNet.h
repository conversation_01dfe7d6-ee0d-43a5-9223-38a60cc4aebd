#ifndef __MINIEYE_ROADNET_H__
#define __MINIEYE_ROADNET_H__

#include <stdio.h>
#include <unistd.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>


#include "mystd.h"

#include "road_sdk.h"
#include "ipcAgent.h"

#include "RoadNetMessage.h"
#include "McuMessage.h"

class RoadNet
{
    typedef struct {
        double          latitude;
        double          longitude;
        double          altitude;
        /*
         * speed (m/s)
         */
        float           speed;
        /** Represents heading in degrees. */
        float           bearing;
        int             sat_num;
        /** Timestamp for the location fix. */
        int64_t         timestamp;
    } MGpsData;

public:
    RoadNet();
    ~RoadNet();
    int32_t start(const char *config_path);
    int32_t stop();
    void comm_send(RoadMsgTypeE cmd, uint8_t *data, int32_t len)
    {
        roadHandler->comm_send(cmd, data, len);
    }
    bool getLicenceInitFlg()
    {
        if (road_sdk::road_net_get_soft_valid() != road_net_status_success) {
            return false;
        } else {
            return true;
        }
    }
    void reportLicenceInitFlg(bool flg)
    {
        char licenceInvalid = ((flg == true) ? 0 : 1);
        comm_send(ROAD_MSG_TYPE_LICENCE_VALID, (uint8_t *)(&licenceInvalid), 1);
    }
private:
    // hostIO mcu消息处理器
    class McuMsgHandler : public my::thread
    {
public:
        McuMsgHandler()
        {
            mClient = new IpcClient("road_net", SH_NAME_HOSTIO);
        }
        int init(RoadNet *roadNet) {
            mRoadNet = roadNet;
            return my::thread::start();
        }
        void deinit() {
            my::thread::stop();
        }
        void comm_send(McuMsgTypeE cmd, uint8_t *data, int32_t len)
        {
            if (cmd == MCU_MSG_TYPE_INVALID) {
                loge("send cmd error");
                return;
            }
            uint8_t buf[MCU_MSG_MAX_SIZE];
            McuMessage *pMsg = (McuMessage *)&buf[0];
            memset(buf, 0, sizeof(buf));
            pMsg->ipcMsg.type = cmd;
            pMsg->ipcMsg.len = len;
            memcpy(&pMsg->u.u8Array[0], data, len);
            mClient->send(&(pMsg->ipcMsg));
        }
        void setLicenceValid(bool val) { mLicenceCheck = val; }
    protected:
        void run()
        {
            my::timestamp checkTm = my::timestamp::now();
            while (!exiting()) {
                uint8_t buf[ROAD_MSG_MAX_SIZE];
                McuMessage *pMsg = (McuMessage *)&buf[0];
                if (mClient->recv(&(pMsg->ipcMsg))) {
                    on_recv(pMsg);
                }
                else {
                    //loge("mcu client read fail!\n");
                }
                if (!mLicenceCheck) {
                    if (checkTm.elapsed() > 30000) {
                        checkTm = my::timestamp::now();
                        /* 未注册则每30s检测一次注册状态 */
                        if (mRoadNet->getLicenceInitFlg()) {
                            loge("road_net licence check success!\n");
                            mLicenceCheck = true;
                            mRoadNet->reportLicenceInitFlg(mLicenceCheck);
                        } else {                            
                            loge("road_net licence not check!\n");
                            /* 通知ccu 地图无法工作 */
                            mRoadNet->reportLicenceInitFlg(mLicenceCheck);
                        }
                    }
                }
            }
        }
        bool on_recv(McuMessage * msg);
    private:
        IpcClient *mClient;
        /* 消息 */
        RoadNet *mRoadNet;
        bool      mLicenceCheck = false;
    };

    class RoadMsgServerHandler : public my::thread{
        public:
            RoadMsgServerHandler()
            {                
            }
            ~RoadMsgServerHandler()
            {
                delete mServer;
            }
            int init(RoadNet *roadNet, int carType, int limitSpeed, int sectionLimit) {
                mServer = new IpcServer(SH_NAME_ROADNET);
                mRoadNet = roadNet;
                mCarType = carType;
                mLimitSpeed = limitSpeed;    /* 基础限速     */
                mSectionLimit = sectionLimit;  /* 高低限速 */
                logd("road server init!\n");
                return my::thread::start();
            }
            void deinit()
            {
                my::thread::stop();
            }
            void comm_send(RoadMsgTypeE cmd, uint8_t *data, int32_t len)
            {
                if (cmd == ROAD_MSG_TYPE_INVALID) {
                    loge("send cmd error");
                    return;
                }
                uint8_t buf[ROAD_MSG_MAX_SIZE];
                RoadMessage *pMsg = (RoadMessage *)&buf[0];
                memset(buf, 0, sizeof(buf));
                pMsg->ipcMsg.type = cmd;
                pMsg->ipcMsg.len = len;
                if (len > 0) {
                    memcpy(&pMsg->u.u8Array[0], data, len);
                }
                mServer->send(&(pMsg->ipcMsg));
            }
            my::string getRoadNetFileHead();
        protected:
            void run()
            {
                while (!exiting()) {
                    uint8_t buf[ROAD_MSG_MAX_SIZE];
                    RoadMessage *pMsg = (RoadMessage *)&buf[0];
                    if (mServer->recv(&(pMsg->ipcMsg))) {
                        on_recv(pMsg);
                    }
                    else {
                        //loge("mcu client read fail!\n");
                    }
                }
            }
            bool on_recv(RoadMessage * msg);
        private:
            my::string mFileName;
            my::string mDeviceId;      /* 设备id */
            int        mLimitSpeed;    /* 基础限速     */
            int        mSectionLimit;  /* 高低限速 */
            int        mCarType;
            IpcServer *mServer = nullptr;
            RoadNet *mRoadNet;
    };

    class RoadMsgStore : public my::thread{
public:
        typedef enum
        {
            STORE_LOOP_INVALID,
            STORE_LOOP_STOP,
            STORE_LOOP_RUN
        }STORE_LOOP;

        typedef enum
        {
            WRITE_TYPE_INVALID,
            WRITE_TYPE_INTERVAL,
            WRITE_TYPE_IMMEDIATELY
        }WRITE_TYPE;
        RoadMsgStore(){
            logd("RoadMsgStore start\n");
        }
        ~RoadMsgStore(){
            if (fp) {
                fclose(fp);
                fp = nullptr;
            }
            logd("RoadMsgStore exit!\n");
        }
        int init(RoadNet *roadNet) {
            mRoadNet = roadNet;
            logd("RoadMsgStore init!\n");
            return true;
        }
        /* 成功返回true,失败返回false         */
        int start(my::string storeFile);
        int saveRoadInfo(const my::string info);
protected:
        void run();
        int stopStore();
        int startStore();
        /*return WRITE_TYPE */
        int checkWrite();
        int findMinMsgId();
        int msgWrite(const int writeType);
        int msgWrite(const my::uint startId, const my::uint num);        
private:
        int         mStoreFlg = STORE_LOOP_INVALID; /* STORE_LOOP */
        int         mMsgId = 0; /* 消息ID */ 
        my::string  mReqName;  /* 请求存储 */
        my::string  mFileName;
        std::mutex  mMsgMutex;
        std::map<my::uint, my::string> mMsgMap;
        FILE        *fp = NULL;
        RoadNet     *mRoadNet;
    };

    int  getGpsPlaybackStatus();
    bool storeRoadNetInfo(MGpsData &gpsData, int speed);
    bool getRoadNetInfo(MGpsData &gpsData);
    my::string getSoftVersion();
    void roadSlpResponse(road_net_location_status* location_status, road_net_spl_result* slp_result);
    void roadDisasterResponse(road_net_location_status* location_status, road_net_disaster_result* disaster_result);
    void roadFrozenResponse(road_net_location_status* location_status, road_net_frozen_result* frozen_result);
    void roadRestrictResponse(road_net_location_status* location_status, road_net_restrict_result *restrict_result);
    void roadUrbanAreaResponse(road_net_location_status* location_status, road_net_urban_area_status *urban_status);
    void roadDetailNameResponse(road_net_location_status* location_status, road_net_detail_name *road_detail_name);
    
    bool makeRoadNetInfo(MGpsData *gpsData, road_net_gps_info *roadNetInfo);
    my::string getRoadNetFileHead();
    int startRoadnetStore(my::string storeFile) {
        return msgStoreHandler->start(storeFile);
    }
    int saveRoadInfo(const my::string info){
        return msgStoreHandler->saveRoadInfo(info);
    }
    int responseGpsPlayback();
protected:
    my::string                      mSoftVersion;
public:
    void setDeviceId(const char *data, int len)
    {
        mDeviceId.append(data, len);
    }

    void setPlateNum(const char *data, int len)
    {
        mPlateNum.append(data, len);
    }
    const char *getDeviceId()
    {
        return mDeviceId.c_str();
    }
    const char *getPlateNum()
    {
        return mPlateNum.c_str();
    }
private:
    std::shared_ptr<McuMsgHandler> mcuHandler;
    std::shared_ptr<RoadMsgServerHandler> roadHandler;
    std::shared_ptr<RoadMsgStore> msgStoreHandler;

    my::string                      mDeviceId;
    my::string                      mPlateNum;
};

#endif

