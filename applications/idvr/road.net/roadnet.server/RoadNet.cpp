#define LOG_TAG  "road_net"

#include <linux/ioctl.h>
#include "RoadNet.h"
#include "system_properties.h"
#include "ttsPlay.h"
#include <string>

#define GPS_PLAY_BACK_DATA_PATH             "/mnt/media_rw/sdcard1/gps_playback/"
#define GPS_PARSE_DATA_WRITE_INTERVAL       10

RoadNet::RoadNet()
{
    logd("~RoadNet +");
}

RoadNet::~RoadNet()
{
    logd("~RoadNet +");
}

void RoadNet::roadSlpResponse(road_net_location_status* location_status, road_net_spl_result* slp_result)
{
    if (NULL == location_status || NULL == slp_result || *location_status != road_net_location_status_on_road)
    {
        //logd("road_net: no slp result received.\n");
        return;
    }

    if (access(GPS_PLAY_BACK_DATA_PATH, R_OK)) {
        roadHandler->comm_send(ROAD_MSG_TYPE_SPL_RLT, (uint8_t *)slp_result, sizeof(road_net_spl_result));
        logd("link id: %llu\n", slp_result->linkId);
        logd("link level: %d\n", slp_result->linkLevel);
        logd("limited speed current: %d\n", slp_result->limited_speed_current.limited_speed);
    } else {
        char cmd[256] = {0};
        // 语音播报超速报警
        if (slp_result->spl_alarm.exceed_speed_voice == 1)
        {
            switch (slp_result->spl_alarm.exceed_speed_alarm)
            {
            case road_net_exceed_speed_alarm_pre:
                logd("您即将超速，当前路段限速值为%d km/h\n", slp_result->limited_speed_current.limited_speed);
                snprintf(cmd, 256, "您即将超速当前路段限速值为%dkm每小时", slp_result->limited_speed_current.limited_speed);
                ttsPlayRightNow(cmd);
                break;
            case road_net_exceed_speed_alarm_low:
                logd("您已超速，请减速，当前路段为低限速路段，请按实际交规行驶\n");
                snprintf(cmd, 256, "您已超速请减速当前路段为低限速路段请按实际交规行驶");
                ttsPlayRightNow(cmd);
                break;
            case road_net_exceed_speed_alarm_normal:
                logd("您已超速，请减速，当前路段限速值为%dkm每小时\n", slp_result->limited_speed_current.limited_speed);
                #if 0
                snprintf(cmd, sizeof(cmd), "tts -s 您已超速，请减速，当前路段限速值为%d公里每小时 &",
                        slp_result->limited_speed_current.limited_speed);
                system(cmd);
                #endif
                snprintf(cmd, 256, "您已超速请减速当前路段限速值为%dkm每小时", slp_result->limited_speed_current.limited_speed);
                ttsPlayRightNow(cmd);
                break;
            default:
                break;
            }
        }
         // 语音播报新路段预警
        switch (slp_result->spl_alarm.new_road_pre_alarm)
        {
        case road_net_new_road_pre_alarm_low:
            logd("您即将进入低限速路段\n");
            snprintf(cmd, 256, "您即将进入低限速路段");
            ttsPlayRightNow(cmd);
            break;
        case road_net_new_road_pre_alarm_normal:
            logd("您即将进入限速%d km/h的路段\n", slp_result->limited_speed_ahead.limited_speed);
            snprintf(cmd, 256, "您即将进入限速%dkm每小时的路段", slp_result->limited_speed_ahead.limited_speed);
            ttsPlayRightNow(cmd);
            break;
        case road_net_new_road_pre_alarm_tunnel_low:
            logd("您即将进入低限速隧道\n");
            snprintf(cmd, 256, "您即将进入低限速隧道");
            ttsPlayRightNow(cmd);
            break;
        case road_net_new_road_pre_alarm_tunnel_normal:
            logd("您即将进入限速值为%d km/h的隧道\n", slp_result->limited_speed_ahead.limited_speed);
            snprintf(cmd, 256, "您即将进入限速值为%dkm每小时的隧道", slp_result->limited_speed_ahead.limited_speed);
            ttsPlayRightNow(cmd);
            break;
        case road_net_new_road_pre_alarm_tunnel:
            logd("您即将进入隧道\n");
            snprintf(cmd, 256, "您即将进入隧道");
            ttsPlayRightNow(cmd);
            break;
        default:
            break;
        }

        // 语音播报新限速路段警报
        switch (slp_result->spl_alarm.new_road_alarm)
        {
        case road_net_new_road_alarm_low:
            logd("此路段为低限速路段，请以实际交规行驶\n");
            snprintf(cmd, 256, "此路段为低限速路段请以实际交规行驶");
            ttsPlayRightNow(cmd);
            break;
        case road_net_new_road_alarm_normal:
            logd("此路段限速值为%d km/h，请谨慎驾驶\n", slp_result->limited_speed_current.limited_speed);
            snprintf(cmd, 256, "此路段限速值为%dkm每小时请谨慎驾驶", slp_result->limited_speed_current.limited_speed);
            ttsPlayRightNow(cmd);
            break;
        default:
            break;
        }
    }
}

void RoadNet::roadDisasterResponse(road_net_location_status* location_status, road_net_disaster_result* disaster_result)
{
    char cmd[256];
    char roadName[256] = {0};
    if (NULL == location_status || NULL == disaster_result || *location_status == road_net_location_status_weak_gps)
    {
        printf("road_net: no disaster result received.\n");
        return;
    }

    if (disaster_result->count == 0)
    {
        printf("road_net: the number of disasters is 0.\n");
        return;
    }

    if (!access(GPS_PLAY_BACK_DATA_PATH, R_OK)) {
        // 语音播报风险源预警，例如"附近2公里有泥石流地质灾害风险点，请谨慎驾驶"
        char* pCmd = NULL;
        for (int i = 0; i < disaster_result->count; i++)
        {
            //logd("disaster_%d: type,%d, level,%d,count:%d\n",i + 1, disaster_result->type[i], disaster_result->level[i], disaster_result->count);
            //snprintf(cmd, 256, "附近2公里有泥石流地质灾害风险请谨慎驾驶");
            pCmd = cmd;
            memset(cmd, 0, 256);
            pCmd  += sprintf(pCmd, "附近2公里有");
            switch (disaster_result->type[i]) {
                case road_net_disaster_type_unstable_slope: {
                    pCmd  += sprintf(pCmd, "%s", "不稳定斜坡风险点");
                    break;
                }
                case road_net_disaster_type_collapse: {
                    pCmd  += sprintf(pCmd, "%s", "崩塌风险点");
                    break;
                }
                case road_net_disaster_type_mud_rock_flow: {
                    pCmd  += sprintf(pCmd, "%s", "泥石流地质灾害风险点");
                    break;
                }
                case road_net_disaster_type_landslips: {
                    pCmd  += sprintf(pCmd, "%s", "滑坡风险点");
                    break;
                }
                default: {
                    pCmd  += sprintf(pCmd, "%s", "未知灾害风险点");
                    break;
                }
            }
            pCmd  += sprintf(pCmd, "请谨慎驾驶");
            logd("%s", cmd);
            ttsPlayRightNow(cmd);
        }
    }
}

void RoadNet::roadFrozenResponse(road_net_location_status* location_status, road_net_frozen_result* frozen_result)
{
    char cmd[256];
    char roadName[256] = {0};
    unsigned int lenth = 0;
    if (NULL == location_status || NULL == frozen_result || *location_status != road_net_location_status_on_road)
    {
        printf("road_net: no frozen result received.\n");
        return;
    }

    // 语音播报提示已离开冰雪路段
    if (frozen_result->leave_flag == 1)
    {
        logd("您已离开冰雪路段\n");
        if (!access(GPS_PLAY_BACK_DATA_PATH, R_OK)) {
            snprintf(cmd, 256, "您已离开冰雪路段");
            ttsPlayRightNow(cmd);
        }
    }

    if (frozen_result->count == 0)
    {
        //logd("road_net: the number of frozen is 0.\n");
        return;
    }

    // 语音播报冰雪路段预警，例如"前方2公里进入府城大道冰雪路段，长度5公里，请谨慎驾驶"
    if (!access(GPS_PLAY_BACK_DATA_PATH, R_OK)) {
        char* pCmd = NULL;
        for (int i = 0; i < frozen_result->count; i++)
        {
            //snprintf(cmd, 256, "前方2公里进入府城大道冰雪路段请谨慎驾驶");
            pCmd = cmd;
            memset(cmd, 0, 256);
            pCmd  += sprintf(pCmd, "前方2公里进入");
            my::gbkToUtf8((char *)frozen_result->roadName[i], roadName);
            pCmd  += sprintf(pCmd, "%s", roadName);
            switch (frozen_result->type[i]) {
                case road_net_frozen_type_ice_snow: {
                    pCmd  += sprintf(pCmd, "%s", "冰雪路段");
                    break;
                }
                case road_net_frozen_type_freezing_rain: {
                    pCmd  += sprintf(pCmd, "%s", "冻雨路段");
                    break;
                }
                case road_net_frozen_type_snow_cover: {
                    pCmd  += sprintf(pCmd, "%s", "积雪路段");
                    break;
                }
                default: {
                    break;
                }
            }
            lenth = frozen_result->length[i];
            if ((lenth % 1000) / 100) {
                pCmd  += sprintf(pCmd, "长度%.2f公里请谨慎驾驶", lenth / 1000.00);
            } else {
                pCmd  += sprintf(pCmd, "长度%d公里请谨慎驾驶", lenth / 1000);
            }
            //logd("frozen_%d: road name: %s, type,%d, length,%d\n", i + 1, frozen_result->roadName[i],  frozen_result->type[i], frozen_result->length[i]);
            logd("%s", cmd);
            ttsPlayRightNow(cmd);
        }
    }
}

void RoadNet::roadRestrictResponse(road_net_location_status* location_status, road_net_restrict_result *restrict_result)
{
    if (NULL == location_status || NULL == restrict_result || *location_status != road_net_location_status_on_road)
    {
        //logd("road_net: no restrict data received.\n");
        return;
    }

    if (!access(GPS_PLAY_BACK_DATA_PATH, R_OK)) {
        bool has_height = restrict_result->height_type == road_net_restrict_type_limited ? true : false;
        bool has_weight = restrict_result->weight_type == road_net_restrict_type_limited ? true : false;

        // 语音播报限高、限重道路预警
        if (has_height || has_weight)
        {
            char print_log[128];
            char cmd[256];
            char tmp[256] = {0};
            memset(cmd, 0x00, sizeof(cmd));
            memset(print_log, 0x00, sizeof(print_log));
            char* pLog = print_log;
            char* pCmd = cmd;

            pLog += sprintf(pLog, "您即将进入");
            pCmd  += sprintf(pCmd, "您即将进入");
            if (has_height)
            {
                pLog += sprintf(pLog, "限高%.2f米,", static_cast<float>(restrict_result->height_value) / 100.0);
                pCmd  += sprintf(pCmd, "限高%d米", restrict_result->height_value / 100);
            }
            if (has_weight)
            {
                pLog += sprintf(pLog, "限重%.2f吨", static_cast<float>(restrict_result->weight_value) / 100.0);
                pCmd  += sprintf(pCmd, "限重%d吨", restrict_result->weight_value / 100);
            }
            pLog += sprintf(pLog, "的%s", strlen(restrict_result->roadName) > 0 ? restrict_result->roadName : "路段");
            if (strlen(restrict_result->roadName) > 0) {
                my::gbkToUtf8((char *)restrict_result->roadName, tmp);
                pCmd  += sprintf(pCmd, "的%s",  tmp);
            } else {
                pCmd  += sprintf(pCmd, "的%s",  "路段");
            }
            ttsPlayRightNow(cmd);
            logd("%s", cmd);
            if (!access("/data/app/weightZhf03Test", F_OK)) {
                roadHandler->comm_send(ROAD_MSG_TYPE_RESTRICT_RLT, (uint8_t *)restrict_result, sizeof(road_net_restrict_result));
            }
        }
    } else {
        roadHandler->comm_send(ROAD_MSG_TYPE_RESTRICT_RLT, (uint8_t *)restrict_result, sizeof(road_net_restrict_result));
    }
}

void RoadNet::roadUrbanAreaResponse(road_net_location_status* location_status, road_net_urban_area_status *urban_status)
{
    char cmd[256];
    if (NULL == location_status || NULL == urban_status || *location_status != road_net_location_status_on_road)
    {
        printf("road_net: no urban status received.\n");
        return;
    }

    if (*urban_status == road_net_urban_area_status_positive)
    {
        //logd("当前位于城市道路.\n");
        //snprintf(cmd, 256, "当前位于城市道路");
        //ttsPlayRightNow(cmd);
        return;
    }
}

void RoadNet::roadDetailNameResponse(road_net_location_status* location_status, road_net_detail_name *road_detail_name)
{
    char cmd[256];
    if (NULL == location_status || NULL == road_detail_name || *location_status != road_net_location_status_on_road)
    {
        printf("road_net: no detail name received.\n");
        return;
    }

    if (road_detail_name->length > 0)
    {
        //logd("当前位于%s.\n", road_detail_name->detail_name);
        //snprintf(cmd, 256, "当前位于%s", road_detail_name->detail_name);
        //ttsPlayRightNow(cmd);
        return;
    }
}

my::string RoadNet::getSoftVersion() {
    return mSoftVersion;
}

int32_t RoadNet::start(const char* config_path)
{
    int32_t ret = road_sdk::load();
    if (ret) {
        logd("road_sdk::load error: %d", ret);
        printf("road_sdk::load error: %d\n", ret);
        return -1;
    }

    // 查询软件版本号
    char soft_version[100] = { 0 };
    road_sdk::road_net_get_soft_version(soft_version, 100);
    logd("soft version: %s", soft_version);
    mSoftVersion = soft_version;
    printf("soft version: %s\n", mSoftVersion.c_str());

    // 查询数据版本号(非必须)
    char adas_route_version[50] = { 0 };
    char risk_source_version[50] = { 0 };
    char icy_road_version[50] = { 0 };
    road_net_data_info version_info[3] = {};
    version_info[0].version = adas_route_version;
    version_info[1].version = risk_source_version;
    version_info[2].version = icy_road_version;
    int data_num = 3;
    road_sdk::road_net_get_data_version(version_info, &data_num);
    for (int i = 0; i < data_num; ++i)
    {
        logd("data_version_%d is:%s ", i, version_info[i].version);
        printf("data_version_%d is:%s \n", i, version_info[i].version);
    }

#if 0
    // 获取设备id
    char propValue[PROP_VALUE_MAX] = {0};
    __system_property_get(PROP_PERSIST_MINIEYE_DEVICEID, propValue);

    // 获取车牌号码
    char plateNum[PROP_VALUE_MAX] = {0};
    __system_property_get(PROP_RW_MINIEYE_PLATE_NUM_GBK, plateNum);
    if (strlen(plateNum) < 1) {
        // 初始化
        road_sdk::road_net_init(config_path, propValue, "8888", "005056C00008");
    } else {
        // 初始化
        road_sdk::road_net_init(config_path, propValue, "8888", plateNum);
    }
#else
    road_sdk::road_net_init(config_path, "1111", "8888", "005056C00008");
#endif
  
    // 获取license状态
    bool licenseVal = true;
    if (road_sdk::road_net_get_soft_valid() != road_net_status_success) {
        loge("license check faild");
        printf("license check faild\n");
        licenseVal = false;
    }

    //设置车辆类型
    road_sdk::road_net_set_vehicle_type(road_net_vehicle_type_truck);
    //设置基础限速值
    road_sdk::road_net_set_basic_speed(100);
    //设置高低限速分界值
    road_sdk::road_net_set_boundary_speed_limit(road_net_boundary_type_10);

    logi("RoadNet init");

    /* gps回放存在，gps数据来源由mcu改为ccu */
    logd("McuMsgHandler init!\n");
    mcuHandler = make_shared<McuMsgHandler>();
    mcuHandler->init(this);

    logi("Road message init");
    roadHandler = make_shared<RoadMsgServerHandler>();
    roadHandler->init(this, road_net_vehicle_type_truck, 100, road_net_boundary_type_10);
    mcuHandler->setLicenceValid(licenseVal);

    logi("Road message store init");
    msgStoreHandler = make_shared<RoadMsgStore>();
    msgStoreHandler->init(this);
    return 0;
}

int32_t RoadNet::stop()
{    
    // 清理
    road_sdk::road_net_destory();
    return 0;
}

bool RoadNet::getRoadNetInfo(MGpsData &gpsData)
{

    //logd("Location %f,%f", gpsData.longitude, gpsData.latitude);

    road_net_gps_info roadNetInfo;
    // 设置GPS位置
    makeRoadNetInfo(&gpsData, &roadNetInfo);

    /* 定位状态 */
    static road_net_location_status location_status;

    /* 限速情况 */
    static road_net_spl_result spl_result;

    /* 风险源信息 */
    static road_net_disaster_result disaster_result;

    /* 冰雪路段信息 */
    static road_net_frozen_result frozen_result;

    /* 道路区域信息 (是否城市道路)        */
    static road_net_urban_area_status urban_status;

    /* 道路限高限重结果 */
    static road_net_restrict_result restrict_result;

    /* 道路详细名称 */
    static char detail_name[512] = { 0 };
    static road_net_detail_name road_detail_name = {};
    road_detail_name.detail_name = detail_name;

    road_sdk::road_net_get_location_result(&roadNetInfo, &location_status, &spl_result, &disaster_result, &frozen_result);
    /* 限速 */
    roadSlpResponse(&location_status, &spl_result);

    /* 风险源提醒 */
    roadDisasterResponse(&location_status, &disaster_result);

    /* 冰雪路段 */
    roadFrozenResponse(&location_status, &frozen_result);

    /* 道路区域 */
    road_sdk::road_net_get_urban_area_status(&urban_status);
    roadUrbanAreaResponse(&location_status, &urban_status);  

    /* 道路限高限重情况 */
    road_sdk::road_net_get_restrict_result(&restrict_result);    
    roadRestrictResponse(&location_status, &restrict_result);

    /* 详细道路名称 */
    road_detail_name.length = 512;
    road_sdk::road_net_get_detail_name(&road_detail_name);    
    roadDetailNameResponse(&location_status, &road_detail_name);

    return true;
}

bool RoadNet::storeRoadNetInfo(MGpsData &gpsData, int speed)
{
    road_net_gps_info roadNetInfo;
    // 设置GPS位置
    makeRoadNetInfo(&gpsData, &roadNetInfo);

    /* 定位状态 */
    static road_net_location_status location_status;

    /* 限速情况 */
    static road_net_spl_result spl_result;

    /* 风险源信息 */
    static road_net_disaster_result disaster_result;

    /* 冰雪路段信息 */
    static road_net_frozen_result frozen_result;

    /* 道路区域信息 (是否城市道路)        */
    static road_net_urban_area_status urban_status;

    /* 道路限高限重结果 */
    static road_net_restrict_result restrict_result;

    /* 道路详细名称 */
    static char detail_name[512] = { 0 };
    static road_net_detail_name road_detail_name = {};
    road_detail_name.detail_name = detail_name;

    road_sdk::road_net_get_location_result(&roadNetInfo, &location_status, &spl_result, &disaster_result, &frozen_result);
    /* 限速 */
    roadSlpResponse(&location_status, &spl_result);

    /* 风险源提醒 */
    roadDisasterResponse(&location_status, &disaster_result);

    /* 冰雪路段 */
    roadFrozenResponse(&location_status, &frozen_result);

    /* 道路区域 */
    road_sdk::road_net_get_urban_area_status(&urban_status);
    roadUrbanAreaResponse(&location_status, &urban_status);  

    /* 道路限高限重情况 */
    road_sdk::road_net_get_restrict_result(&restrict_result);    
    roadRestrictResponse(&location_status, &restrict_result);

    /* 详细道路名称 */
    road_detail_name.length = 512;
    road_sdk::road_net_get_detail_name(&road_detail_name);    
    roadDetailNameResponse(&location_status, &road_detail_name);

    /* 记录路网地图解析的数据 */
    my::string roadInfo;
    roadInfo.assignf("%f,%d,%d,%d,%d,%d", speed / 10.0, 
                                    spl_result.limited_speed_current.limited_speed, 
                                    spl_result.spl_alarm.exceed_speed_alarm, 
                                    spl_result.spl_alarm.new_road_pre_alarm,
                                    urban_status, spl_result.linkLevel);
    saveRoadInfo(roadInfo);
    return true;
}



bool RoadNet::makeRoadNetInfo(MGpsData *gpsData, road_net_gps_info *roadNetInfo)
{
    struct tm st;
    time_t t = gpsData->timestamp + 8 * 3600;
    gmtime_r(&t, &st);

    roadNetInfo->gprmc_status = 'A';
    roadNetInfo->gprmc_mode   = 'A';
    roadNetInfo->mode         = 2;
    roadNetInfo->sat_num      = gpsData->sat_num;
    roadNetInfo->date_time.year           = st.tm_year + 1900;
    roadNetInfo->date_time.month          = st.tm_mon + 1;
    roadNetInfo->date_time.day            = st.tm_mday;
    roadNetInfo->date_time.hour           = st.tm_hour;
    roadNetInfo->date_time.minute         = st.tm_min;
    roadNetInfo->date_time.second         = st.tm_sec;
    roadNetInfo->date_time.millisecond    = 0;
    roadNetInfo->pos.kind     = road_net_coordinate_wgs84;
    roadNetInfo->pos.lon      = gpsData->longitude;
    roadNetInfo->pos.lat      = gpsData->latitude;
    roadNetInfo->pos.alt      = gpsData->altitude;
    roadNetInfo->orient       = gpsData->bearing;
    roadNetInfo->speed        = gpsData->speed;
    roadNetInfo->hdop         = 0;
    roadNetInfo->vdop         = 1;
    roadNetInfo->pdop         = 0;
    roadNetInfo->speed_up     = 0;

    printf("%02d %04d %02d %02d %02d %02d %02d %02d %f %f %f %f %f", 
         roadNetInfo->sat_num, roadNetInfo->date_time.year, roadNetInfo->date_time.month,
         roadNetInfo->date_time.day, roadNetInfo->date_time.hour, roadNetInfo->date_time.minute,
         roadNetInfo->date_time.second, roadNetInfo->date_time.millisecond,
         roadNetInfo->pos.lon, roadNetInfo->pos.lat,roadNetInfo->pos.alt,
         roadNetInfo->orient, roadNetInfo->speed);
    return true;
}

my::string RoadNet::getRoadNetFileHead() {
    return roadHandler->getRoadNetFileHead();
}

int RoadNet::getGpsPlaybackStatus()
{
    char value[PROP_VALUE_MAX];
    //int len = __system_property_get(PROP_RW_MINIEYE_GPS_PLAYBACK_ENABLE, value);
    int len = __system_property_get("rw.minieye.gps.playback", value);
    int gpsPlaybackFlg = 0;
    if (len > 0) {
        gpsPlaybackFlg = atoi(value);
    }

    return gpsPlaybackFlg;
}

int RoadNet::responseGpsPlayback()
{
    uint8_t tmp;
    if (roadHandler == nullptr){
        return false;
    }
    roadHandler->comm_send(ROAD_MSG_TYPE_PLAYBACK_FILE_NAME, (uint8_t *)&tmp, 1);
    return true;
}

//#define TEST_ROAD_NET
#ifdef TEST_ROAD_NET
/* 重庆绕城高速坐标 */
static unsigned int lbsTm = 1636422523;
static double          latitude = 106.284273;
static double          longitude = 29.532808;
static double          altitude = 346.440002;
#endif

bool RoadNet::McuMsgHandler::on_recv(McuMessage * msg)
{
    switch(msg->ipcMsg.type)
    {
        case MCU_MSG_TYPE_STAT: {
            if (MCU_MSG_SIZE_STAT == msg->ipcMsg.len) {
                MGpsData gpsData = {0};
                struct LBS * lbs = &msg->u.stat[0].lbs;
                // 定位
#ifdef TEST_ROAD_NET
                {
#else
                if (lbs->status == 1) {
#endif
                    static uint64_t after = 0;
                    if (mRoadNet->getGpsPlaybackStatus()) {
                        //logd("gps time:%lld\n", lbs->time - 28800);
                        /* gps play back */
                        if (after != (lbs->time - 28800)) {
                            gpsData.timestamp = lbs->time - 28800; /* 传递给地图的时间不需要转换为北京时间 */
                            gpsData.sat_num = lbs->sat;
                            gpsData.longitude =  lbs->lng_x1kw / 10000000.0;
                            gpsData.latitude = lbs->lat_x1kw / 10000000.0;
                            gpsData.altitude = lbs->alt_x10 / 10.0;
                            gpsData.bearing = lbs->dir_x100 / 100.0;
                            gpsData.speed = lbs->speed_x10 / 10.0 / 3.6;
                            #if 0
                            if (gpsData.speed > 10)
                            {
                                logd("timestamp%lld sat_num:%d, longitude:%llf, latitude:%llf altitude:%llf, bearing:%f, speed:%f!\n",
                                    gpsData.timestamp, gpsData.sat_num, gpsData.longitude, gpsData.latitude,
                                    gpsData.altitude, gpsData.bearing, gpsData.speed);
                            }
                            #endif
                            mRoadNet->storeRoadNetInfo(gpsData, lbs->speed_x10);
                            // 1HZ
                            after = gpsData.timestamp;
                        }
                    }else {
                        uint64_t now = time(0);
                        if (now >= after) {
                        #ifdef TEST_ROAD_NET
                        /* 模拟限速 */
                        gpsData.timestamp = (lbsTm++) - 28800; /* 传递给地图的时间不需要转换为北京时间 */
                        gpsData.sat_num = 27;
                        latitude += 0.000001;
                        gpsData.longitude =  latitude;
                        longitude += 0.000001;
                        gpsData.latitude = longitude;
                        altitude += 0.000001;
                        gpsData.altitude = altitude;
                        gpsData.bearing = 346.440002;
                        //gpsData.speed = 32.277779;
                        gpsData.speed = 22.6117779;
                        #else
                        gpsData.timestamp = lbs->time - 28800; /* 传递给地图的时间不需要转换为北京时间 */
                        gpsData.sat_num = lbs->sat;
                        gpsData.longitude =  lbs->lng_x1kw / 10000000.0;
                        gpsData.latitude = lbs->lat_x1kw / 10000000.0;
                        gpsData.altitude = lbs->alt_x10 / 10.0;
                        gpsData.bearing = lbs->dir_x100 / 100.0;
                        gpsData.speed = lbs->speed_x10 / 10.0 / 3.6;
                        #endif
                        //if (gpsData.speed > 10)
                        {
                            logd("timestamp%lld sat_num:%d, longitude:%llf, latitude:%llf altitude:%llf, bearing:%f, speed:%f!\n",
                                gpsData.timestamp, gpsData.sat_num, gpsData.longitude, gpsData.latitude,
                                gpsData.altitude, gpsData.bearing, gpsData.speed);
                        }
                        mRoadNet->getRoadNetInfo(gpsData);
                        // 1HZ
                        after = now + 1;
                    }
                }
            }else {
                //logd("gps invalid!\n");
                //logd("gps time:%lld\n", lbs->time - 28800);
            }
            break;
        }
        default: {
            break;
        }
        }
    }
    return true;
}

my::string RoadNet::RoadMsgServerHandler::getRoadNetFileHead() {
    my::string head;
    my::string version = mRoadNet->getSoftVersion();

    char adas_route_version[50] = { 0 };
    char risk_source_version[50] = { 0 };
    char icy_road_version[50] = { 0 };
    road_net_data_info version_info[3];
    version_info[0].version = adas_route_version;
    version_info[1].version = risk_source_version;
    version_info[2].version = icy_road_version;
    int data_num = 3;
    road_sdk::road_net_get_data_version(version_info, &data_num);
    for (int i = 0; i < data_num; ++i)
    {
        logd("data_version_%d is:%s \n", i, version_info[i].version);
    }
    /* 设备 id,软件版本,限速地图版本,车型,基础限速值,高低限速分界值 */
    head.assignf("%s,%s,%s,%d,%d,%d\n", mDeviceId.c_str(), version.c_str(), version_info[0].version, mCarType, mLimitSpeed, mSectionLimit);
    logd("head:%s!\n", head.c_str());
    return head;
}

bool RoadNet::RoadMsgServerHandler::on_recv(RoadMessage * msg)
{
    switch(msg->ipcMsg.type)
    {
        case ROAD_MSG_TYPE_SWVER: {
            logd("road server recieve soft version request!\n");
            my::string version = mRoadNet->getSoftVersion();
            if (version.length() > 0) comm_send(ROAD_MSG_TYPE_SWVER, (uint8_t *)(version.c_str()), version.length());
            break;
        }
        case ROAD_MSG_TYPE_SET_LIMIT: {
            if (msg->u.splLimit[0].speedLimitMax != mLimitSpeed) {
                mLimitSpeed = msg->u.splLimit[0].speedLimitMax;
                road_sdk::road_net_set_basic_speed(mLimitSpeed);
                logd("set basic speed:%d, boundary speed:%d\n", msg->u.splLimit[0].speedLimitMax,
                        msg->u.splLimit[0].speedLimitlow);
            }
            //设置高低限速分界值
            if (msg->u.splLimit[0].speedLimitlow != mSectionLimit) {
                mSectionLimit = msg->u.splLimit[0].speedLimitlow;
                road_sdk::road_net_set_boundary_speed_limit((road_net_boundary_type)mSectionLimit);
                logd("set basic speed:%d, boundary speed:%d\n", msg->u.splLimit[0].speedLimitMax,
                        msg->u.splLimit[0].speedLimitlow);
            }
            break;
        }
        case ROAD_MSG_TYPE_SET_CAR_TYPE: {
            //设置车辆类型
            if (msg->u.int32[0] != mCarType) {
                mCarType = msg->u.int32[0];
                road_sdk::road_net_set_vehicle_type(mCarType);
                logd("set car type:%d!\n", msg->u.int32[0]);
            }
            break;
        }
        case ROAD_MSG_TYPE_ROAD_NAME:
            logd("request road name!\n");
            break;
        case ROAD_MSG_TYPE_PLAYBACK_FILE_NAME: {
            /* GPS回放解析文件存储路径 */
            my::string storeName;
            my::string deviceId;
            storeName = msg->u.parseInfo[0].fileName;
            deviceId = msg->u.parseInfo[0].deviceId;
            if ((storeName.length() > 0) && (deviceId.length() > 0)) {
                mFileName = storeName;
                mDeviceId = deviceId;
                logd("gps play back file %s,deviceId:%s!\n", mFileName.c_str(), deviceId.c_str());
                mRoadNet->startRoadnetStore(mFileName);
            }
            break;
        }
        case ROAD_MSG_TYPE_REGISTER: {
            char tmp[256] = {0};
            my::gbkToUtf8(msg->u.registerParam[0].plateNum, tmp);
            loge("ROAD_MSG_TYPE_REGISTER, deviceId:%s,plateNum:%s!\n", 
                    msg->u.registerParam[0].deviceId, tmp);
            if ((strlen(msg->u.registerParam[0].deviceId) > 0) 
                && (strlen(msg->u.registerParam[0].plateNum) > 0)) {
                mRoadNet->setDeviceId(msg->u.registerParam[0].deviceId, strlen(msg->u.registerParam[0].deviceId));
                mRoadNet->setPlateNum(msg->u.registerParam[0].plateNum, strlen(msg->u.registerParam[0].plateNum));
                road_sdk::road_net_init("/data/app/road_net/data/road_net.ini", 
                                            mRoadNet->getDeviceId(), "8888", mRoadNet->getPlateNum());
                loge("road_net_init DeviceId:%s PlateNum:%s!\n", mRoadNet->getDeviceId(), mRoadNet->getPlateNum());
                bool initFlg = mRoadNet->getLicenceInitFlg();
                mRoadNet->reportLicenceInitFlg(initFlg);
            }
            break;
        }
        default: {
            break;
        }
    }
    return true;
}

int RoadNet::RoadMsgStore::stopStore() {
    /* 写入文件 */
    msgWrite(WRITE_TYPE_IMMEDIATELY);
    mStoreFlg = STORE_LOOP_INVALID;
    if (fp != nullptr) {
        fflush(fp);
        fsync(fileno(fp));
        /* 关闭文件 */
        fclose(fp);
        fp = nullptr;
    }

    logd("stop store\n");
    return true;
}

int RoadNet::RoadMsgStore::start(my::string storeFile) {
    int ret = true;
    mReqName = storeFile;

    if (!my::thread::working()) {
        /* 任务未启动，则启动任务 */
        logd("start thread!\n");
        ret = !my::thread::start();
        if (ret != true) {
            logd("start thread failed!\n");
            return ret;
        }
    }
    logd("start %s playback,mStoreFlg:%d,ret:%d!\n", storeFile.c_str(), mStoreFlg, ret);
    return ret;
}

int RoadNet::RoadMsgStore::startStore() {
    if (fp) {
        fclose(fp);
        fp = nullptr;
    }
    if (!access(mReqName.c_str(), R_OK)) {
        /* 删除同名文件 */
        remove(mReqName.c_str());
    }
    mFileName = mReqName;
    fp = fopen(mFileName.c_str(), "w+");
    if (fp == nullptr) {
        return false;
    } else {
        mReqName.clear();
    }
    /* 获取起始行 */
    my::string lineStart = mRoadNet->getRoadNetFileHead();
    int len = lineStart.length();
    if (len <= 0) {
        logd("get roadnet file head failed!\n");
        return false;
    }
    logd("lineStart:%s", lineStart.c_str());
    /* 写入起始行 */
    fseek(fp, 0, SEEK_SET);
    size_t bytes = fwrite(lineStart.c_str(), 1, len, fp);
    if (bytes != len) {
        logd("fwrite error !\n");
        return false;
    }

    mMsgId = 1;
    mStoreFlg = STORE_LOOP_RUN;

    mRoadNet->responseGpsPlayback();
    return true;
}

int RoadNet::RoadMsgStore::checkWrite() {
    if (mStoreFlg == STORE_LOOP_STOP) {
        /* 立即写入 */
        return WRITE_TYPE_IMMEDIATELY;
    } else {
        std::lock_guard<std::mutex> lock(mMsgMutex);
        if (mMsgMap.size() > GPS_PARSE_DATA_WRITE_INTERVAL) {
            /* 按固定间隔写入 */
            return WRITE_TYPE_INTERVAL;
        }
        //logd("map size:%d\n", mMsgMap.size());
    }
    return WRITE_TYPE_INVALID;
}

int RoadNet::RoadMsgStore::findMinMsgId() {
    my::uint msgId = 0;
    std::lock_guard<std::mutex> lock(mMsgMutex);
    auto i = mMsgMap.begin();
    while (i != mMsgMap.end()) {
        if (msgId == 0 || i->first < msgId) {
            msgId = i->first;
            //logd("drop %ld, left %d", i->first, mImuDataMap.size());
        }
        i++;
    }
    //logd("find msgId%d\n", msgId);
    return msgId;
}

int RoadNet::RoadMsgStore::msgWrite(const my::uint startId, const my::uint num) {
    my::uint id = startId;
    my::uint writeNum = num;
    map<my::uint, my::string>::iterator iter;

    std::lock_guard<std::mutex> lock(mMsgMutex);
    do {
        iter = mMsgMap.find(id);
        if (iter != mMsgMap.end()) {
            /* 写入文件 */
            my::string parseInfo;
            parseInfo.assignf("%d,%s\n", iter->first, iter->second.c_str());
            size_t len = parseInfo.length();
            size_t bytes = fwrite(parseInfo.c_str(), 1, len, fp);
            if (bytes != len) {
                logd("fwrite error !\n");
            } else {
                //logd("write:%s !", parseInfo.c_str());
            }
            id++;
            writeNum--;
            /* 从map删除该条数据 */
            mMsgMap.erase(iter);
        } else {
            id++;
        }
    }while(writeNum > 0);
    return true;
}

int RoadNet::RoadMsgStore::msgWrite(const int writeType) {
    my::uint startId = 0;
    //logd("writeType:%d!\n", writeType);
    if (WRITE_TYPE_IMMEDIATELY == writeType) {
        if (mMsgMap.size() <= 0) {
            if (mStoreFlg != STORE_LOOP_INVALID) {
                mStoreFlg = STORE_LOOP_INVALID;
            }
            return false;
        }
        startId = findMinMsgId();
        if (startId > 0) {
            logd("write from %d!\n", startId);
            msgWrite(startId, mMsgMap.size());
        }
        // 全部写入文件
        mStoreFlg = STORE_LOOP_INVALID;
    } else if (WRITE_TYPE_INTERVAL == writeType) {
        // 写入num行
        startId = findMinMsgId();
        if (startId > 0) {
            logd("write from %d!\n", startId);
            msgWrite(startId, GPS_PARSE_DATA_WRITE_INTERVAL);
        } else {
            //logd("not find firt id!\n");
        }
    }
    return true;
}

int RoadNet::RoadMsgStore::saveRoadInfo(const my::string info) {
    mMsgMap[mMsgId] = info;
    //logd("msg%d %s!\n", mMsgId, info.c_str());
    mMsgId++;
    return true;
}

void RoadNet::RoadMsgStore::run()
{
    int ret = 0;
    int iCount = 0;
    while (!exiting()) {
        if (mReqName.length() > 0) {
            /* 停止上一次的任务 */
            stopStore();
            mMsgId = 1;
            logd("start sotre %s!\n", mReqName.c_str());
            /* 启动存储 */
            if (startStore()) {
                mStoreFlg = STORE_LOOP_RUN;
            }
        }

        /* 文件写入检测 */
        ret = checkWrite();
        if (ret != WRITE_TYPE_INVALID) {
            msgWrite(ret);
        } else {
            //logd("no data need write!\n");
            if (mStoreFlg == STORE_LOOP_INVALID) {
                stopStore();
                logd("no data exit store!\n");
                break;
            }
        }

        if (mStoreFlg == STORE_LOOP_RUN && !mRoadNet->getGpsPlaybackStatus()) {
            /* 回放结束等待2s结束任务 */
            if (iCount++ > 20)
                mStoreFlg = STORE_LOOP_STOP;
        } else if (iCount > 0) {
            iCount = 0;
        }

        usleep(100000);
    }
}

