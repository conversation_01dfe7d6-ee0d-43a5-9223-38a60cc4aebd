#define LOG_TAG     "road_net"

#include <stdio.h>
#include <unistd.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>


#include "RoadNet.h"
#include "mystd.h"

int main()
{
    logd("main +");
    const char* config_path = "/data/app/road_net/data/road_net.ini";

    RoadNet *rn = new RoadNet();
    int32_t ret = rn->start(config_path);
    if (ret) {
        logd("RoadNet start error %d", ret);
        return 0;
    }


    while (1) {
        sleep(1);
    }
    logd("main -");
    rn->stop();
    return 0;
}

