#ifndef ROAD_SDK_H
#define ROAD_SDK_H

#include "roadnet_api.h"

namespace road_sdk
{

    
    // 定义初始化接口函数指针
    typedef road_net_status (*FUNC_road_net_init)(const char*, const char*, const char*, const char*);
    // 定义退出接口函数指针
    typedef road_net_status (*FUNC_road_net_destory)();
    // 定义设置车辆类型函数指针
    typedef road_net_status (*FUNC_road_net_set_vehicle_type)(int);
    // 定义设置基础限速值函数指针
    typedef road_net_status (*FUNC_road_net_set_basic_speed)(unsigned short);
    // 定义获取定位结果函数指针
    typedef road_net_status (*FUNC_road_net_get_location_result)(road_net_gps_info*, road_net_location_status*, road_net_spl_result*, road_net_disaster_result*, road_net_frozen_result*);
    // 定义获取软件版本号函数指针
    typedef void (*FUNC_road_net_get_soft_version)(char*, int);
    // 定义获取当前软件license的校验结果函数指针
    typedef road_net_status (*FUNC_road_net_get_soft_valid)();
    // 定义安装升级包函数指针
    typedef road_net_install_upgrade_package_result (*FUNC_road_net_install_upgrade_package)(const char*);
    // 定义获取限速地图、风险源、冰雪路段数据版本号函数指针
    typedef road_net_status (*FUNC_road_net_get_data_version)(road_net_data_info*, int*);
    // 定义设置高低限速分界值函数
    typedef road_net_status (*FUNC_road_net_set_boundary_speed_limit)(const road_net_boundary_type);
    // 定义获取城区状态结果函数
    typedef road_net_status (*FUNC_road_net_get_urban_area_status)(road_net_urban_area_status*);
    // 定义获取道路前方200米内的新限高、限重道路信息函数
    typedef road_net_status (*FUNC_road_net_get_restrict_result)(road_net_restrict_result*);
    // 定义获取道路详细名称函数
    typedef road_net_status (*FUNC_road_net_get_detail_name)(road_net_detail_name*);


    extern FUNC_road_net_init road_net_init;
    extern FUNC_road_net_destory road_net_destory;
    extern FUNC_road_net_set_vehicle_type road_net_set_vehicle_type;
    extern FUNC_road_net_set_basic_speed road_net_set_basic_speed;
    extern FUNC_road_net_get_location_result road_net_get_location_result;
    extern FUNC_road_net_get_soft_version road_net_get_soft_version;
    extern FUNC_road_net_get_soft_valid road_net_get_soft_valid;
    extern FUNC_road_net_install_upgrade_package road_net_install_upgrade_package;
    extern FUNC_road_net_get_data_version road_net_get_data_version;
    extern FUNC_road_net_set_boundary_speed_limit road_net_set_boundary_speed_limit;
    extern FUNC_road_net_get_urban_area_status road_net_get_urban_area_status;
    extern FUNC_road_net_get_restrict_result road_net_get_restrict_result;
    extern FUNC_road_net_get_detail_name road_net_get_detail_name;

    
    // 加载动态库及动态库内的符号
    int load();
};

#endif
