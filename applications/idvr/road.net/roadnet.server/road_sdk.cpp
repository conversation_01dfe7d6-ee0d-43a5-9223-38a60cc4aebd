#include "road_sdk.h"
#include "mystd.h"
#include <dlfcn.h>    // dlopen, dlerror, dlsym, dlclose
#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>   // EXIT_FAILURE


namespace road_sdk
{
    const char dllPath[100] = "/data/app/road_net/libroadnet.so";

    FUNC_road_net_init road_net_init;
    FUNC_road_net_destory road_net_destory;
    FUNC_road_net_set_vehicle_type road_net_set_vehicle_type;
    FUNC_road_net_set_basic_speed road_net_set_basic_speed;
    FUNC_road_net_get_location_result road_net_get_location_result;
    FUNC_road_net_get_soft_version road_net_get_soft_version;
    FUNC_road_net_get_soft_valid road_net_get_soft_valid;
    FUNC_road_net_install_upgrade_package road_net_install_upgrade_package;
    FUNC_road_net_get_data_version road_net_get_data_version;
    FUNC_road_net_set_boundary_speed_limit road_net_set_boundary_speed_limit;
    FUNC_road_net_get_urban_area_status road_net_get_urban_area_status;
    FUNC_road_net_get_restrict_result road_net_get_restrict_result;
    FUNC_road_net_get_detail_name road_net_get_detail_name;

    // 加载动态库及动态库内的符号
    int32_t load()
    {
        bool loaded = false;
        void* handle = dlopen(dllPath, RTLD_LAZY);
        if (!handle) {
            loge("[%s](%d) dlopen get error: %s\n", __FILE__, __LINE__, dlerror());
            return -1;
        }

        road_net_init = (FUNC_road_net_init)dlsym(handle, "road_net_init");
        road_net_destory = (FUNC_road_net_destory)dlsym(handle, "road_net_destory");
        road_net_set_vehicle_type = (FUNC_road_net_set_vehicle_type)dlsym(handle, "road_net_set_vehicle_type");
        road_net_set_basic_speed = (FUNC_road_net_set_basic_speed)dlsym(handle, "road_net_set_basic_speed");
        road_net_get_location_result = (FUNC_road_net_get_location_result)dlsym(handle, "road_net_get_location_result");
        road_net_get_soft_version = (FUNC_road_net_get_soft_version)dlsym(handle, "road_net_get_soft_version");
        road_net_get_soft_valid = (FUNC_road_net_get_soft_valid)dlsym(handle, "road_net_get_soft_valid");
        road_net_install_upgrade_package = (FUNC_road_net_install_upgrade_package)dlsym(handle, "road_net_install_upgrade_package");
        road_net_get_data_version = (FUNC_road_net_get_data_version)dlsym(handle, "road_net_get_data_version");
        road_net_set_boundary_speed_limit = (FUNC_road_net_set_boundary_speed_limit)dlsym(handle, "road_net_set_boundary_speed_limit");
        road_net_get_urban_area_status = (FUNC_road_net_get_urban_area_status)dlsym(handle, "road_net_get_urban_area_status");
        road_net_get_restrict_result = (FUNC_road_net_get_restrict_result)dlsym(handle, "road_net_get_restrict_result");
        road_net_get_detail_name = (FUNC_road_net_get_detail_name)dlsym(handle, "road_net_get_detail_name");

        loaded = (road_net_init && road_net_destory && road_net_set_vehicle_type
                    && road_net_set_basic_speed && road_net_get_location_result
                    && road_net_get_soft_version && road_net_get_soft_valid && road_net_install_upgrade_package
                    && road_net_get_data_version && road_net_set_boundary_speed_limit && road_net_get_urban_area_status
                    && road_net_get_restrict_result && road_net_get_detail_name);

        if (!loaded) {
            loge("Bad functions of mg6864 camera.\n");
            return -2;
        }
        return 0;
    }
}
