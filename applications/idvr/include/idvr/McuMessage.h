#ifndef __MINIEYE_MCU_MESSAGE_H__
#define __MINIEYE_MCU_MESSAGE_H__

#include <stdio.h>
#include <unistd.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include "ipcMessage.h"
#include "ipcAgent.h"

#define SH_NAME_HOSTIO "/mnt/obb/hostio"
#define MCU_MSG_PAYLOAD_MAX_SIZE        (256)
typedef enum
{
    MCU_MSG_TYPE_INVALID,
    MCU_MSG_TYPE_SWVER,

    MCU_MSG_TYPE_LOAD_CFG,
    MCU_MSG_TYPE_MCU_CONFIG,

    MCU_MSG_TYPE_CONFIG_CAN,
    MCU_MSG_TYPE_SET_CAN_FILTER,
    MCU_MSG_TYPE_CLEAR_ALL_FILTER,
    MCU_MSG_TYPE_RAW_CAN0, /* speed */
    MCU_MSG_TYPE_RAW_CAN1, /* display */

    MCU_MSG_TYPE_CAR_INFO,

    MCU_MSG_TYPE_UPGRADE,

    MCU_MSG_TYPE_STAT,
    MCU_MSG_TYPE_IC_CARD,  /* 合法的IC信息 */
    MCU_MSG_TYPE_IC_CARD_WR,
    MCU_MSG_TYPE_IC_CARD_RD,
    MCU_MSG_TYPE_IC_CARD_FIX_RST,

    MCU_MSG_TYPE_PRINTER_TEST,
    MCU_MSG_TYPE_PRINT,

    MCU_MSG_TYPE_TRIP_ALERT,
    MCU_MSG_TYPE_GPS_PASS_ENABLE,
    MCU_MSG_TYPE_GPS_CMD, /* GPS模块指令*/

    MCU_MSG_TYPE_DB9,
    MCU_MSG_TYPE_RS485,
    MCU_MSG_TYPE_RS485_CONFIG,
    MCU_MSG_TYPE_IO_CTRL,
    MCU_MSG_TYPE_PWR_DOWN,
    MCU_MSG_TYPE_PWR_DOWN_PENDING,

    MCU_MSG_TYPE_GB19056_LOG_DUMP,
    MCU_MSG_TYPE_GB19056_RECORD_DUMP,

    MCU_MSG_TYPE_MCU_VERSION,
    MCU_MSG_TYPE_MCU_FIRMWARE_INFO,
    MCU_MSG_TYPE_MCU_UPGRD_AUTHEN, /* upgrade authentication */
    MCU_MSG_TYPE_HOSTIO_VERSION,

    MCU_MSG_TYPE_LOG_CTRL,
    MCU_MSG_TYPE_MCU_RST,
    MCU_MSG_TYPE_MCU_BLD_MODE,
    MCU_MSG_TYPE_MCU_APP_MODE,

    MCU_MSG_TYPE_MCU_GB19056_FETCH,
    MCU_MSG_TYPE_MCU_GB19056_FETCH_ALL,

    MCU_MSG_TYPE_LCD_CTRL,
    MCU_MSG_TYPE_IC_CARD_LOGIN,
    MCU_MSG_TYPE_IC_CARD_LOGOUT,

    MCU_MSG_TYPE_TRIP_INFO,

    MCU_MSG_TYPE_ADAS_CAN700,
    MCU_MSG_TYPE_CAN1_DISPLAY,

    MCU_MSG_TYPE_IDVR_CONFIG_MODIRIED,
    MCU_MSG_TYPE_IDVR_CONFIG_WR,

    MCU_MSG_TYPE_RECORD_STATUS,

    MCU_MSG_TYPE_EXT_LBS,
    MCU_MSG_TYPE_F9KGPS_LIBS,

    MCU_MSG_TYPE_CAN_STAT,
    MCU_MSG_TYPE_CAN_CLR_STAT,

    MCU_MSG_TYPE_MCU_REG,

    MCU_MSG_TYPE_SOC_RST,

    MCU_MSG_TYPE_SWITCH_CTRL,
    MCU_MSG_TYPE_SAT_INFO,

    MCU_MSG_TYPE_MAX,
} McuMsgTypeE;

typedef enum {
    LOG_TYPE_GPS,
    LOG_TYPE_STATUS,
    LOG_TYPE_CAN,
    LOG_TYPE_ASPEED,
    LOG_TYPE_MAX,
}LOG_TYPE_CTRL_E;

typedef enum {
    PWR_DOWN_THREAD_MEDIA,
    PWR_DOWN_THREAD_MAX,
} PWR_DOWN_THREAD_E;

typedef enum {
    MINOR_GPIO_OIL_HIGH,
    MINOR_GPIO_OIL_LOW,

    MINOR_GPIO_PWR_5V_ON,
    MINOR_GPIO_PWR_5V_OFF,

    MINOR_GPIO_PWR_12V_ON,
    MINOR_GPIO_PWR_12V_OFF,

    MINOR_GPIO_PWR_SYS_ON,
    MINOR_GPIO_PWR_SYS_OFF,

    MINOR_GPIO_CAP_ENCHAR_ON,
    MINOR_GPIO_CAP_ENCHAR_OFF,

    MINOR_GPIO_PWR_ETH_ON,
    MINOR_GPIO_PWR_ETH_OFF,

    MINOR_GPIO_PWR_GPS_ON,
    MINOR_GPIO_PWR_GPS_OFF,

    MINOR_GPIO_PWR_4G_ON,
    MINOR_GPIO_PWR_4G_OFF,
}McuIOTypeE;

typedef enum {
    MCU_RST_SOC_ON,
    MCU_RST_SOC_OFF,

    CAP_CHARING_AUTO,
    CAP_CHARING_MANUAL,

    LED_R_ON,
    LED_G_ON,
    LED_R_FLASH_1S,
    LED_G_FLASH_1S,
}McuSwitchTypeE;

/* 工作模式 */
typedef enum
{
    WORK_MODE_FT,
    WORK_MODE_AGING,
    WORK_MODE_SETUP,
    WORK_MODE_NORMAL,
    WORK_MODE_SAMPLE,
    WORK_MODE_MAX,
}WorkModeE;

typedef enum
{
    MCU_CAN_IDX_CAN0_SPEED,
    MCU_CAN_IDX_CAN1_DISP,
    MCU_CAN_IDX_CAN_MAX,
} McuMsgCanIdxE;

typedef enum
{
    MCU_CAN_SPEED_INVALID,
    MCU_CAN_SPEED_1M,
    MCU_CAN_SPEED_800K,
    MCU_CAN_SPEED_500K,
    MCU_CAN_SPEED_250K,
    MCU_CAN_SPEED_125K,
    MCU_CAN_SPEED_100K,
    MCU_CAN_SPEED_50K,
    MCU_CAN_SPEED_20K,
    MCU_CAN_SPEED_10K,
    MCU_CAN_SPEED_5K,
    MCU_CAN_SPEED_MAX,
} McuCanSpeedE;

typedef enum
{
    MCU_CAN_WORKING_MODE_NORMAL     = 0,
    MCU_CAN_WORKING_MODE_LOOPBACK   = 1,
    MCU_CAN_WORKING_MODE_SILENT     = 2,
    MCU_CAN_WORKING_MODE_SILENT_LOOPBACK    = 3,
    MCU_CAN_WORKING_MODE_MAX,
} McuCanWorkingModeE;

typedef struct UrtpUartConfT
{
    UrtpUartConfT(int b, uint8_t s, char p)
        : baudrate(b)
        , stopbit(s)
        , parity(p)
    {

    }
    uint32_t         baudrate;
    uint8_t          stopbit;
    char             parity;
} __attribute__((packed)) UrtpUartConfT;

typedef struct {
    uint16_t disable_thres;
    uint16_t enable_thres;
} bat_threshold_t;

typedef struct
{
    McuMsgCanIdxE       canx;
    McuCanSpeedE        speed;
    McuCanWorkingModeE  mode;
    uint8_t             use_json;
    char                file[128];

} __attribute__((packed)) McuMsgCanConfigT;

typedef struct
{
    uint32_t    id;
    uint8_t     len;
    uint8_t     data[8];
}  __attribute__((packed)) McuMsgCanT;

#define MCU_CAN_STD_ID_MAX      ((1 << 11))
#define MCU_CAN_EXT_ID_MAX      ((1 << 29))
typedef struct
{
    #define CAN_ID_ARRAY_SIZE   (4)
    uint8_t     enable;
    uint8_t     canIdx;
    uint8_t     useListMode;
    uint32_t    canIds[CAN_ID_ARRAY_SIZE];
} __attribute__((packed)) McuMsgCanFilterT;

typedef struct
{
    uint8_t     enableE1;
    uint8_t     enableAspeed;
    uint8_t     reserved: 7;
} __attribute__((packed)) McuMsgMcuConfigT;

typedef struct
{
    uint8_t major;
    uint8_t middle;
    uint8_t minor;
} __attribute__((packed)) McuMsgSWVerT;

typedef struct
{
    uint32_t    last_recv_second;
    uint32_t    reserved_info_a;
    uint32_t    reserved_info_b;
    uint32_t    reserved_info_c;
}  __attribute__((packed)) McuMsgCanInfo;

typedef struct {
    uint8_t canDoorBits = 0;    //开门信号 bit0 front, bit1 middle bit2 back, 0xff invalid
    uint8_t canTurn     = 0;    //转向
    uint8_t canBrake    = 0;    //刹车
    uint8_t accelerator = 0;    /*加速踏板开度，百分比*/

    uint8_t gear         : 4;   /*档位模式：0 N, 1 D, 2 R, 3 L, 4 M, 5 P, 6 S*/
    uint8_t canReverse   : 1;   //倒车
    uint8_t EHB_state    : 1;
    uint8_t EHB_park_req : 1;
    uint8_t EHB_park_done: 1;

    uint8_t FLWheelSpdStat : 2;  /*0 正常，1 故障，2， 初始，3 保留*/
    uint8_t FRWheelSpdStat : 2;  /*0 正常，1 故障，2， 初始，3 保留*/
    uint8_t BLWheelSpdStat : 2;  /*0 正常，1 故障，2， 初始，3 保留*/
    uint8_t BRWheelSpdStat : 2;  /*0 正常，1 故障，2， 初始，3 保留*/
    double FLWheelSpd       = 0; /*前左轮轮速*/
    double FRWheelSpd       = 0; /*前右轮轮速*/
    double BLWheelSpd       = 0; /*后左轮轮速*/
    double BRWheelSpd       = 0; /*后右轮轮速*/

    double canSpeed         = 0; //速度
    double canMileTotal     = 0; //总里程
    double canMileSingle    = 0; //单里程
    double canFuelTotal     = 0; //总油耗
    double canFuelAvg       = 0; //瞬时油耗
    double canTurnSpeed     = 0; //转速
}  __attribute__((packed)) McuMsgCarInfoT;

/**************************IDVR ADD******************************/

// IO状态
typedef union
{
    struct
    {
        uint16_t acc:1;               // 点火开关
        uint16_t emergency_alarm:1;   // 报警事件触发
        uint16_t backwards:1;         // 倒车信号
        uint16_t normal_light:1;      // 近光灯
        uint16_t far_light:1;         // 远光灯
        uint16_t left_turn:1;         // 左转弯
        uint16_t right_turn:1;        // 右转弯
        uint16_t brake:1;             // 刹车信号

        uint16_t door:1;              // 车门信号
        uint16_t can_signal_loss:1;
        uint16_t crash_warn: 1;
        uint16_t roll_alarm : 1;
        uint16_t car_load_stat : 2;     /*00 空车；01 半载；10 保留；11满载*/
        uint16_t iccard_inserted : 1;
        uint16_t resv:1;
    };
    uint16_t value;
} IOStatus;

typedef union
{
    struct
    {
        uint32_t printer_hot:1;
        uint32_t printer_no_paper:1;
        uint32_t panel_door_open:1;   //前面板开合
        uint32_t iccard_inserted:1;
        uint32_t resv:28;
    };
    uint32_t value;
} DVR_IO_t;

typedef union
{
    struct
    {
        uint32_t power_low:1;    // 低电压
        uint32_t power_off:1;    // 电瓶电关闭
        uint32_t resv:30;
    };
    uint32_t value;
}SysStatus_t;

typedef union{
    struct {
        uint16_t acc:1;
        uint16_t pwrSys:1;     /* 0为低功耗模式 */
        uint16_t pwrExt12v:1;
        uint16_t pwrExt5v:1;
        uint16_t batDischar:1;
        uint16_t batEnchar:1;
        uint16_t soc_sys_pwr:1;
        uint16_t pwr_eth:1;
        uint16_t pwr_gps:1;
        uint16_t pwr_4g:1;

        uint16_t resv:6;
    };
    uint16_t val;
} pwr_set_t;

typedef union{
    struct {
        uint32_t fMcuRstSoc:1;      // 允许mcu超时复位soc标志
        uint32_t fAutoCtlCap:1;     // 允许电容自动充放电控制

        uint32_t resv:30;
    };
    uint32_t val;
} flag_sta_t;

enum {
    SIM_SYSTEM_SERVICE,
    SIM_SYSTEM_SERVICE_DOMAIN,
    SIM_ROAM_STATUS,
    SIM_SYSTEM_MODE,
    SIM_INSERT,
    SIM_MAX
};

// RTK 定位相关数据
struct RTKData {
    bool enable;        // 是否使用RTK模块
    unsigned char sig;  // 定位状态 （0=Invalid, 1=2D/3D, 2=DGNSS, 4=Fixed RTK, 5=Float RTK, 6=Dead）
    double  lat;        // 经度原始值 保证精度
    double  lng;        // 纬度原始值
};
typedef struct {
    uint32_t ver;
    uint8_t regId[7];
    uint8_t authToken[32];
    uint32_t intevalHb;
    uint32_t intevalLocate;
} ST_HOST_JT808_INFO;
typedef struct SatellitesInfo
{
    unsigned char num;
    struct {
        unsigned char  type : 3;/*1 gps 2 bds 3 glonass 4 galileo*/
        unsigned char  idx  : 5;
        unsigned char  elevation ;
        unsigned char  cnr;
        unsigned short bearing;
    } s[40];
} SatellitesInfoT;
static_assert(sizeof(struct SatellitesInfo) <= (MCU_MSG_PAYLOAD_MAX_SIZE), "Invalid struct SatellitesInfo size > MCU_MSG_PAYLOAD_MAX_SIZE!");

// 卫星数据
struct LBS
{
#define ANTENNA_UNKNOW  0
#define ANTENNA_OPEN    1
#define ANTENNA_OK      2
#define ANTENNA_SHORT   3

    uint8_t sat;           // 卫星数量
    uint8_t status:4;     // 0未定位, 1已定位
    uint8_t sig_level:4;  // 信号强度等级 0 1 2 3依次增强
    uint8_t antenna;    // 天线状态  0未知，1断开，2正常，3短路

    unsigned short HDOP; // 水平精度
    int lat_x1kw;       // 经度 x 10000000
    int lng_x1kw;       // 纬度 x 10000000
    int alt_x10;        // 高度 x 10

    unsigned short dir_x100;  // 方向 x 100
    unsigned short speed_x10; // 速度 x10   km/h

    unsigned int time;

    RTKData rtkData;
};
static_assert(sizeof(struct LBS) <= (MCU_MSG_PAYLOAD_MAX_SIZE), "Invalid struct LBS size > MCU_MSG_PAYLOAD_MAX_SIZE!");

typedef enum {
    ADC_CHN_VER,
    ADC_CHN_CAP,
    ADC_CHN_TEM,
    ADC_CHN_VDD,
    ADC_CHN_DCIN,
    ADC_CHN_MAX,
} ADC_TYPE_T;

typedef struct McuStat
{
    IOStatus vehicle_io;
    DVR_IO_t dvr_io;
    struct LBS lbs;
    int net_info[SIM_MAX]; /* SIM卡状态 */
    int csq[2];            /* 第一个字节为4G信号强度 */
    char imei[21];
    char iccid[21];

    /* 脉冲速度 */
    int pulses_speed_x10;
    uint8_t pulses_turnl;
    uint8_t pulses_turnr;

    /* CAN速度 */
    int can_speed_x10;
    uint8_t can_turnl;
    uint8_t can_turnr;

    /* 安装选择的速度 */
    int speed_x10;
    uint8_t turnl;
    uint8_t turnr;

    double total_mileage;
    uint32_t adcRaw[ADC_CHN_MAX];
    float adcVol[ADC_CHN_MAX];
    uint8_t key[2];
    bool accOff_event;

    SysStatus_t sys;
    pwr_set_t pwr_set;
} McuMsgMcuStatT;


#define ICCARD_SIZE_MAX (256-32)
/* IC 卡写入 buf*/
typedef struct _ICCardWrBuf
{
    /* GB190565 */
    char id[18]; // 驾驶证
    char expiry_bcd[3]; // 有效期, BCD 格式
    char cert[18]; // 从业资格证

    /*扩展字段*/
    char name[20]; // 姓名
    char code[10]; // 司机编号
    char org[60]; // 发卡组织, utf8的3个字节一个中文, 最多20个中文，
} __attribute__ ((packed)) ICCardWrBuf;

typedef enum
{
    CMD_CLEAR_ALARM,
    CMD_MAX,
}CMD_E;

typedef struct CmdTag
{
	CMD_E    cmd;       //命令字
    uint8_t  paramLen;  //参数长度
    uint8_t  param[0];  //参数
} McuMsgCmdT;

typedef struct TripInfoTag
{
    uint32_t startTime;
    uint32_t endTime;
    uint32_t todayTotal;
} TripInfoT;

typedef struct ImuDataTag
{
    unsigned long timestamp;
    short gyro[3];
    short accel[3];

    float temp;
    double roll;
    double pitch;

    std::string toJson() {
        char jbuf[1024];
        snprintf(jbuf, sizeof(jbuf),
            "{\"ts\" : %ld,"\
            "\"gyro\" : {\"x\" : %d, \"y\" : %d, \"z\" : %d},"
            "\"accel\": {\"x\" : %d, \"y\" : %d, \"z\" : %d},"
            "\"temp\" : %f,"
            "\"roll\" : %.3f,"
            "\"pitch\": %.3f}\n",
            timestamp,
            gyro[0],  gyro[1],  gyro[2],
            accel[0], accel[1], accel[2],
            temp, roll, pitch);
        return jbuf;
    }
} ImuDataT;
/**************************IDVR ADD END***************************/

typedef struct
{
    IpcMessage ipcMsg;

    union
    {
        uint8_t         u8Array[0];
        char            schar[0];
        uint8_t         uchar[0];
        int32_t         int32[0];
        McuMsgCanT      canMsg[0];
        McuMsgCarInfoT  carInfo[0];
        McuMsgCanConfigT    canConfig[0];
        McuMsgCanFilterT filter[0];
        McuMsgMcuConfigT mcuConfig[0];
        McuMsgSWVerT     swVer[0];
        McuMsgCanInfo    canInfo[0];
        McuMsgMcuStatT   stat[0];
        ICCardWrBuf      icCard[0];
        McuMsgCmdT       cmd[0];
        TripInfoT        trip[0];
        ImuDataT         imuData[0];
        LBS              lbs[0];
        SatellitesInfo   satInfo[0];
    } u;

} McuMessage;

#define MCU_MSG_MAX_SIZE        (sizeof(McuMessage) + MCU_MSG_PAYLOAD_MAX_SIZE)
#define MCU_MSG_HEADER_SIZE     (sizeof(McuMessage))
#define MCU_MSG_SIZE(_s)        (sizeof(_s))

#define MCU_MSG_SIZE_CAN            (MCU_MSG_SIZE(McuMsgCanT))
#define MCU_MSG_SIZE_CAN_CONFIG     (MCU_MSG_SIZE(McuMsgCanConfigT))
#define MCU_MSG_SIZE_UPGRADE        (MCU_MSG_SIZE(McuMsgUpgradeT))
#define MCU_MSG_SIZE_CAN_FILTER     (MCU_MSG_SIZE(McuMsgCanFilterT))
#define MCU_MSG_SIZE_MCU_CONFIG     (MCU_MSG_SIZE(McuMsgMcuConfigT))
#define MCU_MSG_SIZE_LED_CTRL       (MCU_MSG_SIZE(McuMsgLedCtrlT))
#define MCU_MSG_SIZE_MDVR_DATA      (MCU_MSG_SIZE(DvrRtData))
#define MCU_MSG_SIZE_STAT           (MCU_MSG_SIZE(McuMsgMcuStatT))
#define MCU_MSG_SIZE_CMD            (MCU_MSG_SIZE(McuMsgCmdT))
#define MCU_MSG_SIZE_ICCARD         (MCU_MSG_SIZE(ICCardWrBuf))
#define MCU_MSG_SIZE_CAR_INFO       (MCU_MSG_SIZE(McuMsgCarInfoT))
#endif

