#ifndef __IDVR_PROPERTY_H__
#define __IDVR_PROPERTY_H__

/**
* 在adb shell里敲入getprop命令来获取当前系统的所有属性内容;
* 还可以敲入类似“getprop 属性名”的命令来获取特定属性的值;
* 设置属性值的方法也很简单，只需敲入“setprop 属性名 新值”命令即可;

* 不同进程只可以通过socket方式，向属性服务发出修改属性值的请求，而不能直接修改属性值
**/


//persist的property都会保存在 /data/property文件夹下，开机时property service会解析加载此文件中的property值
#define PROP_PERSIST_BSD_CAN1DISPLAY                                "persist.bsd.can1display"
#define PROP_PERSIST_MINIEYE_VOLUME                                 "persist.minieye.volume" //设置音量
#define PROP_PERSIST_MINIEYE_AUDIO_ENCODE                           "persist.minieye.audio.encode"
#define PROP_PERSIST_MINIEYE_IPC_PROT                               "persist.minieye.ipc.prot"
#define PROP_PERSIST_MINIEYE_G726_BITS                              "persist.minieye.g726.bits"
#define PROP_PROP_PERSIST_MINIEYE_IMEI_SET                          "persist.minieye.imei_set"
#define PROP_PERSIST_MINIEYE_ICCID                                  "persist.minieye.iccid"
#define PROP_PERSIST_MINIEYE_DEVICEID                               "persist.minieye.deviceId"
#define PROP_PERSIST__JTT_PROT_VERSION                              "persist.minieye.jtt808_version"
#define PROP_PERSIST_HENANTELCOM_LOADSIGBIT                         "persist.henanTelecom.loadSigBit"
#define PROP_PERSIST_MINIEYE_TRIPOSDCOLOR                           "persist.minieye.tripOsdColor"

#define PROP_PERSIST_MINIEYE_FACEID_AUDIO_ENABLE                    "persist.minieye.prot_faceid_aud" //人脸比对相关的语音播报开关，大于0时开启，0关闭
#define PROP_PERSIST_HENANTELCOM_FACEID_PERIOD                      "persist.henanTelecom.facePeriod" //人脸比对比对周期，单位s，未设置是默认周期60s
#define PROP_PERSIST_HENANTELCOM_FACEID_TRIG                        "persist.henanTelecom.faceIdTrig" //人脸比对触发类型，0关闭 1定时 2定距 3插卡 4保留 5驾驶员变更

#define PROP_PERSIST_MINIEYE_PWRKEYREBOOT                           "persist.minieye.pwrkeyReboot"
#define PROP_PERSIST_MINIEYE_GPS_NMEM_LOG_ENABLE                    "persist.minieye.gps.nmeaLog"
#define PROP_PERSIST_MINIEYE_LCD_BACK_LIGHT_TIME                    "persist.minieye.backlight"
#define PROP_PERSIST_MINIEYE_CAMERA_CHANNEL_SHOW                    "persist.minieye.camChShow"
#define PROP_PERSIST_MINIEYE_ADAS_DISP_MODE                         "persist.minieye.adasDispMode"
#define PROP_PERSIST_MINIEYE_RS485_EXTERNAL_GPS                     "persist.minieye.rs485gps"
#define PROP_PERSIST_MINIEYE_GPS_NMEA_TO_DB9                        "persist.minieye.gps2db9"
#define PROP_PERSIST_MINIEYE_NMEA_DATA_ENABLE                       "persist.minieye.nmea.enable" //为1时libflow发送nmea数据给Android屏，为2时发送nmea数据到ringbuf，为3时都发
#define PROP_PERSIST_MINIEYE_IME                                    "persist.minieye.imei"
#define PROP_PERSIST_MINIEYE_ONLY_GPS_SPEED_ENABLE                  "persist.minieye.only_gpsSpeed"
#define PROP_PERSIST_MINIEYE_SMS_SUM                                "persist.minieye.sms_num"
#define PROP_PERSIST_MINIEYE_DB_MODE                                "persist.minieye.db.mode"
#define PROP_PERSIST_ANNCR_LOC_SET                                  "persist.sanfeng.anncr.location"
#define PROP_PERSIST_808_ATT_SEEK                                   "persist.808.att.seek"
#define PROP_PERSIST_808_ATT_DURATION                               "persist.808.att.duration"
#define PROP_PERSIST_MINIEYE_TALK_CH_BASE                           "persist.minieye.talkChBase"
#define PROP_PERSIST_JT808_LIVEWITHOUTAUDIO                         "persist.jtt808.liveWithoutAudio"
#define PROP_PERSIST_MINIEYE_FATIGE_DRIVE                           "persist.minieye.fatigue_drv"
#define PROP_PERSIST_MINIEYE_JTT808RPTLVLSPD                        "persist.minieye.jtt808RptLvlSpd"
#define PROP_PERSIST_MINIEYE_JTT808PICATTNUM                        "persist.minieye.jtt808PicAttNum"
#define PROP_PERSIST_MINIEYE_JTT808_EVT_VAL_FMT                     "persist.jtt808.%s"
#define PROP_PERSIST_JTT808_NOT_RPT_LBS_HISTORY                     "persist.jtt808.NotRptLbsHistory"
#define PROP_PERSIST_JTT808_0200_NOALARMATT                         "persist.jtt808.0200_noAlarmAtt"
#define PROP_PERSIST_JTT808_TTS_PWR_SHORT                           "persist.jtt808.tts_pwr_short"
#define PROP_PERSIST_JTT808_ALARM_MASK                              "persist.jtt808.alarm_mask"
#define PROP_PERSIST_MINIEYE_ATT_SEND_ON_LEVEL                      "persist.minieye.att_snd_on_lvl"
#define PROP_PERSIST_HENANTELCOM_BIND_STAT                          "persist.henanTelecom.bindStatus"
#define PROP_PERSIST_HENANTELCOM_BIND_ICCID                         "persist.henanTelecom.bindIccid"
#define PROP_PERSIST_HENANTELCOM_BIND_IMEI                          "persist.henanTelecom.bindImei"
#define PROP_PERSIST_MINIEYE_FACE_MATCH_THRES                       "persist.minieye.faceMatchThres" //default 65
#define PROP_PERSIST_HELANTEC_MAXSPDLIMIT                           "persist.hualanTec.maxSpdLimit"
#define PROP_PERSIST_NANJING_FORCE9206SNDATT                        "persist.nanjing.force9206sndAtt"
#define PROP_PERSIST_MINIEYE_JTT808CMD_RETRY                        "persist.minieye.jtt808cmd.retry"
#define PROP_PERSIST_MINIEYE_ZTC_AUDIO_ENABLE                       "persist.minieye.prot_ztc_aud" //渣土相关的语音播报 0：关闭播报，1：打开播报
#define PROP_PERSIST_MINIEYE_MACID                                  "persist.minieye.macId"
#define PROP_PERSIST_MINIEYE_LOC_SAVE_DAY_MAX                       "persist.minieye.locRecMaxDay"
#define PROP_PERSIST_MINIEYE_WEIGHT_CALIB                           "persist.minieye.weight_calib"
#define PROP_PERSIST_MINIEYE_LOC_REC_MAX_DAY                        "persist.minieye.locRecmaxDay"
#define PROP_PERSIST_MINIEYE_YB1078_ENBALE                          "persist.minieye.yb1078"  /* 粤标1078扩展 */
#define PROP_PERSIST_MINIEYE_GPS_PDOP_THRES                         "persist.minieye.gps.PDOP"
#define PROP_PERSIST_MINIEYE_GPS_TMSYNC_THRES                       "persist.gps.time.sync.thres"
#define PROP_PERSIST_MINIEYE_TRACE_DATA_ENABLE                      "persist.minieye.collectLoc"
#define PROP_PERSIST_MINIEYE_COLLECT_PIC_ENABLE                     "persist.minieye.collectPic"
#define PROP_PERSIST_MINIEYE_TRAFFIC_CTRL                           "persist.minieye.limitTraffic"
#define PROP_PERSIST_MINIEYE_JTT808_NOMSG_TIMEOUT                   "persist.jtt808.rcv.timeout.sec"
#define PROP_PERSIST_MINIEYE_GPS_SWITCH                             "persist.minieye.gps.switch"        // GPS 模块选择，F9K
#define PROP_PERSIST_MINIEYE_RADAR_CALIBRATION                      "persist.minieye.radar_cal"
#define PROP_PERSIST_MINIEYE_EXPAND_TIMEOUT_ENABLE                  "persist.expand.timeout_enable"     // false: 关闭超时断连，true: 开启超时断连

#define PROP_PERSIST_BSD_BMP_SHOW_DOURATION                         "persist.bsd.IconDuration"        /* 图标持续时间 */
#define PROP_PERSIST_BSD_BMP_SHOW_INTERVAL                          "persist.bsd.IconInterval"        /* 图标显示周期 */
#define PROP_PERSIST_BSD_VOICE_PLAY_INTERVAL                        "persist.bsd.voiceInterval"      /* 语音播放周期 */
#define PROP_PERSIST_BSD_ALARM_TIMEOUT_MS                           "persist.bsd.alarm.timeout.ms"      /* 告警超时时间，毫秒 */
#define PROP_PERSIST_RADAR_BMP_SHOW_DOURATION                       "persist.radar.IconDuration"        /* 图标持续时间 */
#define PROP_PERSIST_RADAR_BMP_SHOW_INTERVAL                        "persist.radar.IconInterval"        /* 图标显示周期 */
#define PROP_PERSIST_RADAR_VOICE_PLAY_INTERVAL                      "persist.radar.voiceInterval"      /* 语音播放周期 */
#define PRO_PERSISI_HAW_HDW_HCW_ENABLE                              "persist.haw.enable"            /* 三急功能使能标志位*/
#define PROP_PERSIST_FUEL_HEIGHT                                    "persist.fuel.height"
#define PROP_PERSIST_ENABLE_FULL_SRC                                "persist.xiashi.fullsrc"        // 是否开启人脸匹配功能
#define PROP_PERSIST_ALARM_INTERVAL                                 "persist.alarm.interval"
#define PROP_PERSIST_DISABLE_CHK_ICCARD                             "persist.prot.chk.iccard.disable"

#define PROP_PERSIST_MINIEYE_LASTGPSLOCATION                        "persist.minieye.lastgpslocation"
//读写
#define PROP_RW_MINIEYE_GPSLASTLOCATION                             "rw.minieye.gpslastlocation"

#define PROP_RW_MINIEYE_GPS_CALIB_RATIO                             "rw.minieye.gcalib_ratio"
#define PROP_RW_MINIEYE_CALIB_STDDEV                                "rw.minieye.gcalib_stddev"

#define PROP_RW_MINIEYE_GPS_PLAYBACK_ENABLE                         "rw.minieye.gps.playback"
#define PROP_RW_MINIEYE_SIGNAL_LEVEL                                "rw.minieye.signal_level"
#define PROP_RW_MINIEYE_POWER_VOL                                   "rw.minieye.power_vol"
#define PROP_RW_MINIEYE_GRID_NUM                                    "rw.minieye.grid_num"
#define PROP_RW_MINIEYE_IDVR_CCU_HEARTBEAT                          "rw.minieye.idvr.ccu_heartbeat"
#define PROP_RW_MINIEYE_GPS_CALIB_ENABLE                            "rw.minieye.gcalib"
#define PROP_RW_MINIEYE_FAKESPEED                                   "rw.minieye.fakespeed"
#define PROP_RW_HENAN_TELECOM_BIND_STAT                             "rw.henanTelecom.bindStatus"
#define PROP_RW_MINIEYE_MSG_IDX                                     "rw.minieye.msg_idx"
#define PROP_RW_MINIEYE_CERT_VAILD                                  "rw.minieye.cert_vaild"
#define PROP_RW_MINIEYE_WORK_MODE                                   "rw.minieye.work_mode"
#define PROP_RW_MINIEYE_ICCARDAUDIO                                 "rw.minieye.iccardAudio"
#define PROP_RW_MINIEYE_DISABLE_GPS                                 "rw.minieye.disableGps" //true GPS回放，false GPS回放结束
#define PROP_RW_MINIEYE_PROT_ONLINE_TS                              "rw.minieye.protOnlineTs"
#define PROP_RW_MINIEYE_DISABLE_TRIP_LOG                            "rw.minieye.disable_trip_log"
#define PROP_RW_MINIEYE_CAMERR                                      "rw.minieye.camerr"
#define PROP_RW_MNT_MEDIA_RW_DISK1                                  "rw.minieye.mnt_media_rw_disk1"
#define PROP_RW_MNT_MEDIA_RW_SDCARD1                                "rw.minieye.mnt_media_rw_sdcard1"
#define PROP_RW_MINIEYE_ENABLE_UPGRADE                              "rw.minieye.allow_upgrade_prot"
#define PROP_RW_MINIEYE_BSD_WARN_BMP_SCALE                          "rw.minieye.bsd_warn_bmp_scale"
#define PROP_RW_MINIEYE_CAM_STATUS                                  "rw.minieye.cam_status"
#define PROP_RW_MINIEYE_OCCLU                                       "rw.minieye.occlu"          /* 遮挡摄像头 */

#define PROP_RW_MINIEYE_WAKEUP_STATUS                               "rw.minieye.wakeup_status"          /* 休眠唤醒状态 */
#define PROP_RW_MINIEYE_PLATE_NUM_GBK                               "rw.minieye.plate.num.gbk"
#define PROP_RW_MINIEYE_808TTS_IDX                                  "rw.minieye.808tts_idx"
#define PROP_RW_MINIEYE_EXT_IO_STATUS                               "rw.minieye.ext_io_status"
#define PROP_RW_MINIEYE_RECOVERY_STATUS                             "rw.minieye.recovery.status"        /*灾备状态，lock：写保护，unlock：已解锁*/

#define PROP_RW_MINIEYE_FACE_ID                                     "rw.minieye.face_id"
#define PROP_RW_MINIEYE_FACEID_MATCH                                "rw.minieye.faceIdMatch"    /* faceid match result */

//只读
#define PROP_RO_BUILD_VERSION_INCREMENTAL                           "ro.build.version.incremental"
#define PROP_RO_BUILD_DATE_UTC                                      "ro.build.date.utc"


#define PROP_RW_MINIEYE_SPDING_COUNT                                "rw.minieye.spding.count"
#define PROP_RW_MINIEYE_FATIGUE_COUNT                               "rw.minieye.fatigue.count"
#define PROP_RW_MINIEYE_ZF_ENABLE                                   "rw.minieye.zf" /* ZF项目 */


#endif



