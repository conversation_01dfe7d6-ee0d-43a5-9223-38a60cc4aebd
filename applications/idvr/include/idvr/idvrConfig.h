#ifndef __IDVR_CONFIG_H__
#define __IDVR_CONFIG_H__
#include "mystd.h"
#include "idvr.h"
#include "system_properties.h"
#include "idvrProperty.h"

#define CFG_ASSERT(cond, r, loge, format, args...)    LOG_IF(cond, loge, format, ## args)

// 每个产品存放数据库、fileprop、或者其他数据的位置不一样。例如nand设备尽量不要刷写，统一放在灾备或者硬盘上
static std::string idvrGetDataPath()
{
    // m5pro型号默认放sdcard2
    return "/tmp/media_rw/sdcard2";
}

typedef struct wakeupTm {
    /* BCD格式数据 */
    uint8_t startHour;
    uint8_t startMin;
    uint8_t endHour;
    uint8_t endMin;
    wakeupTm() {
        startHour = 0;
        startMin = 0;
        endHour = 0;
        endMin = 0;
    }
    my::string str() const {
        my::string body;

        body << my::hton;
        body << startHour << startMin << endHour << endMin;
        return body;
    }
    uint32_t startMinutes() {
        uint32_t hour = startHour;
        uint32_t minute = startMin;
        return ((((hour >> 4) & 0xf) * 10 + (hour & 0xf)) * 60 + (((minute >> 4) & 0xf) * 10 + (minute & 0xf)));
    }
    uint32_t EndMinutes() {
        uint32_t hour = endHour;
        uint32_t minute = endMin;
        return ((((hour >> 4) & 0xf) * 10 + (hour & 0xf)) * 60 + (((minute >> 4) & 0xf) * 10 + (minute & 0xf)));
    }
}wakeupTm_t;

typedef struct tmWakeupParam {
    uint8_t         timeWakeupEnFlg;       /* 定时唤醒启用标志 */
    wakeupTm_t      tm[4];
    tmWakeupParam() {
        timeWakeupEnFlg = 0;
    }
    my::string str() const {
        my::string body;

        body << my::hton;
        body << timeWakeupEnFlg;
        for (int i = 0; i < 4; i++) {
            body << tm[i].str();
        }
        return body;
    }
}tmWakeupParam_t;

typedef struct wakeupParam {
    uint8_t         wakeupMode;
    uint8_t         singalWakeUpType;
    uint8_t         tmWakeUpWeek;   /* 定时唤醒日设置 */
    tmWakeupParam_t tmWakeup;
    wakeupParam() {
        wakeupMode = 0;
        singalWakeUpType = 0;
        tmWakeUpWeek = 0;
    }
    my::string str() const {
        my::string body;

        body << my::hton;
        body << wakeupMode << singalWakeUpType << tmWakeUpWeek << tmWakeup.str();
        return body;
    }
}wakeupParam_t;


enum class IDVR_BOARD_TYPE : uint8_t
{
	DEFALUT = 1,
    C1      = 2,
    C2      = 3,
    C3      = 4,
    B1      = 5,
};
#define MAX_CLIENTS 8 // 最大中心数量

enum IDVR_GPIO_IN
{
    IDVR_GPIO_IN_MIN = 0,
	IDVR_GPIO_IN_BACK = 0,
    IDVR_GPIO_IN_NEAR,
    IDVR_GPIO_IN_FAR,
    IDVR_GPIO_IN_LEFT,
    IDVR_GPIO_IN_RIGHT,
    IDVR_GPIO_IN_BRAKE,
    IDVR_GPIO_IN_DOOR,
    IDVR_FRONT_DOOR_OPEN,
    IDVR_MIDDLE_DOOR_OPEN,
    IDVR_BACK_DOOR_OPEN,
    IDVR_GPIO_IN_MAX,
};

enum FUCTION_CAR_TYPE{
    FUCTION_CAR_TYPE_RISK = 0,  /* 两客一危 */
    FUCTION_CAR_TYPE_TRUCK,     /* 重型货车 */
    FUCTION_CAR_TYPE_RURAL_BUS, /* 农村客运   车 */
    FUCTION_CAR_TYPE_FOUR_BUS,  /* 四类客运车 */
};

typedef struct {
    uint32_t sigalLose             : 1;//视频信号丢失报警
    uint32_t sigalOcclusion       : 1;//视频信号遮挡报警
    uint32_t storageFailure        : 1;//存储单元故障报警
    uint32_t otherFailure          : 1;//其他视频设备故障报警
    uint32_t carOverload           : 1;//客车超员报警
    uint32_t drivingAbnormal       : 1;//异常驾驶行为报警
    uint32_t SpecialRecordOverFlow : 1;//特殊报警录像达到存储阈值报警
    uint32_t res                    : 25;
}__attribute__((packed)) videoAlarmBits_t;

typedef union {
    uint32_t                 val;
    videoAlarmBits_t         set;
}__attribute__((packed)) videoAlarmMask_t;

typedef struct {
    uint32_t emergTrig  : 1;
    uint32_t speeding   : 1;
    uint32_t tiredDrv   : 1;
    uint32_t dangerAlarm: 1;
    uint32_t gnssFail   : 1;
    uint32_t gnssNoAnt  : 1;
    uint32_t gnssAntSC  : 1;
    uint32_t pwrShort   : 1; // 终端主电源欠压

    uint32_t pwrDWN     : 1; // 终端主电源掉电
    uint32_t dispFail   : 1;
    uint32_t ttsFail    : 1;
    uint32_t camFail    : 1;
    uint32_t icCardFail : 1;
    uint32_t speedingAlarm  : 1;
    uint32_t tiredDrvAlarm  : 1;
    uint32_t unlawDrvAlarm  : 1;

    uint32_t tirePressureAlarm : 1;
    uint32_t rightBSDAlarm  : 1;
    uint32_t drvTmOvflw : 1;
    uint32_t parkTmOv   : 1;
    uint32_t inOutAreaAlm   : 1;
    uint32_t inOutRoutineAlm  : 1;
    uint32_t isDrvTmAb  : 1;/*路段时间过短或过长*/
    uint32_t abRoutine  : 1;

    uint32_t vssFail    : 1;
    uint32_t oilAbnormal: 1;
    uint32_t carStolen  : 1;
    uint32_t carInvalidStart    : 1;
    uint32_t carInvalidOffset   : 1;
    uint32_t fcwAlarm       : 1;
    uint32_t rollAlarm          : 1;
    uint32_t illegalOpenDrAlarm : 1;
}__attribute__((packed))AlarmMaskBits_t;

typedef union {
    uint32_t                 val;
    AlarmMaskBits_t          set;
}__attribute__((packed)) AlarmMask_t;

static IDVR_BOARD_TYPE idvrGetBoardType()
{
    FILE *fp;
    char buf[200] = {0};
    IDVR_BOARD_TYPE type = IDVR_BOARD_TYPE::DEFALUT;
    if((fp = popen("cat /proc/driver/mdevid", "r")) == NULL) {
        loge("fail to popen /proc/driver/mdevid !");
    } else {
        fread(buf, sizeof(char), sizeof(buf), fp);
        logi("%s + %s", __FUNCTION__, buf);
        if (!strncmp(buf, "2", 1)) {
            type = IDVR_BOARD_TYPE::C1;
        } else if (!strncmp(buf, "3", 1)) {
            type = IDVR_BOARD_TYPE::C2;
        } else if (!strncmp(buf, "4", 1)) {
            type = IDVR_BOARD_TYPE::C3;
        } else if (!strncmp(buf, "5", 1)) {
            type = IDVR_BOARD_TYPE::B1;
        } else {
            type = IDVR_BOARD_TYPE::DEFALUT;
        }
        pclose(fp);
    }
    return type;
}

// 显示区域定义
typedef struct win_site {
    int x;
    int y;
    int width;
    int height;

    bool operator != (const struct win_site & s) const
    {
        return (x != s.x || y != s.y || width != s.width || height != s.height);
    }
} win_site;

typedef struct {
    uint16_t time          : 1;
    uint16_t plateNum      : 1;
    uint16_t channelId     : 1;
    uint16_t lbs           : 1;
    uint16_t speed         : 1;   /* 行车记录速度 */
    uint16_t lbsSpeed      : 1;
    uint16_t tripTm        : 1;   /* 连续驾驶时间 */
    uint16_t rsev          : 9;
}__attribute__((packed))norOsdSet;

typedef union {
    uint16_t                val;
    norOsdSet               set;
}__attribute__((packed)) NOR_OSD_PARAM;

typedef struct {
    uint32_t businessScope  : 1;
    uint32_t businessCompanyName : 1;
    uint32_t driverName     : 1;
    uint32_t roadName       : 1;
    uint32_t alarmType      : 1;
    uint32_t siteInOut       : 1;
    uint32_t lineInfo       : 1;
    uint32_t rsev           : 25;
}__attribute__((packed))extOsdSet;
typedef union {
    uint32_t                val;
    extOsdSet               set;
}__attribute__((packed)) EXT_OSD_PARAM;

typedef struct osd_set {
    NOR_OSD_PARAM norSet;
    EXT_OSD_PARAM extSet;
} osd_set;
typedef struct ChannelCfg {
    bool isIpc;
    short av;   // 工作模式 0-通道未启用, 1-启用音频, 2-启用视频, 3-启用音视频
    short save; // 存储形式 0-不保存, 1-保存音频, 2-保存视频, 3-保存音视频

    char label[64];
    bool labelShow;
    short cam_res_width;
    short cam_res_height;
    uint8_t flipType;   //0不翻转，1水平翻转，2垂直翻转, 3水平垂直都翻转

    struct {
        short audio_input; // 录像音频输入源 1-mic1, 2-mic2, 3-mic3, 4-mic4
        short video_input; // 录像视频输入源 0-主码流，1-子码流
    } record;

    struct {
        short width;
        short height;
        int fps;
        int bps;
        int codec;      //0 - h264, 1 - h265
        int gop;        /* I帧间隔 */
        int mode;       /* ENCODE_MODE */
    } video[2];

    struct {
        bool enable;
        short input; // 0, 1
        short attachment; // 附件使用的码流 0-主码流，1-子码流
        char color[8];
        char func[8];
        uint32_t attachChBitTbl; /* 算法告警视频附件通道号 bit位置1采集对应通道视频 */
    } ai;

    my::string camMap;

    int8_t rotate;      // 旋转角度，0默认，1顺时针90°，2顺时针180°，3顺时针270°

    int8_t ch;
    int8_t winIdx;		//显示到第几个栅格
    osd_set osdSet;     /* osd配置 */
    int8_t ptz;         /* 是否连接云台 0:未连接1:连接 */
}ChannelCfg;
struct CommParam
{
    uint8_t warnSpeedThreshold;  /* 报警判断速度阈值 单位 km/h，取值范围0~60 */
    uint8_t warnVolume;         /* 报警提示音量 0~8，8最大，0静音 0xFF表示不修改参数 */

    uint8_t iPhotoMode;         /* 主动拍照策略 0x00:不开启 0x01:定时 0x02:定距 0x03:插卡触发(dms) 0xFF不修改参数*/
    uint16_t iPhotoGapSec;     /* 主动定时拍照时间间隔 单位秒，取值范围 0~3600                    0表示不抓拍，0xFFFF表示不修改参数 主动拍照策略为0x01 时有效                 */
    uint16_t iPhotoGapMeter;  /* 主动定距拍照距离间隔 单位米，取值范围 0~60000，默认值200， 0 表示不抓拍，0xFFFF表示不修改参数 主动拍照策略为0x02 时有效*/

    uint8_t photoNum;   /* 单次主动拍照张数取值范围 1-10，默认值3 0xFF 表示不修改参数 */
    uint8_t photoInterval; /* 单次主动拍照时间间隔         单位100ms，取值范围1~10，默认值2，0xFF表示不修改参数 */

    uint8_t imgResolution; /* 拍照分辨率 默认值0x01， 0xFF 表示不修改参数， 该参数也适用于报警触发拍照分辨率。*/
    uint8_t videoResolution; /*视频录制分辨率 默认值0x01 0xFF 表示不修改参数 该参数也适用于报警触发视频分辨率*/

    uint32_t    warnEnBits;             /*报警使能*/
    uint32_t    evtEnBits;              /*事件使能*/
    CommParam()
    {
        warnSpeedThreshold = 30;
        warnVolume = 6;
        iPhotoMode = 0;
        iPhotoGapSec = 3600;
        iPhotoGapMeter = 200;
        photoNum = 3;
        photoInterval = 2;
        imgResolution = 1;
        videoResolution = 1;
        warnEnBits = 0x00010FFF;
        evtEnBits = 3;
    }

    my::string str() const
    {
        my::string body;
        body << my::hton;

        body << warnSpeedThreshold << warnVolume << iPhotoMode << iPhotoGapSec
             << iPhotoGapMeter << photoNum << photoInterval << imgResolution
             << videoResolution << warnEnBits << evtEnBits;

        return body;
    }
} __attribute__((packed));

struct SnapParam
{
    uint8_t pics;       /* 拍照张数 */
    uint8_t picsInterval; /* 拍照间隔 */
    SnapParam()
    {
       pics = 3;
       picsInterval = 2;
    }
    my::string str() const
    {
        my::string body;
        body << my::hton;

        body << pics << picsInterval;
        return body;
    }
} __attribute__((packed));

struct AccessoryParam
{
    uint8_t     videoTm;    /* 视频录制时间 */
    SnapParam   snapParam;
    AccessoryParam()
    {
       videoTm = 5;
    }

    my::string str() const
    {
        my::string body;
        body << my::hton;

        body << videoTm << snapParam.str();
        return body;
    }
} __attribute__((packed));

struct FuncItemParam
{
    uint8_t          gradingSpd; /* 分级速度 */
    AccessoryParam   accessoryParam;
    FuncItemParam()
    {
       gradingSpd = 50;
    }

    my::string str() const
    {
        my::string body;
        body << my::hton;

        body << gradingSpd << accessoryParam.str();
        return body;
    }
} __attribute__((packed));

struct AdasParam {
    CommParam comm;
    uint8_t reserve1;
    uint8_t barrierDis;  /* 障碍物报警距离阈值 */
    FuncItemParam barrier;
    uint8_t hourlyLDWTm; /* 频繁变道判断时间 */
    uint8_t hourlyLDWCnt; /* 频繁变道判断次数 */
    FuncItemParam hourlyLDW;
    FuncItemParam Ldw;
    uint8_t fcwTm; /* 前向碰撞时间阈值 */
    FuncItemParam Fcw;
    uint8_t pcwTm; /* 行人碰撞时间阈值 */
    FuncItemParam Pcw;
    uint8_t HwTm; /* 车距监控报警距离阈值 */
    FuncItemParam Hw;
    SnapParam tsrSnap;
    uint8_t turnOverTm;
    uint8_t turnOverEnSpd;
    AccessoryParam turnOverAccessory;
    uint8_t reserve[4];
    AdasParam () {
        barrierDis = 30;
        hourlyLDWTm = 60;
        hourlyLDWCnt = 5;
        fcwTm = 27;
        pcwTm = 30;
        HwTm = 10;
        turnOverTm = 10;
        turnOverEnSpd = 10;
    }
    my::string str() const
    {
        uint8_t nal;
        my::string body;

        body << my::hton;
        body << comm.str() << reserve1 << barrierDis << barrier.str() << hourlyLDWTm
             << hourlyLDWCnt << hourlyLDW.str() << Ldw.str() << fcwTm << Fcw.str()
             << pcwTm << Pcw.str() << HwTm << Hw.str() << tsrSnap.str() << turnOverTm
             << turnOverEnSpd << turnOverAccessory.str() << nal << nal << nal << nal;
        return body;
    }
} __attribute__((packed));


struct AdasParamGD {
    /*--------苏标定义---------*/
    CommParam           comm;
    uint8_t             reserve1;           /* 预留*/
    uint8_t             barrierDis;         /* 障碍物报警距离阈值 */
    FuncItemParam       barrier;            /* 障碍物报警速度阈值*/
    uint8_t             hourlyLDWTm;        /* 频繁变道判断时间 */
    uint8_t             hourlyLDWCnt;       /* 频繁变道判断次数 */
    FuncItemParam       hourlyLDW;          /* 频繁变道*/
    FuncItemParam       Ldw;                /* 车道偏离*/
    uint8_t             fcwTm;              /* 前向碰撞时间阈值 */
    FuncItemParam       Fcw;                /* 前向碰撞*/
    uint8_t             pcwTm;              /* 行人碰撞时间阈值 */
    FuncItemParam       Pcw;                /* 行人碰撞*/
    uint8_t             HwTm;               /* 车距监控报警距离阈值 */
    FuncItemParam       Hw;                 /* 车距过近*/
    SnapParam           tsrSnap;            /* 道路标志识别*/

    /*--------粤标定义---------*/
    FuncItemParam       Sllc;               /* 实线变道*/
    FuncItemParam       PeopleDect;         /* 车厢过道行人检测*/

    uint8_t reserve[4];
    AdasParamGD () {
        barrierDis = 30;
        hourlyLDWTm = 60;
        hourlyLDWCnt = 5;
        fcwTm = 27;
        pcwTm = 30;
        HwTm = 10;
    }

    bool decode(my::string s)
    {
        return true;
    }

    my::string encode() const
    {
        uint8_t nal = 0;
        my::string body;

        body << my::hton;
        body << comm.str() << nal << barrierDis << barrier.str() << hourlyLDWTm
             << hourlyLDWCnt << hourlyLDW.str() << Ldw.str() << fcwTm << Fcw.str()
             << pcwTm << Pcw.str() << HwTm << Hw.str() << tsrSnap.str() << Sllc.str()
             << PeopleDect.str() << nal << nal << nal << nal;
        return body;
    }
} __attribute__((packed));


struct DmsParam {
    CommParam comm;
    uint16_t SmokeWarnGap;
    uint16_t CallWarnGap;
    uint8_t reserve[3];
    FuncItemParam fatigue;
    FuncItemParam phone;
    FuncItemParam smoking;
    FuncItemParam distract;
    FuncItemParam abnormal;
    uint8_t faceIdTrigTyp;/*0x00：不开启 0x01：定时触发 0x02：定距触发 0x03：插卡开始行驶触发 0x04：保留默认值为 0x01 0xFF 表示不修改参数*/
    uint8_t forbiddenStart[3];  // BCD[3] hh-mm-ss（GMT+8 时间）
    uint8_t forbiddenEnd[3];    // BCD[3] hh-mm-ss（GMT+8 时间）
    AccessoryParam forbiddenAccessory;
    uint16_t forbiddenCheckInterval;
    AccessoryParam longTmDrvAccessory;
    uint8_t  longTmDrvCheckInterval;
    uint8_t   reserve1[2];

    DmsParam () {
        SmokeWarnGap = 180;
        CallWarnGap = 120;
        faceIdTrigTyp = 1;
        forbiddenStart[0] = 21;
        forbiddenStart[1] = 0;
        forbiddenStart[2] = 0;
        forbiddenEnd[0] = 6;
        forbiddenEnd[1] = 0;
        forbiddenEnd[2] = 0;
        forbiddenCheckInterval = 300;
        longTmDrvCheckInterval = 100;
    }

    my::string str() const {
        uint8_t nal;
        my::string body;

        body << my::hton;
        body << comm.str() << SmokeWarnGap << CallWarnGap << nal << nal << nal
             << fatigue.str() << phone.str() << smoking.str() << distract.str()
             << abnormal.str() << faceIdTrigTyp << forbiddenStart[0] << forbiddenStart[1]
             << forbiddenStart[2] << forbiddenEnd[0] << forbiddenEnd[1] << forbiddenEnd[2]
             << forbiddenAccessory.str() << forbiddenCheckInterval << longTmDrvAccessory.str()
             << longTmDrvCheckInterval << nal << nal;
        return body;
    }
} __attribute__((packed));

struct DmsParamGD {
    CommParam           comm;
    uint16_t            SmokeWarnGap;       /* 吸烟报警判断时间间隔*/
    uint16_t            CallWarnGap;        /* 接打手持电话报警判断时间间隔*/
    uint8_t             reserve[3];
    FuncItemParam       fatigue;            /* 疲劳驾驶*/
    FuncItemParam       phone;              /* 接打手持电话*/
    FuncItemParam       smoking;            /* 吸烟*/
    FuncItemParam       distract;           /* 不目视前方*/
    FuncItemParam       abnormal;           /* 驾驶行为异常*/
    uint8_t             faceIdTrigTyp;      /* 驾驶员身份识别触发方式：0x00：不开启 0x01：定时触发 0x02：定距触发 0x03：插卡开始行驶触发 0x04：保留默认值为 0x01 0xFF 表示不修改参数*/
    uint8_t             shelterCamera;      /* 摄像头遮挡报警分级速度阈值*/
    FuncItemParam       safetyBelt;         /* 不系安全带*/
    FuncItemParam       eyeOcclusion;       /* 红外墨镜阻断失效*/
    FuncItemParam       handsOff;           /* 双手脱把*/
    FuncItemParam       playPhone;          /* 玩手机*/
    uint8_t             res[2];

    DmsParamGD () {
        SmokeWarnGap = 180;
        CallWarnGap = 120;
        faceIdTrigTyp = 1;
        shelterCamera = 50;
    }

    bool decode(my::string s)
    {
        return true;
    }
    my::string encode() const {
        uint8_t nal = 0;
        my::string body;

        body << my::hton;
        body << comm.str() << SmokeWarnGap << CallWarnGap << nal << nal << nal
             << fatigue.str() << phone.str() << smoking.str() << distract.str()
             << abnormal.str() << faceIdTrigTyp << shelterCamera << safetyBelt.str()
             << eyeOcclusion.str() << handsOff.str() << playPhone.str() <<  nal << nal;
        return body;
    }
} __attribute__((packed));

struct LoadParam {
    uint16_t loadValMax;
    uint16_t torqueMax;
    uint32_t speed1CanId;
    uint32_t speed2CanId;
    uint8_t  installOrientation;
    uint8_t  calculateInterval;
    uint16_t calculateFilterVal;
    uint16_t acceleratedInitialval;
    LoadParam () {
        loadValMax = 5000;
        torqueMax = 1000;
        speed1CanId = 0;
        speed2CanId = 0;
        installOrientation = 0x01;
        calculateInterval = 1;
        calculateFilterVal = 0xff;
        acceleratedInitialval = 0xff;
    }
    my::string str() const {
        uint8_t nal;
        my::string body;

        body << my::hton << loadValMax << torqueMax << speed1CanId << speed2CanId << installOrientation << calculateInterval
             << calculateFilterVal << acceleratedInitialval;
        return body;
    }
} __attribute__((packed));

typedef struct {
    uint8_t enChBits;
    uint8_t storeChBits;

    uint16_t isMin          : 1;
    uint16_t time           : 15; /* must >= 5s*/
}__attribute__((packed)) FIX_TM_SNAP_PARAM_S;
typedef struct {
    uint8_t enChBits;
    uint8_t storeChBits;

    uint16_t isKM          : 1;
    uint16_t distance      : 15; /* must >= 100m*/
}__attribute__((packed)) FIX_DIST_SNAP_PARAM_S;
typedef union {
    my::uint i;
    FIX_TM_SNAP_PARAM_S     time;
    FIX_DIST_SNAP_PARAM_S   dist;
}__attribute__((packed)) FIX_X_SNAP_PARAM;
struct IP_CTL_BITS
{
    my::uchar lock          : 1;
    my::uchar lockModify    : 1;
    my::uchar attLock   : 1;
    my::uchar attModify : 1;
    my::uchar attEnUpload   : 1;
    my::uchar attEnUpModify : 1;
    my::uchar reserved : 2;
}__attribute__((packed));

typedef struct activeAlarmParam {
    uint8_t passengerLimit;       /* 核载人数 */
    uint8_t tiredThred;            /* 疲劳程度阈值 */
    activeAlarmParam() {
        passengerLimit = 80;
        tiredThred = 5;
    }
    my::string str() const {
        my::string body;

        body << my::hton;
        body << passengerLimit << tiredThred;
        return body;
    }
}activeAlarmParam_t;

typedef struct conf_t {
    int ver;
    IDVR_BOARD_TYPE boardType;
    int cameras;
    int ipcs;
    int tcp_timeout_ms;         /* TCP链路主动断开时间单位ms           */
    bool lock_record_switch; //前面板锁是否可以控制录像的开关
    uint32_t osdColor; // 录像水印颜色
    char vehicle_num[32];
    videoAlarmMask_t  videoAlarmMask;
    AlarmMask_t       alarmMask;

    bool              anncrServer;  /* 启用广告牌功能 */

    uint32_t snap_alarm_enBits;/*报警拍摄开关，对应位置上报告警位*/
    uint32_t snap_storage_enBits;/*报警拍摄存储开关，对应位置上报告警位,对应告警位1为存储，0为实时上传*/
    uint32_t snap_key_bits;/*关键标志，对应位置上报告警位,对应告警位1为关键报警*/

    bool display_switch;    //显示配置，false屏幕不显示任何画面
    my::string language;      //UI语言
    int windows;
    struct {
        int8_t ch;
    } display[MAX_CHANNELS];

    struct {
        int8_t gain;
    } soundRec[4];

    char storage[4][16];
    int32_t storageFileDuration;
    int32_t storageFileSizeMax;

    activeAlarmParam_t activeParam;
    struct {
        uint8_t     videoProportion; /* 告警录像文件在主存储器中空间占比 1~99*/
        int32_t     videoTm_s;       /* 告警录像最大时长          单位:s */
        int32_t     alarmFrontTm_s; /* 告警发生前录像时长           单位:s */
    } alarmMedia;

    ChannelCfg ch[MAX_CHANNELS];

    int acc_time_on;
    int acc_time_off;
    int speaker; // 外置喇叭:1 内置喇叭:0 全部打开:2
#if 1//protocol configs
    my::string ignore_spdth;
    my::string associated_video;
    my::string sim; // SIM卡号
    struct {
        my::string vendor; // 制造商
        my::string model; // 型号
        my::string ccc; // 3C代码
        my::uint sn; // 出厂编号
        my::uint date; // 出厂日期, yyyymmdd格式
        my::string id; // 厂商ID
    } product;

	struct
	{
		my::uint date = 0;
		float mileage_x10  = 0.0;
	} setup;

    struct
    {
        my::string labels;
    } gpio[8];

    struct {
        my::ushort province; // 车辆归属省份
        my::ushort city; // 车辆归属县市
        my::ushort plate_color; // 车牌颜色
        my::string vin; // 车架号
        my::string plate_type; // 车牌分类
        my::string plate_num; // 车牌号码
        my::ushort operationType; /* 运营车辆类型        FUCTION_CAR_TYPE */
        my::string businessScope; /* 经营范围 */
        my::string operationCompanyName; /* 运输企业名称 */
    } vehicle;

    struct {
        my::uchar   canChannel;
        my::uint    frameID;
        my::uchar   frameTpye;
        my::uint    frameInterval;
        my::uint    frameIDSecond; /*  chuanbiao ext 第2个can id */
        my::uint    frameIDThird; /*  chuanbiao ext 第3个can id */
    }can1ID;

    struct canSetting {
        my::uchar   canChannel;
        my::uint    frameID;
        my::uchar   frameTpye;
        my::uint    frameInterval;
    };

    canSetting can_setting_0110;
    canSetting can_setting_0111;
    canSetting can_setting_load_ZHF03_speed1;
    canSetting can_setting_load_ZHF03_speed2;

    struct {
        my::ushort can1CollectInterval;
        my::ushort can2CollectInterval;
        my::uint can1UploadInterval;
        my::uint can2UploadInterval;
    }canParam;

    struct {
        my::ushort torque;       /* 扭矩 200~3000Nm */
        my::uchar  orientation; /* 1:朝向驾驶室 2:朝向车尾 */
        my::ushort acceleratedFilter; /* 加速度系数50~800       */
        my::ushort impactFilter;    /* 冲击度系数 50~800      */
        my::ushort acceleratedInitial; /* 加速度初值 -1000~1000 发给载重传感器需加上偏移量1000即 -1000发送给载重传感器0（-1000+1000=0）      */
        my::ushort fullLoadLimit; /* 满载值   1000~60000kg  */
        my::ushort halfLoadLimit; /* 半载值   1000~60000kg  */
        my::ushort emptyLoadLimit; /* 空载值   1000~60000kg  */
        my::uchar fullLoadLimitCoefficient; /* 满载标定系数0~200         */
        my::uchar halfLoadLimitCoefficient; /* 半载标定系数0~200         */
        my::uchar emptyLoadLimitCoefficient; /* 空载标定系数0~200         */
        my::uchar transmissionType;         /* 0:手动档    1:自动档 */
        my::uchar transmissionStatus;     /* 0:传动系信号0为断开 1:传动系信号1为断开                 */
    }loadTransducerParam;

    struct {
        my::uchar carMode;//车辆模式
        my::uchar limitSpeed;//限制速度
        my::uchar limitLift;//限制举升
        my::uchar lockCar;//锁车
    }carCtrl;

    struct {
        bool alarmEnable;
        my::uchar alarmInterval;
    } recoderAlarm;

    struct {
        /*chongqing ext*/
        struct {
            my::uchar datetime_bits;//时间周期控制位 2
            my::uchar week_bits;
            int time_bgn;//since 2000
            int time_end;//since 2000

            int alarm;// 提前告警时间, 单位分钟
            int alarm_spch_gap;//禁行时段提醒时间间隔，单位：分钟
            my::string alarm_spch;//禁行时段到达提醒语音
            my::string warn_spch;//禁行时段到达报警语音
            int alarm_tms;//禁行时段到达提醒播报的次数。0表示不提示，255表示不停提示。
            int warn_tms; //报警提示次数，0：表示不提示，255：表示不停提示。
            int warn_spch_gap;//禁行时段到达报警报时间间隔，单位:分钟，0 表示按提示的次数参数设置提示。
            int stop_spd_thres;//停车判定速度(公里/小时)
            int stop_time_thres;//停车判断：持续运行时间，单位：分钟
        } night_stop_rest;
        /*chongqing ext*/
        struct {
            my::uchar datetime_bits;//时间周期控制位 2
            my::uchar week_bits;
            int time_bgn;//since 2000
            int time_end;//since 2000

            int alarm;// 夜间超速值及预警值定义，最高位为 0 时为设置的超速值及超速提醒的比例值，默认80(百分比) 。为0时候停用夜间限速功能。
            int alarm_spch_minus;//预警时间，单位：分钟
            my::string alarm_spch;//夜间预警语音
            int alarm_tms;//夜间预警提示单次重复播报的次数。0表示不提示，255 表示不停提示。3
            int alarm_time_gap;//预警间隔分钟数 5
        } night_spding;
        /*chongqing ext*/
        struct {
            my::uchar datetime_bits;//时间周期控制位 2
            my::uchar week_bits;
            int time_bgn;//since 2000
            int time_end;//since 2000

            int  drv_tm_max_sec;//夜间最长连续驾驶时间，单位：秒，0：无夜间疲劳驾驶报警。(国标连续驾驶门限参数为0时，关闭整个疲劳驾驶功能)
            int  rest_tm_min_sec;//夜间驾驶最小休息时间，单位秒   1200
            my::string alarm_spch;//请你勿疲劳驾驶
            my::string warn_spch;//请你勿疲劳驾驶
            int  alarm_ahead_minus;//超时驾驶预警提示时间提前值，单位:分钟。 30
            int  alarm_tms;//超时驾驶预警提示单次重复播报的次数。0表示不提示，255表示不停提示 1
            int  alarm_time_gap;//超时驾驶预警提示时间间隔，单位:分钟，0 表示按提示的次数参数设置提示 0
            int  warn_tms;//3
            int  warn_time_gap;//5 min

            /* chuanbiao ext */
            int  day_drv_tm_max_sec;//日间最长连续驾驶时间，单位：秒，0：无日间疲劳驾驶报警。(国标连续驾驶门限参数为0时，关闭整个疲劳驾驶功能)
            int  day_rest_tm_min_sec;//日间驾驶最小休息时间，单位秒                 默认1200
        } tired_drv;
        struct {
            char enable; // 功能开关
            int limit; // 最大驾驶时间, 单位1/10小时=360s
            int remainder;//limit秒数余数
            int alarm; // 提前告警时间, 单位分钟
            int alarmRmndr;
            int rest; // 休息时间, 单位分钟
            int restRmndr;
            int oneDayLimit;//秒
            int maxParkSec;//秒，最大停车时间
        } overtime;

        struct {
            char enable; // 功能开关
            int delta; // 预警和告警速度差值, 单位 1/10 kmph
            int limit; // 最大驾驶速度, 单位km/h
            int alarm; // 提前告警速度, 单位km/h
            int spdKeepTm;//超速持续时间
            char boundaryType; /* 高低限速类型 */

            /*chongqing ext*/
            my::string alarm_spch;/*您即将超速，请保持安全行车速度！*/
            my::string warn_spch;/*请注意，你的车已超速请减速行驶 */
            int warn_spch_tms;
            int warn_spch_gap;
            int alarm_spch_keep;
            int alarm_spch_gap;
            int alarm_spch_tms;
            int upload_gap;  /* 上传间隔 */
        } overspeed;

        /*chongqing ext*/
        struct {
            int warn_times;
            int out_line_spch_gap;
            my::string warn_spch;
        } area_warn_param;
        /*chongqing ext*/
        struct {
            uint32_t evt_bits;
            uint32_t time_gap;
        } loc_rpt;
        struct {
            int spd_thres;
        } bsd_rpt;

        uint32_t forbid_drv_tm;/*hhmmHHMM*/
    } warn;
    struct
    {
        my::string sim;
        my::string id;
        my::string prot_type;
        my::string prot_subtype;
        my::string auth;
        my::string ip[3];
        my::uint port[3];
        my::uchar  ipCtlBits[3];//struct IP_CTL_BITS
        int prot_version;
        std::map<std::string/*algo name*/, std::vector<std::string>/*evt name list*/> disAlgoAttUpload;
        bool is_algo_evt_att_disable(const char * algo, const char * evt_name) {
            auto it = disAlgoAttUpload.find(algo);
            if (it != disAlgoAttUpload.end()) {
                if (it->second[0] == "all") {
                    return true;
                }
                for (auto ie : it->second) {
                    if (ie == std::string(evt_name)) {
                        return true;
                    }
                }
            }
            return false;
        }
    } net[MAX_CLIENTS];

    struct {
        bool enable;
        bool ignore_spd;
        bool associate_ch;
        my::string server;
        my::ushort  port;
    } subiao;/*苏标设备间协议*/

    struct {
        char src; // 时钟源
    } clock;

    struct {
        // 通过IO切换摄像头全屏
        int32_t fullsc[IDVR_GPIO_IN_MAX];
    } io;

    my::string osd_mask; // OSD掩码
    my::string disk_order; // 磁盘存储顺序

    struct {
        my::uchar mode; // 定位模式
        my::uchar baudrate; // 波特率
        my::uchar detail_frequency; // 模块详细定位数据输出频率
        my::uint detail_inteval;    // 采集频率
        my::uchar detail_upmode;        // 详细定位数据上传方式
        my::uint detail_upint;   // 数据上传设置
    } gnss;

    struct {
        my::uint mode;      // 汇报策略
        my::uint plan;      // 方案
        my::uint not_login_inteval; // 未登陆
        my::uint sleep_delay;       // 延迟进入休眠时间
        my::uint acc_off_inteval;   // acc off的时间间隔
        my::uint sleep_inteval;     // 休眠汇报时间间隔
        my::uint urgency_inteval;   // 紧急汇报时间间隔
        my::uint default_inteval;  // 缺省汇报时间间隔
        my::uint default_distance;
        my::uint hearbeat_inteval;
        my::uint tcp_timeout;
        my::uint tcp_retry_max;
    } report;

    struct {
        my::uint channel_bits;
        my::uint time_gap;
        my::uint min_after_acc_off;
    } auto_snap;//chongqing prot

    FIX_X_SNAP_PARAM mFixTmSnapParam;
    FIX_X_SNAP_PARAM mFixDistSnapParam;
    my::uchar   icCardInSnap; /* 插卡自动抓拍 */
    uint16_t    loadCapacity;//载重单位0.1t

    DmsParam dmsParam;
    AdasParam adasParam;
    wakeupParam_t wakeup;
    uint16_t       collisionParam; /* collisionParam_t */
    std::map<uint32_t, my::string> fakeCfg;
#endif

    /* chuangbiao ext 碰撞侧翻角度阈值 单位度         */
    my::uchar       rollOverAngle;
    my::uint        rollOverCnt;

    my::ushort      carHeight; /* 车身高度，单位毫米 */

    struct {
        my::uint imageQuality;  /* 图像质量1-10 */
        my::uint imageLuminance; /* 亮度 0~255    */
        my::uint imageContrast; /* 对比度 0~127 */
        my::uint imageSaturation; /* 饱和度 0~127 */
        my::uint imageChroma; /* 色度 0~255    */
    } image_param;

    // dump配置文件到字符串中
    const char* conf2str(my::string& str)
    {
        str = "";
        str.appendf("  media.cameras=[%d]\n", cameras);
        str.appendf("  media.storage=[%s, %s, %s, %s]\n", storage[0], storage[1], storage[2], storage[3]);
        str.appendf("  media.acc.time.on=[%d]\n", acc_time_on);
        str.appendf("  media.acc.time.off=[%d]\n", acc_time_off);
        str.appendf("  media.manufacture.speaker=[%d]\n", speaker);
        str.appendf("  media.storage.fileDuration=[%d]\n", storageFileDuration);
        str.appendf("  media.storage.storageFileSizeMax=[%d]\n", storageFileSizeMax);

        for (int i = 0; i < (cameras + ipcs); i++) {
            const char * chType = ch[i].isIpc == false ? "ch" : "ipc";

            if (ch[i].av == 0) {
                str.appendf("  media.%s%d.av=[0]\n", chType, i + 1);

            } else {
                str.appendf("  media.%s%d.label=[%s]\n", chType, i + 1, ch[i].label);
                str.appendf("  media.%s%d.labelShow=[%d]\n", chType, i + 1, ch[i].labelShow);
                str.appendf("  media.%s%d.win=[%d]\n", chType, i + 1, ch[i].winIdx);
                str.appendf("  media.%s%d.ch=[%d]\n", chType, i + 1, ch[i].ch);
                str.appendf("  media.%s%d.av=[%d]\n", chType, i + 1, ch[i].av);
                str.appendf("  media.%s%d.save=[%d]\n", chType, i + 1, ch[i].save);
                str.appendf("  media.%s%d.rotate=[%d]\n", chType, i + 1, ch[i].rotate);
                str.appendf("  media.%s%d.cam.res=[%dx%d]\n", chType, i + 1, ch[i].cam_res_width, ch[i].cam_res_height);////
                str.appendf("  media.%s%d.cam.flipType=[%d]\n", chType, i + 1, ch[i].flipType);
                str.appendf("  media.%s%d.video0.resl=[%dx%d]\n", chType, i + 1, ch[i].video[0].width, ch[i].video[0].height);
                str.appendf("  media.%s%d.video0.fps=[%d]\n", chType, i + 1, ch[i].video[0].fps);
                str.appendf("  media.%s%d.video0.bps=[%d]\n", chType, i + 1, ch[i].video[0].bps);
                str.appendf("  media.%s%d.video0.codec=[%d]\n", chType, i + 1, ch[i].video[0].codec);
                str.appendf("  media.%s%d.video1.resl=[%dx%d]\n", chType, i + 1, ch[i].video[1].width, ch[i].video[1].height);
                str.appendf("  media.%s%d.video1.fps=[%d]\n", chType, i + 1, ch[i].video[1].fps);
                str.appendf("  media.%s%d.video1.bps=[%d]\n", chType, i + 1, ch[i].video[1].bps);
                str.appendf("  media.%s%d.video1.codec=[%d]\n", chType, i + 1, ch[i].video[1].codec);

                str.appendf("  media.%s%d.record.audio_input=[%d]\n", chType, i + 1, ch[i].record.audio_input);
                str.appendf("  media.%s%d.record.video_input=[%d]\n", chType, i + 1, ch[i].record.video_input);

                if (ch[i].ai.input >= 0) {
                    str.appendf("  media.%s%d.ai.enable=[%d]\n", chType, i + 1, ch[i].ai.enable);
                    str.appendf("  media.%s%d.ai.input=[video%d]\n", chType, i + 1, ch[i].ai.input);
                    str.appendf("  media.%s%d.ai.func=[%s]\n", chType, i + 1, ch[i].ai.func);
                    str.appendf("  media.%s%d.ai.color=[%s]\n", chType, i + 1, ch[i].ai.color);
                    str.appendf("  media.%s%d.ai.attachment=[video%d]\n", chType, i + 1, ch[i].ai.attachment);
                }
            }
        }

        for (int i = 0; i < (cameras + ipcs); i++) {
            str.appendf("  display.win%d=ch%d\n", i + 1, display[i].ch + 1);
        }

        for (int i = 0; i < 4; i++) {
            str.appendf("  sound.mic%d.gain=%d\n", i + 1, soundRec[i].gain);
        }

        return str.c_str();
    }
    int ldconf(my::conf::ini & ini, const char * path)
    {
        LOG_RETURN_IF(my::conf::ini::load(ini, path) < 0, -1, loge, "[idvr.media.sdk] Failed to load '%s'.", path);

        return ldconf(ini);
    }

    int ldconf(const char * path)
    {
        my::conf::ini mIniCfg;
        LOG_RETURN_IF(my::conf::ini::load(mIniCfg, path) < 0, -1, loge, "[idvr.media.sdk] Failed to load '%s'.", path);

        return ldconf(mIniCfg);
    }
    const my::string* kvGet(my::conf::ini & ini, const char * section, const char * key, const char * defaultVal)
    {
        const my::string* v = ini.get(section, key);
        if (!v) {
            ini.set(section, key, defaultVal);
            loge("Missing : set default value [%s] %s = ", section, key, defaultVal);
        }
        return v;
    }

    void numstr2bcd3(uint8_t * bcd, const int time) const {
        char time_s[10] = {0};
        snprintf(time_s, 10, "%02d%02d%02d", (time / 3600), (time % 3600) / 60, (time % 3600) % 60);
        my::string timeStr = time_s;
        my::constr str(timeStr);
        for (int i = 0; i < 3; i++) {
            uint8_t v0 = (*str - '0');
            str += 1;
            uint8_t v1 = (*str - '0');
            str += 1;
            *bcd++ = v0 << 4 | v1;
        }
    }
    void InitDmsParam(){
        dmsParam.comm.warnEnBits = 0x1ff;
        dmsParam.comm.evtEnBits = 0x3;

        /* 夜间禁行参数 */
        struct tm tvb;
        struct tm tve;
        time_t tmp = warn.night_stop_rest.time_bgn + 946684800;/*2000-1-1 8:00:00*/
        localtime_r(&tmp, &tvb);
        int forbiddenStart = (int)(tvb.tm_hour * 3600 + tvb.tm_min * 60 + tvb.tm_sec);
        logd("night_stop_rest.time_bgn %d:%d:%d!\n", tvb.tm_hour, tvb.tm_min, tvb.tm_sec);
        numstr2bcd3(dmsParam.forbiddenStart,forbiddenStart);
        tmp = warn.night_stop_rest.time_end + 946684800;/*2000-1-1 8:00:00*/

        localtime_r(&tmp, &tve);
        int forbiddenEnd = (int)(tve.tm_hour * 3600 + tve.tm_min * 60 + tve.tm_sec);
        logd("night_stop_rest.time_end %d:%d:%d!\n", tve.tm_hour, tve.tm_min, tve.tm_sec);
        numstr2bcd3(dmsParam.forbiddenEnd, forbiddenEnd);
        dmsParam.forbiddenCheckInterval = warn.night_stop_rest.alarm_spch_gap * 60;

        int chId = -1;
        for (int i = 0; i < (ipcs + cameras); i++) {
            if (!strcmp(ch[i].ai.func, "dms")) {
                chId = i;
                break;
            }
        }
        /* 主动抓拍 */
        if (chId >= 0 && (mFixTmSnapParam.time.enChBits & 0x1 << chId) > 0) {
            dmsParam.comm.iPhotoMode = 0x1;
        }
        if (chId >= 0 && (mFixDistSnapParam.dist.enChBits & 0x1 << chId) > 0) {
            dmsParam.comm.iPhotoMode = 0x2;
        }
        if (chId >= 0 && icCardInSnap) {
            dmsParam.comm.iPhotoMode = 0x3;
        }

        uint16_t val;
        val = mFixDistSnapParam.time.time;
        if (val > 0) {
            if (mFixDistSnapParam.time.isMin) {
                dmsParam.comm.iPhotoGapSec = val * 60;
            } else {
                dmsParam.comm.iPhotoGapSec = val;
            }
        }
        val = mFixDistSnapParam.dist.distance;
        if (val > 0) {
            if (mFixDistSnapParam.dist.isKM) {
                dmsParam.comm.iPhotoGapMeter = val * 1000;
            } else {
                dmsParam.comm.iPhotoGapMeter = val;
            }
        }
    }

    void InitAdasParam() {
        adasParam.comm.warnEnBits = 0x10fff;
        adasParam.comm.evtEnBits = 0x3;

        int chId = -1;
        for (int i = 0; i < (ipcs + cameras); i++) {
            if (!strcmp(ch[i].ai.func, "adas")) {
                chId = i;
                break;
            }
        }
        /* 主动抓拍 */
        if (chId >= 0 && (mFixTmSnapParam.time.enChBits & 0x1 << chId) > 0) {
            adasParam.comm.iPhotoMode = 0x1;
        }

        if (chId >= 0 && (mFixDistSnapParam.dist.enChBits & 0x1 << chId) > 0) {
            adasParam.comm.iPhotoMode = 0x2;
        }
        uint16_t val;
        val = mFixDistSnapParam.time.time;
        if (val > 0) {
            if (mFixDistSnapParam.time.isMin) {
                adasParam.comm.iPhotoGapSec = val * 60;
            } else {
                adasParam.comm.iPhotoGapSec = val;
            }
        }
        val = mFixDistSnapParam.dist.distance;
        if (val > 0) {
            if (mFixDistSnapParam.dist.isKM) {
                adasParam.comm.iPhotoGapMeter = val * 1000;
            } else {
                adasParam.comm.iPhotoGapMeter = val;
            }
        }
    }

    // 加载配置文件
    int ldconf(my::conf::ini & ini)
    {
        // 动态获取板子型号
        boardType = idvrGetBoardType();
        logi("idvr board type is %d", boardType);

        {
            /* 图像参数 */
            image_param.imageQuality = 5;
            image_param.imageLuminance = 125;
            image_param.imageContrast = 65;
            image_param.imageSaturation = 65;
            image_param.imageChroma = 125;
        }

        std::vector<my::constr> params;
        // 解析基本信息
        {
            const my::string* v;
            //const my::string* v = ini.get("this", "ver");
            // CFG_ASSERT(!v || !v->length(), -1, loge, "[idvr.media.sdk] Field 'this.ver' not found or empty.");
            //ver = atoi(v->c_str());

            v = ini.get("base", "vehicle.plate_num");

            if (!v) {
                logw("[idvr.media.sdk] Field 'base.vehicle.plate_num' not found.");

            } else {
                memcpy(vehicle_num, v->c_str(), v->length());
            }

            v = ini.get("base", "anncr.server.enable");
            if (!v) {
                anncrServer = false;
            } else {
                anncrServer = (strcmp(v->c_str(), "true") == 0) ? true : false;
            }

            v = ini.get("subiao", "enable");
            if (!v) {
                subiao.enable = false;
            } else {
                subiao.enable = (strcmp(v->c_str(), "true") == 0) ? true : false;
            }

            v = ini.get("subiao", "ignore_spd");
            if (!v) {
                subiao.ignore_spd = false;
            } else {
                subiao.ignore_spd = (strcmp(v->c_str(), "true") == 0) ? true : false;
            }

            v = ini.get("subiao", "associate_ch");
            if (!v) {
                subiao.associate_ch = false;
            } else {
                subiao.associate_ch = (strcmp(v->c_str(), "true") == 0) ? true : false;
            }

            v = ini.get("subiao", "server");
            if (!v) {
                subiao.server = "192.168.100.88";
            } else {
                subiao.server = v->c_str();
            }

            v = ini.get("subiao", "port");
            if (!v) {
                subiao.port = 8888;
            } else {
                subiao.port = atoi(v->c_str());
            }

            char propValue[PROP_VALUE_MAX] = {0};
            memset(propValue, 0, sizeof(propValue));
            /* 报警相关 */
            if (__system_property_get(PROP_PERSIST_JTT808_ALARM_MASK, propValue) > 0) {
                alarmMask.val = static_cast<uint32_t>(atoi(propValue));
            } else {
            v = ini.get("base", "normal.alarm.mask");
            if (!v) {
                /* 默认启用所有报警 */
                alarmMask.val = 0;
            } else {
                alarmMask.val = atoi(v->c_str());
            }
            }

            /* 视频相关告警使能 */
            memset(propValue, 0, sizeof(propValue));
            if (__system_property_get(PROP_PERSIST_JTT808_0200_NOALARMATT, propValue) > 0) {
                /* 旧设备兼容配置 */
                if (!strcmp(propValue, "true")) {
                    /* 兼容旧设备，禁用所有报警 */
                    videoAlarmMask.val=  127;
                } else {
                    videoAlarmMask.val = 0;
                }
            } else {
	            v = ini.get("base", "video.alarm.mask");
	            if (!v) {
	                /* 默认启用所有报警 */
	                        videoAlarmMask.val = 0;
	            } else {
	                        videoAlarmMask.val = atoi(v->c_str());
	            }
            }
            snap_alarm_enBits = static_cast<my::uint>(atoi(ini.get("base", "snap_alarm_enBits", 0)));
            snap_storage_enBits = static_cast<my::uint>(atoi(ini.get("base", "snap_storage_enBits", 0)));
            snap_key_bits = static_cast<my::uint>(atoi(ini.get("base", "snap_key_bits", 0)));

            /* 碰撞 */
            collisionParam = static_cast<my::ushort>(atoi(ini.get("base", "collisionParam", 0x1212)));

            /* 侧翻角度 */
            rollOverAngle = static_cast<my::uchar>(atoi(ini.get("base", "rollOver.angle", 30)));
            /* 侧翻判定次数门限，侧翻角度达到后维持一点次数后判定侧翻 */
            rollOverCnt = static_cast<my::uint>(atoi(ini.get("base", "rollOver.cnt", 20)));
            logd("rollOverAngle:%d, rollOverCnt:%d!\n", rollOverAngle, rollOverCnt);

            /* 车身高度     默认2000毫米 */
            carHeight = static_cast<my::ushort>(atoi(ini.get("base", "car.height", 2000)));
            logd("carHeight:%d!\n", carHeight);
        }

        /* 休眠唤醒 */
        {
            wakeup.wakeupMode = static_cast<uint8_t>(atoi(ini.get("wakeup", "mode", "0")));
            wakeup.singalWakeUpType = static_cast<uint8_t>(atoi(ini.get("wakeup", "singalType", "0")));
            wakeup.tmWakeUpWeek = static_cast<uint8_t>(atoi(ini.get("wakeup", "weekDay", "0")));
            wakeup.tmWakeup.timeWakeupEnFlg = static_cast<uint8_t>(atoi(ini.get("wakeup", "tmSectionEn", "0")));
            uint32_t tmp;
            tmp = static_cast<uint32_t>(atoi(ini.get("wakeup", "tmSection1", "0")));
            wakeup.tmWakeup.tm[0].startHour = static_cast<uint8_t>(tmp >> 24);
            wakeup.tmWakeup.tm[0].startMin = static_cast<uint8_t>((tmp >> 16) & 0xff);
            wakeup.tmWakeup.tm[0].endHour = static_cast<uint8_t>((tmp >> 8) & 0xff);
            wakeup.tmWakeup.tm[0].endMin = static_cast<uint8_t>(tmp & 0xff);
            tmp = static_cast<uint32_t>(atoi(ini.get("wakeup", "tmSection2", "0")));
            wakeup.tmWakeup.tm[1].startHour = static_cast<uint8_t>(tmp >> 24);
            wakeup.tmWakeup.tm[1].startMin = static_cast<uint8_t>((tmp >> 16) & 0xff);
            wakeup.tmWakeup.tm[1].endHour = static_cast<uint8_t>((tmp >> 8) & 0xff);
            wakeup.tmWakeup.tm[1].endMin = static_cast<uint8_t>(tmp & 0xff);
            tmp = static_cast<uint32_t>(atoi(ini.get("wakeup", "tmSection3", "0")));
            wakeup.tmWakeup.tm[2].startHour = static_cast<uint8_t>(tmp >> 24);
            wakeup.tmWakeup.tm[2].startMin = static_cast<uint8_t>((tmp >> 16) & 0xff);
            wakeup.tmWakeup.tm[2].endHour = static_cast<uint8_t>((tmp >> 8) & 0xff);
            wakeup.tmWakeup.tm[2].endMin = static_cast<uint8_t>(tmp & 0xff);
            tmp = static_cast<uint32_t>(atoi(ini.get("wakeup", "tmSection4", "0")));
            wakeup.tmWakeup.tm[3].startHour = static_cast<uint8_t>(tmp >> 24);
            wakeup.tmWakeup.tm[3].startMin = static_cast<uint8_t>((tmp >> 16) & 0xff);
            wakeup.tmWakeup.tm[3].endHour = static_cast<uint8_t>((tmp >> 8) & 0xff);
            wakeup.tmWakeup.tm[3].endMin = static_cast<uint8_t>(tmp & 0xff);
        }

        /* 载重传感器配置 */
        {
            loadTransducerParam.torque = static_cast<my::ushort>(atoi(ini.get("load.sensor", "loadTransducerParam.torque", 2200)));
            loadTransducerParam.orientation = static_cast<my::uchar>(atoi(ini.get("load.sensor", "loadTransducerParam.orientation", 1)));
            loadTransducerParam.acceleratedFilter = static_cast<my::ushort>(atoi(ini.get("load.sensor", "loadTransducerParam.acceleratedFilter", 700)));
            loadTransducerParam.impactFilter = static_cast<my::ushort>(atoi(ini.get("load.sensor", "loadTransducerParam.impactFilter", 550)));
            /* 设置值为实际参数加上偏移量1000 */
            loadTransducerParam.acceleratedInitial = static_cast<my::ushort>(atoi(ini.get("load.sensor", "loadTransducerParam.acceleratedInitial", 900)) + 1000);
            loadTransducerParam.fullLoadLimit = static_cast<my::ushort>(atoi(ini.get("load.sensor", "loadTransducerParam.fullLoadLimit", 49000)));
            loadTransducerParam.halfLoadLimit = static_cast<my::ushort>(atoi(ini.get("load.sensor", "loadTransducerParam.halfLoadLimit", 1000)));
            loadTransducerParam.emptyLoadLimit = static_cast<my::ushort>(atoi(ini.get("load.sensor", "loadTransducerParam.emptyLoadLimit", 1000)));
            loadTransducerParam.fullLoadLimitCoefficient = static_cast<my::uchar>(atoi(ini.get("load.sensor", "loadTransducerParam.fullLoadLimitCoefficient", 100)));
            loadTransducerParam.halfLoadLimitCoefficient = static_cast<my::uchar>(atoi(ini.get("load.sensor", "loadTransducerParam.halfLoadLimitCoefficient", 0)));
            loadTransducerParam.emptyLoadLimitCoefficient = static_cast<my::uchar>(atoi(ini.get("load.sensor", "loadTransducerParam.emptyLoadLimitCoefficient", 0)));
            loadTransducerParam.transmissionType = static_cast<my::uchar>(atoi(ini.get("load.sensor", "loadTransducerParam.transmissionType", 0)));
            loadTransducerParam.transmissionStatus = static_cast<my::uchar>(atoi(ini.get("load.sensor", "loadTransducerParam.transmissionStatus", 1)));
        }
        logd("loadTransducerParam.torque:%d\n", loadTransducerParam.torque);
        logd("loadTransducerParam.orientation:%d\n", loadTransducerParam.orientation);
        logd("loadTransducerParam.acceleratedFilter:%d\n", loadTransducerParam.acceleratedFilter);
        logd("loadTransducerParam.impactFilter:%d\n", loadTransducerParam.impactFilter);
        logd("loadTransducerParam.acceleratedInitial:%d\n", loadTransducerParam.acceleratedInitial);
        logd("loadTransducerParam.fullLoadLimit:%d\n", loadTransducerParam.fullLoadLimit);
        logd("loadTransducerParam.halfLoadLimit:%d\n", loadTransducerParam.halfLoadLimit);
        logd("loadTransducerParam.emptyLoadLimit:%d\n", loadTransducerParam.emptyLoadLimit);
        logd("loadTransducerParam.fullLoadLimitCoefficient:%d\n", loadTransducerParam.fullLoadLimitCoefficient);
        logd("loadTransducerParam.halfLoadLimitCoefficient:%d\n", loadTransducerParam.halfLoadLimitCoefficient);
        logd("loadTransducerParam.emptyLoadLimitCoefficient:%d\n", loadTransducerParam.emptyLoadLimitCoefficient);
        logd("loadTransducerParam.transmissionType:%d\n", loadTransducerParam.transmissionType);
        logd("loadTransducerParam.transmissionStatus:%d\n", loadTransducerParam.transmissionStatus);

        // 解析media基本信息
        {
            int32_t BoardMaxCams = MAX_AHD_NUM;
            // DFH1935-C1 DFH1935-C3只有4路摄像头
            if (boardType == IDVR_BOARD_TYPE::C1 || boardType == IDVR_BOARD_TYPE::C3) {
                BoardMaxCams = C1_MAX_AHD_NUM;
            }

            // 摄像头数量
            const my::string* v = ini.get("media", "cameras");
            if (v) {
                cameras = atoi(v->c_str());
                if (cameras < 0 || cameras > BoardMaxCams) {
                    loge("[idvr.media.sdk] Bad 'media.cameras'(%d).", cameras);
                    cameras = BoardMaxCams;
                }
            } else {
                cameras = BoardMaxCams;
                loge("[idvr.media.sdk] Field 'media.cameras' not found.");
            }

            /* TCP音视频传输通道 */
            v = ini.get("media", "tcpTimeoutTm");
            if (v) {
                tcp_timeout_ms = atoi(v->c_str());
                if (tcp_timeout_ms < 0 || tcp_timeout_ms > 90000) {
                    /* 最少1.5min */
                    loge("[idvr.media.sdk] tcpTimeoutTm(%d).", tcp_timeout_ms);
                    tcp_timeout_ms = 90000;
                }
            } else {
                tcp_timeout_ms = 90000;  /* 1.5 min */
                loge("[idvr.media.sdk] tcpTimeoutTm(%d).", tcp_timeout_ms);
            }

            // IPC数量
            ipcs = 0;
            v = ini.get("media", "ipcs");

            if (v) {
                ipcs = atoi(v->c_str());
            }

            if ((ipcs < 0) || ((cameras + ipcs) > MAX_CHANNELS)) {
                loge("[idvr.media.sdk] Bad 'media.ipcs'(%d)! range [0, %d].", MAX_CHANNELS - cameras);
                ipcs = 0;
            }


            // 存储顺序
            v = ini.get("media", "storage");

            if (!v) {
                logw("[idvr.media.sdk] Field 'media.storage' not found.");

            } else {
                storage[0][0] = 0;
                params = v->split(',');

                for (size_t i = 0; i < params.size(); i++) {
                    strcpy(storage[i], params[i].str().c_str());
                }
            }

            //存储文件参数
            v = ini.get("media", "storage.fileDuration");

            if (v) {
                storageFileDuration = atoi(v->c_str());

            } else {
                storageFileDuration = 0;
            }

            v = ini.get("media", "storage.fileSizeMax");

            if (v) {
                storageFileSizeMax = atoi(v->c_str());

            } else {
                storageFileSizeMax = 0;
            }

            v = ini.get("media", "active.passengerLimit");
            if (v) {
                activeParam.passengerLimit = atoi(v->c_str());

            } else {
                activeParam.passengerLimit = 50;
            }

            v = ini.get("media", "active.tiredThred");
            if (v) {
                activeParam.tiredThred = atoi(v->c_str());

            } else {
                activeParam.tiredThred = 5;
            }

// ACC熄火3分钟，则关闭退出
#define ACC_POWER_OFF_TIME (3 * 60)
// ACC打开10秒钟，则开启业务程序
#define ACC_POWER_ON_TIME (10)
            // 从acc打火到启动录像程序的时间
            v = ini.get("media", "acc.time.on");

            if (!v) {
                logw("[idvr.media.sdk] Field 'media.acc.time.on' not found.");
                acc_time_on = ACC_POWER_ON_TIME;

            } else {
                acc_time_on = atoi(v->c_str());
            }

            // 从acc熄火到关闭录像程序的时间
            v = ini.get("media", "acc.time.off");

            if (!v) {
                logw("[idvr.media.sdk] Field 'media.acc.time.on' not found.");
                acc_time_off = ACC_POWER_OFF_TIME;

            } else {
                acc_time_off = atoi(v->c_str());
            }

            // 解析是内置喇叭还是外置喇叭发声
            v = ini.get("media", "manufacture.speaker");

            if (v) {
                speaker = (my::uint)atoi(v->c_str());
            } else {
                speaker = 0;
            }

            my::string str_switch = ini.get("media", "lock.record.switch", "false");
            lock_record_switch = (strcmp(str_switch.c_str(), "true") == 0) ? true : false;

            str_switch = ini.get("media", "display.switch", "true");
            display_switch = (strcmp(str_switch.c_str(), "true") == 0) ? true : false;

            language = ini.get("media", "language", "zh");

            // get osd color
            errno = 0;
            char *end;
            my::string strOsdColor = ini.get("media", "osd.color", "0x0000ff");
            osdColor = strtol(strOsdColor.c_str(), &end, 0);
            if (*end) {
                loge("%s not an integer\n", osdColor);
                osdColor = 0x0000ff;
            }
            if (errno) {
                loge("strtol: %s: %s\n", osdColor, strerror(errno));
                osdColor = 0x0000ff;
            }


        }

        // 解析media的通道信息
        char name[32] = "";

        for (int i = 0; i < (cameras + ipcs); ++i) {
            if (i < cameras) {
                sprintf(name, "media.ch%d", i + 1);
                ch[i].isIpc = false;

            } else {
                sprintf(name, "media.ipc%d", i - cameras + 1);
                ch[i].isIpc = true;
            }

            ch[i].av = 0; // 默认不使用该通道
			ch[i].winIdx = i;
			ch[i].ch = i;
            ch[i].labelShow = false;
            ch[i].osdSet.norSet.val = 0xffff;
            ch[i].osdSet.extSet.val = 0;
            ch[i].ptz = 0;

            if (ini.get(name)) {
            	const my::string* v = ini.get(name, "win");
                if (v && v->length()) {
                    ch[i].winIdx = atoi(v->c_str()) - 1;
                }

            	v = ini.get(name, "ch");
                if (v && v->length()) {
                    ch[i].ch = atoi(v->c_str()) - 1;
                    if (ch[i].ch >= (cameras + ipcs) ||
                    	ch[i].ch < 0) {
						ch[i].ch = i;
                    }
                }

                if (ch[i].isIpc == false) {
                    v = ini.get(name, "osd.norSet");
                    if (!v || !v->length()) {
                        ch[i].osdSet.norSet.val = 0xffff;
                    } else {
                        ch[i].osdSet.norSet.val = static_cast<my::ushort>(atoi(v->c_str()));
                    }

                    v = ini.get(name, "osd.extSet");
                    if (!v || !v->length()) {
                        ch[i].osdSet.extSet.val = 0;
                    } else {
                        ch[i].osdSet.extSet.val = static_cast<my::ushort>(atoi(v->c_str()));
                    }
                }

				v = ini.get(name, "av");

                if (!v || !v->length()) {
                    continue;
                }

                ch[i].av = atoi(v->c_str()) & 0x03;

                if (!ch[i].av) {
                    continue;
                }

                v = ini.get(name, "label");

                if (v) {
                    memcpy(ch[i].label, v->c_str(), v->length());
                }

            	v = ini.get(name, "labelShow");
                if (v && v->length()) {
                    ch[i].labelShow = (bool) atoi(v->c_str());
                }

                v = ini.get(name, "ptz");
                if (v && v->length()) {
                    ch[i].ptz = (bool) atoi(v->c_str());
                }

                v = ini.get(name, "save");
                ch[i].save = (v && v->length()) ? atoi(v->c_str()) & 0x03 : 0;

                v = ini.get(name, "rotate");
                ch[i].rotate = (v && v->length()) ? atoi(v->c_str()) : 0;

                // 解析cam.res参数
                v = ini.get(name, "cam.res");

                if (!v || !v->length()) {
                    loge("[idvr.media.sdk] Field '%s.cam.res' not found or empty.", name);
                    break;
                }

                params = v->split('x');

                if (params.size() != 2) {
                    loge("[idvr.media.sdk] Bad %s.cam.res(%s).", name, v->c_str());
                    break;
                }

                ch[i].cam_res_width = atoi(params[0].str().c_str());

                if (ch[i].cam_res_width <= 0) {
                    loge("[idvr.media.sdk] Bad %s.cam.res(width=%d).", name, ch[i].video[0].width);
                    break;
                }

                ch[i].cam_res_height = atoi(params[1].str().c_str());

                if (ch[i].cam_res_height <= 0) {
                    loge("[idvr.media.sdk] Bad %s.cam.res(height=%d).", name, ch[i].video[0].height);
                    break;
                }

                v = ini.get(name, "cam.flipType");

                if (v && v->length()) {
                    ch[i].flipType = atoi(v->c_str());
                }

                // 解析video0参数
                ch[i].video[0].width = 1280;
                ch[i].video[0].height = 720;
                ch[i].video[0].fps = 15;
                ch[i].video[0].bps = 128 * 8 * 1024;
                ch[i].video[0].codec = 0;
                ch[i].video[0].gop = 14;
                ch[i].video[0].mode = 1; /* E_ENCODE_MODE_VBR */
                v = ini.get(name, "video0.resl");

                do {
                    if (!v || !v->length()) {
                        loge("[idvr.media.sdk] Field '%s.video0.resl' not found or empty.", ch);
                        break;
                    }

                    params = v->split('x');

                    if (params.size() != 2) {
                        loge("[idvr.media.sdk] Bad %s.video0.resl(%s).", ch, v->c_str());
                        break;
                    }

                    ch[i].video[0].width = atoi(params[0].str().c_str());

                    if (ch[i].video[0].width <= 0) {
                        loge("[idvr.media.sdk] Bad %s.video0.resl(width=%d).", ch, ch[i].video[0].width);
                        break;
                    }

                    ch[i].video[0].height = atoi(params[1].str().c_str());

                    if (ch[i].video[0].height <= 0) {
                        loge("[idvr.media.sdk] Bad %s.video0.resl(height=%d).", ch, ch[i].video[0].height);
                        break;
                    }

                    v = ini.get(name, "video0.fps");

                    if (v) {
                        ch[i].video[0].fps = atoi(v->c_str());
                    }

                    v = ini.get(name, "video0.bps");

                    if (v) {
                        ch[i].video[0].bps = atoi(v->c_str());
                    }

                    v = ini.get(name, "video0.codec");

                    if (v) {
                        ch[i].video[0].codec = atoi(v->c_str());
                    }

                    v = ini.get(name, "video0.codec.mode");
                    if (v) {
                        ch[i].video[0].mode = atoi(v->c_str());
                    }

                    v = ini.get(name, "video0.gop");
                    if (v) {
                        ch[i].video[0].gop = atoi(v->c_str());
                    } else {
                        ch[i].video[0].gop = ch[i].video[0].fps - 1;
                    }
                } while (0);

                // 解析video0参数
                ch[i].video[1].width = 640;
                ch[i].video[1].height = 480;
                ch[i].video[1].fps = 15;
                ch[i].video[1].bps = 128 * 8 * 1024;
                ch[i].video[1].codec = 0;
                ch[i].video[1].mode = 1; /* E_ENCODE_MODE_VBR */
                ch[i].video[1].gop = 14;
                v = ini.get(name, "video1.resl");

                do {
                    if (!v || !v->length()) {
                        loge("[idvr.media.sdk] Field '%s.video1.resl' not found or empty.", ch);
                        break;
                    }

                    params = v->split('x');

                    if (params.size() != 2) {
                        loge("[idvr.media.sdk] Bad %s.video1.resl(%s).", ch, v->c_str());
                        break;
                    }

                    ch[i].video[1].width = atoi(params[0].str().c_str());

                    if (ch[i].video[1].width <= 0) {
                        loge("[idvr.media.sdk] Bad %s.video1.resl(width=%d).", ch, ch[i].video[1].width);
                        break;
                    }

                    ch[i].video[1].height = atoi(params[1].str().c_str());

                    if (ch[i].video[1].height <= 0) {
                        loge("[idvr.media.sdk] Bad %s.video1.resl(height=%d).", ch, ch[i].video[1].height);
                        break;
                    }

                    v = ini.get(name, "video1.fps");

                    if (v) {
                        ch[i].video[1].fps = atoi(v->c_str());
                    }

                    v = ini.get(name, "video1.bps");

                    if (v) {
                        ch[i].video[1].bps = atoi(v->c_str());
                    }

                    v = ini.get(name, "video1.codec");

                    if (v) {
                        ch[i].video[1].codec = atoi(v->c_str());
                    }

                    v = ini.get(name, "video1.codec.mode");
                    if (v) {
                        ch[i].video[1].mode = atoi(v->c_str());
                    }

                    v = ini.get(name, "video1.gop");
                    if (v) {
                        ch[i].video[1].gop = atoi(v->c_str());
                    } else {
                        ch[i].video[1].gop = ch[i].video[1].fps - 1;
                    }
                } while (0);

                {
                    // 解析录像输入源
                    // 解析audio
                    ch[i].record.audio_input = 1;
                    const my::string* audio_input = ini.get(name, "record.audio.input");

                    if (audio_input) {
                        // 获取摄像头输入
                        if (*audio_input == "mic-1") {
                            ch[i].record.audio_input = 1;

                        } else if (*audio_input == "mic-2") {
                            ch[i].record.audio_input = 2;
                        } else if (*audio_input == "mic-3") {
                            ch[i].record.audio_input = 3;
                        } else if (*audio_input == "mic-4") {
                            ch[i].record.audio_input = 4;
                        }
                    }

                    // 解析video
                    ch[i].record.video_input = 0;
                    const my::string* video_input = ini.get(name, "record.video.input");

                    if (video_input) {
                        ch[i].record.video_input = *video_input == "video0" ? 0 : 1;
                    }

                }

                // 解析算法参数
                ch[i].ai.enable = false;

                do {
                    const my::string* enable = ini.get(name, "ai.enable");
                    const my::string* input = ini.get(name, "ai.input");
                    const my::string* attachment = ini.get(name, "ai.attachment");
                    const my::string* func = ini.get(name, "ai.func");
                    const my::string* color = ini.get(name, "ai.color");
                    logd("ch%d  ai.enable:%s!\n", i, enable->c_str());
                    ch[i].ai.attachChBitTbl = static_cast<uint32_t>(atoi(ini.get(name, "ai.attachChBitTbl", 0)));
                    logd("ch%d attachChBitTbl:%x, ai.enable:%s!\n", i, ch[i].ai.attachChBitTbl, enable->c_str());

                    if (!enable || !input || !func || !color) {
                        logd("load ch%d ai config error!\n", i);
                        break;
                    }

                    // 算法是否使能
                    ch[i].ai.enable = (strcmp(enable->c_str(), "true") == 0) ? true : false;

                    // 获取算法函数
                    strcpy(ch[i].ai.func, func->c_str());

                    // 获取算法需要的颜色
                    if (*color != "bgr888" && *color != "y") {
                        loge("[idvr.media.sdk] Bad %s.ai.color('%s'), only 'bgr888' or 'y' supported.", ch, color->c_str());
                        break;
                    }

                    strcpy(ch[i].ai.color, color->c_str());

                    // 获取算法图片源
                    if (*input != "video0" && *input != "video1") {
                        loge("[idvr.media.sdk] Bad %s.ai.input('%s'), only 'video0' or 'video1' supported.", ch, input->c_str());
                        break;
                    }

                    ch[i].ai.input = *input == "video0" ? 0 : 1;

                    //协议附件源
                    if (attachment) {
                        if (*attachment != "video0" && *attachment != "video1") {
                            loge("[idvr.media.sdk] Bad %s.ai.attachment('%s'), only 'video0' or 'video1' supported.", ch, attachment->c_str());
                            break;
                        }

                        ch[i].ai.attachment = ((*attachment == "video0") ? 0 : 1);

                    } else {
                        ch[i].ai.attachment = 1;
                    }
                } while (0);


                // 解析摄像头通道映射：
                ch[i].camMap = ini.get(name, "cam.phy", "");
            }
        }
        do {
            const my::string *v = ini.get("display", "windows");
            if (v) {
                windows = atoi(v->c_str());
                if ((windows < 0) || (windows > MAX_CHANNELS)) {
                    windows = cameras;
                }
            } else {
                windows = cameras;
            }
        } while (0);
        // GUI显示布局
        for (int i = 0; i < MAX_CHANNELS; ++i) {
            char win[32] = {0};
            snprintf(win, sizeof(win), "win%d", i + 1);
            const my::string *v = ini.get("display", win);
            if (!v) {
                loge("[idvr.media.sdk] Field 'display.%s' not found.", win);
                display[i].ch = -1;
            } else {
                char *p = strstr((char *) v->c_str(), "ch");
                if (p == nullptr) {
                    loge("[idvr.media.sdk] 'display.%s' conf error ! %s", v->c_str());
                    display[i].ch = -1;
                } else {
                    p += strlen("ch");
                    display[i].ch = atoi(p) - 1;
		        }
            }
		}

        // MIC配置
        for (int i = 0; i < 4; ++i) {
            char micGain[32] = {0};
            snprintf(micGain, sizeof(micGain), "mic%d.gain", i + 1);
            const my::string *v = ini.get("sound", micGain);
            if (!v) {
                loge("[idvr.media.sdk] Field 'sound.%s' not found.", micGain);
                soundRec[i].gain = 5;
            } else {
                soundRec[i].gain = atoi((char *) v->c_str());
            }
		}

        //协议相关参数
        sim = ini.get("base", "sim", "13000000000");
        ignore_spdth = ini.get("mprot", "ignore_spdth", "false");
        associated_video = ini.get("mprot", "associated_video", "false");
        product.vendor = ini.get("base", "manufacture.vendor", "44030072504");
        product.model = ini.get("base", "model", "idvr1.0");
        product.ccc = ini.get("base", "ccc", "C001123");
        if (product.ccc == "C000000") {
            product.ccc = "C001123";
            ini.set("base", "ccc", product.ccc.c_str());
        }
        product.sn = (my::uint)atoi(ini.get("base", "manufacturer.sn", "0"));
        //ro.build.date.utc
        char pv[PROP_VALUE_MAX] = {0};
        __system_property_get(PROP_RO_BUILD_DATE_UTC, pv);
        time_t utcTm = atoi(pv);
        struct tm dt;
        localtime_r(&utcTm, &dt);
        snprintf(pv, sizeof(pv), "%04d%02d%02d", dt.tm_year + 1900, dt.tm_mon + 1, dt.tm_mday);
        //logd("product.date = %s\n", pv);
        product.date = (my::uint)atoi(pv);

        product.id = ini.get("base", "manufacture.id", "");
    	// 获取setup section的配置
    	setup.date = (my::uint)atoi(ini.get("base", "init.date", "0"));
    	setup.mileage_x10 = (float)atof(ini.get("base", "init.mileage", "0")) * 10;
	    
        vehicle.province = (my::ushort)atoi(ini.get("base", "vehicle.province", "0"));
        vehicle.city = (my::ushort)atoi(ini.get("base", "vehicle.city", "0"));
        vehicle.plate_color = (my::ushort)atoi(ini.get("base", "vehicle.plate_color", "0"));
        vehicle.vin = ini.get("base", "vehicle.vin", "");
        vehicle.plate_type = ini.get("base", "vehicle.plate_type", "小客车");
        vehicle.plate_num = ini.get("base", "vehicle.plate_num", "苏A00001");
        vehicle.operationType = (my::ushort)atoi(ini.get("base", "vehicle.operationType", "0"));
        vehicle.businessScope = ini.get("base", "vehicle.businessScope", "农村客运");
        vehicle.operationCompanyName = ini.get("base", "vehicle.operationCompanyName", "佑驾");

        logd("vehicle.plate_type:%s!\n", vehicle.plate_type.c_str());

        canParam.can1CollectInterval = (my::ushort)atoi(ini.get("can", "canParam.can1CollectInterval", "0"));
        canParam.can2CollectInterval = (my::ushort)atoi(ini.get("can", "canParam.can2CollectInterval", "0"));
        canParam.can1UploadInterval =  (my::uint)atoi(ini.get("can", "canParam.can1UploadInterval", "0"));
        canParam.can2UploadInterval =  (my::uint)atoi(ini.get("can", "canParam.can2UploadInterval", "0"));
        #if 0
        can1ID.canChannel = atoi(ini.get("base", "can1ID.canChannel", "0"));
        can1ID.frameID = atoi(ini.get("base", "can1ID.frameID", "0"));
        can1ID.frameTpye = atoi(ini.get("base", "can1ID.frameTpye", "0"));
        can1ID.frameInterval = atoi(ini.get("base", "can1ID.frameInterval", "0"));
        /* chuanbiao ext */
        can1ID.frameIDSecond = atoi(ini.get("base", "can1ID.frameIDSecond", "0"));
        can1ID.frameIDThird = atoi(ini.get("base", "can1ID.frameIDThird", "0"));
        can2ID.canChannel = atoi(ini.get("base", "can2ID.canChannel", "1"));
        can2ID.frameID = atoi(ini.get("base", "can2ID.frameID", "0"));
        can2ID.frameTpye = atoi(ini.get("base", "can2ID.frameTpye", "0"));
        can2ID.frameInterval = atoi(ini.get("base", "can2ID.frameInterval", "0"));
        /* chuanbiao ext */
        #endif

        can_setting_0110.canChannel = atoi(ini.get("can", "can_setting_0110.canChannel", "0"));
        can_setting_0110.frameID = atoi(ini.get("can", "can_setting_0110.frameID", "0"));
        can_setting_0110.frameTpye = atoi(ini.get("can", "can_setting_0110.frameTpye", "0"));
        can_setting_0110.frameInterval = atoi(ini.get("can", "can_setting_0110.frameInterval", "0"));

        can_setting_0111.canChannel = atoi(ini.get("can", "can_setting_0111.canChannel", "1"));
        can_setting_0111.frameID = atoi(ini.get("can", "can_setting_0111.frameID", "0"));
        can_setting_0111.frameTpye = atoi(ini.get("can", "can_setting_0111.frameTpye", "0"));
        can_setting_0111.frameInterval = atoi(ini.get("can", "can_setting_0111.frameInterval", "0"));

        recoderAlarm.alarmEnable = (bool)atoi(ini.get("base", "recoder.alarmEnable", "0"));
        recoderAlarm.alarmInterval = atoi(ini.get("base", "recoder.alarmInterval", "10"));

        carCtrl.carMode = (my::uchar)atoi(ini.get("carctrl", "car_mode", "0"));
        carCtrl.limitSpeed = (my::uchar)atoi(ini.get("carctrl", "limit_speed", "0"));
        carCtrl.lockCar = (my::uchar)atoi(ini.get("carctrl", "lock_car", "0"));
        carCtrl.limitLift = (my::uchar)atoi(ini.get("carctrl", "limit_lift", "0"));

        warn.night_stop_rest.datetime_bits = (my::uchar)atoi(ini.get("warn", "night_stop_rest.datetime_bits", "4"));
        warn.night_stop_rest.week_bits = (my::uchar)atoi(ini.get("warn", "night_stop_rest.week_bits", "0"));
        warn.night_stop_rest.time_bgn = (int)atoi(ini.get("warn", "night_stop_rest.time_bgn", "64800"));//since 1970-01-02 02:00:00
        warn.night_stop_rest.time_end = (int)atoi(ini.get("warn", "night_stop_rest.time_end", "75600"));//since 1970-01-02 05:00:00
        warn.night_stop_rest.alarm = (int)atoi(ini.get("warn", "night_stop_rest.alarm", "30"));
        warn.night_stop_rest.alarm_spch_gap = (int)atoi(ini.get("warn", "night_stop_rest.alarm_spch_gap", "5"));
        warn.night_stop_rest.alarm_spch     = ini.get("warn", "night_stop_rest.alarm_spch", "请按规定时间停车休息");
        warn.night_stop_rest.warn_spch      = ini.get("warn", "night_stop_rest.warn_spch", "请按规定时间停车休息");
        warn.night_stop_rest.alarm_tms      = (int)atoi(ini.get("warn", "night_stop_rest.alarm_tms", "1"));
        warn.night_stop_rest.warn_tms      = (int)atoi(ini.get("warn", "night_stop_rest.warn_tms", "3"));
        warn.night_stop_rest.warn_spch_gap = (int)atoi(ini.get("warn", "night_stop_rest.warn_spch_gap", "5"));
        warn.night_stop_rest.stop_spd_thres = (int)atoi(ini.get("warn", "night_stop_rest.stop_spd_thres", "20"));//kmph
        warn.night_stop_rest.stop_time_thres = (int)atoi(ini.get("warn", "night_stop_rest.stop_time_thres", "5"));//min

        warn.night_spding.datetime_bits = (my::uchar)atoi(ini.get("warn", "night_spding.datetime_bits", "4"));
        warn.night_spding.week_bits = (my::uchar)atoi(ini.get("warn", "night_spding.week_bits", "0"));
        warn.night_spding.time_bgn = (int)atoi(ini.get("warn", "night_spding.time_bgn", "50400"));//since 1970-1-1 22:00:00;
        warn.night_spding.time_end = (int)atoi(ini.get("warn", "night_spding.time_end", "75600"));//since 1970-01-02 05:00:00
        warn.night_spding.alarm = (int)atoi(ini.get("warn", "night_spding.alarm", "0"));
        warn.night_spding.alarm_spch_minus = (int)atoi(ini.get("warn", "night_spding.alarm_spch_minus", "10"));
        warn.night_spding.alarm_spch = ini.get("warn", "night_spding.alarm_spch", "即将进入夜间限速时段，请控制好车速！");
        warn.night_spding.alarm_tms = (int)atoi(ini.get("warn", "night_spding.alarm_tms", "3"));
        warn.night_spding.alarm_time_gap = (int)atoi(ini.get("warn", "night_spding.alarm_time_gap", "5"));//minus

        warn.tired_drv.datetime_bits = (my::uchar)atoi(ini.get("warn", "tired_drv.datetime_bits", "4"));
        warn.tired_drv.week_bits = (my::uchar)atoi(ini.get("warn", "tired_drv.week_bits", "0"));
        warn.tired_drv.time_bgn = (int)atoi(ini.get("warn", "tired_drv.time_bgn", "50400"));//since 1970-1-1 22:00:00;
        warn.tired_drv.time_end = (int)atoi(ini.get("warn", "tired_drv.time_end", "75600"));//since 1970-01-02 05:00:00
        warn.tired_drv.drv_tm_max_sec = (int)atoi(ini.get("warn", "tired_drv.drv_tm_max_sec", "0"));
        warn.tired_drv.rest_tm_min_sec = (int)atoi(ini.get("warn", "tired_drv.rest_tm_min_sec", "1200"));
        warn.tired_drv.alarm_spch = ini.get("warn", "tired_drv.alarm_spch", "请你勿疲劳驾驶");
        warn.tired_drv.warn_spch = ini.get("warn", "tired_drv.warn_spch", "请你勿疲劳驾驶");
        warn.tired_drv.alarm_ahead_minus = (int)atoi(ini.get("warn", "tired_drv.alarm_ahead_minus", "30"));
        warn.tired_drv.alarm_tms = (int)atoi(ini.get("warn", "tired_drv.alarm_tms", "1"));
        warn.tired_drv.alarm_time_gap = (int)atoi(ini.get("warn", "tired_drv.alarm_time_gap", "10"));
        warn.tired_drv.warn_tms = (int)atoi(ini.get("warn", "tired_drv.warn_tms", "3"));
        warn.tired_drv.warn_time_gap = (int)atoi(ini.get("warn", "tired_drv.warn_time_gap", "5"));//min
        warn.tired_drv.day_drv_tm_max_sec = (int)atoi(ini.get("warn", "tired_drv.day_drv_tm_max_sec", "0"));
        warn.tired_drv.day_rest_tm_min_sec = (int)atoi(ini.get("warn", "tired_drv.day_rest_tm_min_sec", "1200"));

        /* 特殊报警录像参数 */
        alarmMedia.videoProportion = (int)atoi(ini.get("base", "alarmMedia.videoProportion", "20"));
        alarmMedia.videoTm_s = (int)atoi(ini.get("base", "alarmMedia.videoTm_s", "300"));
        alarmMedia.alarmFrontTm_s = (int)atoi(ini.get("base", "alarmMedia.alarmFrontTm_s", "60"));

        // 获取warn section的配置
        const my::string* v = NULL;
        /*  v = ini.get("warn", "overtime.enable"); if (!v) return false; warn.overtime.enable = (char)atoi(v->c_str());
            v = ini.get("warn", "overtime.alarm"); if (!v) return false; warn.overtime.alarm = atoi(v->c_str());

            v = ini.get("warn", "overtime.limit"); if (!v) return false; warn.overtime.limit = atoi(v->c_str());
            v = ini.get("warn", "overtime.rest"); if (!v) return false; warn.overtime.rest = atoi(v->c_str());
        */
        warn.overtime.enable = (char)atoi(ini.get("warn", "overtime.enable", "1"));
        warn.overtime.limit = (int)atoi(ini.get("warn", "overtime.limit", "40"));
        warn.overtime.remainder = (int)atoi(ini.get("warn", "overtime.remainder", "0"));
        warn.overtime.alarm = (int)atoi(ini.get("warn", "overtime.alarm", "15"));
        warn.overtime.alarmRmndr = (int)atoi(ini.get("warn", "overtime.alarmRmndr", "0"));
        warn.overtime.rest = (int)atoi(ini.get("warn", "overtime.rest", "20"));
        warn.overtime.restRmndr = (int)atoi(ini.get("warn", "overtime.restRmndr", "0"));
        warn.overtime.oneDayLimit =  (int)atoi(ini.get("warn", "overtime.oneDayLimit", "57600")); /*16 hours*/
        warn.overtime.maxParkSec  =  (int)atoi(ini.get("warn", "overtime.maxParkSec", "0")); /*16 hours*/

        warn.overspeed.enable = (char)atoi(ini.get("warn", "overspeed.enable", "1"));
        warn.overspeed.delta = (int)atoi(ini.get("warn", "overspeed.delta", "50"));
        warn.overspeed.limit = (int)atoi(ini.get("warn", "overspeed.limit", "120"));
        v = ini.get("warn", "overspeed.max_drv_tm_thres");/*兼容老配置开关，后续统一用overtime.limit*/
        if (v) {
            int tmp = atoi(v->c_str());

            if (!tmp) {
                warn.overtime.limit = tmp;
                ini.set("warn", "overtime.limit", v->c_str());
            }
            ini.del("warn", "overspeed.max_drv_tm_thres");
        }
        warn.overspeed.alarm = warn.overspeed.limit - warn.overspeed.delta / 10;
        ini.setf("warn", "overspeed.alarm", "%d", warn.overspeed.alarm);

        warn.overspeed.spdKeepTm = (int)atoi(ini.get("warn", "overspeed.spdKeepTm", "10"));
        warn.overspeed.alarm_spch = ini.get("warn", "overspeed.alarm_spch", "您即将超速，请保持安全行车速度");
        warn.overspeed.warn_spch = ini.get("warn", "overspeed.warn_spch", "请注意，你的车已超速请减速行驶");
        warn.overspeed.warn_spch_tms = atoi(ini.get("warn", "overspeed.warn_spch_tms", "3"));
        warn.overspeed.warn_spch_gap = atoi(ini.get("warn", "overspeed.warn_spch_gap", "5"));
        warn.overspeed.alarm_spch_keep = atoi(ini.get("warn", "overspeed.alarm_spch_keep", "5"));
        warn.overspeed.alarm_spch_gap = atoi(ini.get("warn", "overspeed.alarm_spch_gap", "5"));
        warn.overspeed.alarm_spch_tms = atoi(ini.get("warn", "overspeed.alarm_spch_tms", "3"));
        warn.overspeed.upload_gap = atoi(ini.get("warn", "overspeed.upload_gap", "60"));
        warn.overspeed.boundaryType = (char)atoi(ini.get("warn", "overspeed.boundaryType", "4"));



        warn.loc_rpt.evt_bits = atoi(ini.get("warn", "loc_rpt.evt_bits", "1"));
        warn.loc_rpt.time_gap = atoi(ini.get("warn", "loc_rpt.time_gap", "2"));

        warn.area_warn_param.warn_times = atoi(ini.get("warn", "area_warn_param.warn_times", "3"));
        warn.area_warn_param.out_line_spch_gap = atoi(ini.get("warn", "area_warn_param.out_line_spch_gap", "5"));
        warn.area_warn_param.warn_spch = atoi(ini.get("warn", "area_warn_param.warn_spch", "请你按规定线路行驶"));
        warn.bsd_rpt.spd_thres = atoi(ini.get("warn", "bsd_rpt.spd_thres", "5"));
        warn.forbid_drv_tm  = atoi(ini.get("warn", "forbid_drv_tm", "0"));
        clock.src = (char)atoi(ini.get("clock", "src", "3"));


        /* io 相关配置 */
        const char *fullsc_name[IDVR_GPIO_IN_MAX] = {
                "gpio.back.fullsc", "gpio.near.fullsc", "gpio.far.fullsc", "gpio.left.fullsc",
                "gpio.right.fullsc", "gpio.brake.fullsc", "gpio.door.fullsc",
                "door.front.fullsc", "door.middle.fullsc", "door.back.fullsc"};
        for (int32_t i = IDVR_GPIO_IN_MIN; i < IDVR_GPIO_IN_MAX; i++) {
            io.fullsc[i] = 0;
            my::string fullsc = ini.get("io", fullsc_name[i], "ch0");
            sscanf(fullsc.c_str(), "ch%d", &(io.fullsc[i]));
            if (io.fullsc[i] > cameras) {
                loge("Bad io.fullsc[i] ch = %d", i, io.fullsc[i]);
                io.fullsc[i] = 0;
            }
        }

        for (int32_t i = 0; i < 8; i++) {
            char labels[32] = {0};
            snprintf(labels, sizeof(labels), "gpio.D%d.labels", i);
            gpio[i].labels = ini.get("io", labels, "");
        }

        gnss.mode = (my::uchar)atoi(ini.get("gnss", "mode", "3"));
        gnss.detail_frequency = (my::uchar)atoi(ini.get("gnss", "detail_frequency", "1"));
        gnss.detail_inteval = (my::uchar)atoi(ini.get("gnss", "detail_inteval", "1"));
        gnss.detail_upmode = (my::uchar)atoi(ini.get("gnss", "detail_upmode", "0"));
        gnss.detail_upint = (my::uchar)atoi(ini.get("gnss", "detail_upint", "30"));

        report.mode = (my::uint)atoi(ini.get("base", "report.mode", "0"));
        report.plan = (my::uint)atoi(ini.get("base", "report.plan", "0"));
        report.not_login_inteval = (my::uint)atoi(ini.get("base", "report.not_login_inteval", "30"));
        report.sleep_delay = (my::uint)atoi(ini.get("base", "report.sleep_delay", "300"));
        report.acc_off_inteval = (my::uint)atoi(ini.get("base", "report.acc_off_inteval", "30"));
        report.sleep_inteval = (my::uint)atoi(ini.get("base", "report.sleep_inteval", "300"));
        report.urgency_inteval = (my::uint)atoi(ini.get("base", "report.urgency_inteval", "1"));
        report.default_inteval = (my::uint)atoi(ini.get("base", "report.default_inteval", "30"));
        report.default_distance = (my::uint)atoi(ini.get("base", "report.default_distance", "1000"));
        report.hearbeat_inteval = (my::uint)atoi(ini.get("base", "report.heartbeat_inteval", "30"));
        report.tcp_timeout = (my::uint)atoi(ini.get("base", "report.tcp_timeout", "20"));
        report.tcp_retry_max = (my::uint)atoi(ini.get("base", "report.tcp_retry_max", "3"));

        auto_snap.channel_bits = (my::uint)atoi(ini.get("auto_snap", "channel_bits", "0"));
        auto_snap.time_gap     = (my::uint)atoi(ini.get("auto_snap", "time_gap", "0"));
        auto_snap.min_after_acc_off = (my::uint)atoi(ini.get("auto_snap", "min_after_acc_off", "0"));

        mFixTmSnapParam.i = (my::uint)atoi(ini.get("auto_snap", "fix_tm_snap_param", "0"));
        mFixDistSnapParam.i = (my::uint)atoi(ini.get("auto_snap", "fix_dist_snap_param", "0"));
        icCardInSnap = (my::uchar)atoi(ini.get("auto_snap", "ic_card_in_snap_param", "0"));

        logd("mFixTmSnapParam enChBits:%x, storeChBits:%x,isMin:%d, time:%d!\n",
                mFixTmSnapParam.time.enChBits,
                mFixTmSnapParam.time.storeChBits,
                mFixTmSnapParam.time.isMin,
                mFixTmSnapParam.time.time);

        loadCapacity = (my::ushort)atoi(ini.get("base", "loadCapacity", "0"));
        dmsParam.faceIdTrigTyp = (my::uchar)atoi(ini.get("dmsParam", "faceIdTrigTyp", "1"));

        char devid[PROP_VALUE_MAX] = {0};
        __system_property_get(PROP_PERSIST_MINIEYE_DEVICEID, devid);
        const my::string * subtypeOld = ini.get("mprot", "prot_subtype");
        // 检查客户定制
        bool bHenanTelecomCustom = false;
        for (int i = 1; i <= MAX_CLIENTS; i++) {
            char tag[32];
            snprintf(tag, 32, "conn%d", i);
            // 支持多中心+多协议
            if (!ini.get(tag)) {
                continue;
            }

            const my::string* protocol = ini.get(tag, "protocol");
            if (!protocol) {
                loge("Field 'protocol' is not specified for client%d.", i);
                net[i - 1].prot_type = "jtt808-1078";
            } else {
                net[i - 1].prot_type = *protocol;
            }
            const my::string defaultSubtype = "";
            const my::string * subtype = ini.get(tag, "prot_subtype");
            if (!subtype) {
                if (subtypeOld) {
                    subtype = subtypeOld;
                } else {
                    subtype = &defaultSubtype;
                }
            }
            net[i - 1].prot_subtype = *subtype;

            const my::string * connSim = ini.get(tag, "sim");
            if (!connSim) {
                if (sim.length()) {
                    connSim = &sim;
                }
            }
            net[i - 1].sim = *connSim;
            const my::string* id = ini.get(tag, "id");
            if (id && id->length() > 3) {
                net[i - 1].id = *id;
            }
            else {

                int idLen = strlen(devid);
                if (idLen >= 7) {
                    net[i - 1].id = &devid[idLen - 7];
                }
            }
            const my::string* auth = ini.get(tag, "auth");
            if (!auth) {
                loge("Field 'auth' is not specified for client%d.", i);
                net[i - 1].auth = "";
            } else {
                net[i - 1].auth = *auth;
            }
            if ((*protocol == "jtt808-1078") && (*subtype == "henanTelecom")) {
                bHenanTelecomCustom = true;
            }
        }

        // 根据配置加载协议服务
        for (int i = 1; i <= MAX_CLIENTS; i++)
        {
            char tag[32];
            snprintf(tag, 32, "conn%d", i);
            // 支持多中心+多协议
            if (!ini.get(tag)) {
                continue;
            }
            const my::string * protocol = &net[i - 1].prot_type;
            const my::string * subtype = &net[i - 1].prot_subtype;
            for (int j = 0; j < 3; j++) {
                const my::string* ip, *port;
                char key[128];

                snprintf(key, sizeof(key), "ip%d", j + 1);
                ip = ini.get(tag, key);
                if (!ip || !ip->length()) {
                    loge("[ComController::start] Field 'ip' is not specified for client%d.", j);
                    break;
                }

                snprintf(key, sizeof(key), "port.tcp%d", j + 1);
                port = ini.get(tag, key);
                if (!port || !port->length()) {
                    loge("[ComController::start Field 'tcp.port' is not specified for client%d.", j);
                    break;
                }

                net[i - 1].ip[j] = *ip;
                net[i - 1].port[j] = atoi(port->c_str());

                IP_CTL_BITS * pctl = (IP_CTL_BITS*)&net[i - 1].ipCtlBits[j];
                snprintf(key, sizeof(key), "ip%d.lock", j + 1);

                int defaultVal = bHenanTelecomCustom && ((*subtype == "huoyun") || (*subtype == "henanTelecom"));
                const my::string * ipLock = kvGet(ini, tag, key, defaultVal ? "1" : "0");
                if (ipLock) {
                    pctl->lock = !!atoi(ipLock->c_str());
                } else {
                    pctl->lock = !!defaultVal;
                }
                snprintf(key, sizeof(key), "ip%d.attLock", j + 1);
                defaultVal = bHenanTelecomCustom;
                const my::string * attLock = kvGet(ini, tag, key, defaultVal ? "1" : "0");
                if (attLock) {
                    pctl->attLock = !!atoi(attLock->c_str());
                } else {
                    pctl->attLock = !!defaultVal;
                }
                snprintf(key, sizeof(key), "ip%d.sendAtt", j + 1);
                defaultVal = bHenanTelecomCustom ? (*subtype == "henanTelecom")/*定制, 默认只有henanTelecom传附件*/ : 1;
                const my::string * attEnUpload = kvGet(ini, tag, key, defaultVal ? "1" : "0");
                if (attEnUpload) {
                    pctl->attEnUpload = !!atoi(attEnUpload->c_str());
                } else {
                    pctl->attEnUpload = !!defaultVal;
                }

                logd("%d protocol->c_str() %s, %s, [%d] : %s:%d, %s", i, protocol->c_str(), tag, j, net[i - 1].ip[j].c_str(), net[i - 1].port[j], subtype->c_str());
            }

            net[i - 1].prot_version = 0;
            const my::string * protVersion = ini.get(tag, "prot_version");
            if (protVersion) {
                net[i - 1].prot_version = atoi(protVersion->c_str());
            } else if (net[i - 1].prot_type == "jtt808-1078") {
                if ((net[i - 1].prot_subtype == "huoyun") ||
                    (net[i - 1].prot_subtype == "henanTelecom") ||
                    (net[i - 1].prot_subtype == "sichuan") ||
                    (net[i - 1].prot_subtype == "guangdong") ||
                    (net[i - 1].prot_subtype == "chengduBus")) {
                    net[i - 1].prot_version = 2019;
                } else {//兼容老配置
                    char version[PROP_VALUE_MAX] = {0};
                    __system_property_get(PROP_PERSIST__JTT_PROT_VERSION, version);
                    int ver = atoi(version);
                    if (ver > 0) {
                        net[i - 1].prot_version = 2019;
                    }
                }
                ini.setf(tag, "prot_version", "%d", net[i - 1].prot_version);
            }
        }

        my::string str;
        logi("[idvr.media.sdk] media.configs = \n");
        conf2str(str);
        std::vector<my::constr> sub_strs;
        sub_strs = str.split('\n');

        for (int i = 0; i < sub_strs.size(); i++) {
            logi("%s", sub_strs[i].str().c_str());
        }
        const std::map<my::string, my::string> * fakeCfgs = ini.get("fake_cfg");
        if (fakeCfgs && fakeCfgs->size()) {
            for (auto fake : *fakeCfgs) {
                char * end = (char *)fake.first.c_str();
                long id = strtol(fake.first.c_str(), &end, 16);
                if (end != fake.first.c_str()) {
                    fakeCfg[id] = my::base64d(fake.second);
                }
            }
        }

        InitDmsParam();
        InitAdasParam();
        return 0;
    }
    conf_t()
    {
        // 配置参数默认值
        product.date = 20200101;
        product.sn = 10000;

        vehicle.province = 0;
        vehicle.city = 0;
        vehicle.plate_color = 0;

        warn.overspeed.enable = 1;
        warn.overspeed.limit = 120;
        warn.overspeed.alarm = 115;

        clock.src = 3;
    }
} conf_t;
static inline bool inDayTmSeg(int bgnDaySec, int endDaySec, uint32_t * curTime = NULL, int32_t * diffBgn = NULL)
{
    struct tm tv;
    struct tm tvb;
    struct tm tve;
    time_t time_seconds = time(NULL);

    if (curTime) {
        time_seconds = *curTime;
    }

    localtime_r(&time_seconds, &tv);

    int cur = (int)(tv.tm_hour * 3600 + tv.tm_min * 60 + tv.tm_sec);

    if (cur < 0) {
        loge("cur < 0!!!");
        return false;
    }

    time_t tmp = bgnDaySec + 946684800;/*2000-1-1 8:00:00*/
    localtime_r(&tmp, &tvb);
    bgnDaySec = (int)(tvb.tm_hour * 3600 + tvb.tm_min * 60 + tvb.tm_sec);

    tmp = endDaySec + 946684800;/*2000-1-1 8:00:00*/
    localtime_r(&tmp, &tve);
    endDaySec = (int)(tve.tm_hour * 3600 + tve.tm_min * 60 + tve.tm_sec);

    bool ret = ((bgnDaySec <= cur) && (cur < endDaySec));

    if (endDaySec < bgnDaySec) {
        ret = (cur < endDaySec || (bgnDaySec <= cur && cur < 86400));
    }

    if (diffBgn) {
        *diffBgn = cur - bgnDaySec;

        if ((endDaySec < bgnDaySec) && (cur < endDaySec)) {
            *diffBgn = cur + 86400 - bgnDaySec;
        }
    }

#if 0

    if (ret && !((cur - bgnDaySec) % 60)) {
        logd("%s > %02d:%02d:%02d - [%02d:%02d:%02d] - %02d:%02d:%02d", __FUNCTION__,
             tvb.tm_hour, tvb.tm_min, tvb.tm_sec,
             tv.tm_hour, tv.tm_min, tv.tm_sec,
             tve.tm_hour, tve.tm_min, tve.tm_sec);
    }

#endif
    return ret;
}

#endif
