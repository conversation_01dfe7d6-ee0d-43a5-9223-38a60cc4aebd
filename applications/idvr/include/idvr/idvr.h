#ifndef _IDVR_H_
#define _IDVR_H_

#define MAX_IPC_NUM  0
#define C1_MAX_AHD_NUM  4
#define MAX_AHD_NUM  6
#define MAX_CHANNELS (MAX_AHD_NUM + MAX_IPC_NUM)
#define MAX_STREAMS  2

struct MSEG_INFO {
    int64_t seq;
    int32_t channel;
    int32_t strm_idx;
    int64_t bgn_time;
    int64_t end_time;
    int32_t data_size;
    std::string  path;

    std::string toStr()
    {
        char tmp[512];
        snprintf(tmp, sizeof(tmp), "seq %5lx, channel %d, strm %d, bgn %8lx, end %8lx, dataSize %8d, path %s\n",
                 seq, channel, strm_idx, bgn_time, end_time, data_size, path.c_str());
        return tmp;
    }
};

#endif
