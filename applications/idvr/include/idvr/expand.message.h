
#ifndef __MINIEYE_EXPAND_MESSAGE_H__
#define __MINIEYE_EXPAND_MESSAGE_H__


#include <vector>
#include <string>
#include <msgpack.h>
#include <msgpack.hpp>

#define EXPAND_LIBFLOW_PORT                   "23877"
#define TAHSENSOR_LIBFLOW_TOPIC               "tahsensor"

#define WEIGHTSENSOR_LIBFLOW_TOPIIC           "weightsensor_SAHX_120B"
#define WEIGHTSENSORZHF03_LIBFLOW_TOPIIC      "weightsensor_ZHF03"

#define TPMS_LIBFLOW_TOPIC                    "TPMS.data.raw"

#define ZFRADAR_LIBFLOW_TOPIIC                "ZfRadar"       
#define WEIGHTSENSOR_FUTAI_LIBFLOW_TOPIIC       "WeightSensor.futai.raw"
#define FUEL_LIBFLOW_RAW_TOPIC                  "FUEL.data.raw"
#define LIQUID_LIBFLOW_RAW_TOPIC                "LIQUID.data.raw"
#define PROP_PERSIST_FUEL_HEIGHT                "persist.fuel.height"

#define PASSENGER_FLOW_METER_LIBFLOW_TOPIC      "passenger.flow.meter"
#define PASSENGER_FLOW_METER_RUN_PROPERTY       "rw.expand.pfm.enable"

#define BSD_ALERT_XIAMENCIBEI_RUN_PROPERTY     "rw.expand.bsd.xiamen.enable"

#define ALGO_CAN_DISPLAY_OUTPUT_PROC_PROPERTY  "rw.algo.can.display.output.proc"

namespace expand
{

class ExpandMessageBase
{
public:
    ExpandMessageBase(std::string type) : mType(type) {
        mUtcTime = 0;
    }
    virtual ~ExpandMessageBase() {};
    std::string mType;
    uint64_t mUtcTime = 0;
    MSGPACK_DEFINE(mType, mUtcTime);
};


/*
    mesgpack test code

    // pack
    expand::TAHSensorMessage inMessage;
    inMessage.mTemps.push_back(22.0);
    inMessage.mTemps.push_back(33.0);
    inMessage.mTemps.push_back(44.0);
    inMessage.mTemps.push_back(55.0);
    inMessage.mHumis.push_back(66.0);
    inMessage.mHumis.push_back(77.0);
    inMessage.mHumis.push_back(88.0);
    inMessage.mHumis.push_back(99.0);
    const char* tmp = "msgpack";
    inMessage.mRawData.ptr  = tmp;
    inMessage.mRawData.size = strlen(tmp) + 1;
    msgpack::sbuffer  sbuf;
    msgpack::pack(sbuf, inMessage);


    // unpack
    msgpack::object_handle unpack = msgpack::unpack(sbuf.data(), sbuf.size());
    msgpack::object  obj = unpack.get();
    expand::TAHSensorMessage outMessage  = obj.as<expand::TAHSensorMessage>();
    logi("%s, %f %f, %d, %s", outMessage.mType.c_str(), outMessage.mTemps[1], outMessage.mHumis[2],
            outMessage.mRawData.size, outMessage.mRawData.ptr);


*/

// temperature and humidity
class TAHSensorMessage : public ExpandMessageBase
{
#define TAH_SENSOR_NOEXIST_VALUE (255.0)
public:
    TAHSensorMessage() : ExpandMessageBase("TAHSensor") {};
    ~TAHSensorMessage() {};

    std::vector<double> mTemps;
    std::vector<double> mHumis;
    msgpack::type::raw_ref  mRawData;
    MSGPACK_DEFINE(MSGPACK_BASE(ExpandMessageBase), mTemps, mHumis, mRawData);
};

// temperature and humidity
class weightSensorMessage : public ExpandMessageBase
{
#define TAH_SENSOR_NOEXIST_VALUE (255.0)
public:
    weightSensorMessage() : ExpandMessageBase("WeightSensor") {};
    ~weightSensorMessage() {};
    bool    mStatus;//称重传感器是否在线
    bool    mIsCalib;//称重传感器是否标定
    uint16_t mUnit;//单位0-0.1Kg；1-1kg；2-10kg；3-100kg
    uint32_t mWeight;
    uint32_t mWeightAD;
    MSGPACK_DEFINE(MSGPACK_BASE(ExpandMessageBase), mStatus,        mIsCalib, mUnit, mWeight, mWeightAD);
};

class weightSensorZHF03Message : public ExpandMessageBase
{
#define TAH_SENSOR_NOEXIST_VALUE (255.0)
public:
    weightSensorZHF03Message() : ExpandMessageBase("WeightSensorZHF03") {};
    ~weightSensorZHF03Message() {};
    bool    mStatus;// 载重稳定标志位
    bool    mIsCalib;//称重传感器是否标定
    uint16_t mUnit;//单位0-0.1Kg；1-1kg；2-10kg；3-100kg
    uint32_t mWeight;
    uint16_t mTorque;   /* 最大扭矩 nm */
    uint16_t mVersion; /* 版本号 */
    std::vector<uint8_t> mRaw; /* 原始帧 */
    MSGPACK_DEFINE(MSGPACK_BASE(ExpandMessageBase), mStatus,         mIsCalib, mUnit, mWeight, mTorque, mVersion, mRaw);
};

//tpms
class TpmsSensorMessage : public ExpandMessageBase
{
#define TAH_SENSOR_NOEXIST_VALUE (255.0)
public:
    TpmsSensorMessage() : ExpandMessageBase("TPMS") {};
    ~TpmsSensorMessage() {};
    int  tyreNum;
    int  tempHiThrs;    /*温度高报警阈值, ℃*/
    float pressHiThrs;   /*压力高报警阈值, Kpa */
    float pressLoThrs;   /*压力低报警阈值, Kpa */

    std::map<int /*idx*/, float/*pressure*/>      tyrePressure;
    std::map<int /*idx*/, float/*temperature*/>   tyreTemperature;
    //msgpack::type::raw_ref  mRawData;
    MSGPACK_DEFINE(MSGPACK_BASE(ExpandMessageBase), \
        tyreNum, tempHiThrs, pressHiThrs, pressLoThrs, \
        tyrePressure, tyreTemperature);
};

class ZfRadarMessage : public ExpandMessageBase
{
public:
    ZfRadarMessage() : ExpandMessageBase("ZfRadar") {};
    ~ZfRadarMessage() {};
    
    bool                    mCalStatus;     // 标定状态
    bool                    mAlarmStatus;   // 报警状态
    std::vector<uint8_t>    mRaw; /* 原始帧 */
    MSGPACK_DEFINE(MSGPACK_BASE(ExpandMessageBase), mCalStatus, mAlarmStatus, mRaw);
};

class FuelMeterSensorMessage : public ExpandMessageBase
{
public:
    FuelMeterSensorMessage() : ExpandMessageBase("FUEL") {};
    ~FuelMeterSensorMessage() {};
    float fuelHeight;
    int type;
    std::string raw;
    MSGPACK_DEFINE(MSGPACK_BASE(ExpandMessageBase), \
        fuelHeight, \
        type,
        raw);
};


class LiquidMeterSensorMessage : public ExpandMessageBase
{
public:
    LiquidMeterSensorMessage() : ExpandMessageBase("LIQUID") {};
    ~LiquidMeterSensorMessage() {};

    std::string raw;
    int id = 0;
    int maxVolume = 0;
    int liquidHeight = 0;
    int _10KRatio = 0;
    double leftVolume = 0;

    MSGPACK_DEFINE(MSGPACK_BASE(ExpandMessageBase), \
        raw, \
        id, \
        maxVolume, \
        liquidHeight, \
        _10KRatio, \
        leftVolume \
    );
};

// temperature and humidity
class WeightSensorFuTaiMessage : public ExpandMessageBase
{
#define TAH_SENSOR_NOEXIST_VALUE (255.0)
public:
    WeightSensorFuTaiMessage() : ExpandMessageBase("WeightSensorFuTai") {};
    ~WeightSensorFuTaiMessage() {};

    std::string raw;
    std::string bcdTime;
    uint8_t hwVer = 0;
    uint8_t fwVer = 0;
    uint16_t BATmV = 0;

    uint32_t seq = 0;
    uint8_t  isValidGps = 0;
    uint8_t  latSNFlag;
    uint8_t  lngEWFlag;
    float lat;
    float lng;
    float netWeightAcc;//累计净重
    uint32_t countAcc;//累计次数
    uint8_t  resultCount;//结果个数

    float grossWeight1;//毛重1
    float tareWeight1;//皮重1
    float netWeight1;//净重1
    float padWeight1;//补偿1
    uint8_t  cardDataLen1;
    std::string cardData1;//刷卡数据1

    float grossWeight2;//毛重2
    float tareWeight2;//皮重2
    float netWeight2;//净重2
    float padWeight2;//补偿2
    uint8_t  cardDataLen2;
    std::string cardData2;//刷卡数据2

    uint8_t faultCode = 0;
    MSGPACK_DEFINE(MSGPACK_BASE(ExpandMessageBase),\
        raw, bcdTime, \
        hwVer, fwVer, BATmV, \
        netWeight1, netWeight2,\
        netWeightAcc, countAcc,\
        faultCode);
};

class PassengerFlowMeterMsg : public ExpandMessageBase
{
public:
    PassengerFlowMeterMsg() : ExpandMessageBase("Passenger.flow.meter") {};
    ~PassengerFlowMeterMsg() {};

    std::string raw;
    int32_t onBoardNum = 0;
    int32_t offBoardNum = 0;
    int32_t errCode = 0;

    MSGPACK_DEFINE(MSGPACK_BASE(ExpandMessageBase), \
        raw, \
        onBoardNum, \
        offBoardNum, \
        errCode
    );
};
}

#endif
