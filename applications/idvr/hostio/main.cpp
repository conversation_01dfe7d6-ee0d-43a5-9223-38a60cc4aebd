#include <signal.h>
#include <execinfo.h>

#include "IOMsgHandler.h"
#include "HostioManager.h"
#include "properties.h"

char HOME[128] = {0};

int usage()
{
    printf("Usage: <home path>\n");
    printf("Usage: <home path> <replay file>\n");
    return -1;
}

__attribute((constructor)) void before_main()
{
    __system_properties_init();
}

/* This structure mirrors the one found in usr/include/asm/ucontext.h */
typedef struct _sig_ucontext {
    unsigned long     uc_flags;
    struct ucontext   *uc_link;
    stack_t           uc_stack;
    struct sigcontext uc_mcontext;
    sigset_t          uc_sigmask;
} sig_ucontext_t;

void crit_err_hdlr(int sig_num, siginfo_t * info, void * ucontext)
{
    void *             array[50];
    void *             caller_address;
    char **            messages;
    int                size, i;
    sig_ucontext_t *   uc;

    uc = (sig_ucontext_t *)ucontext;

    /* Get the address at the time the signal was raised from the EIP (x86) */
    caller_address = (void *) uc->uc_mcontext.arm_pc;
    FILE * fp = fopen("/data/hostio.bt.log", "a+");
    fprintf(fp, "signal %d (%s), address is %p from %p\n",
            sig_num, strsignal(sig_num), info->si_addr, (void *)caller_address);

    size = backtrace(array, sizeof(array) / sizeof(array[0]));

    /* overwrite sigaction with caller's address */
    array[1] = caller_address;

    messages = backtrace_symbols(array, size);

    /* skip first stack frame (points here) */
    for (i = 1; i < size && messages != NULL; ++i) {
        fprintf(fp, "[bt]: (%d) %s\n", i, messages[i]);
    }

    fprintf(fp, "=========\n");
    fclose(fp);
    free(messages);

    exit(EXIT_FAILURE);
}

int main(int argc, char** argv)
{
    if (!access("/data/sig_trace", R_OK)) {
        struct sigaction sigact;
        sigact.sa_sigaction = crit_err_hdlr;
        sigact.sa_flags = SA_RESTART | SA_SIGINFO;

        if (sigaction(SIGSEGV, &sigact, (struct sigaction *)NULL) != 0) {
            fprintf(stderr, "error setting signal handler for %d (%s)", SIGSEGV, strsignal(SIGSEGV));
            exit(EXIT_FAILURE);
        }

        if (sigaction(SIGFPE, &sigact, (struct sigaction *)NULL) != 0) {
            fprintf(stderr, "error setting signal handler for %d (%s)", SIGFPE, strsignal(SIGFPE));
            exit(EXIT_FAILURE);
        }
        
        if (sigaction(SIGABRT, &sigact, (struct sigaction *)NULL) != 0) {
            fprintf(stderr, "error setting signal handler for %d (%s)", SIGABRT, strsignal(SIGABRT));
            exit(EXIT_FAILURE);
        }
    }
    my::log::setLogTag(LOG_NAME);
    std::string home;

    if (argc == 1) {
        home = "/data/minieye/idvr/";

    } else if (argc != 2 && argc != 3) {
        return usage();

    } else {
        home = argv[1];
    }

    strcpy(HOME, home.c_str());

    HostioManager & m = HostioManager::getInstance();
    m.init();
    m.debug();
    m.start();

    if (argc == 2) {
        while (true) {
            sleep(30);
        }

    }

    return 0;
}
