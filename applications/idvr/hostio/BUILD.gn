executable("hostio") {

    sources = [
        "main.cpp",
        "HostioManager.cpp",
        "FileMonitor.cpp",
        "IOMsgHandler.cpp",
        "common.cpp",
        "can/CanCtrl.cpp",
        "can/can_decode.cpp",
        "can/DispProto.cpp",
        "can/McuConfig.cpp",
        "gps/GpsHandler.cpp",
        "gps/GpsSpeedCalib.cpp",
        "gps/GpsTotalMiles.cpp",
        "uart/EvUart.cpp",
    ]

    include_dirs = [
        ".",
        "./can",
        "./gps",
        "./uart",
        "//applications/idvr/include/idvr",
        "//third_party/rapidjson-1.1.0",
    ]


    deps = [
        "//foundation/base/core/filelog",
        "//foundation/communication/socketcmd",
        "//foundation/base/core/mystd",
        "//foundation/communication/libflow:flowWrap",
        "//foundation/base/service/tts/libtts",
        "//foundation/communication/ipcAgent",
        "//foundation/communication/property:property",
        "//foundation/communication/ringbuf:ringbuf",
        "//foundation/communication/libevservice",
        "//foundation/base/core/nmea_decode:nmea_decode",
        "//foundation/communication/fileprop:fileprop",
    ]

    cflags_cc = [
        "-Wno-unused-parameter",
        "-Wno-psabi",
    ]

    defines = [ 
        "LOG_TAG_STR=${target_name}",
     ]

    libs = [    ]

    ldflags = [
        "-ldl",
        "-lpthread",
    ]
}
