#include <math.h>
#include "system_properties.h"
#include "common.h"
#include "idvrProperty.h"
#include "rapidjson.wrap.h"
#include "mystd.h"

std::string utcToCstTimestamp(time_t s)
{
    char buffer[64];
    time_t t = s;
    struct tm* timeinfo;

    memset(buffer, 0, sizeof buffer);
    timeinfo = localtime(&t);
    strftime(buffer, sizeof buffer, "%Y-%m-%d %H:%M:%S", timeinfo);
    return buffer;
}

std::string getTimestamp(time_t t)
{
    char buffer[64];
    struct tm* timeinfo;

    memset(buffer, 0, sizeof buffer);
    timeinfo = localtime(&t);
    strftime(buffer, sizeof buffer, "%Y-%m-%d %H:%M:%S", timeinfo);
    return buffer;
}


uint64_t systemTime(void)
{
    uint64_t ns = 0;

    struct timespec now;
    clock_gettime(CLOCK_MONOTONIC, &now);
    ns = now.tv_sec*1000000000UL + now.tv_nsec;

    return ns;
}


std::string uptime(time_t sec)
{
    int day = sec/86400;
    int hour = (sec%86400)/3600;
    int min = ((sec%86400)%3600)/60;
    int second = ((sec%86400)%3600)%60;

    char buf[128];

    snprintf(buf, sizeof(buf), "%d day, %02d:%02d:%02d", day, hour, min, second);
    return buf;
}


std::string getTimestamp(time_t *t)
{
    struct tm* timeinfo;
    struct timeval tv;
    char msglog[128];
    char buffer[64];

    memset(buffer, 0, sizeof buffer);

    if (t) {
        timeinfo = localtime(t);
        strftime(buffer, sizeof buffer, "%Y-%m-%d %H:%M:%S", timeinfo);
        return buffer;
    }

    gettimeofday(&tv, NULL);
    timeinfo = localtime(&tv.tv_sec);
    strftime(buffer, sizeof buffer, "%Y-%m-%d %H:%M:%S.", timeinfo);
    snprintf(msglog, sizeof(msglog), "%s%" FMT_LLD, buffer, tv.tv_usec/1000);

    return msglog;
}

const double EARTH_RADIUS = 6378.137;       // 地球半径
const double EARTH_RADIUS_C = 6378137;      // 赤道半径
const double EARTH_RADIUS_J = 6356725;      // 极半径
const double PI = 3.141592654;              // PI

double Rad(double d)
{
       return d * PI / 180.0;
}


double getDistance(double la1, double lo1, double la2, double lo2)
{
    double a = Rad(la1) - Rad(la2);
    double b = Rad(lo1) - Rad(lo2);
    double s = 2 * asin(sqrt(pow(sin(a/2), 2) + cos(Rad(la1))*cos(Rad(la2))*pow(sin(b/2),2)));

    s = s * EARTH_RADIUS;
    return s;
}


void getNetStatus(int csq[2], int net_info[SIM_MAX])
{
    char prop[PROP_VALUE_MAX] = {0};
    memset(prop, 0, PROP_VALUE_MAX);
    __system_property_get("rw.minieye.sim_state", prop);
    if (prop[0] == '0') {
        csq[0] = 0;
    } else {
        memset(prop, 0, PROP_VALUE_MAX);

        if (__system_property_get(PROP_RW_MINIEYE_SIGNAL_LEVEL, prop)) {
            sscanf(prop, "%d", &csq[0]);
        } else {
            csq[0] = 0;
        }
    }

}


void getIccid(char *iccid)
{
    char prop[PROP_VALUE_MAX] = "";
    memset(prop, 0, PROP_VALUE_MAX);
    __system_property_get(PROP_PERSIST_MINIEYE_ICCID, prop);
    strcpy(iccid,prop);
}

void getImei(char *imei)
{
    char prop[PROP_VALUE_MAX] = "";
    memset(prop, 0, PROP_VALUE_MAX);
    __system_property_get(PROP_PERSIST_MINIEYE_IME, prop);
    strcpy(imei,prop);
}

bool pwrkeyRebootIsMark(void)
{
    char propValue[PROP_VALUE_MAX];
    memset(propValue, 0, sizeof(propValue));

    __system_property_get(PROP_PERSIST_MINIEYE_PWRKEYREBOOT, propValue);

    const char *p = propValue;
    if(!strcmp(p, "done")) {
        return true;
    }
    return false;
}

void pwrkeyRebootMark(void)
{
    char propValue[PROP_VALUE_MAX];
    snprintf(propValue, sizeof(propValue), "done");
    __system_property_set(PROP_PERSIST_MINIEYE_PWRKEYREBOOT, propValue);
}

void pwrkeyRebootMarkClear(void)
{
    char propValue[PROP_VALUE_MAX];
    snprintf(propValue, sizeof(propValue), "undo");
    __system_property_set(PROP_PERSIST_MINIEYE_PWRKEYREBOOT, propValue);
}


void getDebugSpeed(int &speed)
{
    int fake_speed=0;
    char prop[PROP_VALUE_MAX] = "";
    memset(prop, 0, PROP_VALUE_MAX);
    if(__system_property_get(PROP_RW_MINIEYE_FAKESPEED, prop)){
        sscanf(prop, "%d", &fake_speed);
        if(fake_speed >= 0)
            speed = fake_speed;
    }
}




bool iccardPlayAudioIsMark(void)
{
    char propValue[PROP_VALUE_MAX];
    memset(propValue, 0, sizeof(propValue));

    __system_property_get(PROP_RW_MINIEYE_ICCARDAUDIO, propValue);

    const char *p = propValue;
    if(!strcmp(p, "done")) {
        return true;
    }

    return false;
}

void iccardPlayAudioMark(void)
{
    char propValue[PROP_VALUE_MAX];
    snprintf(propValue, sizeof(propValue), "done");
    __system_property_set(PROP_RW_MINIEYE_ICCARDAUDIO, propValue);
}

void iccardPlayAudioMarkClear(void)
{
    char propValue[PROP_VALUE_MAX];
    snprintf(propValue, sizeof(propValue), "undo");
    __system_property_set(PROP_RW_MINIEYE_ICCARDAUDIO, propValue);
}


