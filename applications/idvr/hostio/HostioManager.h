#ifndef __HOSTIO_MANAGER_H__
#define __HOSTIO_MANAGER_H__

#include <vector>
#include "mystd.h"
#include "McuMessage.h"
#include "CmdListener.h"
#include "idvrConfig.h"
#include "idvrProperty.h"
#include "IOMsg.h"

//配置文件接收类
class HostIOConfigReceiver : public CmdBroadcastReceiver
{
public:
   HostIOConfigReceiver() : CmdBroadcastReceiver("config") {}
   ~HostIOConfigReceiver() {}
   bool OnBroadcastCallback(vector<string>& vec);
   bool refresh(char* section,char* key,char* value);
};

class IOMsgHandler;
class HostioManager
    : public my::thread,
      public my::Singleton<HostioManager>
{
    friend class my::Singleton<HostioManager>;

public:
    ~HostioManager();

    bool init();
    int start();
    void stop();

    void debug();

    const char * getDataSavePath();


protected:
    void run();
public:
    conf_t mConfig;

    bool mcuAgentServerSendMsg(McuMsgTypeE cmd, const uint8_t *data, uint32_t size);
    bool mcuAgentServerRecvMsg(McuMessage *msg);

private:
    void refreshGpsRecord();
    void writeGpsRecordProperty();
    int propertySet(const char *key, const char *value);
    HostioManager();
    IOMsgHandler *mMsgHandler;
    IpcServer *mMcuAgentServer;
    bool            mAccOffWrite;
    my::timestamp   mGpsWriteTm;

    std::string mDataSavePath;
};

#endif
