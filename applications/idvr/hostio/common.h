#ifndef __COMMON_API_H__
#define __COMMON_API_H__
#include <time.h>
#include <string>
#include "McuMessage.h"


extern std::string utcToCstTimestamp(time_t s);
extern std::string getTimestamp(time_t t);
extern std::string getTimestamp(time_t *t);
extern uint64_t systemTime(void);
extern std::string uptime(time_t sec);
extern double Rad(double d);
extern double getDistance(double la1, double lo1, double la2, double lo2);
extern void getNetStatus(int csq[2], int net_info[SIM_MAX]);
extern void getIccid(char *iccid);
extern void getImei(char *imei);
extern void getDebugSpeed(int &speed);
extern bool setDevId(void);
extern bool pwrkeyRebootIsMark(void);
extern void pwrkeyRebootMark(void);
extern void pwrkeyRebootMarkClear(void);


#endif
