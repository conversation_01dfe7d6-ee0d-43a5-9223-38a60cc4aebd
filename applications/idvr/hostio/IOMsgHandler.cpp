﻿#include <stdio.h>
#include <time.h>
#include "system_properties.h"
#include "GpsTotalMiles.h"
#include "idvrProperty.h"
#include "IOMsgHandler.h"
#include "HostioManager.h"

using namespace rapidjson;

const char work_mode_name[WORK_MODE_MAX][32] = {
    "ft",
    "aging",
    "setup",
    "normal",
    "sample",
};

extern char HOME[];

SINGLETON_STATIC_INSTANCE(IOMsgHandler);

IOMsgHandler::IOMsgHandler()
{
    memset(&mSpeedTurn, 0, sizeof(SpeedTurnInfo));
    memset(&mMCUInfo, 0, sizeof(McuInfo));

    mStack.state = 0;
    mStack.header.len = 0;

    mSys.power_low = 0;
    mSys.power_off = 0;

    mbCalcMileageBySpd = !access("/data/calcMileageBySpd", R_OK);
}

IOMsgHandler::~IOMsgHandler()
{
    delete mLogger;
    delete mGpsSpdCalib;
    stop();
}

bool IOMsgHandler::setDevId(void)
{
    char propValue[PROP_VALUE_MAX];
    rapidjson::Document doc;
    RAPIDJSON_LOAD("/data/c4/c4.json");
    RAPIDJSON_GET_JSON_STRING(doc, "id", mDeviceId);

    logd("get device id %s", mDeviceId.c_str());
    snprintf(propValue, sizeof(propValue), "%s", mDeviceId.c_str());
    __system_property_set(PROP_PERSIST_MINIEYE_DEVICEID, propValue);
    return true;
}

void IOMsgHandler::getLocalProp(void)
{
    /* gps calib aspeed */
    char value[PROP_VALUE_MAX];
    memset(value, 0, PROP_VALUE_MAX);
    int len = 0;
    memset(value, 0, sizeof(value));
    len = __system_property_get(PROP_RW_MINIEYE_GPS_CALIB_RATIO, value);

    if (len == 0) {
        mGpsCalibRatio = 0.0;
    } else {
        mGpsCalibRatio = atof(value);
    }

    memset(value, 0, sizeof(value));
    len = __system_property_get(PROP_RW_MINIEYE_CALIB_STDDEV, value);

    if (len == 0) {
        mGpsCalibStddev = 1e6;
    } else {
        mGpsCalibStddev = atof(value);
    }
    mGpsSpdCalib = new GpsSpeedCalib(mGpsCalibRatio, mGpsCalibStddev);

    memset(value, 0, sizeof(value));
    len = __system_property_get(PROP_PERSIST_MINIEYE_ADAS_DISP_MODE, value);
    if (len > 0) {
        if (!strcmp(value, "normal")) {
            mAdisp_mode = ADISP_NORMAL;
        } else if(!strcmp(value, "speed")) {
            mAdisp_mode = ADISP_SPEED;
        }
    }

    mDataPath = HostioManager::getInstance().getDataSavePath();
    mFilePropTotalMile = make_shared<FileProp>(mDataPath, "totalMiles");

    // todo 加载总里程
    char filePropValue[FILE_PROP_VALUE_MAX] = {0};
    FILE_PROP_RET ret = mFilePropTotalMile->getProp(filePropValue);
    if (ret != FILE_PROP_RET::OK) {
        logd("failed to get file prop %s in totalMiles (err: %s)", mDataPath, FileProp::errToString(ret).c_str());
    } else {
        mTotal_mileage = atof(filePropValue);
        logd("succ to get file prop %s in totalMiles %lf, %s", mDataPath, mTotal_mileage, filePropValue);
    }

}

std::string IOMsgHandler::dumpStatStr(void)
{
    std::string msg = "\n";
    APPEND_STAT_MSG(msg, "gps_time", "%u", mLbs.time);
    APPEND_STAT_MSG(msg, "gps_mode", "[%d] GPS状态：0=未定位，1=单点定位，2=SBAS差分定位，4=RTK固定解，5=RTK浮点解，6=惯导定位", mLbs.rtkData.sig);
    APPEND_STAT_MSG(msg, "rtd_lbs", "lat:%lf, lng:%lf", mLbs.rtkData.lat, mLbs.rtkData.lng);
    APPEND_STAT_MSG(msg, "gps_level", "[%d] 信号强度等级 0 1 2 3依次增强", mLbs.sig_level);
    APPEND_STAT_MSG(msg, "mLbs", "lng = %f, lat = %f, alt = %.1f, satelite_num = %d, located = %d, dir = %.1f, antenna = %d",
        mLbs.lng_x1kw / 10000000.0, mLbs.lat_x1kw / 10000000.0, mLbs.alt_x10 / 10.0, mLbs.sat, mLbs.status, mLbs.dir_x100 / 100.0, mLbs.antenna);
    APPEND_STAT_MSG(msg, "gps_speed", "%.1f", mLbs.speed_x10 / 10.0);
    APPEND_STAT_MSG(msg, "speed", "%.1f, %.1f", mSpeedTurn.selectSpeedx10 / 10.0, mLbs.speed_x10 / 10.0);
    APPEND_STAT_MSG(msg, "mileage", "%.2f", mTotal_mileage);
    APPEND_STAT_MSG(msg, "vehicle_io", "acc %d alarm %d near %d far %d left %d right %d brake %d can_loss %d, selectTurnL %d selectTurnR %d",
        mVehicleIo.acc, mVehicleIo.emergency_alarm, mVehicleIo.normal_light,
        mVehicleIo.far_light, mVehicleIo.left_turn, mVehicleIo.right_turn, mVehicleIo.brake,
        mVehicleIo.can_signal_loss, mSpeedTurn.selectTurnL, mSpeedTurn.selectTurnR);
    APPEND_STAT_MSG(msg, "can_info", "reverse %d, brake %d, turn %d", mCan.mCarInfo.reverse, mCan.mCarInfo.brake, mCan.mCarInfo.turn);
    APPEND_STAT_MSG(msg, "adc", "Ver:%d(%.0f)-Cap:%d(%.2fV)-Tem:%d(%.2f℃)-Vdd:%d(%.2fV)-DcIn:%d(%.2fV)",
        mAdcRaw[ADC_CHN_VER], mAdcAct[ADC_CHN_VER],
        mAdcRaw[ADC_CHN_CAP], mAdcAct[ADC_CHN_CAP],
        mAdcRaw[ADC_CHN_TEM], mAdcAct[ADC_CHN_TEM],
        mAdcRaw[ADC_CHN_VDD], mAdcAct[ADC_CHN_VDD],
        mAdcRaw[ADC_CHN_DCIN], mAdcAct[ADC_CHN_DCIN]);
    APPEND_STAT_MSG(msg, "mcu", "uptime = '%s', temprature = %d", uptime(mMCUInfo.mcuUptime).c_str(), mMCUInfo.mcuTempture_x100);
    APPEND_STAT_MSG(msg, "power", "sys_pwr = %d (0x%04x), pwr_low = %d, pwr_off = %d, eth_pwr = %d, gps_pwr = %d, 4g_pwr = %d",
        mPwrSet.pwrSys, mPwrSet.val, mSys.power_low, mSys.power_off, mPwrSet.pwr_eth, mPwrSet.pwr_gps, mPwrSet.pwr_4g);
    APPEND_STAT_MSG(msg, "record", "rec status bits = 0x%02x, disk status = 0x%x", mRecStatus, mDiskStatus);
    APPEND_STAT_MSG(msg, "flagsta", "McuRstSoc:%d, AutoCtlCap:%d",
        mFlagSta.fMcuRstSoc, mFlagSta.fAutoCtlCap);
    return msg;
}
std::string IOMsgHandler::logLevel(const char * key, int level)
{
    std::string msg = "\n";
    static const char * logName[LOG_TYPE_MAX] = {"GPS", "STATUS", "CAN", "ASPEED"};
    int i = 0, found = 0;
    if (key) {
        for (i = 0; i < LOG_TYPE_MAX; ++i) {
            if (!strcmp(logName[i], key)) {
                setLogVerbose((LOG_TYPE_CTRL_E)i, level);
                found = 1;
                break;
            }
        }
    }
    if (!found) {
        for (i = 0; i < LOG_TYPE_MAX; ++i) {
            APPEND_STAT_MSG(msg, logName[i], "%d", mLogVerbose[i]);
        }
    }
    return msg;
}

void IOMsgHandler::queryMcuVersion(void)
{
    IOMsg msg(MAJOR_VERSION, VERSION_BASE);
    logd("query mcu version\n");
    sendMsgToMcu(msg);
}

#if 0
void IOMsgHandler::bat_thres_config(uint16_t disable_thres, uint16_t enable_thres)
{
    IOMsg msg(MAJOR_BAT_CONFIG, 0);
    bat_threshold_t conf;

    conf.disable_thres = disable_thres;
    conf.enable_thres = enable_thres;
    msg.data.append((const char *)&conf, sizeof(bat_threshold_t));

    logd("config bat threshold\n");
    sendMsgToMcu(msg);
}
#endif

int IOMsgHandler::init()
{
    mWorkerHandle = ev_service_worker_init("IOMsgHandler");
    getWorkMode();

    mLogger = new FileLog("/data/minieye/idvr/mlog/hostio_log/");
    mLogger->prepare();
    struct timespec now;
    clock_gettime(CLOCK_MONOTONIC, &now);
    mLogger->mlog("##hostio start, uptime: %s", uptime(now.tv_sec).c_str());

    memset(mLogVerbose, 0, LOG_TYPE_MAX);

    pMonitor = new MonitorThead(this);
    pMonitor->start();

    // 选择GPS模块, 选择F9K就不添加观察者了
    mGpsHandler.init();
    char value[PROP_VALUE_MAX];
    memset(value, 0, sizeof(value));
    int len = __system_property_get(PROP_PERSIST_MINIEYE_GPS_SWITCH, value);
    if(len > 0) {
        std::string gpsSwitch(value, len);
        if (gpsSwitch != "F9K") {
            mGpsHandler.attachObserver(this);
        }
    } else {
        mGpsHandler.attachObserver(this);
    }

    getLocalProp();
    setDevId();
    get_last_gps_location();
    return 0;
}

// 开启IO消息处理器
int32_t IOMsgHandler::start()
{
    int32_t ret = 0;
    if (!isEvUartConnected()) {
        ret = evUartOpen(mWorkerHandle, UART_DEVICE, 921600, false);
    }
    my::thread::start();
    return ret;
}

// 关闭IO消息处理器
void IOMsgHandler::stop()
{
    evUartClose();
	my::thread::stop();
}

void IOMsgHandler::run()
{
    prctl(PR_SET_NAME, "HostIOMsgHandler");
    while (!exiting()) {
        // 接收ipcAgent server的消息并进行处理，阻塞式
        mcuAgentServerHandleMsg();
    }
}

void IOMsgHandler::mcuAgentServerHandleMsg(void)
{
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];

    //logd("Mcu listen pthread enter\n");

    //logd("McuAgent %p pthread read\n", mcuAgent);
    if (HostioManager::getInstance().mcuAgentServerRecvMsg(pMsg)) {
        //logd("Mcu listen recv type  %d len %d\n", pMsg->ipcMsg.type, pMsg->ipcMsg.len);

        switch(pMsg->ipcMsg.type) {
            /* 快处理 */
            case MCU_MSG_TYPE_MCU_CONFIG:
                {
                    if(pMsg->ipcMsg.len > 0) {

                    } else {
                        mMCUInfo.mcuInited = false;
                    }
                }
                break;
            case MCU_MSG_TYPE_UPGRADE:
                {
                    logd("send mcu upgrade frame len %d\n", pMsg->ipcMsg.len);
                    IOMsg msg(MAJOR_UPGRADE, 0);
                    msg.data.append((const char *)pMsg->u.u8Array, pMsg->ipcMsg.len);
                    sendMsgToMcu(msg);
                }
                break;
            case MCU_MSG_TYPE_CONFIG_CAN:
                {
                    McuMsgCanConfigT *p = (McuMsgCanConfigT *)pMsg->u.uchar;
                    if (pMsg->ipcMsg.len < sizeof(McuMsgCanConfigT)) {
                       break;
                    }

                    if (p->use_json) {
                        logd("using can json  %s\n", p->file);
                        mCan.reloadConfig(p->file);
                    } else {
                        logd("set baud %d\n", p->speed);
                        mCan.setCanMode(p->canx, p->mode, p->speed);
                    }
                }
                break;
            case MCU_MSG_TYPE_SET_CAN_FILTER:
                {
                    McuMsgCanFilterT* canFilter = (McuMsgCanFilterT*)pMsg->u.filter;
                    uint8_t canChannel = canFilter->canIdx;
                    bool bListMode = canFilter->useListMode ? true : false;
                    logd("can++ filter canID %d, frameID %x bListMode:%d mask:%x", canChannel, canFilter->canIds, bListMode, canFilter->canIds);

                    if (MCU_CAN_IDX_CAN0_SPEED == canChannel) {//can0
                        mCan.setCanFilter(MCU_CAN_IDX_CAN0_SPEED, true, bListMode, MCU_CAN_IDX_CAN0_FILTER_RES1, canFilter->canIds, true);
                    } else if (MCU_CAN_IDX_CAN1_DISP == canChannel) { //can1
                        mCan.setCanFilter(MCU_CAN_IDX_CAN1_DISP, true, bListMode, MCU_CAN_IDX_CAN1_FILTER_RES1, canFilter->canIds, true);
                    }
                }
                break;
            case MCU_MSG_TYPE_CLEAR_ALL_FILTER:
                {
                    mCan.clearAllCanFilterAndStart();
                }
                break;
            case MCU_MSG_TYPE_RAW_CAN0:
                {
                    /* speed can */
                    IOMsg msg(MAJOR_CAN, CAN_MINOR_SPEED_CAN0_MSG);
                    msg.data.append((const char *)pMsg->u.u8Array, pMsg->ipcMsg.len);
                    sendMsgToMcu(msg);
                }
                break;
            case MCU_MSG_TYPE_RAW_CAN1:
                {
                    IOMsg msg(MAJOR_CAN, CAN_MINOR_DISP_CAN1_MSG);
                    msg.data.append((const char *)pMsg->u.u8Array, pMsg->ipcMsg.len);
                    sendMsgToMcu(msg);
                    //logd("send disp.\n");
                }
                break;
            case MCU_MSG_TYPE_IO_CTRL:
                {
                    uint8_t minor;
                    if(pMsg->ipcMsg.len > 0) {
                        minor = pMsg->u.u8Array[0];
                        IOMsg msg(MAJOR_GPIO, minor);
                        sendMsgToMcu(msg);
                    }
                }
                break;
            case MCU_MSG_TYPE_PWR_DOWN:
                {
                    if(pMsg->ipcMsg.len < 0) {
                        break;
                    }
                    if(pMsg->u.u8Array[0] >= PWR_DOWN_THREAD_MAX) {
                        logd("msg(%d) invalid\n", MCU_MSG_TYPE_PWR_DOWN);
                    }
                    logi("pthread %d send pwr down.", pMsg->u.u8Array[0]);
                    MY_SPINLOCK_X(mPwrlock);
                    mPwrDn |= 1 << pMsg->u.u8Array[0];
                }
                break;
            case MCU_MSG_TYPE_PWR_DOWN_PENDING:
                {
                    if(pMsg->ipcMsg.len < 0) {
                        break;
                    }
                    if(pMsg->u.u8Array[0] >= PWR_DOWN_THREAD_MAX) {
                        logd("msg(%d) invalid\n", MCU_MSG_TYPE_PWR_DOWN);
                    }
                    MY_SPINLOCK_X(mPwrlock);
                    mPwrDnPending |= 1 << pMsg->u.u8Array[0];

                    logd("pending...\n");
                    /* update pending time*/
                    mPowerDownPendingTime = my::timestamp::now();
                }
                break;
            case MCU_MSG_TYPE_RS485:
                    {
                        IOMsg msg(MAJOR_UART, EXT_UART_RS485);
                        msg.data.append((const char *)pMsg->u.u8Array, pMsg->ipcMsg.len);
                        sendMsgToMcu(msg);
                        logd("send rs485\n");
                    }
                    break;
            case MCU_MSG_TYPE_RS485_CONFIG:
                {
                    UrtpUartConfT *conf = (UrtpUartConfT *)&pMsg->u.schar[0];
                    logd("baud %d stop %d parity %d"\
                            , conf->baudrate
                            , conf->stopbit
                            , conf->parity);
                    IOMsg msg(MAJOR_UART_CONFIG, EXT_UART_RS485);
                    msg.data.append((const char *)conf, sizeof(UrtpUartConfT));
                    sendMsgToMcu(msg);
                    logd("send rs485\n");
                }
                break;
            case MCU_MSG_TYPE_MCU_VERSION:
                {
                    queryMcuVersion();
                }
                break;
            case MCU_MSG_TYPE_MCU_FIRMWARE_INFO:
                {
                    uint8_t buf[MCU_MSG_MAX_SIZE];
                    McuMessage *pMsg = (McuMessage *)&buf[0];
                    IOMsg msg(MAJOR_VERSION, VERSION_FIRMWARE_INFO);
                    logd("query mcu firmware info\n");
                    sendMsgToMcu(msg);
                }
                break;
            case MCU_MSG_TYPE_HOSTIO_VERSION:
                {

                }
                break;
            case MCU_MSG_TYPE_LOG_CTRL:
                {
                    if(pMsg->ipcMsg.len < 2) {
                        break;
                    }
                    if (pMsg->u.uchar[0] <0 || pMsg->u.uchar[0] >= LOG_TYPE_MAX) {
                        logd("MCU_MSG_TYPE_LOG_CTRL invalid");
                        break;
                    }
                    setLogVerbose((LOG_TYPE_CTRL_E)pMsg->u.uchar[0], pMsg->u.uchar[1]);

                    if (pMsg->u.uchar[0] == LOG_TYPE_CAN) {
                        mCan.mVerbose = pMsg->u.uchar[1];
                    }
                }
                break;
            case MCU_MSG_TYPE_MCU_RST:
                {
                    logd("send reset mcu cmd\n");
                    IOMsg msg(MAJOR_RESET, 0);
                    //send(msg);
                    sendMsgToMcu(msg);
                }
                break;
            case MCU_MSG_TYPE_MCU_UPGRD_AUTHEN:
                {
                    logd("send mcu upgrade authen cmd\n");
                    IOMsg msg(MAJOR_VERSION, VERSION_AUTHEN);
                    msg.data.append((const char *)pMsg->u.uchar, pMsg->ipcMsg.len);
                    sendMsgToMcu(msg);
                }
                break;
            case MCU_MSG_TYPE_ADAS_CAN700:
                {
                    //logd("adas can700\n");
                    ADAS_CAN700 *p = (ADAS_CAN700 *)&pMsg->u.uchar[0];

                    if (mAdisp_mode == ADISP_NORMAL) { /*adas display speed mode */
                        mCan.adas_alert_disp(p, mSpeedTurn.selectSpeedx10);
                    }
                }
                break;
            case MCU_MSG_TYPE_CAN1_DISPLAY: {
                if (!access("/mnt/obb/dump_bsd_can_data", R_OK)) {
                    my::constr data(pMsg->u.schar, sizeof(UrtpCanT));
                    logd("MCU_MSG_TYPE_CAN1_DISPLAY %s", my::hex(data, true).c_str());
                }
                mCan.sendMsg(MCU_CAN_IDX_CAN1_DISP, (UrtpCanT*)(pMsg->u.uchar));
                break;
            }
            case MCU_MSG_TYPE_F9KGPS_LIBS:
            {
                LBS *gpsInfo = (LBS*)pMsg->ipcMsg.u.u8Array;
                updateLbs(gpsInfo);
                //logd("len %d sig %d lng %lf lat %lf", pMsg->ipcMsg.len, gpsInfo->rtkData.sig, gpsInfo->rtkData.lng, gpsInfo->rtkData.lat);
            }
            case MCU_MSG_TYPE_CAN_STAT:
                {
                    IOMsg msg(MAJOR_CAN, CAN_MINOR_STATISTICS);
                    logd("query mcu can statistics\n");
                    sendMsgToMcu(msg);
                }
                break;
            case MCU_MSG_TYPE_CAN_CLR_STAT:
                {
                    IOMsg msg(MAJOR_CAN, CAN_MINOR_CLEAR_STATISTICS);
                    logd("clear mcu can statistics\n");
                    sendMsgToMcu(msg);
                }
                break;
            case MCU_MSG_TYPE_MCU_REG:
                {
                    logd("get mcu reg(0x%X) value.(len:%d)\n", *(uint32_t *)(pMsg->u.u8Array), pMsg->ipcMsg.len);
                    IOMsg msg(MAJOR_GET_REG, 0);
                    msg.data.append((const char *)pMsg->u.u8Array, pMsg->ipcMsg.len);
                    sendMsgToMcu(msg);
                }
                break;
            case MCU_MSG_TYPE_SOC_RST:
                {
                    logd("send reset soc cmd\n");
                    IOMsg msg(MAJOR_PWRKEY_RST, 0);
                    sendMsgToMcu(msg);
                }
                break;
            case MCU_MSG_TYPE_SWITCH_CTRL:
                {
                    uint8_t minor;
                    if(pMsg->ipcMsg.len > 0) {
                        minor = pMsg->u.u8Array[0];
                        IOMsg msg(MAJOR_SWITCH, minor);
                        sendMsgToMcu(msg);
                    }
                }
                break;
            default:
                logd("Mcu listen recv unknow...%d\n", pMsg->ipcMsg.type);
                break;
        }
    } else {
        logd("Mcu listen recv fail\n");
    }

}

bool IOMsgHandler::tryPowerOff(bool forcePwrDn)
{
    bool PwrCmdReady = true;
    bool pwrDnPendingTimeout = false;
    for(uint32_t i = PWR_DOWN_THREAD_MEDIA; i< PWR_DOWN_THREAD_MAX; i++) {
        if((mPwrDn & (1 << i)) == 0) { /* thread not send pwroff cmd*/
            PwrCmdReady = false;
        }
    }

    if (mPowerDownPendingTime.elapsed() > 20 * 1000) {
        pwrDnPendingTimeout = true;
        logi("power down pending timeout!\n");
    }

    if(forcePwrDn || PwrCmdReady || pwrDnPendingTimeout) {
        logw("send pwr down cmd, forcePowerDown %d PwrCmdReady %d pendigTimeout %d\n"\
                , forcePwrDn, PwrCmdReady, pwrDnPendingTimeout);
        mLogger->mlog("send sys-off, force %d ready %d timeout %d",forcePwrDn, PwrCmdReady, pwrDnPendingTimeout);
        sync();

        IOMsg msg(MAJOR_GPIO, MINOR_GPIO_PWR_SYS_OFF);
        sendMsgToMcu(msg);
        return true;
    } else {
        return false;
    }
}

// 处理收到的未转义原始消息包
bool IOMsgHandler::onDataRecv(const char* data, int size)
{
	const char* p = data;
	const char* q = data + size;
	if (size <= 0) return false;

	while (p < q)
	{
		switch (mStack.state)
		{
		case 0: // 查找0x13
			{
				while (p < q && *p != (char)0x13) p++;
				if (p >= q) break;
				mStack.state = 1;
				p++;
			}
			break;
		case 1: // 查找0x14
			{
				if (*p++ != (char)0x14)
					mStack.state = 0;
				else
				{
					mStack.state = 2;
					mStack.header.len = 0;
				}
			}
			break;
		case 2: // 累积消息头
			{
				while (p < q && mStack.header.len < 6) mStack.header.data[mStack.header.len++] = *p++;
				if (mStack.header.len == 6) // 检查消息头校验码
				{
					char chksum = mStack.header.data[1] + mStack.header.data[2] + mStack.header.data[3] + mStack.header.data[4] + mStack.header.data[5];
					if (chksum != mStack.header.data[0])
					{
						logw("Bad message header.");
						char buf[6];
						memcpy(buf, mStack.header.data, 6);

						mStack.state = 0;
						onDataRecv(buf, 6);
						break;
					}
					else
					{
						// 获取长度
						my::ushort n;
						my::constr d(mStack.header.data+4, 2);
						d >> my::ntoh >> n;

						if (n != 0) // 如果长度不为0
						{
							mStack.body = "";
							mStack.state = 3;
						}
						else
						{
							proc(mStack.header.data[1], mStack.header.data[2], 0, 0);
							mStack.state = 0;
						}
					}
				}
			}
			break;
		case 3: // 累积消息体
			{
				// 获取长度
				my::ushort n;
				my::constr d(mStack.header.data + 4, 2);
				d >> my::ntoh >> n;

				my::ushort c = (my::ushort)(q - p);

				const char* body = mStack.body;
				my::ushort bodylen = mStack.body.length();
				if (bodylen + c >= n) // 长度够了
				{
					// 计算消息体检验码
					char chksum = 0;
					for (my::ushort i = 0; i < bodylen; i++)
					{
						chksum += body[i];
					}

					c = n - bodylen;
					for (my::ushort i = 0; i < c; i++)
					{
						chksum += p[i];
					}

					// 如果消息体校验码正确
					if (mStack.header.data[3] == chksum)
					{
					    mStack.body.append(p, c);
				        bodylen = mStack.body.length();

	                    //logd("[IOMsgHandler::head] %s\n", my::hex(my::constr(mStack.header.data, 6)).c_str());
	                    //logd("[IOMsgHandler::body] %s\n", my::hex(my::constr(body, bodylen)).c_str());

                        /*body append 之后body指针没有更新， 直接使用body.c_str()*/
	                    //logd("[IOMsgHandler::body] %s\n", my::hex(my::constr(mStack.body.c_str(), bodylen)).c_str());
						proc(mStack.header.data[1], mStack.header.data[2], mStack.body.c_str(), bodylen);
						p += c;
						mStack.state = 0;
					}
					else // 不正确的情况下需要重新检查
					{
						logw("Bad message body.");

						// 压入消息体头
						char buf[6];
						memcpy(buf, mStack.header.data, 6);

						mStack.state = 0;
						onDataRecv(buf, 6);

						// 压入消息体
						if (bodylen > 0)
						{
							onDataRecv(body, bodylen);
						}
					}
				}
				else // 长度不够
				{
					mStack.body.append(p, c);
					p += c;
				}
			}
			break;
		}
	}
	return true;
}

/* 不要修改i此内容，
 * 外部脚本监测property */
void IOMsgHandler::setPowerVol(void)
{
    char prop[PROP_VALUE_MAX] = "";
    memset(prop, 0, PROP_VALUE_MAX);

    snprintf(prop, sizeof(prop), "power/acc/ext1/ext2 %.1fv,%d,%.1fv,%.1fv",
        mAdcAct[ADC_CHN_VDD], mVehicleIo.acc, 0.0, 0.0);
    __system_property_set(PROP_RW_MINIEYE_POWER_VOL, prop);
}

/* 可以酌情修改内容，
 * 外部脚本监测property，用于进入aging模式*/
void IOMsgHandler::setExtIoStatusToProperty(void)
{
    char prop[PROP_VALUE_MAX] = "";
    memset(prop, 0, PROP_VALUE_MAX);

    snprintf(prop, sizeof(prop), "brake/alarm/high_beam/low_beam %d,%d,%d,%d",
        mVehicleIo.brake, mVehicleIo.emergency_alarm ? 0 : 1, mVehicleIo.far_light, mVehicleIo.normal_light);
    __system_property_set(PROP_RW_MINIEYE_EXT_IO_STATUS, prop);
}

void IOMsgHandler::mcuConfig(void)
{
    if(!mMCUInfo.mcuVersionOk) {
        queryMcuVersion(); // 查询mcu版本
    } else {
        if(!mMCUInfo.mcuInited) {
            mMCUInfo.mcuInited = true;

            /*config mcu*/
            logd("Can configed.\n");
            mCan.McuCanConfig(); //配置can参数can0 can1的波特率 模式
            //bat_thres_config(7000, 6500); // 电池相关配置
        }
    }
}

void IOMsgHandler::chkRecordStatus(void)
{
    if (my::getClockSec() - mRecordStatusChkTime > 10) {
        mRecordStatusChkTime = my::getClockSec();

        //todo
        bool stat1 = false;
        char cmd1[64] = {0};
        char resp1[64] = {0};
        // 返回的字符解析
        int ret_str1[3];
        for (int i = 0; i < HostioManager::getInstance().mConfig.cameras + HostioManager::getInstance().mConfig.ipcs; i++) {
            sprintf(cmd1, "cmd recStatus %d", i);
            stat1 = LogCallProxyCmd::sendReq("recorder", cmd1, resp1, sizeof(resp1), 1);
            if (stat1) { // 远程调用成功
                sscanf(resp1, "%d %d %d", &ret_str1[0], &ret_str1[1], &ret_str1[2]);
                // 读取录像状态
                if (ret_str1[2] == 1) {
                    mRecStatus |= 1 << i;
                }
                else if (ret_str1[2] == 0){
                    mRecStatus &= ~(1 << i);
                }
            } else {
                    // 远程调用失败，直接退出
                    mRecStatus = 0;
                    break;
            }
        }
        if (mRecStatus) {
            IOMsg msg(MAJOR_SWITCH, LED_G_FLASH_1S);
            sendMsgToMcu(msg);
        } else {
            IOMsg msg(MAJOR_SWITCH, LED_G_ON);
            sendMsgToMcu(msg);
        }

        mDiskStatus = 0;
        FILE * fp = popen("mount|grep media_rw|cut -d ' ' -f 3", "r");
        if (fp) {
            char line[256] = {0};
            while (fgets(line, sizeof(line), fp)) {
                if (strstr(line, "/disk1")) {
                    mDiskStatus |= 0b01;
                } else if (strstr(line, "/sdcard1")) {
                    mDiskStatus |= 0b0100;
                }
                memset(line, 0, sizeof(line));
            }
            pclose(fp);
        }
        if (!(mDiskStatus & 0b01) && !access("/mnt/obb/ldisk", R_OK)) {
            FILE * pFile = fopen("/sys/block/sda/device/vendor", "r");
            if (pFile) {
                char vendor[128] = {0};
                fread(vendor, sizeof(vendor), 1, pFile);
                if (!strstr(vendor, "USB TO")) {
                    mDiskStatus |= 0b10;
                }
                fclose(pFile);
            }
        }
        if (!(mDiskStatus & 0b0100) && !access("/mnt/obb/lsdcard", R_OK)) {
            mDiskStatus |= 0b1000;
        }
        uint32_t status = ((mDiskStatus << 16) | mRecStatus);
        HostioManager::getInstance().mcuAgentServerSendMsg(MCU_MSG_TYPE_RECORD_STATUS, (const uint8_t*)&status, sizeof(status));
    }
}

void IOMsgHandler::mcuCarInfoSync(void)
{
    McuMsgCarInfoT carInfo;
    carInfo.accelerator     = mCan.mCarInfo.accelerator;
    carInfo.canTurn         = mCan.mCarInfo.turn;
    carInfo.canBrake        = mCan.mCarInfo.brake;
    carInfo.canDoorBits     = mCan.mCarInfo.door_open;

    carInfo.gear            = mCan.mCarInfo.gear;
    carInfo.canReverse      = mCan.mCarInfo.reverse;
    carInfo.EHB_state       = mCan.mCarInfo.EHB_state;
    carInfo.EHB_park_req    = mCan.mCarInfo.EHB_park_req;
    carInfo.EHB_park_done   = mCan.mCarInfo.EHB_park_done;

    carInfo.FLWheelSpdStat  = mCan.mCarInfo.FLWheelSpdStat;
    carInfo.FRWheelSpdStat  = mCan.mCarInfo.FRWheelSpdStat;
    carInfo.BLWheelSpdStat  = mCan.mCarInfo.BLWheelSpdStat;
    carInfo.BRWheelSpdStat  = mCan.mCarInfo.BRWheelSpdStat;
    carInfo.FLWheelSpd      = mCan.mCarInfo.FLWheelSpd;
    carInfo.FRWheelSpd      = mCan.mCarInfo.FRWheelSpd;
    carInfo.BLWheelSpd      = mCan.mCarInfo.BLWheelSpd;
    carInfo.BRWheelSpd      = mCan.mCarInfo.BRWheelSpd;

    carInfo.canSpeed        = mCan.mCarInfo.speed;
    carInfo.canMileSingle   = mCan.mCarInfo.mileSingle;
    carInfo.canMileTotal    = mCan.mCarInfo.mileTotal;
    carInfo.canFuelAvg      = mCan.mCarInfo.fuelAvg;
    carInfo.canFuelTotal    = mCan.mCarInfo.fuelTotal;
    carInfo.canTurnSpeed    = mCan.mCarInfo.turnSpeed;

    HostioManager::getInstance().mcuAgentServerSendMsg(MCU_MSG_TYPE_CAR_INFO, (const uint8_t*)&carInfo, sizeof(carInfo));
}

void IOMsgHandler::mcuStatusSync(void)
{
    mSyncMsg.vehicle_io = mVehicleIo;
    mSyncMsg.dvr_io = mDvrIo;
    mSyncMsg.sys = mSys;
    mSyncMsg.pwr_set = mPwrSet;
    mSyncMsg.lbs = mLbs;

    //模拟速度 左转 右转
    mSyncMsg.pulses_speed_x10 = mSpeedTurn.pulseSpeedx10;
    mSyncMsg.pulses_turnl = mSpeedTurn.pluseTurnL;
    mSyncMsg.pulses_turnr = mSpeedTurn.pluseTurnR;

    //can速度 左转 右转
    mSyncMsg.can_speed_x10 = mSpeedTurn.canSpeedx10;
    mSyncMsg.can_turnl = mSpeedTurn.canTurnL;
    mSyncMsg.can_turnr = mSpeedTurn.canTurnR;

    //选中速度 左转 右转
    mSyncMsg.speed_x10 = mSpeedTurn.selectSpeedx10;
    mSyncMsg.turnl = mSpeedTurn.selectTurnL;
    mSyncMsg.turnr = mSpeedTurn.selectTurnR;

    mSyncMsg.total_mileage= mTotal_mileage;
    getNetStatus(&mSyncMsg.csq[0], &mSyncMsg.net_info[0]);
    getIccid(mSyncMsg.iccid);
    getImei(mSyncMsg.imei);
    memcpy(mSyncMsg.adcRaw, mAdcRaw, sizeof(mAdcRaw));
    memcpy(mSyncMsg.adcVol, mAdcAct, sizeof(mAdcAct));
    mSyncMsg.accOff_event = mAccOffEvent;
    HostioManager::getInstance().mcuAgentServerSendMsg(MCU_MSG_TYPE_STAT, (const uint8_t*)&mSyncMsg, sizeof(mSyncMsg));
}

void IOMsgHandler::getWorkMode(void)
{
    static WorkModeE last_mode = WORK_MODE_MAX;
    char propValue[PROP_VALUE_MAX] = {0};

    memset(propValue, 0, sizeof(propValue));

    int len = __system_property_get(PROP_RW_MINIEYE_WORK_MODE, propValue);
    if(len == 0) {
        __system_property_set(PROP_RW_MINIEYE_WORK_MODE, "ft");
    }

    const char *p = propValue;
    if (!strcmp(p, "ft")) {
        mMode = WORK_MODE_FT;
    } else if(!strcmp(p, "aging")) {
        mMode = WORK_MODE_AGING;
    } else if(!strcmp(p, "setup")) {
        mMode = WORK_MODE_SETUP;
    } else if(!strcmp(p, "normal")) {
        mMode = WORK_MODE_NORMAL;
    } else if(!strcmp(p, "sample")) {
        mMode = WORK_MODE_SAMPLE;
    } else {
        mMode = WORK_MODE_FT;
    }

    /* 模式切换 */
    if (last_mode != mMode) {
        mSocRebootEnable = true;
        logi("work mode enter: %s\n", work_mode_name[mMode]);
    }
    last_mode = mMode;
}

void IOMsgHandler::accMonitor(void)
{
    static uint32_t acc_off_cnt, acc_on_cnt;
    static bool sysPwrOffDone = false;
    GpsTotalMiles & gpsTotalMiles = GpsTotalMiles::getInstance();

    gpsTotalMiles.hadAcc = mVehicleIo.acc;
    if (!mVehicleIo.acc && acc_off_cnt++ > 5 && !mAccOffEvent) { /* ACC OFF */
        logi("ACC OFF event\n");
        acc_off_cnt = 0;
        acc_on_cnt = 0;
        mAccOffEvent = true;
        MY_SPINLOCK_X(mPwrlock);
        mPowerDownPendingTime = my::timestamp::now();
        mLogger->mlog("acc off event");
    }

    if (mVehicleIo.acc && acc_on_cnt++ > 5 && mAccOffEvent) { /* ACC ON */
        logi("ACC ON event\n");
        acc_on_cnt = 0;
        acc_off_cnt = 0;
        sysPwrOffDone = false;
        mAccOffEvent = false;
        mPwrKeyRstDone = false;
        MY_SPINLOCK_X(mPwrlock);
        mPwrDn = 0;
        pwrkeyRebootMarkClear();
        mLogger->mlog("acc on event");

        //IOMsg msg(MAJOR_GPIO, GPIO_MINOR_BAT_ON);
        //sendMsgToMcu(msg);
        //logi("send bat pwr on cmd\n");
    }

    /* 根据ACC状态关机 */
    if (mPwrSet.pwrSys && mAccOffEvent && !sysPwrOffDone) {
        bool ret = tryPowerOff();
        if (ret) { /* 进入低功耗模式后 */
            mSysOffTime = my::timestamp::now(); /* 进入低功耗的时间 */
        }
    } else if (!mPwrSet.pwrSys && mAccOffEvent) { /* 确定syspwr 断电*/
        /* mcu返回的sysPwr状态*/
        sysPwrOffDone = true;
    }

    if (mHeartBeatTime.elapsed() > 5 * 1000) { /*5秒发送一次心跳*/
        IOMsg msg(MAJOR_HEART_BEAT, 0);
        sendMsgToMcu(msg);
        mHeartBeatTime = my::timestamp::now();
    }

    if (mSocRebootEnable) {
        /* 进入低功耗模式10分钟后复位一次8838G*/
        if (!mPwrSet.pwrSys && mSysOffTime.elapsed() > 600 * 1000) {
            if (!mPwrKeyRstDone && !pwrkeyRebootIsMark()) {
                pwrkeyRebootMark();

#if 0
                mLogger->mlog("reboot system");
                sync();
                sync();
                system("reboot");
#else
                mLogger->mlog("send cmd reset 8838G");
                sync();
                sync();
                logi("send power key reset cmd\n");
                IOMsg msg(MAJOR_PWRKEY_RST, 0);
                sendMsgToMcu(msg);
#endif
                mPwrKeyRstDone = true;
            }
        }
    }

}

void IOMsgHandler::systemPowerMonitor(void)
{
    if (mSys.power_off) { /* 掉电后关闭欠压状态 */
        mSys.power_low = 0;
    } else {
        /* 仅在低于10v 大于 8.9v 无法正常工作情况下才上报主电源欠压报警 */
        if (mAdcAct[ADC_CHN_VDD] < ADC_PWR_LOW_VAL) {
            if (!mSys.power_low) {
                mSys.power_low = 1;
                logi("main power low(%.2fv) enter", mAdcAct[ADC_CHN_VDD]);
                mLogger->mlog("main power low(%.2fv) enter", mAdcAct[ADC_CHN_VDD]);
            }
        } else {
            if (mSys.power_low) {
                logi("main power low(%.2fv) exit", mAdcAct[ADC_CHN_VDD]);
                mLogger->mlog("main power low(%.2fv) exit", mAdcAct[ADC_CHN_VDD]);
                mSys.power_low = 0;
            }
        }
    }

    /* 主电源掉电判断 */
    if (!mVehicleIo.acc && mAdcAct[ADC_CHN_VDD] < ADC_PWR_OFF_VAL) {
        if(!mSys.power_off) {
            mSys.power_off = 1;
            logi("main power off(%.2fv) enter", mAdcAct[ADC_CHN_VDD]);
            mLogger->mlog("main power off(%.2fv) enter", mAdcAct[ADC_CHN_VDD]);
            // 针对B1版本之后的机器，电池电压可以用来判断电池是否存在，超级电容电压也可以用来判断
            //
            //conf_t& sys = HostioManager::getInstance().mConfig;
            //if (sys.boardType >= IDVR_BOARD_TYPE::B1) {
            //    if (mAdcAct[ADC_CHN_BAT] <= ADC_BAT_ABSENT_VAL) { // 没有电池（只有超级电容）
                    mLogger->mlog("main power off cap(%.2fv), turn off 12V", mAdcAct[ADC_CHN_CAP]);
                    // 关闭12V电源
                    IOMsg msg(MAJOR_GPIO, MINOR_GPIO_PWR_12V_OFF); // 12V在MCU会随ACC打开
                    sendMsgToMcu(msg);
            //    }
            //}
        }
    } else {
        if(mSys.power_off) {
            mSys.power_off = 0;
            logi("main power off(%.2fv) exit", mAdcAct[ADC_CHN_VDD]);
            mLogger->mlog("main power off(%.2fv) exit", mAdcAct[ADC_CHN_VDD]);
        }
    }

    /*进入低功耗模式后，且外部电源掉电, 关闭电池*/
    if (!mPwrSet.pwrSys && mSys.power_off) {
        logi("dcin(%.1f) off, send bat-off\n", mAdcAct[ADC_CHN_VDD]); /* 网口adb日志无法输出，应该网卡已经断电 */
        mLogger->mlog("dcin(%.1f) off, send bat-off", mAdcAct[ADC_CHN_VDD]);
        sync();
        //IOMsg msg(MAJOR_GPIO, GPIO_MINOR_BAT_OFF);
        //sendMsgToMcu(msg);
    }
}

void IOMsgHandler::dumpStatusInfo(void)
{
    char logbuf[2048] = {0};

    if (mLogVerbose[LOG_TYPE_STATUS] > 0 && mDumpStatusTS.elapsed() >= 1000)
    {
        // 打印日志
        snprintf(logbuf, sizeof(logbuf), "gps_time[%u] mLbs[%f, %f, %.1f] sat %d located %d dir %.1f antenna %d speed[%.1f, %.1f] mileage %.2f "
                "acc %d alarm %d near %d far %d left %d right %d brake %d can_loss %d "
                "adc[Ver:%d(%.0f)-Cap:%d(%.2fV)-Tem:%d(%.2f℃)-Vdd:%d(%.2fV)-DcIn:%d(%.2fV)] "
                "mcuUpTime[%s] boardTempture %d  sys_pwr %d(0x%04x) pwr_low %d pwr_off %d McuRstSoc %d AutoCtlCap %d",
            mLbs.time,
            mLbs.lng_x1kw / 10000000.0, mLbs.lat_x1kw / 10000000.0, mLbs.alt_x10 / 10.0,
            mLbs.sat, mLbs.status, mLbs.dir_x100 / 100.0, mLbs.antenna,
            mSpeedTurn.selectSpeedx10 / 10.0, mLbs.speed_x10 / 10.0,
            mTotal_mileage,
            mVehicleIo.acc, mVehicleIo.emergency_alarm, mVehicleIo.normal_light,
            mVehicleIo.far_light, mVehicleIo.left_turn, mVehicleIo.right_turn, mVehicleIo.brake, mVehicleIo.can_signal_loss,
            mAdcRaw[ADC_CHN_VER],
            mAdcAct[ADC_CHN_VER],
            mAdcRaw[ADC_CHN_CAP],
            mAdcAct[ADC_CHN_CAP],
            mAdcRaw[ADC_CHN_TEM],
            mAdcAct[ADC_CHN_TEM],
            mAdcRaw[ADC_CHN_VDD],
            mAdcAct[ADC_CHN_VDD],
            mAdcRaw[ADC_CHN_DCIN],
            mAdcAct[ADC_CHN_DCIN],
            uptime(mMCUInfo.mcuUptime).c_str(),
            mMCUInfo.mcuTempture_x100,
            mPwrSet.pwrSys,
            mPwrSet.val,
            mSys.power_low,
            mSys.power_off,
            mFlagSta.fMcuRstSoc,
            mFlagSta.fAutoCtlCap);
        logd("%s", logbuf);
        mDumpStatusTS = my::timestamp::now();

    }

}

void IOMsgHandler::calcAdcVoltage()
{
    mAdcAct[ADC_CHN_VER] = mAdcRaw[ADC_CHN_VER] / 0x200;
#if 0
    mAdcAct[ADC_CHN_VDD] = mAdcRaw[ADC_CHN_VDD] * 3.3 * (1069.8) / (69.8 * 4096);
    if (mPwrSet.sys_pwr && !mSys.power_off) { /* 不在低功耗模式，电流很大，需要补偿 */
        mAdcAct[ADC_CHN_VDD] = mAdcAct[ADC_CHN_VDD] + 0.2 + (mAdcAct[ADC_CHN_VDD] * 0.2)/12; /* 补偿0.2-0.4 */
    }
#else
    mAdcAct[ADC_CHN_VDD] = mAdcRaw[ADC_CHN_VDD] * 3.45 * (1068) / (68 * 4096) + 0.4;    /* 补偿0.4 */
#endif
    mAdcAct[ADC_CHN_CAP] = mAdcRaw[ADC_CHN_CAP] * 3.45 * 2.941 / 4096;
    mAdcAct[ADC_CHN_TEM] = mAdcRaw[ADC_CHN_TEM] * 0; // TODO

    mAdcAct[ADC_CHN_DCIN] = mAdcRaw[ADC_CHN_DCIN] * 3.45 * (1068) / (68 * 4096);
}

void IOMsgHandler::proc(my::uchar major, my::uchar minor, const char* data1, int size1)
{
    GpsTotalMiles & gpsTotalMiles = GpsTotalMiles::getInstance();

    // logd("[IOMsgHandler::proc] %s\n", my::hex(my::constr(data1, size1)).c_str());
    switch (major)
    {
    case MAJOR_HOST: // 状态上报
        {
            my::string tmp;
            tmp.capacity(size1);
            tmp.assign(data1, size1);
            my::constr data(tmp);

            uint32_t mcu_tick=0;
            uint32_t resv[5];
            uint16_t resv16[5];
            uint16_t frame_crc_err=0, frame_len_err=0;

            int speedPluseFre = 0;

            int turnLPulseFre = 0;
            int turnRPulseFre = 0;

            //logd("[IOMsgHandler::proc] %s\n", my::hex(my::constr(data1, size1)).c_str());

            data >> my::ntoh
                 >> speedPluseFre
                 >> mVehicleIo.value
                 >> mAdcRaw[ADC_CHN_VER] >> mAdcRaw[ADC_CHN_CAP]
                 >> mAdcRaw[ADC_CHN_TEM] >> mAdcRaw[ADC_CHN_VDD]
                 >> mcu_tick >> mMCUInfo.mcuTempture_x100
                 >> frame_crc_err >> frame_len_err
                 >> turnLPulseFre >> turnRPulseFre >> mPwrSet.val;

            try {
                data >> my::ntoh >> mAdcRaw[ADC_CHN_DCIN] >> mFlagSta.val;
            } catch (...) {
                mAdcRaw[ADC_CHN_DCIN] = 0;
                mFlagSta.val = 0;
            }

            //logd("mAdcRaw[ADC_CHN_DCIN] is %d, mFlagSta.val is %d \n", mAdcRaw[ADC_CHN_DCIN], mFlagSta.val);

            /* 初始化 */
            mVehicleIo.can_signal_loss = 0;

            mMCUInfo.mcuUptime = mcu_tick / 1000;
            mSpeedTurn.lastPulseFreq = (speedPluseFre ? 1.0 * 64000U / speedPluseFre : 0);

            auto as = mCan.mConfig->mConfig.analog_cfg.analogSignals[AS_IO_HI_BEAM];
            if (as.enable && !as.polarity) {
                mVehicleIo.far_light = !mVehicleIo.far_light;
            }
            as = mCan.mConfig->mConfig.analog_cfg.analogSignals[AS_IO_LO_BEAM];
            if (as.enable && !as.polarity) {
                mVehicleIo.normal_light = !mVehicleIo.normal_light;
            }
            as = mCan.mConfig->mConfig.analog_cfg.analogSignals[AS_IO_BRAKE];
            if (as.enable && !as.polarity) {
                mVehicleIo.brake = !mVehicleIo.brake;
            }
            as = mCan.mConfig->mConfig.analog_cfg.analogSignals[AS_IO_TURNLAMP];
            if (as.enable && !as.polarity) {
                mVehicleIo.left_turn = !mVehicleIo.left_turn;
                mVehicleIo.right_turn= !mVehicleIo.right_turn;
            }
            as = mCan.mConfig->mConfig.analog_cfg.analogSignals[AS_IO_SOS];
            if (as.enable && !as.polarity) {
                mVehicleIo.emergency_alarm = !mVehicleIo.emergency_alarm;
            }
            mSpeedTurn.pluseTurnL = mVehicleIo.left_turn;
            mSpeedTurn.pluseTurnR = mVehicleIo.right_turn;

            float turnl = turnLPulseFre ? 64000U / turnLPulseFre: 0;
            float turnr = turnRPulseFre ? 64000U / turnRPulseFre: 0;
            //logd("[raw fre] speed %d left %d right %d\n", mSpeed_pulses_fre,  mTurnl_pulses_fre, mTurnr_pulses_fre);

            calibAspeed(mSpeedTurn.lastPulseFreq);
            updateCurrentSpeed();

            accMonitor(); /*do before status sync*/

            calcAdcVoltage();
            mMCUInfo.mcuTempture_x100 = (int16_t)(mAdcAct[ADC_CHN_TEM] * 100);

            mcuConfig();
            setPowerVol();
            setExtIoStatusToProperty();
            getWorkMode();

            mcuStatusSync();
            mcuCarInfoSync();
            systemPowerMonitor();

            dumpStatusInfo();
        }
        break;
    case MAJOR_UART: // 串口
        {
            switch (minor)
            {
                case EXT_UART_RS485:
                    {
                        logd("rs485 recv len %d\n", size1);
                        HostioManager::getInstance().mcuAgentServerSendMsg(MCU_MSG_TYPE_RS485, (const uint8_t*)data1, size1);
                    }
                    break;
                default:
                    break;
            }
        }
        break;
    case MAJOR_CAN:
        {
            if (minor == CAN_MINOR_SPEED_CAN0_MSG) {
                if (size1 != sizeof(UrtpCanT)) {
                    loge("Can Msg len error!\n");
                    break;
                }
                const UrtpCanT *p = reinterpret_cast<const UrtpCanT *>(data1);
                if(mCan.mVerbose > 0 || mLogVerbose[LOG_TYPE_CAN]) {
                    logd("can[%d] ID[0x%x] len[%d]: %02x-%02x-%02x-%02x-%02x-%02x-%02x-%02x\n",\
                            minor, p->id, p->len,\
                            p->data[0],p->data[1],p->data[2],p->data[3],\
                            p->data[4],p->data[5],p->data[6],p->data[7]);
                }
                mVehicleIo.can_signal_loss = 0;
                mCanMsgUpdateTime = my::timestamp::now();
                decodeCanMsg(p);
                HostioManager::getInstance().mcuAgentServerSendMsg(MCU_MSG_TYPE_RAW_CAN0, (const uint8_t*)data1, size1);
            } else if (minor == CAN_MINOR_DISP_CAN1_MSG) {
                HostioManager::getInstance().mcuAgentServerSendMsg(MCU_MSG_TYPE_RAW_CAN1, (const uint8_t*)data1, size1);
            } else if (minor == CAN_MINOR_STATISTICS) {
                    struct Can_stat {
                        uint32_t    can1_tx_msg;
                        uint32_t    can1_tx_act;
                        uint32_t    can1_rx_act;
                        uint32_t    can1_rx_msg;
                        uint32_t    can1_err;
                        uint32_t    can2_tx_msg;
                        uint32_t    can2_tx_act;
                        uint32_t    can2_rx_act;
                        uint32_t    can2_rx_msg;
                        uint32_t    can2_err;
                    } *canStat = (struct Can_stat *)data1;
                    logd("can1: tx-msg:%d tx-act:%d rx-act:%d rx-msg:%d err:%d can2: tx-msg:%d tx-act:%d rx-act:%d rx-msg:%d err:%d\n",\
                            canStat->can1_tx_msg, canStat->can1_tx_act, canStat->can1_rx_act, canStat->can1_rx_msg, canStat->can1_err,
                            canStat->can2_tx_msg, canStat->can2_tx_act, canStat->can2_rx_act, canStat->can2_rx_msg, canStat->can2_err);
            }
        }
        break;
    case MAJOR_VERSION:
        {
            if (minor == VERSION_BASE) {
                mMCUInfo.mcuVersionOk = true;
                logd("Mcu version(%d): %s", size1, data1);
                //mMCUInfo.mcuVersion.assign(data1);
                memcpy(mMCUInfo.mcuVersion, data1, size1);
                HostioManager::getInstance().mcuAgentServerSendMsg(MCU_MSG_TYPE_MCU_VERSION, (const uint8_t *)data1, size1);
            } else if (minor == VERSION_FIRMWARE_INFO) { /* firmware info */
                logd("recv Mcu firmware info\n");
                HostioManager::getInstance().mcuAgentServerSendMsg(MCU_MSG_TYPE_MCU_FIRMWARE_INFO, (const uint8_t *)data1, size1);
            } else if (minor == VERSION_AUTHEN) { /* authen ack */
                logd("recv Mcu authen ack\n");
                HostioManager::getInstance().mcuAgentServerSendMsg(MCU_MSG_TYPE_MCU_UPGRD_AUTHEN, (const uint8_t *)data1, size1);
            }
        }
        break;
    case MAJOR_RESET:
        {
            logd("mcu reset ack\n");
        }
        break;
    case MAJOR_BOOT_MODE:
        {
            if (minor == CMD_MCU_BLD_MODE) {
                logd("mcu boot bld mode\n");
                mLogger->mlog("mcu boot bld mode\n");
                HostioManager::getInstance().mcuAgentServerSendMsg(MCU_MSG_TYPE_MCU_BLD_MODE, NULL, 0);
            } else if (minor == CMD_MCU_APP_MODE) {
                logd("mcu boot app mode\n");
                mLogger->mlog("mcu boot app mode\n");
                HostioManager::getInstance().mcuAgentServerSendMsg(MCU_MSG_TYPE_MCU_APP_MODE, NULL, 0);
            } else if (minor == CMD_MCU_ALONE_MODE) {
                logd("mcu boot alone mode\n");
                mLogger->mlog("mcu boot alone mode\n");
            }
        }
        break;
    case MAJOR_UPGRADE:
        {
            logd("recv mcu upgrade ack\n");
            HostioManager::getInstance().mcuAgentServerSendMsg(MCU_MSG_TYPE_UPGRADE, (const uint8_t *)data1, size1);
        }
        break;
    case MAJOR_GET_REG:
        {
            struct REG
            {
                uint32_t addr;
                uint32_t val;
            };
            struct REG *reg = (REG *)data1;
            logd("the mcu reg(0x%08X) value is 0x%08X\n", reg->addr, reg->val);
        }
        break;
    default:
        {
            logw("[IOMsgHandler::proc] unkown message: major=[0x%02x], minor=[0x%02x], data=[%d].", major, minor, size1);
        }
    }
}

void IOMsgHandler::calibAspeed(float freq)
{
    uint64_t now;
    static int contigousBigAccl = 0;
    static uint64_t lastPulseTime;
    bool bValid = true;
    double speed = 0.0;
    static double last_speed = 0.0;
    double speedAccl = 0.0;

    double ratio = mCan.mConfig->mConfig.analog_cfg.aspeed.ratio;

    now = systemTime();

#if 0
    if (!mCan.mConfig->mConfig.analog_cfg.aspeed.enable) {
        return;
    }
#endif

    //For factory test scenario
    if(mMode == WORK_MODE_FT) {
        mGpsCalibEnable = false;
        ratio = 1.0;
    }

    if (mCan.mConfig->mConfig.analog_cfg.aspeed.disableGpsCal) {
        mGpsCalibEnable = false;
    }

    if (mGpsCalibEnable) {
        if (mGpsCalibRatio >= 1e-6) {
            ratio = mGpsCalibRatio;
        }
    }
    speed = ratio * freq;
    //logd("gps %.1f aspeed %.1d fre %.2f ratio %f", mLbs.speed_x10/10.0, speed, freq, ratio);

    speedAccl = (speed - last_speed);
    uint32_t ms_add = (now - lastPulseTime)/1000000;


    if (mLogVerbose[LOG_TYPE_ASPEED] > 0) {
        logd("gps %.1f aspeed %.1f last %.1f ratio %lf fre %.1f ms %u spd_abs %.1lf\n",
              mLbs.speed_x10 / 10.0, speed, last_speed, ratio, freq, ms_add, fabs(speedAccl));
    }

    if (::fabs(speedAccl) > 20) {
        logw("Drop invalid speed %f last speed %f accl %f", speed, last_speed, speedAccl);
        mLogger->mlog("Drop invalid speed %f last speed %f accl %f", speed, last_speed, speedAccl);
        contigousBigAccl ++;
        bValid = false;
    }

    if (contigousBigAccl >= 15) {
        logw("contigous flowout speed %.1f\n", speed);
        mLogger->mlog("contigous flowout speed %.1f\n", speed);
        bValid = true;
    }
    if (bValid) {
        last_speed = speed;
        mSpeedTurn.pulseSpeedx10 = speed * 10;
        lastPulseTime = now;
        contigousBigAccl = 0;
    }
}

bool IOMsgHandler::queryLastGps(double &lat, double &lng)
{
    char prop[PROP_VALUE_MAX] = {0};
    double lat_df = 22.536779;
    double lng_df = 113.95273;

    lat = lat_df;
    lng = lng_df;
    if (__system_property_get(PROP_PERSIST_MINIEYE_LASTGPSLOCATION, prop)) {
        sscanf(prop, "Location %lf,%lf", &lat_df, &lng_df);
        lat = lat_df;
        lng = lng_df;
    }

    return true;
}

void IOMsgHandler::get_last_gps_location(void)
{
    double lat, lng;
    char prop[PROP_VALUE_MAX] = {0};

    if (__system_property_get(PROP_RW_MINIEYE_GPSLASTLOCATION, prop)) {
        logd("get last gps location %lf %lf, int %d %d\n", lat, lng, mLbs.lat_x1kw, mLbs.lng_x1kw);
        sscanf(prop, "Location %lf,%lf", &lat, &lng);
    } else if (queryLastGps(lat, lng)) {
        snprintf(prop, sizeof(prop), "Location %f,%f", lat, lng);
        __system_property_set(PROP_RW_MINIEYE_GPSLASTLOCATION, prop);
    }

    mLbs.lat_x1kw = (int)(lat * 10000000);
    mLbs.lng_x1kw = (int)(lng * 10000000);
}


// 函数功能：根据gps速度，更新模拟参数 校正参数 并保存到property中
// mGpsCalibRatio中的值会在handleAspeed中使用
void IOMsgHandler::updateAspeedCalibParam(void)
{
    double calibRatio = 0.0f, speedX10 = (double)mLbs.speed_x10;
    CalibMidState midstate;
    float freq = mSpeedTurn.lastPulseFreq;

    int32_t ret = mGpsSpdCalib->Update(speedX10 / 10, freq, &calibRatio, &midstate);

    if (mLogVerbose[LOG_TYPE_ASPEED] > 0) {
        logd("Ret %02d | %6.3f %6.3f | size %05d sum %8.3f avg %8.3f var %8.3f stddev %8.3f result %8.3f",
                ret,
                speedX10 / 10, freq,
                midstate.sampleNum,
                midstate.sum,
                midstate.avg,
                midstate.variance,
                midstate.stddev, calibRatio);
    }

    if (0 == ret) {
        mLogger->mlog("calib done Ratio %f", calibRatio);
        mGpsCalibRatio = calibRatio;

        char value[PROP_VALUE_MAX];
        memset(value, 0, sizeof(value));
        snprintf(value, sizeof(value), "%f", calibRatio);
        __system_property_set(PROP_RW_MINIEYE_GPS_CALIB_RATIO, value);

        memset(value, 0, sizeof(value));
        snprintf(value, sizeof(value), "%f", midstate.stddev);
        __system_property_set(PROP_RW_MINIEYE_CALIB_STDDEV, value);
    }
}

void IOMsgHandler::updateCurrentSpeed(void)
{
    static int last_speed_x10 = 0;
    int fakeSpeed = -1;
    getDebugSpeed(fakeSpeed); /* 如果假速度存在就使用假速度 */
    struct timespec now;
    static struct timespec last = {0, 0};
    clock_gettime(CLOCK_MONOTONIC, &now);

    if (fakeSpeed >= 0) {
        mSpeedTurn.selectSpeedx10 = fakeSpeed;
    } else {
        /* can 速度没有判断使能， 放在前面 */
        if (mCanSpeedUpdateTime.elapsed() < 2 * 1000) {
            mSpeedTurn.canSpeedx10 = mCan.mCarInfo.speed * 10;
            if (mMode != WORK_MODE_FT) {
                mSpeedTurn.selectSpeedx10 = mSpeedTurn.canSpeedx10;
            }
            //logd("--update can speed\n");
        }

        /* GPS 速度 */
        if (mCan.mConfig->mConfig.useGpsSpeed) {
            //logd("--update gps speed\n");
            mSpeedTurn.selectSpeedx10 = mLbs.speed_x10;
        }

        /* 设置模拟速度 */
        if (mCan.mConfig->mConfig.analog_cfg.aspeed.enable) {
            mSpeedTurn.selectSpeedx10 = mSpeedTurn.pulseSpeedx10;
            //logd("aspeed %d\n", mSpeed_x10/10);
        }
    }

    /* can 转向没有判断使能， 放在前面 */
    if (mCanTurnUpdateTime.elapsed() < 2 *1000) {
        mSpeedTurn.canTurnL = mCan.mCarInfo.turn & 0x01;
        mSpeedTurn.canTurnR = (mCan.mCarInfo.turn >> 1) & 0x01;

        if (mMode != WORK_MODE_FT) {
            mSpeedTurn.selectTurnL = mSpeedTurn.canTurnL;
            mSpeedTurn.selectTurnR =  mSpeedTurn.canTurnR;
        }
#if 0
        logd("--update can turn %d, selectTurnL:%d, selectTurnR:%d!\n",
                mCan.mCarInfo.turn, mSpeedTurn.selectTurnL, mSpeedTurn.selectTurnR);
#endif
    }
    /* 设置模拟转向 */
    if (mCan.mConfig->mConfig.analog_cfg.analogSignals[AS_IO_TURNLAMP].enable) {
        mSpeedTurn.selectTurnL = mSpeedTurn.pluseTurnL;
        mSpeedTurn.selectTurnR = mSpeedTurn.pluseTurnR;
    }

    /* 如果使用的can速度或者转向 */
    if (mCanMsgUpdateTime.elapsed() >= 4000) { /* can消息超时 */
        mVehicleIo.can_signal_loss = 1;
        if (mMode != WORK_MODE_FT) {
            if (!mCan.mConfig->mConfig.useGpsSpeed && !mCan.mConfig->mConfig.analog_cfg.aspeed.enable &&
                (fakeSpeed < 0)) {
                /* 使用can速度才清0 */
                mSpeedTurn.selectSpeedx10 = 0;
            }

            if (!mCan.mConfig->mConfig.analog_cfg.analogSignals[AS_IO_TURNLAMP].enable) {
                mSpeedTurn.selectTurnL = 0;
                mSpeedTurn.selectTurnR = 0;
            }
        }
    }

    if (mAdisp_mode == ADISP_SPEED) { /*adas display speed mode */
        if (mSpeedTurn.selectSpeedx10 != last_speed_x10) {
            mCan.sendSpeedToDisp(mSpeedTurn.selectSpeedx10/10);
        }
    }

    if (mLogVerbose[LOG_TYPE_STATUS] >= 1 && now.tv_sec >= last.tv_sec + 5) {
        last = now;
        mLogger->mlog("mcu(%s) soc(%s) acc %d sys_pwr %d 12v %d 5v %d cap_char %d cap_dischar %d "
            "McuRstSoc %d AutoCtlCap %d pwr_off %d pwr_low %d "
            "DIN %.1fv speed_x10 %u turnl %d turnr %d locate %d lon %f lat %f sat %d antenna %d tempture %.2f",
                uptime(mMCUInfo.mcuUptime).c_str(),\
                uptime(now.tv_sec).c_str(),\
                mVehicleIo.acc,\
                mPwrSet.pwrSys,\
                mPwrSet.pwrExt12v,\
                mPwrSet.pwrExt12v,\
                mPwrSet.batEnchar,\
                mPwrSet.batDischar,\
                mFlagSta.fMcuRstSoc,\
                mFlagSta.fAutoCtlCap,\
                mSys.power_off,\
                mSys.power_low,\
                mAdcAct[ADC_CHN_VDD],\
                mSpeedTurn.selectSpeedx10,\
                mSpeedTurn.selectTurnL,\
                mSpeedTurn.selectTurnR,\
                mLbs.status,\
                mLbs.lng_x1kw / 10000000.0,\
                mLbs.lat_x1kw / 10000000.0,\
                mLbs.sat,\
                mLbs.antenna,\
                mMCUInfo.mcuTempture_x100/100.0);
    }
    last_speed_x10 = mSpeedTurn.selectSpeedx10;

    if (mbCalcMileageBySpd) {
        double avgSpd = 0.0;
        if (mSpdListTs.elapsed() >= 1000) {
            mSpdListTs = my::timestamp::now();
            for (auto i : mSpdList) {
                avgSpd += i;
            }
            avgSpd /= (mSpdList.size() + !mSpdList.size());
            logd("avgSpd = %f", avgSpd);
            // 将根据速度算出的1s的里程，加到 diff里程
            saveTotalMiles(avgSpd);
            mSpdList.clear();
        }
        double spd = (double)mSpeedTurn.selectSpeedx10 / 36000;
        mSpdList.push_back(spd);
    }

}

/*
    canId:can报文的id
    sig:can信号参数
    return:true是can信号的报文；false不是can信号的报文
*/
bool IOMsgHandler::isCanSig(uint32_t canId, const can_signal &sig)
{
    if ((canId == sig.id)
        || ((sig.mask != 0) && ((canId & sig.mask) == (sig.id & sig.mask)))) {
        /*
            列表模式下报文id与can id一致则判定该报文是can信号的报文。
            掩码过滤模式下只关注屏蔽码mask中值为1的bit位，我们称之为确定位；验证码id中确定位的值为确定值；
            报文canId的确定位的值与验证码id的确定值一致则判定该报文为can信号的报文。
            例如：mask的bit0~bit3为1即mask=0xf;id=0x3;
                  报文id的bit0~bit3的值为0x3的所有报文都是can信号的报文;如0x13\0x23都是符合要求的can报文。
        */
        return true;
    } else {
        return false;
    }
}

uint8_t IOMsgHandler::decodeCanMsg(const UrtpCanT *pCan)
{
    int ret = 0;

    for (auto & r : mCan.mConfig->mConfig.csMap) {
        if (isCanSig(pCan->id, r.second)) {
            double value = 0;
            can_decode_signal(&r.second, pCan->data, pCan->len, &value);
            switch (r.first) {
                case CS_SPEED: {
                    mCan.mCarInfo.speed = value;
                    mCanSpeedUpdateTime = my::timestamp::now();
                    break;
                }
                case CS_LEFT:
                case CS_RIGHT:
                {
                    static std::map<CS_TYPE_E, my::timestamp> last_on;
                    mCanTurnUpdateTime = my::timestamp::now();
                    can_decode_signal(&r.second, pCan->data, pCan->len, &value);
                    uint8_t turn = 1 + (r.first == CS_RIGHT);
                    if (value) {
                        mCan.mCarInfo.turn |= turn;
                        last_on[r.first] = mCanTurnUpdateTime;
                    } else {
                        auto it = last_on.find(r.first);
                        /* turn off > 600 ms */
                        if ((it == last_on.end()) ||
                            (it->second.elapsed() > 600)) {
                            mCan.mCarInfo.turn &= ~(turn);
                        }
                    }
                    break;
                }
                case CS_DOOR_FRONT:
                case CS_DOOR_MID:
                {
                    static std::map<CS_TYPE_E, my::timestamp> last_open;
                    mCanDoorOpenStatTS = my::timestamp::now();
                    can_decode_signal(&r.second, pCan->data, pCan->len, &value);
                    uint8_t door = 1 + (r.first == CS_DOOR_MID);
                    if (value) {
                        mCan.mCarInfo.door_open |= door;
                        last_open[r.first] = mCanDoorOpenStatTS;
                    } else {
                        auto it = last_open.find(r.first);
                        /* turn off > 600 ms */
                        if ((it == last_open.end()) ||
                            (it->second.elapsed() > 600)) {
                            mCan.mCarInfo.door_open &= ~(door);
                        }
                    }
                    break;
                }
                case CS_REVERSE: {
                    mCan.mCarInfo.reverse = !!value;
                    break;
                }
                case CS_BRAKE: {
                    mCan.mCarInfo.brake = !!value;
                    break;
                }
                case CS_ACCELERATOR: {
                    mCan.mCarInfo.accelerator = (uint8_t)value;
                    break;
                }
                case CS_GEAR: {
                    mCan.mCarInfo.gear = (uint8_t)value;
                    break;
                }
                case CS_EHB_STATE: {
                    mCan.mCarInfo.EHB_state = !!value;
                    break;
                }
                case CS_EHB_PARK_REQ: {
                    mCan.mCarInfo.EHB_park_req = !!value;
                    break;
                }
                case CS_EHB_PARK_DONE: {
                    mCan.mCarInfo.EHB_park_done = !!value;
                    break;
                }
                case CS_FRONT_LEFT_WHEEL_SPD: {
                    mCan.mCarInfo.FLWheelSpd = value;
                    break;
                }
                case CS_FRONT_LEFT_WHEEL_STAT: {
                    mCan.mCarInfo.FLWheelSpdStat = (uint8_t)value;
                    break;
                }
                case CS_FRONT_RIGHT_WHEEL_SPD: {
                    mCan.mCarInfo.FRWheelSpd = value;
                    break;
                }
                case CS_FRONT_RIGHT_WHEEL_STAT: {
                    mCan.mCarInfo.FRWheelSpdStat = (uint8_t)value;
                    break;
                }
                case CS_BACK_LEFT_WHEEL_SPD: {
                    mCan.mCarInfo.BLWheelSpd = value;
                    break;
                }
                case CS_BACK_LEFT_WHEEL_STAT: {
                    mCan.mCarInfo.BLWheelSpdStat = (uint8_t)value;
                    break;
                }
                case CS_BACK_RIGHT_WHEEL_SPD: {
                    mCan.mCarInfo.BRWheelSpd = value;
                    break;
                }
                case CS_BACK_RIGHT_WHEEL_STAT: {
                    mCan.mCarInfo.BRWheelSpdStat = (uint8_t)value;
                    break;
                }
                case CS_MILE_TOTAL: {
                    mCan.mCarInfo.mileTotal = value;
                    break;
                }
                case CS_MILE_TRIP: {
                    mCan.mCarInfo.mileSingle = value;
                    break;
                }
                case CS_FUEL_TOTAL: {
                    mCan.mCarInfo.fuelTotal = value;
                    break;
                }
                case CS_FUEL_AVG: {
                    mCan.mCarInfo.fuelAvg = value;
                    break;
                }
                case CS_TURN_SPEED: {
                    mCan.mCarInfo.turnSpeed = value;
                    break;
                }
                default: {
                    break;
                }
            }
        }
    }

    if(mCan.mVerbose > 0 || mLogVerbose[LOG_TYPE_CAN]) {
        logd("CAN: turn %d speed %.2f brake %d reverse %d mile_single %10f mile_total %10f fuel_total %10f fuel_avg %10f turn_speed %10f\n",
            mCan.mCarInfo.turn, mCan.mCarInfo.speed, mCan.mCarInfo.brake, mCan.mCarInfo.reverse, mCan.mCarInfo.mileSingle, mCan.mCarInfo.mileTotal, mCan.mCarInfo.fuelTotal, mCan.mCarInfo.fuelAvg, mCan.mCarInfo.turnSpeed);
    }
    return ret;
}

void IOMsgHandler::onEvUartDisConnected()
{
    mLogger->mlog("IOMsgHandler uart %s disconnect!", UART_DEVICE);
    return;
}

bool IOMsgHandler::onEvUartRecved(const char *data, int32_t dataLen)
{
    onDataRecv(data, dataLen);
    return true;
}

bool IOMsgHandler::setLogVerbose(LOG_TYPE_CTRL_E key, int32_t level)
{
    mLogVerbose[key] = level;
    if (key == LOG_TYPE_GPS) {
        mGpsHandler.setDebugLevel(level);
    }
    return true;
}

/**
  * @brief: 保存总里程
  * @param {double} miles
  * @return {bool}
 **/
bool IOMsgHandler::saveTotalMiles(double miles)
{
    bool isNeedSave = false;
    static uint8_t prePowerOff = 0;

    addDiffMiles(miles);

    // 主电源状态 接通 -> 断开
    if (prePowerOff != mSys.power_off) {
        prePowerOff = mSys.power_off;
        if (1 == mSys.power_off) {
            isNeedSave = true;
        }
    }

    // 间隔10s存一次
    if (mTotalMilesTs.elapsed() > 10 * 1000) {
        mTotalMilesTs = my::timestamp::now();
        isNeedSave = true;
    }

    if (isNeedSave) {
        char value[PROP_VALUE_MAX] = {0};

        // 从fileProp中读取出记录的总里程
        double totalMiles = 0;
        FILE_PROP_RET ret = mFilePropTotalMile->getProp(value);
        if (ret != FILE_PROP_RET::OK) { // 保存失败就不要再进行后面的操作了，避免清零
            logd("failed to get file prop %s in totalMiles (err: %s)", mDataPath, FileProp::errToString(ret).c_str());

            // 存储路径已存在，并且文件不存在 则创建一个初始值为0.0的文件
            if (0 == access(mDataPath, R_OK) && FILE_PROP_RET::FILE_ERR == ret) {
                mFilePropTotalMile->setProp("0.0");
            } else {
                return false;
            }
        } else {
            totalMiles = atof(value);
            setTotalMiles(totalMiles);
            logd("succ to get file prop %s in totalMiles %lf, %s", mDataPath, totalMiles, value);
        }

        // 加上diff里程
        addTotalMiles(getDiffMiles());

        // 将里程重新写回fileProp
        snprintf(value, sizeof(value), "%lf", getTotalMiles());
        ret = mFilePropTotalMile->setProp(value);

        if (ret != FILE_PROP_RET::OK) {
            logd("failed to set file prop in %s totalMiles to %s (err: %s)", mDataPath, value, FileProp::errToString(ret).c_str());
            return false;
        } else {
            // fileProp写成功则 将diff里程清零
            clearDiffMiles();
            logd("succ to set file prop in %s totalMiles to %lf", mDataPath, getTotalMiles());
        }
    }
    return true;
}

void IOMsgHandler::updateLbs(LBS *lbs)
{
    mLbs = *lbs;

    // 使用gps算里程
    if (!mbCalcMileageBySpd) {
        // 已定位，计算两个坐标之间的距离，并累加得到总里程
        if (lbs->status) {
            GpsTotalMiles &calMiles = GpsTotalMiles::getInstance();
            double miles = calMiles.getDistance((double)lbs->lat_x1kw / 10000000.0, (double)lbs->lng_x1kw / 10000000.0, lbs->sig_level);
            // 根据gps坐标算出diff里程。
            saveTotalMiles(miles);
        }
    }
}

bool IOMsgHandler::sendMsgToMcu(IOMsg& msg)
{
    if (msg.data.length() < 8)
        return false;

    // 填入长度
    my::ushort len = msg.data.length() - 8;
    msg.data.write(6, len);

    // 计算消息体校验码
    char chksum = 0;
    char* p = &msg.data[8];
    for (my::ushort i = 0; i < len; i++)
    {
        chksum += *p++;
    }
    msg.data.write(5, chksum);

    // 计算消息头校验码
    p = msg.data;
    p[2] = p[3] + p[4] + p[5] + p[6] + p[7];

    logd("[IOMsgHandler::send] %s\n", my::hex(my::constr(p, msg.data.length())).c_str());

    evUartsendData(p, msg.data.length());

    return 0;
}

