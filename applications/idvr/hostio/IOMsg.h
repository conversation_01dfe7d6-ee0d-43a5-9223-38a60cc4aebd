#ifndef __IOMSG__
#define __IOMSG__

#include "mystd.h"

enum
{
    MAJOR_HOST = 0x00,
    MAJOR_HEART_BEAT,
    MAJOR_VERSION,
    MAJOR_RESET,
    MAJOR_PWRKEY_RST,
    MA<PERSON><PERSON>_UPGRADE,
    MAJOR_BOOT_MODE,
    MAJOR_GPIO = 0x10,
    MAJOR_UART,
    MAJOR_UART_CONFIG,
    MAJOR_CAN,
    MAJOR_GET_REG,
    MAJOR_SWITCH,
};

enum {
    EXT_UART_RS485,
};

enum {
    FT_SOC_RS232_1,
    FT_SOC_RS232_2,
};

enum{
    CMD_MCU_BLD_MODE,
    CMD_MCU_APP_MODE,
    CMD_MCU_ALONE_MODE,
};

enum {
    CAN_MINOR_SPEED_CAN0_MSG,
    CAN_MINOR_DISP_CAN1_MSG,
    CAN_MINOR_CONFIG,
    CAN_MINOR_SET_FILTER,
    CAN_MINOR_CLEAR_FILTER,
    CAN_MINOR_STATISTICS,
    CAN_MINOR_CLEAR_STATISTICS,
};

enum{
    VERSION_BASE,
    VERSION_FIRMWARE_INFO,
    VERSION_AUTHEN,
};

class MsgBase
{
public:
    template<class T>
    inline my::string& operator<<(const T& t)
    {
        return data << t;
    }


public:
    my::string data; // 消息体
};

// IO消息(1314协议)
class IOMsg : public MsgBase
{
public:
	IOMsg(my::uchar major = 0, my::uchar minor = 0)
    {
        my::uchar chkCode = 0;
        my::ushort len = 0;
        data = "";

        data << my::hton << (char)0x13 << (char)0x14;
        data << chkCode; // 消息头校验码(1B)
        data << my::hton << major << minor;
        data << chkCode << len; // 消息体校验码(1B) + 消息体长度(2B)
    }

	void set(my::uchar major, my::uchar minor)
    {
        my::uchar chkCode = 0;
        my::ushort len = 0;
        data = "";

        data << my::hton << (char)0x13 << (char)0x14;
        data << chkCode; // 消息头校验码(1B)
        data << my::hton << major << minor;
        data << chkCode << len; // 消息体校验码(1B) + 消息体长度(2B)
    }
};

#endif

