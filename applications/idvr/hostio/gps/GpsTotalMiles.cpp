#include "GpsTotalMiles.h"
#include "idvrProperty.h"

SINGLETON_STATIC_INSTANCE(GpsTotalMiles);

GpsTotalMiles::GpsTotalMiles()
{
    char value[PROP_VALUE_MAX];
    int len = 0;
    gpsA = 6378245.0;
    gpsEE = 0.00669342162296594323;
    latitude = 0.0;
    longitude = 0.0;
    gpsTotalMiles = 0.0;
    latitude_GCJ02 = 0.0;
    longitude_GCJ02 = 0.0;
    lastLatitude_GCJ02 = 0.0;
    lastLongitude_GCJ02 = 0.0;
    gpsPI               = 3.141592654;

    gpsOutFd = -1;
    endFlag  = 0;
    readonec = 1;
    gpslinecount =0;
    distanceErrorCount = 0;

    startCalculate    = false;
    hadAcc            = false;
    onlyGpsSpeed      = false;
    samplingInterval  = 0;

    //__system_property_set(PROP_PERSIST_MINIEYE_ONLY_GPS_SPEED_ENABLE, "1");

    memset(value, 0, sizeof(value));
    len = __system_property_get(PROP_PERSIST_MINIEYE_ONLY_GPS_SPEED_ENABLE, value);
    if (len > 0) {
       onlyGpsSpeed = atoi(value);
       logd("CalGpsMiles get gps onlyGpsSpeed %d", onlyGpsSpeed);
    } else {
        onlyGpsSpeed = true;
    }

}

GpsTotalMiles::~GpsTotalMiles() {

    if(gpsOutFd != -1) {
        close(gpsOutFd);
    }

    if(gpsFp) {
        fclose(gpsFp);
    }
}

bool GpsTotalMiles::outOfChina(double lat, double lon) {

    if (lon < 72.004 || lon > 137.8347) {
        return true;
    }
    if (lat < 0.8293 || lat > 55.8271) {
        return true;
    }
    return false;
}

double GpsTotalMiles::WGS84ToGCJ02Lat(double x, double y) {

    double ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * sqrt(fabs(x));
    ret += (20.0 * sin(6.0 * x * gpsPI) + 20.0 * sin(2.0 * x * gpsPI)) * 2.0 / 3.0;
    ret += (20.0 * sin(y * gpsPI) + 40.0 * sin(y / 3.0 * gpsPI)) * 2.0 / 3.0;
    ret += (160.0 * sin(y / 12.0 * gpsPI) + 320 * sin(y * gpsPI / 30.0)) * 2.0 / 3.0;
    return ret;
}

double GpsTotalMiles::WGS84ToGCJ02Lon(double x, double y) {

    double ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 *  sqrt( fabs(x));
    ret += (20.0 *  sin(6.0 * x *  gpsPI) + 20.0 *  sin(2.0 * x *  gpsPI)) * 2.0 / 3.0;
    ret += (20.0 *  sin(x *  gpsPI) + 40.0 *  sin(x / 3.0 *  gpsPI)) * 2.0 / 3.0;
    ret += (150.0 *  sin(x / 12.0 *  gpsPI) + 300.0 *  sin(x / 30.0 *  gpsPI)) * 2.0 / 3.0;
    return ret;
}

int GpsTotalMiles::WGS84ToGCJ02(double wgLat, double wgLon,double *mgLat,double *mgLon) {

//    if (outOfChina(wgLat, wgLon)) {
//        logd("CalGpsMiles WGS84ToGCJ02 error outOfChina ");
//        return -1;
//    }

    double dLat = WGS84ToGCJ02Lat(wgLon - 105.0, wgLat - 35.0);
    double dLon = WGS84ToGCJ02Lon(wgLon - 105.0, wgLat - 35.0);
    double radLat = wgLat / 180.0 *  gpsPI;
    double magic =  sin(radLat);
    magic = 1 - gpsEE * magic * magic;
    double sqrtMagic =  sqrt(magic);
    dLat = (dLat * 180.0) / ((gpsA * (1 - gpsEE)) / (magic * sqrtMagic) *  gpsPI);
    dLon = (dLon * 180.0) / (gpsA / sqrtMagic *  cos(radLat) *  gpsPI);
    *mgLat = wgLat + dLat;
    *mgLon = wgLon + dLon;
    return 0;
}

double GpsTotalMiles::distance(double lat1, double lon1, double lat2, double lon2)
{
    double latitude1, longitude1, latitude2, longitude2;
    double dlat, dlon;
    latitude1 = lat1;
    longitude1 = lon1;
    latitude2 = lat2;
    longitude2 = lon2;
    double a, c, distance;
    dlon = fabs((longitude2 - longitude1)) * gpsPI / 180.0;
    dlat = fabs((latitude2 - latitude1)) * gpsPI / 180.0;
    a = (sin(dlat / 2.0) * sin(dlat / 2.0)) + cos(latitude1 * gpsPI / 180.0) * cos(latitude2 * gpsPI / 180.0) * (sin(dlon / 2.0) * sin(dlon / 2.0));
    if (a == 1.0) {
        c = gpsPI;
    }else {
        c = 2 * atan(sqrt(a) / sqrt(1 - a));
    }
    distance = 6378137.0 * c;
    return distance;
}

double GpsTotalMiles::getDistance(double lat, double lon, unsigned char gpsSig)
{

    char onePoint[100] = {0};
    double miles = 0.00000;
    double restrictMiles = 0.00001;
    double seconds = 0.0;
    struct timespec now;
    static struct timespec last;
    bool   hadMilse = false;

    static unsigned char goodSigCount = 0;
    char value[PROP_VALUE_MAX];
    clock_gettime(CLOCK_MONOTONIC, &now);
    int ret = WGS84ToGCJ02(lat,lon,&latitude_GCJ02,&longitude_GCJ02);
    if (ret != 0) {
        logd("getDistance error return 0.00;");
        return 0.00;
    }
    if ( (lastLatitude_GCJ02 == latitude_GCJ02) && (lastLongitude_GCJ02 == longitude_GCJ02) ) {
        logd("latitude_GCJ02 and longitude_GCJ02 not change;");
        return 0.00;
    }

    if (startCalculate) {
        samplingInterval++;
        if(samplingInterval == SAMPLINGINTERVAL) {
            samplingInterval = 0;
            //计算两点坐标距离单位是km
            miles = distance(lastLatitude_GCJ02, lastLongitude_GCJ02, latitude_GCJ02, longitude_GCJ02) / 1000.0;
            seconds = ( now.tv_sec - last.tv_sec) + ( (now.tv_nsec - last.tv_nsec)/10000000000.0);
            restrictMiles = (MAXSPEEDKMSECOND * seconds);
            //logd("CalGpsMiles GetDistance interval seconds=%f,miles =%f,restrictMiles=%f,hadAcc=%d",seconds,miles,restrictMiles,hadAcc);
            /*
            有acc的时候才统计gsp里程，否则原地gps坐标也是在小范围内变化的。
            测试30分钟，gps固定不动里程变化：287.5659 - 287.5439 = 0.022km = 22米
            算出两点距离和时间x最大速度120km/h得到的距离对比，不能超过这个距离，否则就当非法坐标不用于计算里程。
            */

            /*
                假设这样的场景：第30s的时候gps位置是(1,1), 到31s的时候gps发生很大的漂移(速度大于120km/h)坐标是(10,20),这样肯定不满足if条件，导致lastLatitude_GCJ02 lastLongitude_GCJ02
                还是(1,1)，32s的时候gps继续漂移是(10,21)，直到38s的时候gps定位准确了这时正确的坐标是(11,22)因为车一直在跑,此时的last坐标还是
                (1,1),假设坐标（11.22）-（1.1）= 1km，但是 seconds 一般是1s左右，算出来的 restrictMiles 一般是0.0333km此时不满足if也不更新last坐标 也就是
                经过长时间的gps不准后就算gps坐标变准确了 还是会导致if不满足，导致实车可能会出现当前这次Trips里程不增加。
                解决办法：
                1.last = now; 更新位置放到if里面, seconds不是固定的1s左右，会把gps发生漂移的时间统计进来restrictMiles不再是0.0333km左右，。
                2.增加万一还是发生了这种情况的恢复机制。

                注：
                新版本也可能不会有这种情况因为gps_handler函数里有if (mInfo.PDOP >= 6.0 || mInfo.speed >= 150) { / 信号质量太差
                这个判断限制并不是只看mLbs.status的值，第一测试版本094h是没有经过这个判断
                直接用的int nread = nmea_parse(&mParser, (const char *)data1, size1, &mInfo);后判断mLbs.status 就用坐标数据。
            */

            if(miles <= restrictMiles) {
                if(hadAcc) {
                    gpsTotalMiles += miles;
                    hadMilse = true;
                }
                lastLatitude_GCJ02 = latitude_GCJ02;
                lastLongitude_GCJ02 = longitude_GCJ02;
                last = now;
            }else {
                distanceErrorCount++;
                if(gpsSig >= 2) {
                    goodSigCount++;
                    if( goodSigCount >= 6) {//gps发生漂移后，信号强度很好超过6次，也就是6s左右的时间里都很好，则强制更新上一次坐标值。
                        lastLatitude_GCJ02 = latitude_GCJ02;
                        lastLongitude_GCJ02 = longitude_GCJ02;
                        last = now;
                        goodSigCount = 0;
                    }
                }
                logd("distanceErrorCount=%d,miles =%f,seconds=%f",distanceErrorCount,miles,seconds);
            }
        }
       // last = now;
    }

    if (startCalculate == 0) {
        lastLatitude_GCJ02 = latitude_GCJ02;
        lastLongitude_GCJ02 = longitude_GCJ02;
        startCalculate = 1;
        last = now;//init last.
        logd("startCalculate = 1;");
    }

    if (endFlag == 0) {
        sprintf(onePoint, "[%10.14f,%10.14f]",longitude_GCJ02,latitude_GCJ02);
        endFlag = 1;
    }else {
        sprintf(onePoint, ",[%10.14f,%10.14f]",longitude_GCJ02,latitude_GCJ02);
    }

    if (hadMilse) {
        return miles;
    }else {
        return 0.00;
    }
}

