#ifndef __GPS_TOTAL_MILES_H__
#define __GPS_TOTAL_MILES_H__
#include "mystd.h"

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include "system_properties.h"
#include <time.h>
#include <math.h>
#include <string.h>

/*定义了则读取nmea文件一秒一行模拟实际行驶计算里程，并导出高德坐标*/
//#define TESTGPSNMEA

#define MAX_LINE 1024
//一秒计算一次距离
#define SAMPLINGINTERVAL 1
//假设最大速度是120km/h = 0.03km/s * (s)
const double  MAXSPEEDKMSECOND = 0.0333;

class GpsTotalMiles
    : public my::Singleton<GpsTotalMiles>
{
    friend class my::Singleton<GpsTotalMiles>;
public:
    char line[MAX_LINE];
    double gpsA;
    double gpsEE;
    double latitude;
    double longitude;
    double latitude_GCJ02;
    double longitude_GCJ02;
    double lastLatitude_GCJ02;
    double lastLongitude_GCJ02;
    double gpsPI;
    double gpsTotalMiles;
    int gpsOutFd;
    int endFlag;
    int readonec;
    int gpslinecount;
    int nmeaErrorCount;
    int distanceErrorCount;

    bool startCalculate ;
    bool hadAcc;
    bool onlyGpsSpeed;

    unsigned char samplingInterval;
    FILE *gpsFp;

    GpsTotalMiles();
    ~GpsTotalMiles();

    double getDistance(double lat, double lon, unsigned char gpsSig);
private:
    int    WGS84ToGCJ02(double wgLat, double wgLon,double *mgLat,double *mgLon) ;
    bool   outOfChina(double lat, double lon) ;
    double WGS84ToGCJ02Lat(double x, double y);
    double WGS84ToGCJ02Lon(double x, double y);
    double distance(double lat1, double lon1, double lat2, double lon2);
};

#endif

