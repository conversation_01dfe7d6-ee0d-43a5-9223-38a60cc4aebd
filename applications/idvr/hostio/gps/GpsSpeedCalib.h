/// @file gps_speed_calib.h
/// <AUTHOR> (<EMAIL>)
/// @date 2018-09-19
/// Copyright (C) 2018 - MiniEye INC.

#pragma once

#ifndef GPS_SPEED_CALIB_H_
#define GPS_SPEED_CALIB_H_

#include <deque>
#include <mutex>  // NOLINT(build/c++11)

typedef struct CalibMidState
{
    uint32_t    sampleNum;
    double      sum;
    double      avg;
    double      variance;
    double      stddev;
} CalibMidState;

class GpsSpeedCalib {
public:
    GpsSpeedCalib(double preRatio, double preStdDev);

    // sg: GPS speed, km/h
    // si: input speed, km/h
    int32_t Update(double sg, double si, double *answer, CalibMidState *midstate = NULL);

protected:
    double mAvgRatio;
    double mStdDev;
    std::deque<double> mRatios;
};

#endif  // GPS_SPEED_CALIB_H_

