#include <math.h>
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include "system_properties.h"
#include "idvrProperty.h"
#include "common.h"

#include "GpsHandler.h"
#include "IOMsgHandler.h"

static void nmeaTraceLoge(const char *str, int str_size)
{
    loge("%s", str);
}
static void nmeaTraceLogd(const char *str, int str_size)
{
    logd("%s", str);
}
GpsHandler::GpsHandler()
{
    nmea_zero_INFO(&mNmeaInfo);
    nmea_parser_init(&mNmeaParser);
    nmea_property()->error_func = nmeaTraceLoge;
    nmea_property()->info_func = nmeaTraceLogd;

    char propValue[PROP_VALUE_MAX] = {0};
    if (__system_property_get(PROP_PERSIST_MINIEYE_GPS_PDOP_THRES, propValue) > 0) {
        mPDOPThres = atof(propValue);
    } else {
        mPDOPThres = 3.0;
    }
    if (__system_property_get(PROP_PERSIST_MINIEYE_GPS_TMSYNC_THRES, propValue) > 0) {
        mGpsTmSyncThres = atoi(propValue);
    } else {
        mGpsTmSyncThres = 10;
    }

    bool req_stat = false;
    char req_resp[64] = {0};
    char req_cmd[128] = {0};
}

GpsHandler::~GpsHandler()
{
    nmea_parser_destroy(&mNmeaParser);
    close(mSarFd);
}

int32_t GpsHandler::init()
{
    mWorkerHandle = ev_service_worker_init("GpsHandler");

    mGpsLogger = new FileLog("/data/minieye/idvr/mlog/gps_log/", 5 * 1024 * 1024, 10);
    mGpsLogger->prepare();

    mNmeaLogger = new FileLog("/data/minieye/idvr/mlog/nmea_log/", 50 * 1024 * 1024, 10);
    mNmeaLogger->prepare();

    char value[PROP_VALUE_MAX];
    int len = 0;
    memset(value, 0, sizeof(value));
    len = __system_property_get(PROP_PERSIST_MINIEYE_GPS_NMEM_LOG_ENABLE, value);
    if(len > 0) {
        mGpsNmeaLog = atoi(value);
        logd("set gps nmea log %d", mGpsNmeaLog);
    }

    memset(value, 0, sizeof(value));
    len = __system_property_get(PROP_PERSIST_MINIEYE_NMEA_DATA_ENABLE, value);
    if (len > 0) {
        mNMEADataEnable = atoi(value);
        logd("set nmea enable %d", mNMEADataEnable);
    }

    if (mNMEADataEnable & 1) {
        mLibflowServer = new LibflowServer("0.0.0.0", "23888", "MINIEYE.NMEA");
        mLibflowServer->start();
    }

    if (mNMEADataEnable & 2) {
        mRbGNSS = new CRingBuf("hostio", "GNSS", (1<<20), CRB_PERSONALITY_WRITER, true);
    }

    getLastGpsLocatoin();

    mSarFd = open(SAR_DEVICE_NAME, O_RDWR);
    if (mSarFd < 0) {
        loge("Error! mSarFd : Failed to open %s, %s\n", SAR_DEVICE_NAME, strerror(errno));
        return -1;
    }
    if (ioctl(mSarFd, MS_SAR_INIT) < 0) {
        loge("ioctl MS_SAR_INIT error");
        close(mSarFd);
        return -2;
    }
    memset(&stSarCfg, 0, sizeof(SAR_CONFIG_READ_ADC));
    stSarCfg.ch = SAR_CHN_1;

    my::thread::start();
    return 0;
}

bool GpsHandler::setDebugLevel(int32_t level)
{
    mDebugLevel = level;
    return true;
}

bool GpsHandler::getCurrentLbs(LBS *lbs)
{
    *lbs = mLbs;
    return true;
}

bool GpsHandler::powerOn()
{
#if 1   // reset复位的方式
    // reset GPS modules
    logd("reset GPS modules");
    char cmd[128] = {0};
    char cmdWithCrc[] = "$PCAS01,5*19\r\n";

    snprintf(cmd, sizeof(cmd), "echo %s > /proc/driver/mgpio", "\"gpio-gps-reset low\"");
    my::runShell(cmd);
    usleep(10 * 1000);
    snprintf(cmd, sizeof(cmd), "echo %s > /proc/driver/mgpio", "\"gpio-gps-reset high\"");
    my::runShell(cmd);
    usleep(10 * 1000);
    snprintf(cmd, sizeof(cmd), "echo %s > /proc/driver/mgpio", "\"gpio-gps-reset low\"");
    my::runShell(cmd);
#else   // 掉电重启的方式
    IOMsg msg(MAJOR_GPIO, PWR_GPS_OFF);
    IOMsgHandler::getInstance().sendMsgToMcu(msg);
    usleep(10 * 1000);
    msg.set(MAJOR_GPIO, PWR_GPS_ON);
    IOMsgHandler::getInstance().sendMsgToMcu(msg);
#endif

    int32_t tryCount = 0;
    do {
        usleep(1000 * 1000);
        if (!!access(GPS_UART_DEVICE, R_OK)) {
            logd("wait for " GPS_UART_DEVICE);
            if (++tryCount >= 10) {
                return false;
            }
        } else {
            break;
        }
    } while (true);

    bool ret = evUartOpen(INVALID_HANDLE, GPS_UART_DEVICE, 9600, false);
    if (ret == false) {
        loge("GpsHandler evUartOpen 9600bps error!!");
    } else {
        logd("GpsHandler cmdWithCrc is %s and len is %d!!", cmdWithCrc, strlen(cmdWithCrc));
        ret = evUartsendDataSync(cmdWithCrc, strlen(cmdWithCrc));
        evUartClose();
        if (ret == false) {
            loge("GpsHandler evUartsendData set 115200 error!!");
        }
    }
    ret = evUartOpen(mWorkerHandle, GPS_UART_DEVICE, 115200, false);
    if (ret == false) {
        loge("GpsHandler evUartOpen 115200bps error!!");
    }
    return ret;
}

void GpsHandler::notifyLbs()
{
    std::lock_guard<std::mutex> lock(mLbsMutex);
    LBS tmp = mLbs;
    if (mGpsObserver != NULL) {
        mGpsObserver->updateLbs(&tmp);
    }
}

void GpsHandler::run()
{
    prctl(PR_SET_NAME, "GpsHandler monitor");

    while (!exiting()) {
        // 监控串口的数据接收状态
        if (!isEvUartConnected()) {
            usleep(100 * 1000);
            powerOn();
        }

        /* gps 超时清零 */
        if (mGpsUpdateTime.elapsed() > 10 * 1000 ) {
            mLbs.status = 0;
            mLbs.sig_level = 0;
            mLbs.sat = 0;
            notifyLbs();
            //logd("gps recv info timeout 10sec ! %d\n", mGpsUpdateTime.tv_sec);
        }

        /* clear gps speed */
        if (mGpsUpdateTime.elapsed() > 30 * 1000) {
            mLbs.speed_x10 = 0;
            notifyLbs();
        }

        if (!ioctl(mSarFd, MS_SAR_SET_CHANNEL_READ_VALUE, &stSarCfg)) {
            //logd("gps stSarCfg.adcRawVal is %d\n", stSarCfg.adcRawVal);
            if (stSarCfg.adcRawVal < 100) {
                mLbs.antenna = ANTENNA_SHORT;
            } else if (stSarCfg.adcRawVal < 990) {
                mLbs.antenna = ANTENNA_OK;
            } else {
                mLbs.antenna = ANTENNA_OPEN;
            }
        } else {
            mLbs.antenna = ANTENNA_UNKNOW;
        }

        if (mLastGnssModeCfgTs.elapsed() >= 3000) {
            my::conf::ini ini;
            my::conf::ini::load(ini, "/data/minieye/idvr/etc/config.ini");
            my::string gnssSearchMode = ini.get("gnss", "mode", "").c_str();
            if (mGnssSearchMode != gnssSearchMode &&
                (gnssSearchMode == "1" || gnssSearchMode == "2" || gnssSearchMode == "3")) {
                mSetGnssModeSucc = false;
                mGnssSearchMode = gnssSearchMode;
                mSetGnssModeCnt = 0;
            }
            mLastGnssModeCfgTs = my::timestamp::now();
        }

        if (!mSetGnssModeSucc && mSetGnssModeCnt < 3 && mLastSetGnssModeTs.elapsed() >= 3000) {
            setGnssSearchModeImpl();
            mLastSetGnssModeTs = my::timestamp::now();
            if (++mSetGnssModeCnt >= 3) {  // 这个命令没有反馈，发三次就视作成功了
                mSetGnssModeSucc = true;
            }
        }

        usleep(100 * 1000);
    }
}

bool GpsHandler::broadCastNmea(const char *data, int32_t size)
{
    if (mNMEADataEnable & 1) {
        if (mLibflowServer) {
            mLibflowServer->send(data, size);
        }
    }
    if (mNMEADataEnable & 2) {
        if (mRbGNSS) {
            static uint32_t gnssFrameIdx = 1;
            RBFrame *pRbFrm = (RBFrame *) mRbGNSS->RequestWriteFrame(size + sizeof(RBFrame), CRB_FRAME_I_SLICE);
            if (CRB_VALID_ADDRESS(pRbFrm)) {
                memset(pRbFrm, 0, sizeof(RBFrame));
                pRbFrm->frameTag = RBFrameTag;
                pRbFrm->frameType = IFrame;
                pRbFrm->channel = 0;
                pRbFrm->frameIdx = gnssFrameIdx++;
                pRbFrm->frameNo  = pRbFrm->frameIdx;
                pRbFrm->dataLen = size;
                pRbFrm->video.VWidth = 0;
                pRbFrm->video.VHeight = 0;
                struct timeval tv;
                gettimeofday(&tv, nullptr);
                pRbFrm->time = tv.tv_sec * 1000 + tv.tv_usec / 1000;
                pRbFrm->pts = pRbFrm->time;
                memcpy(pRbFrm->data, (char *) data, size);
                mRbGNSS->CommitWrite();
            }
        }
    }

    return true;
}

bool GpsHandler::setGnssSearchModeImpl() {
    my::string gnssSearchMode = mGnssSearchMode;
    if (gnssSearchMode == "1") {  // gnss模块和ini的单模定义是相反
        gnssSearchMode = "2";
    } else if (gnssSearchMode == "2") {
        gnssSearchMode = "1";
    }

    char cmd[128];
    snprintf(cmd, sizeof(cmd), "$PCAS04,%s*", gnssSearchMode.c_str());

    uint8_t checksum = 0;
    for (int i = 1; cmd[i] != '*'; i++) {
        checksum ^= cmd[i];
    }
    snprintf(cmd + strlen(cmd), sizeof(cmd) - strlen(cmd), "%02X\r\n", checksum);

    logd("Sending GNSS search mode command: %s", cmd);

    bool ret = evUartsendDataSync(cmd, strlen(cmd));
    if (!ret) {
        loge("Failed to send GNSS search mode command");
        return false;
    }

    return true;
}

/* 返回值表示是否定位 */
bool GpsHandler::onNmeaMsgResv(const char *data1, int32_t size1)
{
    bool ret = false;
    mNmeaDataBuf << my::constr(data1, size1);
    if (mNmeaDataBuf.length() < 1024) {
        return ret;
    }

    const char *data = mNmeaDataBuf.c_str();
    int32_t size = mNmeaDataBuf.length();

    broadCastNmea(data, size);
    // 记录原始数据
    if (mGpsNmeaLog) {
        mNmeaLogger->mlog_raw(data, size);
    }

    if (mDebugLevel >= 3) {
        char buf[8194];
        char *p = &buf[0];
        for (int i = 0; i < size; i++) {
            if (data[i] == 0 || data[i] == '\r' || data[i] == '\n') {
                p[i] = ' ';
            } else {
                p[i] = data[i];
            }
        }
        p[size] = 0;
        logd("gps_raw(%d): %s\n", size, p);
    }

    int nread = nmea_parse(&mNmeaParser, (const char *)data, size, &mNmeaInfo);
    mNmeaDataBuf.clear();
    if (nread <= 0) {
        logd("gpsDataHandler nmea_parse return error\n");
        return false;
    }
#if 0
    if (mNmeaInfo.txt != NULL) {
        //logd("External GPS txt info %.20s ", mInfo.txt);
        if(strstr(mNmeaInfo.txt, "OPEN")) // 天线状态
            mLbs.antenna = ANTENNA_OPEN;
        else if(strstr(mNmeaInfo.txt, "OK"))
            mLbs.antenna = ANTENNA_OK;
        else if(strstr(mNmeaInfo.txt, "SHORT"))
            mLbs.antenna = ANTENNA_SHORT;
    }
#endif
    mLbs.status = mNmeaInfo.fix != 1;             // 1未定位, 2 2D定位 3 3D定位

    getGpsSignalLevel();
    getGpsTime();
    //gpsDataFilter();
    dumpGpsInfo();

    if (mNmeaInfo.PDOP >= mPDOPThres || mNmeaInfo.speed >= 119  || mLbs.sat < 5) { /* 车速限制最快是120km，信号质量太差 */
        logd("Invalid GPS info; PDOP (%f), speed (%f) sat %d!\n", mNmeaInfo.PDOP, mNmeaInfo.speed, mLbs.sat);
        return false;
    }
    mGpsUpdateTime = my::timestamp::now();

    mDeg_lat = nmea_ndeg2degree(mNmeaInfo.lat);
    mDeg_lon = nmea_ndeg2degree(mNmeaInfo.lon);

    /*更新GPS经纬度, 不定位时，保留上次经纬度*/
    if (mLbs.status) {
        mLbs.lat_x1kw = mDeg_lat * 10000000;
        mLbs.lng_x1kw = mDeg_lon * 10000000;
        mLbs.alt_x10 = mNmeaInfo.elv * 10; // 高度
        updataLastGpsLocation();
        ret = true;
    }
    mLbs.dir_x100 = mNmeaInfo.direction * 100; // 方向
    mLbs.speed_x10 = mNmeaInfo.speed * 10; // 速度 km/h

    notifyLbs();

    return ret;
}

void GpsHandler::getGpsSignalLevel(void)
{
    time_t now = my::timestamp::system_seconds();
    static time_t last = now;
    list<uint32_t> v1;
    uint32_t sat_num = 0;

    if (mDebugLevel >= 1) {
        logd("Visible satellites %d", mNmeaInfo.satinfo.inview + mNmeaInfo.BDsatinfo.inview);
    }
    for (int32_t i = 0; i < mNmeaInfo.satinfo.inview; i++) {
        if (mDebugLevel >= 1) {
            logd("Satellite #%03d type %-8s SNR %03d(db-HZ) %c",
                    mNmeaInfo.satinfo.sat[i].id, "GPS", (int32_t)mNmeaInfo.satinfo.sat[i].sig,
                    mNmeaInfo.satinfo.sat[i].in_use ? '+': '-');
        }

        if (mNmeaInfo.satinfo.sat[i].in_use) {
            v1.push_back((uint32_t)mNmeaInfo.satinfo.sat[i].sig);
            if (mNmeaInfo.satinfo.sat[i].sig > 0)
            {
                sat_num++;
            }
        }
    }
    for (int32_t i = 0; i < mNmeaInfo.BDsatinfo.inview; i++) {
        if (mDebugLevel >= 1) {
            logd("Satellite #%03d type %-8s SNR %03d(db-HZ) %c",
                    mNmeaInfo.BDsatinfo.sat[i].id, "BEIDOU", (int32_t)mNmeaInfo.BDsatinfo.sat[i].sig,
                    mNmeaInfo.BDsatinfo.sat[i].in_use ? '+' : '-');
        }

        if (mNmeaInfo.BDsatinfo.sat[i].in_use) {
            v1.push_back((uint32_t)mNmeaInfo.BDsatinfo.sat[i].sig);
            if (mNmeaInfo.BDsatinfo.sat[i].sig > 0)
            {
                sat_num++;
            }
        }
    }
    if (mDebugLevel >= 1) {
        logd("Satellite num %d", sat_num);
    }

    if(now < last + 2)  {
        return;
    } else {
        last = now;
    }

    mLbs.sat = sat_num; //信号强度大于0的卫星数量

    v1.sort();
    uint32_t sat_inuse = v1.size();
    if (!sat_inuse) {
        mLbs.sig_level = 0;
        return;
    }
    uint32_t max_sat_num_cal = sat_inuse > 5 ? 5 : sat_inuse;
    uint32_t sig_sum = 0;
    for (uint32_t i = 0; i < max_sat_num_cal; i++) {
        sig_sum += v1.back();
        v1.pop_back();
    }

    /* 取信号最强的几颗卫星(上限5颗)的平均信号强度作为定位信号强度 */
    int avg = sig_sum / max_sat_num_cal;
    if (avg < 20) {
        mLbs.sig_level = 0;
    } else if (avg < 30) {
        mLbs.sig_level = 1;
    } else if (avg < 40) {
        mLbs.sig_level = 2;
    } else {
        mLbs.sig_level = 3;
    }
    //logd("gps avg %d level %d\n", avg, mLbs.sig_level);
}

void GpsHandler::getGpsTime(void)
{
    struct tm stm;
    stm.tm_year        = mNmeaInfo.utc.year;
    stm.tm_mon         = mNmeaInfo.utc.mon - 1;
    stm.tm_mday        = mNmeaInfo.utc.day;
    stm.tm_hour        = mNmeaInfo.utc.hour;
    stm.tm_min         = mNmeaInfo.utc.min;
    stm.tm_sec         = mNmeaInfo.utc.sec;
    stm.tm_isdst       = -1;

    time_t         now = time(NULL);
    struct tm      tm_local;
    struct tm      tm_utc;
    unsigned long  time_local, time_utc;
    gmtime_r(&now, &tm_utc);
    localtime_r(&now, &tm_local);
    tm_local.tm_isdst = -1;
    tm_utc.tm_isdst = -1;
    time_local = mktime(&tm_local);
    time_utc = mktime(&tm_utc);
    long utc_diff = time_utc - time_local;
    mLbs.time = (long long)(mktime(&stm) - utc_diff);

    // 如果GPS定位，判断是否需要校时, 时间大于2011年1月1日才认为有效
    if (mLbs.status &&  mLbs.time > 1293811200) {
        auto use_gps_time = [](unsigned int gps_time, unsigned int diff) {
            unsigned int now = my::timestamp::utc_seconds();
            return (abs((int64_t)gps_time - (int64_t)now) > diff);
        };
        if (mGpsTmSyncThres < 0) {
            logd("mGpsTmSyncThres %d < 0!", mGpsTmSyncThres);
        } else if (use_gps_time(mLbs.time, mGpsTmSyncThres)) { // GPS时间和系统时间相差10s就会触发校时
            if (mLastGpsTimeTs.elapsed() > (30 * 1000)) {
                mGpsLogger->mlog("gps_time %u sys_time %u, set rtc !!!", mLbs.time, my::timestamp::utc_seconds());
                struct timeval stime = {0, 0};
                stime.tv_sec = mLbs.time;
                settimeofday(&stime, NULL);
                // 设置硬件时钟
                system("hwclock -wu &");
            }
        } else {
            mLastGpsTimeTs = my::timestamp::now();
        }
    }
}

void GpsHandler::gpsDataFilter(void)
{
    static uint32_t skip_cnt = 0;
    static uint64_t recv_cnt = 0;
    double cur_lat = 0.0;
    double cur_lon = 0.0;

    cur_lat = nmea_ndeg2degree(mNmeaInfo.lat);
    cur_lon = nmea_ndeg2degree(mNmeaInfo.lon);

    if (fabs(cur_lat - mDeg_lat) < 1e-7 && fabs(cur_lon - mDeg_lon) < 1e-7) {
        //logd("gps location the same: %.7lf %.7lf %.7lf %.7lf", cur_lat, cur_lon, mDeg_lat, mDeg_lon);
        return;
    }

    if (mNmeaInfo.fix == 2 || mNmeaInfo.fix == 3) { /* located */
        //logd("gps recv_cnt %d", recv_cnt);
        if (recv_cnt >= 5) { /* 收到5次定位后使能过滤 */
            double s = getDistance(cur_lat, cur_lon, mDeg_lat, mDeg_lon);
            //logd("gps location %d distance %lf speed %lf cur_la %lf cur_lo %lf last_la %lf last_lo %lf", mInfo.fix, s, mInfo.speed, cur_lat, cur_lon, mDeg_lat, mDeg_lon);
            if (s > 0.5) { /* 大于0.5km, dropped */
                skip_cnt++;
                logd("gps_skip_cnt %u distance %lf cur_la %lf cur_lo %lf last_la %lf last_lo %lf", skip_cnt, s, cur_lat, cur_lon, mDeg_lat, mDeg_lon);
                mGpsLogger->mlog("gps_skip_cnt %u distance %lf cur_la %lf cur_lo %lf last_la %lf last_lo %lf", skip_cnt, s, cur_lat, cur_lon, mDeg_lat, mDeg_lon);
            } else { /* */
                skip_cnt = 0;
            }

            if (skip_cnt >0 && skip_cnt <=5) { /* filter*/
                logd("gps location skip %d recv %d", skip_cnt, recv_cnt);
                return;
            } else if(skip_cnt > 5) {
                logd("gps location skipping used");
                mGpsLogger->mlog("gps location skipping used");
                skip_cnt = 0;
                recv_cnt = 0; /*误差坐标转正后，重新接受5帧后再使能过滤功能*/
            }
        }

        mDeg_lat = cur_lat;
        mDeg_lon = cur_lon;
        recv_cnt++;
    } else { /* 不定位之后清0*/
        skip_cnt = 0;
        recv_cnt = 0;
    }
}

void GpsHandler::dumpGpsInfo(void)
{
    if (mDebugLevel >= 2) {
        logd("gps read: mask 0x%x sig %d fix %d PDOP %.1f HDOP %.1f VDOP %.1f lat %.4f lon %.4f elv %.1f "\
                "sog %.1f speed %.1f dir %.2f, dec %.2f mode %c sat %d use %d BDsat %d BDuse %d\n"\
                ,mNmeaInfo.smask
                ,mNmeaInfo.sig        /**< GPS quality indicator (0 = Invalid; 1 = Fix; 2 = Differential, 3 = Sensitive) */
                ,mNmeaInfo.fix        /**< Operating mode, used for navigation (1 = Fix not available; 2 = 2D; 3 = 3D) */

                ,mNmeaInfo.PDOP       /**< Position Dilution Of Precision */
                ,mNmeaInfo.HDOP       /**< Horizontal Dilution Of Precision */
                ,mNmeaInfo.VDOP       /**< Vertical Dilution Of Precision */

                ,mNmeaInfo.lat        /**< Latitude in NDEG - +/-[degree][min].[sec/60] */
                ,mNmeaInfo.lon        /**< Longitude in NDEG - +/-[degree][min].[sec/60] */
                ,mNmeaInfo.elv        /**< Antenna altitude above/below mean sea level (geoid) in meters */
                ,mNmeaInfo.sog        /**< ÊýÖµ ¶ÔµØËÙ¶È£¬µ¥Î»Îª½Ú */
                ,mNmeaInfo.speed      /**< Speed over the ground in kilometers/hour */
                ,mNmeaInfo.direction  /**< Track angle in degrees True */
                ,mNmeaInfo.declination /**< Magnetic variation degrees (Easterly var. subtracts from true course) */
                ,mNmeaInfo.mode
                ,mNmeaInfo.satinfo.inview          // 卫星数量
                ,mNmeaInfo.satinfo.inuse          // 卫星数量
                ,mNmeaInfo.BDsatinfo.inview          // 卫星数量
                ,mNmeaInfo.BDsatinfo.inuse          // 卫星数量
                );       /**< ×Ö·û ¶¨Î»Ä£Ê½±êÖ¾ (A = ×ÔÖ÷Ä£Ê½, D = ²î·ÖÄ£Ê½, E = ¹ÀËãÄ£Ê½, N = Êý¾ÝÎÞÐ§) */
    }
}

void GpsHandler::getLastGpsLocatoin(void)
{
    double lat_x1kw, lng_x1kw;
    double lat_x1kw_df = 22.536779;
    double lng_x1kw_df = 113.95273;
    char prop[PROP_VALUE_MAX] ={0};

    if (0 == __system_property_get(PROP_RW_MINIEYE_GPSLASTLOCATION, prop)) {
        if (__system_property_get(PROP_PERSIST_MINIEYE_LASTGPSLOCATION, prop)) { /* never location, use default gps location */
            __system_property_set(PROP_RW_MINIEYE_GPSLASTLOCATION, prop);
        }
    }
    sscanf(prop, "Location %lf,%lf", &lat_x1kw, &lng_x1kw);
    mLbs.lat_x1kw = (int)(lat_x1kw * 10000000);
    mLbs.lng_x1kw = (int)(lng_x1kw * 10000000);
    logd("get last gps location %lf %lf, int %d %d\n", lat_x1kw, lng_x1kw, mLbs.lat_x1kw, mLbs.lng_x1kw);
}

void GpsHandler::updataLastGpsLocation(void)
{
    double lat_x1kw, lng_x1kw;
    char prop[PROP_VALUE_MAX] = {0};

    lat_x1kw = mLbs.lat_x1kw / 10000000.0;
    lng_x1kw = mLbs.lng_x1kw / 10000000.0;

    snprintf(prop, sizeof(prop), "Location %f,%f", lat_x1kw, lng_x1kw);
    __system_property_set(PROP_RW_MINIEYE_GPSLASTLOCATION, prop);
}

void GpsHandler::onEvUartDisConnected()
{
    mGpsLogger->mlog("GpsHandler uart %s disconnect!", GPS_UART_DEVICE);
    return;
}

bool GpsHandler::onEvUartRecved(const char *data, int32_t dataLen)
{
    onNmeaMsgResv(data, dataLen);
    return true;
}

