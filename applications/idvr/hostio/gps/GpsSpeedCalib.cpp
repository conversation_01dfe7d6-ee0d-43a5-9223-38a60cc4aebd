#include <math.h>
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include "mystd.h"
#include <sys/time.h>
#include <iostream>
#include <algorithm>
#include <numeric>
#include "GpsSpeedCalib.h"

static const size_t MaxQueueSize = 100;
static const double MinGpsSpeed = 30.0;

template <typename I>
static double variance(I beg, I end, double avg) {
  double variance = 0.0;
  for (I i = beg; i != end; ++i) {
    double sample = *i;
    double diff = sample - avg;
    variance += diff * diff;
  }

  return variance / (end - beg);
}

GpsSpeedCalib::GpsSpeedCalib(double preRatio, double preStdDev)
    : mAvgRatio(preRatio)
    , mStdDev(preStdDev)
{
}

int32_t GpsSpeedCalib::Update(double sg, double si, double *answer, CalibMidState *midstate)
{
    memset(midstate, 0, sizeof(*midstate));
    if (midstate) {
        midstate->sampleNum = mRatios.size();
    }

    if (sg < MinGpsSpeed || si < 1e-6) {
        mRatios.clear();
        return -1;
    }

    double ratio = sg / si;
    mRatios.push_back(ratio);
    size_t qsize = mRatios.size();
    if (qsize < MaxQueueSize) {
        return -1;
    } else if (qsize > MaxQueueSize) {
        mRatios.pop_front();
    }

    auto beg = mRatios.begin();
    auto end = mRatios.end();
    std::sort(beg, end);
    // beg += 1;
    // end -= 1;
    double sum = std::accumulate(beg, end, 0.0);
    double avg = sum / (end - beg);
    double var = variance(beg, end, avg);
    double stddev = sqrt(var);
    if (midstate) {
        midstate->sum = sum;
        midstate->avg = avg;
        midstate->variance = var;
        midstate->stddev = stddev;
    }

    if ((stddev >= avg * 0.5) ||
            (stddev >= mStdDev * 0.95)) {
        return 1;
    }

    mAvgRatio = avg;
    mStdDev = stddev;
    mRatios.clear();
    *answer = avg;
    return 0;
}

