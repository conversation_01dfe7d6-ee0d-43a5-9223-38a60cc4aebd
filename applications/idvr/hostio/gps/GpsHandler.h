#ifndef GPS_HANDLER_H_
#define GPS_HANDLER_H_

#include <deque>

#include "mystd.h"
#include "FileLog.h"
#include "McuMessage.h"
#include "libflow.h"
#include "CRingBuf.h"
#include "nmea/nmea.h"
#include "EvUart.h"

#define GPS_UART_DEVICE     "/dev/ttyS3"
#define SAR_DEVICE_NAME     "/dev/sar"

#define SARADC_IOCTL_MAGIC                          'a'
#define MS_SAR_INIT                                 _IO(SARADC_IOCTL_MAGIC, 0x00)
#define MS_SAR_SET_CHANNEL_READ_VALUE               _IO(SARADC_IOCTL_MAGIC, 0x01)
#define SAR_IOCTL_CMD_COUNT                         0x01

typedef struct
{
    unsigned int ch;            // 0~3
    unsigned int adcRawVal;     // 读取到的值
}SAR_CONFIG_READ_ADC;

typedef enum {
    SAR_CHN_0,
    SAR_CHN_1,
    SAR_CHN_2,
    SAR_CHN_3,
    SAR_CHN_MAX,
}SAR_CHN_NUM;

enum GnssSearchMode {
    GNSS_SEARCH_MODE_NONE = 0b00,
    GNSS_SEARCH_MODE_BEIDOU = 0b01,
    GNSS_SEARCH_MODE_GPS = 0b10,
};

class GpsObserver
{
public:
    virtual void updateLbs(LBS *lbs) = 0;
};

class GpsHandler
    : public EvUart,
      public my::thread
{
public:
    GpsHandler();
    ~GpsHandler();
    int32_t init();
    bool onNmeaMsgResv(const char *data1, int32_t size1);
    bool getCurrentLbs(LBS *lbs);
    bool setDebugLevel(int32_t level);
    void attachObserver(GpsObserver *pObserver)
    {
        mGpsObserver = pObserver;
    }

protected:
    // 循环体
    void run();

private:
    // 处理原始数据
    void onEvUartDisConnected();
    bool onEvUartRecved(const char *data, int32_t dataLen);

    bool setGnssSearchModeImpl();

    bool powerOn(void);
    void getGpsSignalLevel(void);
    void gpsDataFilter(void);
    void getGpsTime(void);
    bool broadCastNmea(const char *data, int32_t size);
    void dumpGpsInfo(void);
    void getLastGpsLocatoin(void);
    void updataLastGpsLocation(void);
    void notifyLbs();
private:
    // libevserice
    worker_handle_t   mWorkerHandle = INVALID_HANDLE;
    GpsObserver *mGpsObserver = NULL;
    std::mutex mLbsMutex;

    my::timestamp mLastGpsTimeTs;
    LBS mLbs = {0};
    bool mGpsNmeaLog = false;
    nmeaINFO mNmeaInfo;
    nmeaPARSER mNmeaParser;
    double mDeg_lat = 0.0;
    double mDeg_lon = 0.0;

    // 用来发送nmea原始数据
    uint32_t mNMEADataEnable = 0;
    LibflowServer *mLibflowServer = NULL;
    CRingBuf * mRbGNSS = NULL;

    // 设置gnss搜索模式
    bool mSetGnssModeSucc = true;
    my::string mGnssSearchMode;
    int mSetGnssModeCnt = 0;
    my::timestamp mLastSetGnssModeTs = 0;
    my::timestamp mLastGnssModeCfgTs = 0;

    double mPDOPThres = 6.0;
    
    // GPS超时清零
    my::timestamp mGpsUpdateTime = 0;

    int32_t mDebugLevel = 0;
    FileLog *mGpsLogger = NULL;
    FileLog *mNmeaLogger = NULL;

    // 缓冲，因为sigmastar的串口太烂了，一次回调的数据太少，等收集多个数据才解析
    my::string mNmeaDataBuf;

    int mSarFd = -1;
    SAR_CONFIG_READ_ADC stSarCfg;

    int32_t mGpsTmSyncThres = 10;//10s
};

#endif  // GPS_SPEED_CALIB_H_

