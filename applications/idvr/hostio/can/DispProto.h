#ifndef __DISP_PROTO_H__
#define __DISP_PROTO_H__


#define DISP_CAN_ID_CAN_CTRL    (0x781)
#define CAN_CTRL_SET_FILTER     (0x1)

#define CAN_CTRL_FORWARD        (0x2)
#define CAN_CTRL_RESET_FOWARD_INFO   (0x3)
#define CAN_CTRL_IGNORE         (0x4)
#define CAN_CTRL_CONFIG_SPEED   (0x5)
#define CAN_CTRL_SET_FT_MODE    (0x6)
#define QUERY_SW_VERSION        (0x7)
#define RESP_SW_VERSION        (0x8)

#define DISP_CAN_ID_LED_CTRL (0x780)
#define DISP_LINE 1
#define DISP_CAR 2
#define DISP_LINE_CAR 3
#define DISP_BLINK 4
#define DISP_CARDIGIT 5
#define DISP_LUMI 6
#define DISP_PEDSTRIAN  7


#define DISP_ONLY_DOT 0
#define DISP_ONLY_DIGIT 1
#define DISP_BLUE_DIGIT_CAR 2
#define DISP_BLUE_DIGIT_RED_CAR 3
#define DISP_RED_DIGIT_CAR 4
#define DISP_ONLY_CAR 5

#define COLOR_RED (0)
#define COLOR_WHITE (1)

#define STATE_NOT_SHOW (0)
#define STATE_SHOW (1)
#define STATE_BLINK (2)



typedef struct MECANWarnMsg{
#if __BIG_ENDIAN__
    uint8_t     byte0_resv:5;
    uint8_t     sound_type:3;

    uint8_t     byte1_resv0:2;
    uint8_t     zero_speed:1;
    uint8_t     byte1_resv1:5;

    uint8_t     headway_measurement:7;
    uint8_t     headway_valid:1;

    uint8_t     byte3_resv:7;
    uint8_t     no_error:1;

    uint8_t     byte4_resv:4;
    uint8_t     fcw_on:1;
    uint8_t     right_ldw:1;
    uint8_t     left_ldw:1;
    uint8_t     ldw_off:1;

    uint8_t     byte5_resv;
    uint8_t     byte6_resv;

    uint8_t     byte7_resv:6;
    uint8_t     headway_warning_level:2;
#else /*Little Endian*/
    uint8_t     sound_type:3;
    uint8_t     time_indicator:2;
    uint8_t     byte0_resv:3;

    uint8_t     byte1_resv1:5;
    uint8_t     zero_speed:1;
    uint8_t     byte1_resv0:2;

    uint8_t     headway_valid:1;
    uint8_t     headway_measurement:7;

    uint8_t     no_error:1;
    uint8_t     error_code:7;

    uint8_t     ldw_off:1;
    uint8_t     left_ldw:1;
    uint8_t     right_ldw:1;
    uint8_t     fcw_on:1;
    uint8_t     byte4_resv:2;
    uint8_t     maintenanc:1;
    uint8_t     failsafe:1;

    uint8_t     byte5_resv0:1;
    uint8_t     peds_fcw:1;
    uint8_t     peds_in_dz:1;
    uint8_t     byte5_resv1:2;
    uint8_t     tamper_alert:1;
    uint8_t     byte5_resv2:1;
    uint8_t     tsrw_enable:1;

    uint8_t     tsrw_warning_level:3;
    uint8_t     byte6_resv:5;

    uint8_t     headway_warning_level:2;
    uint8_t     hw_repeatable_enable:1;
    uint8_t     byte7_resv:5;
#endif
} __attribute__((packed)) MECANWarnMsg_S;


typedef struct 
{
    uint32_t id;
    uint8_t  len;
    uint8_t  data[8];
}  __attribute__((packed)) UrtpCanT;

class DispProto {
    public:
        DispProto(){};
        ~DispProto(){};

        int WarnCar(int car_color, int car_state, int hw0, int hw1,\
        int dot_show, int hw_state, int hw_color);
        void displinecar(int left, int right, int level, int digit1, int digit2);
        void displine(int left, int right);
        void WarnLine(int left, int right);
        void WarnLumi(int idx);
        void WarnLineCar(int left, int right, int level, float time, float speed);
        void WarnPedestrian(int32_t state);
        UrtpCanT mPkg;

    private:
        int get_disp_level(int level, float time, float speed);
        void fill_can_frame(int32_t id, uint8_t payload[8]);
        int check_state(int state);

        MECANWarnMsg_S mLastAdasMsg;
};







#endif






