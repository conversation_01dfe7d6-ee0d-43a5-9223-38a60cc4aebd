#define LOG_TAG     "mcu"

#include "McuConfig.h"
#include "mystd.h"

const static std::map<CS_TYPE_E, const char *> gCSType2Name = {
    {CS_SPEED,                      "speed"},
    {CS_LEFT,                       "left_turn"},
    {CS_RIGHT,                      "right_turn"},
    {CS_REVERSE,                    "reverse"},
    {CS_BRAKE,                      "brake"},
    {CS_ACCELERATOR,                "accelerator"},
    {CS_GEAR,                       "gear"},
    {CS_EHB_STATE,                  "ehb_state"},
    {CS_EHB_PARK_REQ,               "ehb_park_req"},
    {CS_EHB_PARK_DONE,              "ehb_park_done"},
    {CS_FRONT_LEFT_WHEEL_SPD,       "fl_wheel_speed"},
    {CS_FRONT_LEFT_WHEEL_STAT,      "fl_wheel_state"},
    {CS_FRONT_RIGHT_WHEEL_SPD,      "fr_wheel_speed"},
    {CS_FRONT_RIGHT_WHEEL_STAT,     "fr_wheel_state"},
    {CS_BACK_LEFT_WHEEL_SPD,        "rl_wheel_speed"},
    {CS_BACK_LEFT_WHEEL_STAT,       "rl_wheel_state"},
    {CS_BACK_RIGHT_WHEEL_SPD,       "rr_wheel_speed"},
    {CS_BACK_RIGHT_WHEEL_STAT,      "rr_wheel_state"},
    {CS_MILE_TOTAL,                 "mile_total"},
    {CS_MILE_TRIP,                  "mile_single"},
    {CS_FUEL_TOTAL,                 "fuel_total"},
    {CS_FUEL_AVG,                   "fuel_avg"},
    {CS_TURN_SPEED,                 "turn_speed"},
    {CS_DOOR_FRONT,                 "front_door"},
    {CS_DOOR_MID,                   "mid_door"}
};

static const char *analog_sigal_names[ANALOG_SIGNAL_MAX] = {"aleft", "aright", "abreak", "awiper", "abeam"};
static const char *can_speed_str[MCU_CAN_SPEED_MAX] = {
    "Invalid",
    "1M",
    "800K",
    "500K",
    "250K",
    "125K",
    "100K",
    "50K",
    "20K",
    "10K",
    "5K",
};
static const char *gs_ehub_prot_strs[] = {
    "invalid", "minieye", "g7", "ft"/* place holder */, "sample",
};

//static_assert(sizeof(gs_ehub_prot_strs)/sizeof(gs_ehub_prot_strs[0]) == EHUB_PROTOCOL_MAX, "too few ehub protocol str");

static void print_can_signal(const char* name, const can_signal& csp) {
    logd("CAN Signal %-10s %-5s 0x%08x 0x%08x [%c] %02d+%02d x%f+%f [%f-%f]\n",
            name, (CAN_SIGNAL_BYTE_ORDER_INTEL == csp.byte_order)? "Intel" : "Motor",
            csp.id, csp.mask, csp.is_unsigned?'+':'-',
            csp.start_bit, csp.size,
            csp.factor, csp.offset,
            csp.min, csp.max);
}

static McuCanSpeedE parse_can_speed(const char* str) {
    if (0 == strcasecmp(str, "1M")) {
        return MCU_CAN_SPEED_1M;
    } else if (0 == strcasecmp(str, "800K")) {
        return MCU_CAN_SPEED_800K;
    } else if (0 == strcasecmp(str, "500K")) {
        return MCU_CAN_SPEED_500K;
    } else if (0 == strcasecmp(str, "250K")) {
        return MCU_CAN_SPEED_250K;
    } else if (0 == strcasecmp(str, "125K")) {
        return MCU_CAN_SPEED_125K;
    } else if (0 == strcasecmp(str, "100K")) {
        return MCU_CAN_SPEED_100K;
    } else if (0 == strcasecmp(str, "50K")) {
        return MCU_CAN_SPEED_50K;
    } else if (0 == strcasecmp(str, "20K")) {
        return MCU_CAN_SPEED_20K;
    } else if (0 == strcasecmp(str, "10K")) {
        return MCU_CAN_SPEED_10K;
    } else if (0 == strcasecmp(str, "5K")) {
        return MCU_CAN_SPEED_5K;
    } else {
        return MCU_CAN_SPEED_INVALID;
    }
}

MCUConfig::MCUConfig()
{
    logd("MCUConfig +");
}

MCUConfig::~MCUConfig()
{
    logd("~MCUConfig +");
}

void MCUConfig::dump_can_cfg(HalioInitInfo *info)
{
    int32_t i = 0;
    static const char *can_scenario_str[] = {"Normal", "EHub", "CAN1 Only", "CAN2 Only"};
    //static_assert(sizeof(can_scenario_str)/sizeof(can_scenario_str[1]) == CAN_SCENARIO_MAX, "too few can scenario str");
    logd("-----------------------Hal CAN config---------------------\n");
    logd("use_obd: %d\n", info->using_OBD);
    logd("can_speed_type: %s\n", can_speed_str[info->input_can_speed]);
    logd("Can scenario: %s\n", can_scenario_str[info->scenario]);
    logd("Enable E.1: %d\n", info->enable_e1);
    logd("Silent Can: %d\n", info->enable_silent_mode);
    logd("Fake speed: enable %d speed %f\n", info->enableFakeSpeed, info->fakeSpeed);

    for (auto & r : info->csMap) {
        auto it = gCSType2Name.find(r.first);
        if (it != gCSType2Name.end()) {
            print_can_signal(it->second, r.second);
        }
    }

    logd("M4 analog turnlamp endble %d polarity %d\n",
            info->analog_cfg.analogSignals[AS_IO_TURNLAMP].enable,
            info->analog_cfg.analogSignals[AS_IO_TURNLAMP].polarity);
    logd("M4 analog speed    endble %d ratio %f disableGpsCal %d\n",
            info->analog_cfg.aspeed.enable,
            info->analog_cfg.aspeed.ratio,
            info->analog_cfg.aspeed.disableGpsCal);
    logd("GPS speed:%d", info->useGpsSpeed);

    if (CAN_SCENARIO_EHUB == info->scenario) {
        logd("EHub protocol %s\n", gs_ehub_prot_strs[info->ehub_cfg.protocol]);
        logd("EHub Can#1 baudrate %s\n", can_speed_str[info->ehub_cfg.can1.can_speed]);
        logd("EHub Can#2 baudrate %s\n", can_speed_str[info->ehub_cfg.can2.can_speed]);
        logd("Analog Speed enable %d ratio %f\n",
                info->ehub_cfg.analog_speed.enable, info->ehub_cfg.analog_speed.ratio);
        for (i = 0; i < ANALOG_SIGNAL_MAX; i++) {
            logd("Analog Sinal#%02d [%-8s] enable %d polarity %d\n",
                    i, analog_sigal_names[i], info->ehub_cfg.analog_signals[i].enable,
                    info->ehub_cfg.analog_signals[i].polarity);
        }
    }

    list<CanRange>::iterator it = mConfig.outputCanList.begin();
    logd("Output IDs:");
    for (it = mConfig.outputCanList.begin(); it != mConfig.outputCanList.end(); it++) {
        logd("Output ID range #%02d 0x%08x->0x%08x", i, it->idBegin, it->idEnd);
        i ++;
    }


    logd("----------------------------------------------------------\n");
}

int MCUConfig::parse_int_8_10_16(const char* str)
{
    int val;

    if (str == NULL || str[0] == '\0') {
        return 0;
    }

    if (str[0] == '0') {
        if (str[1] == 'x' || str[1] == 'X') {  // hex
            sscanf(str, "%x", &val);
        } else {  // octal
            sscanf(str, "%o", &val);
        }
    } else {  // decimal
        sscanf(str, "%d", &val);
    }

    return val;
}

int MCUConfig::fill_main_info(const rapidjson::Value& val, HalioInitInfo* info)
{
    info->using_OBD = val["use_obd"].GetBool();

    std::string baudrate_str = val["baudrate"].GetString();
    info->input_can_speed = parse_can_speed(baudrate_str.c_str());

    if (val.HasMember("scenario")) {
        info->scenario = static_cast<CAN_SCENARIO_TYPE_E>(val["scenario"].GetInt());
    }
    if (val.HasMember("enable_e1")) {
        info->enable_e1 = static_cast<bool>(val["enable_e1"].GetBool());
    }
    if (val.HasMember("silent_can")) {
        info->enable_silent_mode = static_cast<bool>(val["silent_can"].GetBool());
    }
    if (val.HasMember("fake_speed")) {
        info->fakeSpeed= static_cast<double>(val["fake_speed"].GetDouble());
        info->enableFakeSpeed = true;
    }

    return 0;
}

int MCUConfig::fill_can_signal(const rapidjson::Value& val, can_signal* csp)
{
    std::string id_hex = val["can_id"].GetString();
    csp->id = parse_int_8_10_16(id_hex.c_str());

    if (val.HasMember("can_mask")) {
        id_hex = val["can_mask"].GetString();
        csp->mask = parse_int_8_10_16(id_hex.c_str());
    } else {
        loge("no can mask!\n");
    }
    
    int byte_order = val["byte_order"].GetInt();
    csp->byte_order = static_cast<CAN_SIGNAL_BYTE_ORDER_E>(byte_order);

    csp->start_bit = val["start_bit"].GetInt();
    csp->size = val["size"].GetInt();
    csp->min = val["min"].GetDouble();
    csp->max = val["max"].GetDouble();
    csp->factor = val["factor"].GetDouble();
    csp->offset = val["offset"].GetDouble();
    csp->is_unsigned = true;
    if (val.HasMember("is_unsigned")) {
        csp->is_unsigned = val["is_unsigned"].GetBool();
    }

    return 0;
}

bool MCUConfig::check_member(const rapidjson::Value& v,
    const char* name, ValueType type, const char* parent)
{
    if (!v.HasMember(name)) {
        loge("Error: missing directive %s.%s", parent, name);
        return false;
    }

    if (type == TObject && !v[name].IsObject()) {
        HANDLE_TYPE_ERROR("Object");
    } else if (type == TArray && !v[name].IsArray()) {
        HANDLE_TYPE_ERROR("Array");
    } else if (type == TNull && !v[name].IsNull()) {
        HANDLE_TYPE_ERROR("Null");
    } else if (type == TString && !v[name].IsString()) {
        HANDLE_TYPE_ERROR("String");
    } else if (type == TBool && !v[name].IsBool()) {
        HANDLE_TYPE_ERROR("Bool");
    } else if (type == TNumber && !v[name].IsNumber()) {
        HANDLE_TYPE_ERROR("Number");
    } else if (type == TInt && !v[name].IsInt()) {
        HANDLE_TYPE_ERROR("Int");
    } else if (type == TDouble && !v[name].IsDouble()) {
        HANDLE_TYPE_ERROR("Double");
    }

    return true;
}

int MCUConfig::fill_ehub_cfg(const rapidjson::Value& ehub, EHubConfig* ehub_cfg)
{
    int32_t i = 0;
    const rapidjson::Value &aspeed = ehub["aspeed"];
    const rapidjson::Value &can1 = ehub["can1"];
    const rapidjson::Value &can2 = ehub["can2"];
    if (ehub.HasMember("protocol")) {
        const rapidjson::Value &prot = ehub["protocol"];
        std::string prot_str = prot.GetString();
        for (i = 0; i < EHUB_PROTOCOL_MAX; i++) {
            if (0 == strcasecmp(prot_str.c_str(), gs_ehub_prot_strs[i])) {
                ehub_cfg->protocol = (EHUB_PROTOCOL_E) i;
                break;
            }
        }
    }

    std::string baudrate_str = can1["baudrate"].GetString();
    ehub_cfg->can1.can_speed = parse_can_speed(baudrate_str.c_str());

    baudrate_str = can2["baudrate"].GetString();
    ehub_cfg->can2.can_speed = parse_can_speed(baudrate_str.c_str());

    ehub_cfg->analog_speed.enable = aspeed["enable"].GetBool();
    ehub_cfg->analog_speed.ratio  = aspeed["ratio"].GetFloat();

    for (i = 0; i < ANALOG_SIGNAL_MAX; i++) {
        const rapidjson::Value &signal = ehub[analog_sigal_names[i]];
        ehub_cfg->analog_signals[i].enable = signal["enable"].GetBool();
        ehub_cfg->analog_signals[i].polarity = static_cast<uint8_t>(signal["polarity"].GetInt());
    }

    return 0;
}

int MCUConfig::fillOutputIds(const rapidjson::Value& output_ids, list<CanRange> *pList)
{
    CanRange aRange;
    string id1, id2;
    int32_t i_id1 = 0, i_id2 = 0;
    logd("fillOutputIds begin");
    pList->clear();
    if (!output_ids.IsArray()) {
        loge("output_ids should be an array");
        return -1;
    }
    for (size_t i = 0; i < output_ids.Size(); i++) {
        const rapidjson::Value &idRange = output_ids[i];
        if (1 == idRange.Size()) {
            id1 = id2 = idRange[0].GetString();
        } else if (2 == idRange.Size()) {
            id1 = idRange[0].GetString();
            id2 = idRange[1].GetString();
        } else {
            loge("More than 2 id specified");
            continue;
        }
        i_id1 = parse_int_8_10_16(id1.c_str());
        i_id2 = parse_int_8_10_16(id2.c_str());
        bool ret = aRange.setRange(i_id1, i_id2);
        if (!ret) {
            loge("Invalid id range given 0x%08x-0x%08x", i_id1, i_id2);
            continue;
        }
        pList->push_back(aRange);
    }
    logd("fillOutputIds end");
    return 0;
}

int MCUConfig::fill_analog_cfg(const rapidjson::Value& analog, AnalogConfig* analog_cfg)
{
    static const char * analogSigName[AS_IO_MAX] = {"ahighbeam", "alowbeam", "abrake", "aturnlamp", "asos"};
    for (int i = 0; i < AS_IO_MAX; i++) {
        if (analog.HasMember(analogSigName[i])) {
            const rapidjson::Value &sigJson = analog[analogSigName[i]];
            analog_cfg->analogSignals[i].enable = sigJson["enable"].GetBool();
            analog_cfg->analogSignals[i].polarity = static_cast<uint8_t>(sigJson["polarity"].GetInt());
        } else {
            analog_cfg->analogSignals[i].enable = false;
            analog_cfg->analogSignals[i].polarity = 1;
        }
    }
    if (analog.HasMember("aspeed")) {
        const rapidjson::Value &aspeed_signal= analog["aspeed"];
        analog_cfg->aspeed.enable = aspeed_signal["enable"].GetBool();
        analog_cfg->aspeed.ratio = aspeed_signal["ratio"].GetFloat();
        if (aspeed_signal.HasMember("disableGpsCal")) {
            analog_cfg->aspeed.disableGpsCal = aspeed_signal["disableGpsCal"].GetBool();
        }
    }
    return 0;
}

int32_t MCUConfig::loadFromFile(const char *configFile)
{
    int32_t result = 0;
    char* buff = NULL;
    FILE* fp = NULL;
    size_t buff_size = 64 * 1024;
    size_t file_size = 0;
    rapidjson::Document doc;
    rapidjson::ParseResult ok;

    mConfig = HalioInitInfo();
    HalioInitInfo *info = &mConfig;

    // check parameters
    if (configFile == NULL || info == NULL) {
        loge("Error: invalid parameters");
        result = 1;
        goto finish;
    }

    // allocate file buffer
    buff = reinterpret_cast<char*>(malloc(buff_size));
    if (buff == NULL) {
        loge("Error: failed to allocate memory");
        result = 2;
        goto finish;
    }

    // open file
    fp = fopen(configFile, "r");
    if (fp == NULL) {
        loge("Error: failed to open file '%s'", configFile);
        result = 3;
        goto finish;
    }

    // read file
    file_size = fread(buff, 1, buff_size, fp);
    buff[file_size] = '\0';

    // parse JSON
    ok = doc.Parse(buff);
    if (!ok) {
        loge("Error: failed to parse JSON: %s (%" FMT_LLU ")",
                rapidjson::GetParseError_En(ok.Code()), ok.Offset());
        result = 4;
        goto finish;
    }

    if (!doc.IsObject()) {
        loge("Error: missing root object");
        result = 5;
        goto finish;
    }

    CHECK_MAIN_INFO("main");
    CHECK_CAN_SIGNAL("speed");
    CHECK_CAN_SIGNAL("left_turn");
    CHECK_CAN_SIGNAL("right_turn");


    // transfer config values
    fill_main_info(doc["main"], info);
    for (auto & r : gCSType2Name) {
        if (check_member(doc, r.second, TObject, "")) {
            CHECK_CAN_SIGNAL(r.second);
            can_signal cs = {0};
            fill_can_signal(doc[r.second], &cs);
            info->csMap[r.first] = cs;
        } else {
            if (!strcmp(r.second, "brake")) {
                if (check_member(doc, "brakePedalPressed", TObject, "")) {
                    CHECK_CAN_SIGNAL("brakePedalPressed");
                    logd("fill_can_signal->brakePedalPressed");
                    can_signal cs = {0};
                    fill_can_signal(doc["brakePedalPressed"], &cs);
                    info->csMap[r.first] = cs;
                }
            }
        }
    }

    if (CAN_SCENARIO_EHUB == info->scenario) {
        if (doc.HasMember("ehub")) {
            fill_ehub_cfg(doc["ehub"], &info->ehub_cfg);
        } else {
            loge("use_ehub is true and ehub config not specified");
            result = 6;
            goto finish;
        }
    } else {
        logd("use_ehub is false");
    }
    if (doc.HasMember("output_ids")) {
        fillOutputIds(doc["output_ids"], &info->outputCanList);
    } else {
        info->outputCanList.clear();
    }
    addBuiltInOutputCanID();
    
    if (doc.HasMember("m4_analog")) {
        fill_analog_cfg(doc["m4_analog"], &info->analog_cfg);
    }
    do {
        auto spdCs = info->csMap.find(CS_SPEED);
        if (((spdCs == info->csMap.end()) || (spdCs->second.id >= MCU_CAN_EXT_ID_MAX)) &&
            (false == info->analog_cfg.aspeed.enable)) {
            info->useGpsSpeed = true;
        }
    } while (0);
    dump_can_cfg(info);

finish:
    if (fp != NULL) {
        fclose(fp);
    }

    if (buff != NULL) {
        free(buff);
    }

    return result;
}

int32_t MCUConfig::calcCanOutSendCmd(uint32_t outCanID)
{
    list<CanRange>::iterator it = mConfig.outputCanList.begin();
    bool bOutput = false;

    struct mcu_option {
        uint8_t can0Mode;
        uint8_t can1Mode;
    } options[CAN_SCENARIO_MAX] = {
        {CAN_MODE_OUTPUT, CAN_MODE_INPUT},
        {CAN_MODE_INPUT | CAN_MODE_OUTPUT, 0},
        {CAN_MODE_INPUT | CAN_MODE_OUTPUT, 0},
        {CAN_MODE_OUTPUT, CAN_MODE_INPUT | CAN_MODE_OUTPUT},
    };

    if (mConfig.scenario < CAN_SCENARIO_NORMAL ||
            mConfig.scenario >= CAN_SCENARIO_MAX) {
        loge("Invalid scenario %d", mConfig.scenario);
        return -1;
    }
    for (it = mConfig.outputCanList.begin(); it != mConfig.outputCanList.end(); it++) {
        if (it->inRange(outCanID)) {
            bOutput = true;
            break;
        }
    }
    uint8_t can0Mode = options[mConfig.scenario].can0Mode;
    uint8_t can1Mode = options[mConfig.scenario].can1Mode;

    return 0;
}

void MCUConfig::addBuiltInOutputCanID(void)
{
    CanRange aRange;
    //Warning message IDs
    aRange.setRange(0x700, 0x700);
    mConfig.outputCanList.push_back(aRange);
    aRange.setRange(0x760, 0x760);
    mConfig.outputCanList.push_back(aRange);

	//DMS message IDs
	aRange.setRange(0x794, 0x79B);
	mConfig.outputCanList.push_back(aRange);

    //Display ehub IDs
    aRange.setRange(0x780, 0x785);
    mConfig.outputCanList.push_back(aRange);
    //Car algo output IDs
    aRange.setRange(0x76D, 0x777);
    mConfig.outputCanList.push_back(aRange);
    //Lane algo output IDs
    aRange.setRange(0x5F0, 0x5F8);
    mConfig.outputCanList.push_back(aRange);
    //Pedestrian algo output IDs
    aRange.setRange(0x77A, 0x77D);
    mConfig.outputCanList.push_back(aRange);
    //Tsr algo output IDs
    aRange.setRange(0x761, 0x765);
    mConfig.outputCanList.push_back(aRange);

    //BSD to AEBS
    aRange.setRange(0x15d, 0x15d);
    mConfig.outputCanList.push_back(aRange);
    
    aRange.setRange(0x130);
    mConfig.outputCanList.push_back(aRange);
}

