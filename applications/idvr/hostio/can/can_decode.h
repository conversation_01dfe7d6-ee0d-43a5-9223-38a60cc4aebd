#ifndef __CAN_H__
#define __CAN_H__
#include <stdint.h>
#include <string>

#define CAN_SIGNAL_NAME_LEN (128)

typedef enum _CAN_SIGNAL_BYTE_ORDER_E
{
    CAN_SIGNAL_BYTE_ORDER_INTEL,
    CAN_SIGNAL_BYTE_ORDER_MOTOROLA,
} CAN_SIGNAL_BYTE_ORDER_E;

typedef struct _can_signal
{
    uint32_t   id = 0;      /* canid or 验证码 */
    uint32_t   mask = 0;    /* 屏蔽码 */
    /*
     *Specify byte_order of the data
     *CAN_SIGNAL_BYTE_ORDER_INTEL       little endian
     *CAN_SIGNAL_BYTE_ORDER_MOTOROLA    big endian
     */
    CAN_SIGNAL_BYTE_ORDER_E byte_order;
    /*
     *Specify the start bit of the data.
     *The start bit is the least significant bit counted from the start of the message data.
     *The start bit must be an integer from 0 through 63.
     */
    uint32_t    start_bit;
    /*
     *Specify the number of bits the signal occupies in the message.
     *The length must be an integer from 1 through 64.
     */
    uint32_t    size;
    /*
     *Specify the minimum/maximum physical value of the signal
     */
    double      min;
    double      max;

    /*
     *physical = raw * factor + offset
     *physical is the value returned by can_decode_signal
     */
    double      factor;
    double      offset;

    bool        is_unsigned;
} can_signal;

int32_t can_decode_signal(const can_signal * signal, const unsigned char *can_buf, uint32_t buf_len, double *phy);
int32_t can_decode_signal_raw(const can_signal * signal, const unsigned char *can_buf, uint32_t buf_len, uint32_t *u32_raw);

#endif
