#include <stdint.h>
#include "DispProto.h"
#include <sys/time.h>
#include <stdio.h>
#include <math.h>
#include <cstring>

void DispProto::fill_can_frame(int32_t id, uint8_t payload[8])
{
    mPkg.id = id;
    mPkg.len = 8;
    
    memcpy(mPkg.data, payload, 8);
}

int DispProto::get_disp_level(int level, float time, float speed)
{
    int disp_level = DISP_ONLY_DOT;

    if (speed < 2.001) {
        if (level == -1)
            //return DISP_ONLY_DOT;
            return DISP_ONLY_DIGIT;
        else
            //return DISP_ONLY_CAR;
            //return DISP_ONLY_DIGIT;
            return DISP_BLUE_DIGIT_CAR;
    } else if (speed < 30.0) {
        if (level == -1)
            return DISP_ONLY_DOT;
        else
            return DISP_ONLY_CAR;
    }

    switch (level) {
    case -1:
        level = DISP_ONLY_DOT;
        break;
    case 0:
        if (time > 2.5)
            disp_level = DISP_ONLY_CAR;
        else
            disp_level = DISP_BLUE_DIGIT_CAR;
        break;
    case 1:
    case 2:
        disp_level = DISP_BLUE_DIGIT_RED_CAR;
        break;
    case 3:
        disp_level = DISP_RED_DIGIT_CAR;
        break;
    default:
        break;
    }
    return disp_level;
}

void DispProto::WarnPedestrian(int32_t state)
{
    uint8_t payload[8];
    memset(payload, 0, sizeof(payload));

    payload[0] = DISP_PEDSTRIAN;
    payload[1] = state;
    fill_can_frame(DISP_CAN_ID_LED_CTRL, payload);
}

int DispProto::check_state(int state)
{
    if (state == STATE_NOT_SHOW
            || state == STATE_SHOW
            || state == STATE_BLINK) {
        return 0;
    } else {
        return -1;
    }
}

int DispProto::WarnCar(int car_color, int car_state, int hw0, int hw1,
        int dot_show, int hw_state, int hw_color)
{
    uint8_t payload[8];
    int err = check_state(car_state) || check_state(hw_state);
    if (err)
        return err;

    payload[0] = DISP_CARDIGIT;
    payload[1] = !(car_color == 0);
    payload[2] = car_state;
    payload[3] = hw0;
    payload[4] = hw1;
    payload[5] = !(dot_show == 0);
    payload[6] = hw_state;
    payload[7] = !(hw_color == 0);
    fill_can_frame(DISP_CAN_ID_LED_CTRL, payload);
    return 0;
}
void DispProto::displinecar(int left, int right, int level, int digit1, int digit2)
{
    uint8_t payload[8];

    payload[0] = DISP_LINE_CAR;
    payload[1] = left;
    payload[2] = right;
    payload[3] = level;
    payload[4] = digit1;
    payload[5] = digit2;
    fill_can_frame(DISP_CAN_ID_LED_CTRL, payload);
}
void DispProto::displine(int left, int right)
{
    uint8_t payload[8];

    payload[0] = DISP_LINE;
    payload[1] = left;
    payload[2] = right;
    fill_can_frame(DISP_CAN_ID_LED_CTRL, payload);
}
void DispProto::WarnLine(int left, int right)
{
    displine(left, right);
}

void DispProto::WarnLumi(int idx) 
{
    uint8_t payload[8];

    payload[0] = DISP_LUMI;
    payload[1] = idx; 
    fill_can_frame(DISP_CAN_ID_LED_CTRL, payload);
}

void DispProto::WarnLineCar(int left, int right, int level, float time, float speed)
{
    int digit1;
    int digit2;
    static struct timeval warn_time_limit = {0, 0};
    struct timeval current;
#ifdef ENABLE_LAST_WARN
    static int last_warn = 0;
#endif
    int disp_level = get_disp_level(level, time, speed);

    gettimeofday(&current, NULL);

#ifdef ENABLE_LAST_WARN
    int new_warn = (left << 24) | (right << 16) | (level == -1);
    if (!new_warn && last_warn) {
        stop_audio();
    }

    if (!t_greater(&current, &warn_time_limit) && (last_warn == new_warn))
        return ;
#endif

    if (speed < 0.00001) {
        digit1 = digit2 = 10;
    } else {
        digit1 = (int)time;
        digit2 = (int)(round((time - digit1)*10));
    }
    printf("disp_level %d speed %f %x.%x\n", disp_level, speed, digit1, digit2);

    displinecar(left, right, disp_level, digit1, digit2);
#ifdef ENABLE_LAST_WARN
    last_warn = new_warn;
#endif
    warn_time_limit.tv_sec += 1;

#if 0
    if (level == 2) {
        //play_audio("warning_yellow_01.mp3", audio_finished_cb, NULL);
    } else if (level ==  3) {
        //play_audio("warning_red_01.mp3", audio_finished_cb, NULL);
    } else if (left || right) {
        //play_audio("ldw_warn.mp3", audio_finished_cb, NULL);
    }
#endif
}

#if 0
void DispProto::do_disp(const sp<AMessage> &msg, MECANCarInfMsg_S *carInf)
{
    int digit_show = STATE_SHOW;
    int car_show = STATE_SHOW;
    int car_color = COLOR_WHITE;
    AString msgDat;

    msg->findString("data", &msgDat);
    MECANWarnMsg_S* pAdasMsg;
    pAdasMsg = (MECANWarnMsg_S*)msgDat.c_str();
    int32_t state = STATE_NOT_SHOW;

    int32_t kEvent = msg->what();
    //ALOGD("disp event 0x%x", kEvent);
    uint8_t speed = reinterpret_cast<uint8_t>(carInf->speed);
    static uint8_t s_lastspeed = 0xFF;
    float time = pAdasMsg->headway_measurement * 0.1;
    int hw0 = pAdasMsg->headway_measurement/10;
    int hw1 = pAdasMsg->headway_measurement%10;

    if (pAdasMsg->left_ldw != mLastAdasMsg.left_ldw) {
        ALOGD("left trigger...");
        displine(pAdasMsg->left_ldw, 0);
    } 
    if (pAdasMsg->right_ldw != mLastAdasMsg.right_ldw) {
        displine(0, pAdasMsg->right_ldw);
        ALOGD("right trigger...");
    }

    if (pAdasMsg->peds_in_dz != mLastAdasMsg.peds_in_dz) {
        ALOGD("peds trigger...");
        state = pAdasMsg->peds_in_dz ? STATE_SHOW : STATE_NOT_SHOW;
        WarnPedestrian(state);
    }

    if (pAdasMsg->peds_fcw != mLastAdasMsg.peds_fcw) {
        ALOGD("peds_fcw trigger...");
        state = pAdasMsg->peds_fcw ? STATE_BLINK : STATE_NOT_SHOW;
        WarnPedestrian(state);
    }
    
    if (pAdasMsg->fcw_on && !mLastAdasMsg.fcw_on) {
        ALOGD("fcw trigger...");
        WarnCar(COLOR_RED, STATE_BLINK, hw0, hw1, STATE_SHOW, STATE_NOT_SHOW, COLOR_RED);
    } 
    if (pAdasMsg->fcw_on) { //highest priority
        goto out;
    }

    //WarnLumi(1);
    if (time == 0) {
        digit_show = STATE_NOT_SHOW;
    } else {
        digit_show = STATE_SHOW;
    }
    if (speed <= 0) {
        hw0 = hw1 = 10;
        digit_show = STATE_SHOW;
    }
    if (pAdasMsg->headway_valid) {
        car_show = STATE_SHOW;
    } else {
        car_show = STATE_NOT_SHOW;
    }
    if(pAdasMsg->headway_warning_level == 2) {
        car_color = COLOR_RED;
    } else {
        car_color = COLOR_WHITE;
    }

    if (pAdasMsg->headway_warning_level != mLastAdasMsg.headway_warning_level ||\
            pAdasMsg->headway_measurement != mLastAdasMsg.headway_measurement ||\
            s_lastspeed != speed ||\
            pAdasMsg->headway_valid != mLastAdasMsg.headway_valid) {
        WarnCar(car_color, car_show, hw0, hw1, STATE_SHOW, digit_show, COLOR_WHITE);
    }

out:
    s_lastspeed = speed;
    mLastAdasMsg = *pAdasMsg;

    return; 
}
#endif

