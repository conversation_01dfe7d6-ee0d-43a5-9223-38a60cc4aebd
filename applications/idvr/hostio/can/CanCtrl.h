#ifndef __CAN_CTRL_H__
#define __CAN_CTRL_H__

#include <stdint.h>
#include "McuConfig.h"
#include "DispProto.h"
#include "adas_alert.h"
#include "HostioManager.h"

/**********CAN prot**********************/
typedef struct
{
    /*
     *0 - disable
     *1 - enable
     */
    uint8_t activate        :1;
    /*
     *0 - mask
     *1 - list
     */
    uint8_t useListMode     :1;
    /*
     *0 - 16 bit
     *1 - 32 bit
     */
    uint8_t use32BitMode    :1;
    /*
     *0 - 28
     */
    uint8_t filterNumber    :5;

    uint32_t fxR1;
    uint32_t fxR2;
} __attribute__((packed)) UrtpCanFilterT;

typedef struct CanBaudParam
{
    /*0 -> 1024*/
    unsigned short  prescaler;
    /*0 -> 3*/
    unsigned char    sjw;
    /*0 -> F*/
    unsigned char    ts1;
    /*0 -> 7*/
    unsigned char    ts2;
} CanBaudParam;

#if 0
typedef struct
{
    uint32_t id;
    uint8_t  len;
    uint8_t  data[8];
}  __attribute__((packed)) UrtpCanT;
#endif

typedef struct
{
    uint8_t accelerator = 0;    /*加速踏板开度，百分比*/
    uint8_t turn        = 0;
    uint8_t brake       = 0;
    uint8_t door_open   = 0;

    uint8_t gear         : 4;   /*档位模式：0 N, 1 D, 2 R, 3 L, 4 M, 5 P, 6 S*/
    uint8_t reverse      : 1;
    uint8_t EHB_state    : 1;
    uint8_t EHB_park_req : 1;
    uint8_t EHB_park_done: 1;

    uint8_t FLWheelSpdStat : 2; /*0 正常，1 故障，2， 初始，3 保留*/
    uint8_t FRWheelSpdStat : 2; /*0 正常，1 故障，2， 初始，3 保留*/
    uint8_t BLWheelSpdStat : 2; /*0 正常，1 故障，2， 初始，3 保留*/
    uint8_t BRWheelSpdStat : 2; /*0 正常，1 故障，2， 初始，3 保留*/
    double FLWheelSpd   = 0;    /*前左轮轮速*/
    double FRWheelSpd   = 0;    /*前右轮轮速*/
    double BLWheelSpd   = 0;    /*后左轮轮速*/
    double BRWheelSpd   = 0;    /*后右轮轮速*/

    double speed        = 0;
    double mileTotal    = 0;
    double mileSingle   = 0;
    double fuelTotal    = 0;
    double fuelAvg      = 0;
    double turnSpeed    = 0;
}  __attribute__((packed)) CanInfo;

#define CAN_CONFIG_FLAG_TTCM    (0)
#define CAN_CONFIG_FLAG_ABOM    (1)
#define CAN_CONFIG_FLAG_AWUM    (2)
#define CAN_CONFIG_FLAG_NART    (3)
#define CAN_CONFIG_FLAG_RFLM    (4)
#define CAN_CONFIG_FLAG_TXFP    (5)

#define CAN_FLAG_SET(x) (1<<x)

typedef struct
{
    uint8_t     canx;
    uint16_t    prescaler;
    uint8_t     sjw;
    uint8_t     bs1;
    uint8_t     bs2;
    uint8_t     mode;
    uint8_t     flags;
} __attribute__((packed)) UrtpCanConfigT;
/**********CAN prot**********************/

typedef enum {
    MCU_CAN_IDX_CAN0_SPEED_FILTER,
    MCU_CAN_IDX_CAN0_TURN_FILTER,
    MCU_CAN_IDX_CAN0_MILE_FILTER,
    MCU_CAN_IDX_CAN0_FUEL_FILTER,
    MCU_CAN_IDX_CAN0_TSPEED_FILTER,
    MCU_CAN_IDX_CAN0_BRAKE_FILTER,
    MCU_CAN_IDX_CAN0_REVERSE_FILTER,

    MCU_CAN_IDX_CAN0_FILTER_RES1 = 11,
    MCU_CAN_IDX_CAN0_FILTER_RES2 = 12,
    MCU_CAN_IDX_CAN0_FILTER_RES3 = 13,

    STM32105_CAN1_FIDX_START = 14,
    MCU_CAN_IDX_CAN1_DISP_FILTER,
    MCU_CAN_IDX_CAN1_FILTER2,

    MCU_CAN_IDX_CAN1_FILTER_RES1 = 24,
    MCU_CAN_IDX_CAN1_FILTER_RES2 = 25,
    MCU_CAN_IDX_CAN1_FILTER_RES3 = 26,

    STM32105_FILTER_NUM_MAX = 27,
} FilterNum_E;

#define STD_ID_SHIFT   (18)
#define FT_CAN_JSON         "/system/etc/can.json"
#define SETUP_CAN_JSON      "/sdcard/run/can_input.json"

class CanCtrl
{
    public:
        CanCtrl ();
        ~CanCtrl ();

        void McuCanConfig(void);
        uint8_t onMessage(uint8_t minor, const char *buf, uint32_t len);
        void sendMsg(McuMsgCanIdxE canx, UrtpCanT *pcan);
        void setCanFilter(const can_signal &majorSig, const can_signal &minorSig, FilterNum_E n, bool activate);
        void setCanFilter(McuMsgCanIdxE canIdx, bool activate, bool bListMode, FilterNum_E n, uint32_t id[4], bool idNumIsTwo);
        void setCanMode(McuMsgCanIdxE canx, McuCanWorkingModeE mode, McuCanSpeedE baud);
        void clearAllCanFilter(void);
        void clearAllCanFilterAndStart(void);
        int32_t reloadConfig(const char *path = SETUP_CAN_JSON);
        void adas_alert_disp(ADAS_CAN700 *pAdasMsg, int speed);
        void sendSpeedToDisp(int speed);

        CanBaudParam    mBaudParam[MCU_CAN_SPEED_MAX];
        bool mCanInSilentMode;
        MCUConfig *mConfig;
        CanInfo mCarInfo;

        int mVerbose;

    private:
        bool mCanTestMode;
        DispProto mDisp;

    private:
        typedef struct FilterReg
        {
            typedef struct Reg32BitMode
            {
                uint32_t    resv        : 1;
                uint32_t    RTR         : 1;
                uint32_t    IDE         : 1;
                uint32_t    extId       : 29;
            } __attribute__((packed)) Reg32BitMode;

            typedef struct Reg16BitMode
            {
                uint16_t    extId17_15  : 3;
                uint16_t    IDE         : 1;
                uint16_t    RTR         : 1;
                uint16_t    stdId       : 11;
            } __attribute__((packed)) Reg16BitMode;

            union
            {
                uint32_t        raw;
                Reg32BitMode    rm32;
                Reg16BitMode    rm16[2];
            } __attribute__((packed)) u;
        } __attribute__((packed)) FilterReg;

        typedef struct Filter
        {
            bool        enable;
            bool        bListMode;
            bool        bBitMode32;
            FilterReg   FxR1; /* 验证码 */
            FilterReg   FxR2; /* 屏蔽码 */
            uint32_t    canIds[4];
            const char * toStr(uint32_t idx)
            {
                static char s[1024];
                snprintf(s, sizeof(s), "Filter#%02d %1d %-2s+%-5s 0x%08X 0x%08X | "
                        "0x%08X 0x%08X 0x%08X 0x%08X",
                        idx, enable, bBitMode32?"32":"16", bListMode?"list":"mask",
                        FxR1.u.raw, FxR2.u.raw,
                        canIds[0], canIds[1], canIds[2], canIds[3]);
                return s;
            }
        } Filter;
        Filter mFilters[STM32105_FILTER_NUM_MAX+1];

        typedef struct EhubCanFilter
        {
            uint32_t filter_mode :1;
            uint32_t filter_idx_msb: 2;
            uint32_t id:29;
            uint32_t filter_idx : 3;
            uint32_t id_mask:29;
        } __attribute__((packed)) EhubCanFilter;
};

#endif

