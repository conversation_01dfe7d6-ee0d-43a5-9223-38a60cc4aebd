#ifndef __MINIEYE_MCU_CONFIG_H__
#define __MINIEYE_MCU_CONFIG_H__

#include <stdio.h>
#include <unistd.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <list>
#include <map>
#include <vector>

using namespace std;

#include "rapidjson.wrap.h"

#include "can_decode.h"
#include "McuMessage.h"

#define HANDLE_TYPE_ERROR(expect)  do {\
        loge("Error: incorrect type of directive %s%s: need %s", parent, name, expect); \
        return false;}while(0)

#define CHECK_MEMBER(a, b, c, d) do{\
    if (!check_member((a), (b), (c), (d))) { \
        result = 5; \
        goto finish; \
    }}while(0)


#define CHECK_MAIN_INFO(name) do{\
    CHECK_MEMBER(doc, name, TObject, ""); \
    CHECK_MEMBER(doc[name], "use_obd", TBool, name); \
    CHECK_MEMBER(doc[name], "baudrate", TString, name);}while(0)

    
#define CHECK_CAN_SIGNAL(name) do{\
        CHECK_MEMBER(doc, name, TObject, ""); \
        CHECK_MEMBER(doc[name], "can_id", TString, name); \
        CHECK_MEMBER(doc[name], "byte_order", TInt, name); \
        CHECK_MEMBER(doc[name], "start_bit", TInt, name); \
        CHECK_MEMBER(doc[name], "size", TInt, name); \
        CHECK_MEMBER(doc[name], "min", TNumber, name); \
        CHECK_MEMBER(doc[name], "max", TNumber, name); \
        CHECK_MEMBER(doc[name], "factor", TNumber, name); \
        CHECK_MEMBER(doc[name], "offset", TNumber, name);}while(0)

#define CAN_MODE_INPUT  (0x1)
#define CAN_MODE_OUTPUT (0x2)


typedef struct CanConfig
{
    McuCanSpeedE can_speed;
    CanConfig()
    {
        can_speed = MCU_CAN_SPEED_500K;
    }
} CanConfig;

typedef enum
{
    EHUB_PROTOCOL_INVALID,
    EHUB_PROTOCOL_MINIEYE,
    EHUB_PROTOCOL_G7,
    EHUB_PROTOCOL_FT,
    EHUB_PROTOCOL_SAMPLE,
    EHUB_PROTOCOL_MAX,
} EHUB_PROTOCOL_E;

typedef struct AnalogSpeedCfg
{
    bool        enable;
    float       ratio;
    bool        disableGpsCal;
    AnalogSpeedCfg()
    {
        enable = false;
        ratio = 0;
        disableGpsCal = false;
    }
} AnalogSpeedCfg;

typedef enum
{
    ANALOG_SIGNAL_LEFT,
    ANALOG_SIGNAL_RIGHT,
    ANALOG_SIGNAL_BREAK,
    ANALOG_SIGNAL_WIPER,
    ANALOG_SIGNAL_BEAM,
    ANALOG_SIGNAL_MAX,
} AnalogSignalType;

typedef struct AnalogSignal
{
    bool    enable;
    uint8_t polarity;
    AnalogSignal()
    {
        enable = false;
        polarity = false;
    }
} AnalogSignal;


typedef struct EHubConfig
{
    CanConfig           can1;
    CanConfig           can2;
    AnalogSpeedCfg      analog_speed;
    AnalogSignal        analog_signals[ANALOG_SIGNAL_MAX];
    EHUB_PROTOCOL_E     protocol;
    EHubConfig()
    {
        protocol = EHUB_PROTOCOL_INVALID;
    }
} EHubConfig;

typedef enum
{
    CAN_SCENARIO_NORMAL,
    CAN_SCENARIO_EHUB,
    CAN_SCENARIO_CAN1_ONLY,
    CAN_SCENARIO_CAN2_ONLY,
    CAN_SCENARIO_MAX,
} CAN_SCENARIO_TYPE_E;

typedef struct CanRange
{
    uint32_t    idBegin;
    uint32_t    idEnd;
    bool inRange(uint32_t id)
    {
        return (id >= idBegin && id <= idEnd);
    }
    bool setRange(uint32_t id)
    {
        idEnd = idBegin = id;
        return true;
    }
    bool setRange(uint32_t id1, uint32_t id2)
    {
        if (id2 < id1) {
            return false;
        }
        idBegin = id1;
        idEnd = id2;
        return true;
    }
} CanRange;

typedef enum {
    AS_IO_HI_BEAM,
    AS_IO_LO_BEAM,
    AS_IO_BRAKE,
    AS_IO_TURNLAMP,
    AS_IO_SOS,
    AS_IO_MAX
}AS_IO_TYPE_E;

typedef struct AnalogConfig
{
    AnalogSignal        analogSignals[AS_IO_MAX];
    AnalogSpeedCfg      aspeed;
    AnalogConfig()
    {
        memset(analogSignals, 0, sizeof(analogSignals));
    }
} AnalogConfig;


typedef enum {
    CS_SPEED,
    CS_LEFT,
    CS_RIGHT,
    CS_REVERSE,
    CS_BRAKE,
    CS_ACCELERATOR,
    CS_GEAR,
    CS_EHB_STATE,
    CS_EHB_PARK_REQ,
    CS_EHB_PARK_DONE,

    CS_FRONT_LEFT_WHEEL_SPD,
    CS_FRONT_LEFT_WHEEL_STAT,
    CS_FRONT_RIGHT_WHEEL_SPD,
    CS_FRONT_RIGHT_WHEEL_STAT,
    CS_BACK_LEFT_WHEEL_SPD,
    CS_BACK_LEFT_WHEEL_STAT,
    CS_BACK_RIGHT_WHEEL_SPD,
    CS_BACK_RIGHT_WHEEL_STAT,

    CS_MILE_TOTAL,
    CS_MILE_TRIP,
    CS_FUEL_TOTAL,
    CS_FUEL_AVG,
    CS_TURN_SPEED,
    CS_DOOR_FRONT,
    CS_DOOR_MID
}CS_TYPE_E;

struct HalioInitInfo
{
    bool                using_OBD;
    McuCanSpeedE        input_can_speed;

    std::map<CS_TYPE_E, can_signal> csMap;

    CAN_SCENARIO_TYPE_E scenario;
    bool                enable_e1;
    EHubConfig          ehub_cfg;
    list<CanRange>      outputCanList;
    bool                enable_silent_mode;
    bool                enableFakeSpeed;
    double              fakeSpeed;
    AnalogConfig        analog_cfg;      // for m4
    bool                useGpsSpeed;
    HalioInitInfo()
    {
        using_OBD = false;
        enable_silent_mode = true;
        enableFakeSpeed = false;
        fakeSpeed = 0.0;
        input_can_speed = MCU_CAN_SPEED_500K;
        scenario = CAN_SCENARIO_NORMAL;
        enable_e1 = true;
        useGpsSpeed = false;
    }
};

class MCUConfig
{
    public:
        MCUConfig();
        ~MCUConfig();
        int32_t loadFromFile(const char *configFile);
        
        int32_t calcCanOutSendCmd(uint32_t outCanID);

    private:
        void dump_can_cfg(HalioInitInfo *info);
        int fill_main_info(const rapidjson::Value& val, HalioInitInfo* info);
        int fill_can_signal(const rapidjson::Value& val, can_signal* csp);
        int parse_int_8_10_16(const char* str);
        int fill_ehub_cfg(const rapidjson::Value& ehub, EHubConfig* ehub_cfg);
        int fillOutputIds(const rapidjson::Value& output_ids, list<CanRange> *pList);
        int fill_analog_cfg(const rapidjson::Value& analog, AnalogConfig* analog_cfg);
        void addBuiltInOutputCanID(void);

        typedef enum
        {
            TObject,
            TArray,
            TNull,
            TString,
            TBool,
            TNumber,
            TInt,
            TDouble,
        } ValueType;
        bool check_member(const rapidjson::Value& v,
            const char* name, ValueType type, const char* parent);
    public:
        HalioInitInfo   mConfig;

};

#endif


