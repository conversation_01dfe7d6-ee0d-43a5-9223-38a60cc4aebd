#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#include "CanCtrl.h"

//#define ENABLE_DEBUG_OUTPUT
#define BITS_PER_BYTE   (8)

int32_t can_decode_signal(const can_signal * signal, const unsigned char *can_buf, uint32_t buf_len, double *phy)
{
    uint32_t raw = 0;
    can_decode_signal_raw(signal, can_buf, buf_len, &raw);
    if ((false == signal->is_unsigned) && (raw & (1 << (signal->size - 1)))) {
        *phy = (int32_t)(-1 * ((1 << signal->size) - raw)) * signal->factor + signal->offset;
    } else {
        *phy = raw * signal->factor + signal->offset;
    }
#ifdef ENABLE_DEBUG_OUTPUT
    printf("%-10s %-10s %-10s %-10s\n", "Phy", "Raw", "Factor", "Offset");
    printf("%010f 0x%08x %010f %010f\n", *phy, raw, signal->factor, signal->offset);
#endif
    if (*phy < signal->min || *phy > signal->max) {
        *phy = 0;
    }
    return 0;
}

int32_t can_decode_signal_raw(const can_signal * signal, const unsigned char *can_buf, uint32_t buf_len, uint32_t *u32_raw)
{
    uint32_t bits_got = 0;
    uint32_t raw = 0;
    uint32_t bit_idx = signal->start_bit;
    if (NULL == signal || NULL == can_buf || 0 == buf_len || buf_len > 8 || NULL == u32_raw) {
        printf("Invalid param signal %p can_buf %p buf_len %d u32_raw %p\n",
                signal, can_buf, buf_len, u32_raw);
        return -1;
    }
    if (signal->byte_order != CAN_SIGNAL_BYTE_ORDER_MOTOROLA &&
        signal->byte_order != CAN_SIGNAL_BYTE_ORDER_INTEL) {
        printf("Invalid byte order %d\n", signal->byte_order);
        return -1;
    }
    if (signal->min > signal->max) {
        printf("Invalid param min %f max %f\n", signal->min, signal->max);
        return -1;
    }
    if (signal->start_bit >= buf_len * BITS_PER_BYTE ||
            signal->size > buf_len * BITS_PER_BYTE || signal->size > BITS_PER_BYTE * sizeof(uint32_t)) {
        printf("Invalid param start_bit %d size %d buf_len %d\n",
                signal->start_bit, signal->size, buf_len);
        return -1;
    }

    int32_t start_byte = signal->start_bit / BITS_PER_BYTE;
    uint32_t start_shift = signal->start_bit % BITS_PER_BYTE;
    while (bits_got < signal->size) {
        if (start_byte < 0 || start_byte > (int32_t)buf_len) {
            printf("Invalid bit range order %d %d + %d  total bits %d\n",
                    signal->byte_order, signal->start_bit, signal->size, buf_len * BITS_PER_BYTE);
            return -1;
        }
        raw |= ((can_buf[start_byte] >> start_shift) << bits_got);
        bits_got += (BITS_PER_BYTE - start_shift);
#ifdef ENABLE_DEBUG_OUTPUT
        printf("Byte#%02d Data 0x%02x Bit#%02d Raw 0x%08x start_shift %02d\n",
                start_byte, can_buf[start_byte], bits_got, raw, start_shift);
#endif
        (CAN_SIGNAL_BYTE_ORDER_MOTOROLA == signal->byte_order) ? start_byte-- : start_byte++;
        start_shift = 0;
    }
#ifdef ENABLE_DEBUG_OUTPUT
    printf("Raw 0x%08x &= 0x%08lx = 0x%08x\n", raw, (1L << signal->size) - 1, (uint32_t)(raw & ((1L << signal->size) - 1L)));
#endif
    raw &= ((1L << signal->size) - 1L);
    *u32_raw = raw;

    return 0;
}
