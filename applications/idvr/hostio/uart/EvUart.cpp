#include <termios.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <errno.h>
#include "EvUart.h"

EvUart::EvUart()
{

}

EvUart::~EvUart()
{

}

std::string EvUart::getDevPath()
{
    return mDevPath;
}

// 错误或者一直没有可读数据
void EvUart::freeUserCb(void *user)
{
    EvUart *c = (EvUart *) user;
    if (c == nullptr) {
        return;
    }

    c->onDisConnected();
}

cb_ret_t EvUart::writeCb(buffer_handle_t bufferHandle, void *user)
{
    EvUart *c = (EvUart *) user;
    if (c == nullptr) {
        return E_CB_RET_ERROR;
    }

    //loge("writeCb");
    c->mBufferHandle = bufferHandle;

    return E_CB_RET_SUCCESS;
}


cb_ret_t EvUart::readCb(buffer_handle_t bufferHandle, void *user)
{
    EvUart *c = (EvUart *) user;
    if (c == nullptr) {
        return E_CB_RET_ERROR;
    }

    if (c->onRead(bufferHandle) == false) {
        loge("%s onRead error !", c->getDevPath().c_str());
        return E_CB_RET_ERROR;
    }

    return E_CB_RET_SUCCESS;
}

// 1s一次心跳，可用来监听数据
cb_ret_t EvUart::timerCb(buffer_handle_t bufferHandle, void *user)
{
    EvUart *c = (EvUart *) user;
    if (c == nullptr) {
        return E_CB_RET_ERROR;
    }

    if (c->mReadTime.elapsed() > 5 * 1000) {
        loge("%s mReadTime error!", c->getDevPath().c_str());
    }

    return E_CB_RET_SUCCESS;
}


bool EvUart::onDisConnected()
{
    {
        std::lock_guard<std::mutex> lock(mMutex);
        logd("~ onDisConnected");
        mBufferHandle = INVALID_HANDLE;
        mConnected = false;
    }

    // 子类处理断开之后的操作
    onEvUartDisConnected();

    return true;
}

bool EvUart::onRead(buffer_handle_t bufferHandle)
{
    char buff[1024] = {0};

    for ( ; ; ) {
        int32_t n = ev_service_buffer_recv_data(bufferHandle, buff, sizeof(buff));
        if (n < 0) {
            loge("ev_service_buffer_recv_data error !");
            return false;
        } else if (n == 0) {
            break;
        } else {
#if 0
            my::string dump(buff, n);
            my::hexdump(dump, true, "EvUart");
#endif
            mReadTime = my::timestamp::now();
            if (onEvUartRecved(buff, n) == false) {
                loge("onEvUartRecved error !");
                return false;
            }

        }
    }

    return true;
}

bool EvUart::evUartsendData(const char *data, int32_t dataLen)
{
    if (data == nullptr ||
        dataLen <= 0) {
        return false;
    }
    {
        std::lock_guard<std::mutex> lock(mMutex);

        if (mBufferHandle == INVALID_HANDLE) {
            loge("send to uart failed: mBufferHandle is NULL");
            return false;
        }
    }
    int32_t len = ev_service_buffer_send_data(mBufferHandle, data, dataLen);
    if (len != dataLen) {
        loge("ev_service_buffer_send_data error ! send len = %d, dataLen = %d", len, dataLen);
        return false;
    }

    return true;
}
bool EvUart::evUartsendDataSync(const char *data, int32_t dataLen)
{
    if (mFd < 0) {
        return false;
    }
    int32_t offset = 0;
    while (offset < dataLen) {
        int32_t once = write(mFd, data + offset, dataLen - offset);
        if (once <= 0) {
            loge("write error! %s\n", strerror(errno));
            break;;
        }
        offset += once;
    }
    return offset == dataLen;
}
bool EvUart::evUartOpen(worker_handle_t workerHandle, const char *dev, int32_t baudrate, bool rtsCts)
{
    std::lock_guard<std::mutex> lock(mMutex);

    logd("connect to uart: %s : %d ...", dev, baudrate);

    mDevPath = dev;
    mBaudrate = baudrate;

    mFd = ::open(mDevPath.c_str(), O_RDWR | O_NOCTTY | O_NONBLOCK);
    if (mFd < 0) {
        logpe("open %s error !", mDevPath.c_str());
        return false;
    }

    logd("open %s success, fd: %d", mDevPath.c_str(), mFd);

    struct termios opt;
    memset(&opt, 0, sizeof(opt));

    tcflush(mFd, TCIOFLUSH);


    if (tcgetattr(mFd, &opt)) {
        return false;
    }

    logd("set tty baudrate %d\n", mBaudrate);

    speed_t speed = speed_convert(mBaudrate);
    cfsetispeed(&opt, speed);
    cfsetospeed(&opt, speed);


    opt.c_iflag = 0;
    opt.c_cflag &= ~CSIZE;
    opt.c_cflag |= CS8;
    opt.c_cflag &= ~(PARENB | PARODD);
    if (rtsCts) {
        opt.c_cflag |= CRTSCTS;
    } else {
        opt.c_cflag &= ~CRTSCTS;
    }
    opt.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);
    opt.c_oflag = 0;
    tcsetattr(mFd, TCSAFLUSH, &opt);

    mReadTime = my::timestamp::now();

    if (workerHandle != INVALID_HANDLE) {
        buffer_user_parm_t userParm = {0};
        userParm.user = this;
        userParm.free_user_cb = freeUserCb;
        userParm.write_cb = writeCb;
        userParm.read_cb = readCb;
        userParm.timer_cb = timerCb;

        if (ev_service_file_dev_create(workerHandle, 60, /* 多长时间没有接收到数据就算超时，单位秒 */
            mFd, &userParm) == false) {
            evUartClose();
            mFd = -1;
            loge("ev_service_file_dev_create error !");
            return false;
        }
    }
    logd("evUartOpen end");
    mConnected = true;

    return true;
}

bool EvUart::evUartClose()
{
    std::lock_guard<std::mutex> lock(mMutex);
    logd("evUartClose");
    if (mBufferHandle != INVALID_HANDLE) {
        ev_service_file_dev_destroy(mBufferHandle);
        mBufferHandle = INVALID_HANDLE;
    }

    if (mFd > 0) {
        ::close(mFd);
    }

    mFd = -1;
    mConnected = false;
    return true;
}

