#ifndef IDVR_EV_UART_H__
#define IDVR_EV_UART_H__
#include "mystd.h"
#include "ev_service.h"

#include <mutex>


class EvUart
{

public:
    EvUart();
    virtual ~EvUart();

    bool evUartOpen(worker_handle_t workerHandle, const char *dev, int32_t baudrate = 115200, bool rtsCts = false);
    bool evUartClose();
    bool evUartsendData(const char *data, int32_t len);
    bool evUartsendDataSync(const char *data, int32_t dataLen);
    bool isEvUartConnected() 
    { 
        return mConnected; 
    }
    std::string getDevPath();

protected:
    virtual void onEvUartDisConnected() = 0;
    virtual bool onEvUartRecved(const char *data, int32_t dataLen) = 0;


private:

    bool onDisConnected();
    bool onRead(buffer_handle_t bufferHandle);

    static void freeUserCb(void *user);
    static cb_ret_t writeCb(buffer_handle_t bufferHandle, void *user);
    static cb_ret_t readCb(buffer_handle_t bufferHandle, void *user);
    static cb_ret_t timerCb(buffer_handle_t bufferHandle, void *user);
    static speed_t speed_convert(int32_t bps)
    {
        switch (bps)
        {
            case 0: return B0;
            case 50: return B50;
            case 75: return B75;
            case 110: return B110;
            case 150: return B150;
            case 200: return B200;
            case 300: return B300;
            case 600: return B600;
            case 1200: return B1200;
            case 1800: return B1800;
            case 2400: return B2400;
            case 4800: return B4800;
            case 9600: return B9600;
            case 19200: return B19200;
            case 38400: return B38400;
            case 57600: return B57600;
            case 115200: return B115200;
            case 230400: return B230400;
            case 460800: return B460800;
            case 500000: return B500000;
            case 576000: return B576000;
            case 921600: return B921600;
            case 1000000: return ********;
            case 1152000: return ********;
            case 1500000: return ********;
            case 2000000: return ********;
            case 2500000: return ********;
            case 3000000: return ********;
            case 3500000: return ********;
            case 4000000: return ********;
            default: return B0;
        }
    }


private:
    std::mutex              mMutex;

    buffer_handle_t         mBufferHandle = INVALID_HANDLE;

    int32_t                 mFd = -1;
    std::string             mDevPath;
    int32_t                 mBaudrate;
    my::timestamp           mReadTime = 0;
    bool                    mConnected = false;

};

#endif

