#ifndef __FILE_MONITOR_H__
#define __FILE_MONITOR_H__

#include <sys/inotify.h>
#include "mystd.h"

enum {
    INOTIFY_SDCARD_RUN_DIR,
    INOTIFY_MAX
};



const char inotify_file[INOTIFY_MAX][64] = {
    "/sdcard/run/",
};

class FileMonitor 
{
    public:
        FileMonitor();
        ~FileMonitor();
        int32_t init(void);
        bool loop(void);
 

    public:
        bool mCanInputJsonWrEvent;

    private:
        void inotifyEvent(struct inotify_event *i);
        int32_t mIfd;
        int32_t mWd[INOTIFY_MAX];
        int32_t mNfd;
};





#endif



