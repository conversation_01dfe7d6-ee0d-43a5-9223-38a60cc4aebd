#include <stdio.h>
#include <stdint.h>
#include <unistd.h>
#include <dlfcn.h>
#include <time.h>
#include <pthread.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <sys/time.h>

#include "rapidjson.wrap.h"


#include "ipcAgent.h"
#include "McuMessage.h"
#include "DispProto.h"


using namespace rapidjson;

IpcClient client("data_collector", SH_NAME_HOSTIO);

#define MAX_LOG_FILENAME_LEN    (128)
struct cmd_line_param
{
    bool enable_camera;
    bool enable_speed;
    bool enable_gps;
    bool enable_gsensor;
    bool enable_mobileye;
    bool enable_radar;
    bool enable_fmu;
    bool enable_wrs;
    bool enable_can_dumper;
    bool dump_display_unit;
    bool enable_uart_receiver;
    bool send_can;
    bool test_display_unit;
    bool show_car_info_on_disp;
    bool enable_cmd_socket;
    bool enable_baudrate_detect;
    char *send_can_arg;
    bool test_mtk_audio;
    uint32_t recordMask;

    bool enable_led;
    bool truncate_log;
    int  display_test_patten;
    int  speed_type;
    int  verbose;
    char output_file[MAX_LOG_FILENAME_LEN];

    bool          using_can_config;
};
struct cmd_line_param param;

void comm_send(McuMsgTypeE cmd, uint8_t *data, int32_t len)
{
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];
    memset(buf, 0, sizeof(buf));
    pMsg->ipcMsg.type = cmd;
    pMsg->ipcMsg.len = len;
    if(len > 0) {
        memcpy(&pMsg->u.u8Array[0], data, len);
    }
    client.send(&(pMsg->ipcMsg));
}

#if 1
void update_gps_json(int located, int qa, int sat)
{
#if 0
    //string, ['not_found', 'not_located', 'located']
    //string, ['excellent', 'good', 'fair', 'poor'],
    {
        "status": "located",
        "level": "excellent",
        "count": 26
    }
#endif

#define ASSIGN_VAL(str) val.assign(str, sizeof(str));writer.String(val.c_str());

    StringBuffer s;
    Writer<StringBuffer> writer(s);
    string val;

    writer.StartObject();               // Between StartObject()/EndObject(),

    writer.Key("status");
    if (located == 0) {
        ASSIGN_VAL("not_located");
    } else if (located == 1) {
        ASSIGN_VAL("located");
    } else {
        ASSIGN_VAL("not_found");
    }

    writer.Key("level");
    if(qa == 0) {
        ASSIGN_VAL("poor");
    } else if (qa == 1) {
        ASSIGN_VAL("fair");
    } else if (qa == 2) {
        ASSIGN_VAL("good");
    } else {
        ASSIGN_VAL("excellent");
    }

    writer.Key("count");
    writer.Int(sat);

    writer.EndObject();
    fprintf(stdout,"%s\n", s.GetString());

    return;
}
#endif


void update_io_json(IOStatus *vehi)
{

#define ASSIGN_VAL(str) val.assign(str, sizeof(str));writer.String(val.c_str());


    //emergency_alarm 1 low_beam 0 high_beam 0 turn_left 0 turn_right 0 brake 0
    StringBuffer s;
    Writer<StringBuffer> writer(s);
    string val;

    writer.StartObject();               // Between StartObject()/EndObject(),

    writer.Key("emergency_alarm");
    writer.Int(vehi->emergency_alarm);

    writer.Key("low_beam");
    writer.Int(vehi->normal_light);

    writer.Key("high_beam");
    writer.Int(vehi->far_light);

    writer.Key("turn_left");
    writer.Int(vehi->left_turn);

    writer.Key("turn_right");
    writer.Int(vehi->right_turn);

    writer.Key("brake");
    writer.Int(vehi->brake);

    writer.EndObject();
    fprintf(stdout,"%s\n", s.GetString());

    return;
}



int loadCanConfig(char *config)
{
    /* config not exsist */
    if(!config || access(config, F_OK)) {
        printf("can config file(%s) not exsist\n", config);
        return -1;
    }

    McuMsgCanConfigT can_config = {
        .canx = MCU_CAN_IDX_CAN0_SPEED,
        .speed = MCU_CAN_SPEED_500K,
        .mode = MCU_CAN_WORKING_MODE_NORMAL,
        .use_json = 1,
    };

    snprintf(can_config.file, sizeof(can_config.file), "%s", config);
    comm_send(MCU_MSG_TYPE_CONFIG_CAN, (uint8_t *)&can_config, sizeof(McuMsgCanConfigT));

    return 0;
}

void rawbuf_dump(struct timeval *tv, int id, unsigned char *canbuf)
{
    char buf[2048];

    if (param.verbose > 1 || param.enable_can_dumper) {
        int i;
        printf("RawCan %010ld %06ld|0x%04x| ", tv->tv_sec, tv->tv_usec, id);
        for (i=0; i<8; i++) {
            printf("0x%02hhx ", canbuf[i]);
        }
        printf("\n");
    }
}

void speed_callback(struct timeval *t, int type, float speed)
{
    static float last_speed = -10000;
    static int32_t last_turn = 0;
    char buf[256];
    DispProto Disp;

    if (type == 0) { /* speed */
        snprintf(buf, sizeof(buf), "speed %f", speed);
        if (param.verbose) printf("%s\n", buf);

        if (param.show_car_info_on_disp && abs(int(speed - last_speed)) > 1) {
            if (speed >= 100) {
                Disp.WarnCar(COLOR_WHITE, STATE_SHOW, ((int)speed - 100) / 10, ((int)speed - 100) % 10,
                        0, 1, COLOR_WHITE);

            } else {
                Disp.WarnCar(COLOR_WHITE, STATE_NOT_SHOW, ((int)speed) / 10, ((int)speed) % 10,
                        0, 1, COLOR_WHITE);
            }
            comm_send(MCU_MSG_TYPE_RAW_CAN1, (uint8_t *)&Disp.mPkg, sizeof(UrtpCanT));
            last_speed = speed;
        }
    } else if (type == 1) { /* turn */
        snprintf(buf, sizeof(buf), "turnlamp %d", (int)speed);
        if (param.verbose) printf("%s\n", buf);
        if (param.show_car_info_on_disp && last_turn != speed) {
            int32_t left_on = ((int32_t) speed ) & 0x1;
            int32_t right_on = ((int32_t) speed ) & 0x2;
            Disp.WarnLine(left_on, right_on);
            comm_send(MCU_MSG_TYPE_RAW_CAN1, (uint8_t *)&Disp.mPkg, sizeof(UrtpCanT));
            last_turn = (int32_t) speed;
        }
    }
}

static int init_cmd_socket(void)
{
    #define DATA_COLLECTOR_CMD_PORT (2017)
    int s = -1;
    int enable = 1;
    int32_t ret = 0;
    const char *server_ip = "127.0.0.1";
    struct sockaddr_in minit_serv_addr;

    s = socket(AF_INET, SOCK_STREAM, 0);
    if (s < 0) {
        printf("Create socket failed %s\n", strerror(errno));
        exit(1);
    }
    setsockopt(s, SOL_SOCKET, SO_REUSEADDR, &enable, sizeof(enable));

    memset(&minit_serv_addr, 0, sizeof(minit_serv_addr));
    minit_serv_addr.sin_family = AF_INET;
    minit_serv_addr.sin_port   = htons(DATA_COLLECTOR_CMD_PORT);

    ret = inet_aton(server_ip, &minit_serv_addr.sin_addr);
    if (0 == ret) {
        printf("inet_aton failed %d %s\n", ret, strerror(errno));
        exit(1);
    }

    ret = bind(s, (struct sockaddr *) &minit_serv_addr, sizeof(minit_serv_addr));
    if (0 != ret) {
        printf("bind failed %d %s\n", ret, strerror(errno));
        exit(2);
    }

    ret = listen(s, 1);
    if (0 != ret) {
        printf("listen failed %d %s\n", ret, strerror(errno));
        exit(3);
    }
    return s;
}

void *cmd_socket_thread(void *)
{
    DispProto Disp;
    int32_t ret = 0;
    int in_sock = -1;
    struct sockaddr_in in_sa;
    uint32_t in_sa_len = 0;
    char cmd_buf[1024];
    int32_t cmd_sock = init_cmd_socket();
    if (cmd_sock < 0) {
        return NULL;
    }
    while (1) {
        in_sock = accept(cmd_sock, (struct sockaddr *) &in_sa, &in_sa_len);
        if (in_sock < 0) {
            printf("accept failed %d %s\n", ret, strerror(errno));
            exit(4);
        }
        while (1) {
            memset(cmd_buf, 0, sizeof(cmd_buf));
            ret = read(in_sock, cmd_buf, sizeof(cmd_buf));
            if (ret < 0) {
                printf("read failed %d %s\n", ret, strerror(errno));
                break;
            } else if (ret == 0) {
                printf("read 0\n");
                //sleep(1);
                //continue;
                close(in_sock);
                break;
            }
            printf("Read %05d|%s\n", ret, cmd_buf);
            if (strstr(cmd_buf, "WarnCar") == cmd_buf) {
                int a, b, c, d, e, f, g;
                sscanf(cmd_buf + 7, "%d%d%d%d%d%d%d", &a, &b, &c, &d, &e, &f, &g);
                Disp.WarnCar(a, b, c, d, e, f, g);
                comm_send(MCU_MSG_TYPE_RAW_CAN1, (uint8_t *)&Disp.mPkg, sizeof(UrtpCanT));

            } else if (strstr(cmd_buf, "WarnLine") == cmd_buf) {
                int left, right;
                sscanf(cmd_buf + 8, "%d%d", &left, &right);
                Disp.WarnLine(left, right);
                comm_send(MCU_MSG_TYPE_RAW_CAN1, (uint8_t *)&Disp.mPkg, sizeof(UrtpCanT));
            } else {
                fprintf(stderr, "Error: unknown command: %s\n", cmd_buf);
            }
        }
        close(in_sock);
    }

    return NULL;
}

typedef enum
{
    CAN_SCENARIO_NORMAL,
    CAN_SCENARIO_EHUB,
    CAN_SCENARIO_CAN1_ONLY,
    CAN_SCENARIO_CAN2_ONLY,
    CAN_SCENARIO_MAX,
} CAN_SCENARIO_TYPE_E;

void read_msg_loop(void)
{
    McuMsgMcuStatT *ss;
    IOStatus *vehi;
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];
    time_t last_time=0, cur_time=0;
    int last_key = 0;
    ss = (pMsg->u.stat);
    vehi = &ss->vehicle_io;

    while(1)
    {
        if (client.recv(&(pMsg->ipcMsg))) {
            struct timeval tv;
            gettimeofday(&tv, NULL);

            if(pMsg->ipcMsg.type == MCU_MSG_TYPE_STAT) {
                if(pMsg->ipcMsg.len != sizeof(McuMsgMcuStatT)) {
                    fprintf(stdout,"McuMsgMcuStatT msg len error!\n");
                }

#define CALLBACK_SPEED 0
#define CALLBACK_TURNLAMP 1
                speed_callback(&tv, CALLBACK_SPEED, ss->speed_x10/10.0);

                int turn = (ss->turnr << 1) | ss->turnl;
                speed_callback(&tv, CALLBACK_TURNLAMP, (float)turn);

            } else if (pMsg->ipcMsg.type == MCU_MSG_TYPE_RAW_CAN0) {
                //printf("can0 msg..\n");
                if (pMsg->ipcMsg.len != sizeof(UrtpCanT)) {
                    printf("Can Msg len error!\n");
                    return;
                }
                UrtpCanT *p = reinterpret_cast<UrtpCanT *>(pMsg->u.uchar);

                rawbuf_dump(&tv, p->id, p->data);
            }
        }
    }
}

int32_t read_msg_timeout(McuMsgTypeE cmd, McuMessage *pMsg, int32_t timeout_ms)
{
    uint32_t ms;
    struct timespec ts;
    struct timespec now;

    clock_gettime(CLOCK_REALTIME, &ts);
    while (1) {
        if (!client.recv(&(pMsg->ipcMsg), 20)) {
            //fprintf(stdout, "recv timeout\n");
        } else {
            if (pMsg->ipcMsg.type == cmd) { /*get msg*/
                break;
            }
        }

        clock_gettime(CLOCK_REALTIME, &now);
        if((now.tv_sec - ts.tv_sec )*1000 + now.tv_nsec/1000000 > ts.tv_nsec/1000000 + timeout_ms) {
            return -1;
        }
    }
    return 0;
}

int32_t get_gps_info(void)
{
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];

    McuMsgMcuStatT *ss;
    ss = reinterpret_cast<McuMsgMcuStatT*>(pMsg->u.u8Array);

    int cnt = 0;
    while(client.recv(&(pMsg->ipcMsg), 10)) {
        //printf("recv loop %d\n", cnt++);
        if(cnt >= 30) {
            break;
        }
    }

    if (!read_msg_timeout(MCU_MSG_TYPE_STAT, pMsg, 150)) {
        //printf("sig level %d\n", ss->lbs.sig_level);
        update_gps_json(ss->lbs.status, ss->lbs.sig_level, ss->lbs.sat);
        return 0;
    }

    update_gps_json(-1, 0, 0);
    return -1;
}

bool read_gpio(IOStatus *vehi)
{
    McuMsgMcuStatT *ss;
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];

    if (!read_msg_timeout(MCU_MSG_TYPE_STAT, pMsg, 20)) {
        ss = reinterpret_cast<McuMsgMcuStatT*>(pMsg->u.uchar);
        memcpy(vehi, &ss->vehicle_io, sizeof(IOStatus));
        return true;
    }
    return false;
}

void read_io_signal(void)
{
#define IO_TO_HUMAN(n) ((n==0) ? "0" : "1")

#define IO_DUMP(io) do{\
    fprintf(stdout,"emergency_alarm %s low_beam %s high_beam %s turn_left %s turn_right %s brake %s\n"\
            ,IO_TO_HUMAN(io.emergency_alarm)\
            ,IO_TO_HUMAN(io.normal_light)\
            ,IO_TO_HUMAN(io.far_light)\
            ,IO_TO_HUMAN(io.left_turn)\
            ,IO_TO_HUMAN(io.right_turn)\
            ,IO_TO_HUMAN(io.brake));\
}while(0)

    IOStatus vehi;

    bool ret = true;;
    while(ret) {
        ret = read_gpio(&vehi);
        //printf("read.\n");
    }
    //IO_DUMP(vehi);
    update_io_json(&vehi);
}

int32_t read_can_frame(McuMsgCanT *can_msg, uint32_t timeout_ms)
{
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];

    if (!read_msg_timeout(MCU_MSG_TYPE_RAW_CAN0, pMsg, timeout_ms)) {
        memcpy(can_msg, pMsg->u.uchar, sizeof(McuMsgCanT));
        return 0;
    }
    return -1;
}

void config_can_speed_with_mode(McuCanSpeedE speed, McuMsgCanIdxE canx, McuCanWorkingModeE mode)
{
    McuMsgCanConfigT can_config = {
        .canx = canx,
        .speed = speed,
        .mode = mode,
        .use_json = 0,
    };
    comm_send(MCU_MSG_TYPE_CONFIG_CAN, (uint8_t *)&can_config, sizeof(McuMsgCanConfigT));
}

int32_t baudrate_detect(CAN_SCENARIO_TYPE_E scenario, bool using_OBD)
{
    int32_t count = 0;
    int32_t ret = 0;
    uint32_t b_idx = 0;
    McuMsgCanT can_msg = {0};
    McuCanSpeedE baudrate_to_detect[ ] = {
        MCU_CAN_SPEED_500K, MCU_CAN_SPEED_250K, MCU_CAN_SPEED_125K,
        MCU_CAN_SPEED_100K, MCU_CAN_SPEED_800K, MCU_CAN_SPEED_1M,
        MCU_CAN_SPEED_50K, MCU_CAN_SPEED_20K, MCU_CAN_SPEED_10K,
    };

    const char *baudrate_str[  ] = {
        "BInvalid", "1M", "800K", "500K", "250K", "125K", "100K",
        "50K", "20K", "10K", "5K",

    };

    /* cleart filter */
    comm_send(MCU_MSG_TYPE_CLEAR_ALL_FILTER, NULL, 0);
    do {
        McuCanSpeedE input_can_speed = baudrate_to_detect[b_idx];

        printf("Try baudrate %s\n", baudrate_str[baudrate_to_detect[b_idx]]);
        config_can_speed_with_mode(input_can_speed, MCU_CAN_IDX_CAN0_SPEED, MCU_CAN_WORKING_MODE_SILENT);
        sleep(1);
        #define CAN_FRAME_WAIT_MS       (5 * 1000)
        #define CAN_FRAME_READ_COUNT    (500)
        count = 0;
        do {
            ret = read_can_frame(&can_msg, CAN_FRAME_WAIT_MS);
            if (0 == ret) {
                printf("#%05d CAN message read 0x%08x\n", count, can_msg.id);
                count ++;
            } else {
                break;
            }
        } while (ret == 0 && ++ count < CAN_FRAME_READ_COUNT);
        if (count >= CAN_FRAME_READ_COUNT) {
            printf("CAN baudrate detected:%s\n",
                    baudrate_str[baudrate_to_detect[b_idx]]);
            return 0;
        } else {
            printf("CAN frame read %d need %d\n", count, CAN_FRAME_READ_COUNT);
        }
    } while (++ b_idx < sizeof(baudrate_to_detect)/sizeof(baudrate_to_detect[0]));
    printf("CAN baudrate detected failed!!\n");
    return -1;
}


#define GETOPT_OPT_STR  "hvc::OidDpgmrfUkawu7nb:A:W:S:o:l:L:t:s:V::C:e:z:E:K:T::"
static void usage(const char *exe_name)
{
    printf("Usage:%s <switches> [option]\n", exe_name);
    printf("\n");

    printf("#Existing collector switches:\n");
    printf("%-10s %s\n", "-p", "get GPS info");
    printf("%-10s %s\n", "-i", "get io info");
    printf("%-10s %s\n", "-s <stype>", "Enable speed collector");
    printf("%-10s %s\n", "-d", "Enable Can message dumper");
    printf("%-10s %s\n", "-D", "Enable Can message dumper, include display unit");
    printf("%-10s %s\n", "-W", "Send warning message to display unit");
    printf("%-10s %s\n", "-S", "Send can message");
    printf("%-10s %s\n", "-O", "Show car info on display uinit");
    printf("%-10s %s\n", "-n", "Listen on port 2017 for cmd");
    printf("%-10s %s\n", "-b <scenario>", "Baudrate detect ehub|normal ");

    printf("\n");
    printf("#Speed protocol supported(-s <stype>):\n");
    printf("%-10s %s\n", "o", "OBD speed protocol");

    printf("\n");
    printf("#Misc options\n");
    printf("%-10s %s\n", "-h", "Show this help message");
    printf("%-10s %s\n", "-v", "Show program version");
    printf("%-10s %s\n", "-V <level>", "Set program verbose level");

    printf("\n");
}

int main(int argc, const char *argv[])
{
    int opt = 0;
    setlinebuf(stdout);
    while ((opt = getopt(argc, (char * const *)argv, GETOPT_OPT_STR)) != -1) {
        switch(opt) {
        default:
        case 'h':
            usage(argv[0]);
            exit(0);
            break;
        case 'i':
            {
                read_io_signal();
            }
            exit(0);
        case 'd':
            param.enable_can_dumper = true;
            break;
        case 'D':
            param.enable_can_dumper = true;
            param.dump_display_unit = true;
            break;
        case 'V':
            if (!optarg) {
                param.verbose = 5;
            } else {
                param.verbose = atoi(optarg);
            }
            break;
        case 'o':
            strncpy(param.output_file, optarg, sizeof(param.output_file));
            break;
        case 'p':
            get_gps_info();
            exit(0);
        case 'O':
            param.show_car_info_on_disp = true;
            break;
        case 'n':
            param.enable_cmd_socket = true;
            break;
        case 'b':
            param.enable_baudrate_detect = true;
            if (0 == strcasecmp("normal", optarg)) {
                return baudrate_detect(CAN_SCENARIO_NORMAL, false);
            }
            break;
        case 's':
            param.enable_speed = true;
            if (!access(optarg, F_OK)) {
                printf("Using can config file %s\n", optarg);
                int err = loadCanConfig(optarg);
                if (err) {
                    printf("loadCanConfig failed %d\n", err);
                    exit(-1);
                }
                param.using_can_config = true;
            }
            break;
        }
    }

    if (param.enable_can_dumper) {
        comm_send(MCU_MSG_TYPE_CLEAR_ALL_FILTER, NULL, 0);
    }

    if (param.enable_cmd_socket) {
        pthread_t pid;
        pthread_create(&pid, NULL, cmd_socket_thread, NULL);
        pthread_detach(pid);
    }

    read_msg_loop();
}

