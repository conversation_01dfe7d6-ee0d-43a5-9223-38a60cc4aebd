executable("data_collector") {
    sources = [
        "data_collector.cpp",
        "../can/DispProto.cpp",
    ]

    deps = [
        "//foundation/communication/property:property",
        "//foundation/communication/ipcAgent:ipcAgent",
        "//foundation/base/core/mystd:mystd",
    ]

    include_dirs = [
        "../can",
        "//applications/idvr/include/idvr",
        "//third_party/rapidjson-1.1.0",
    ]

    ldflags = [ "-lpthread" ]
}

executable("mcu_tool") {
    sources = [
        "mcu_tool.cpp",
        "../can/DispProto.cpp",
        "uart.cpp",
        "mcu_upgrade.cpp",
        "common.cpp",
    ]

    deps = [
        "//foundation/communication/property:property",
        "//foundation/communication/ipcAgent:ipcAgent",
        "//foundation/base/core/mystd:mystd",
        "//foundation/base/service/tts/libtts",
        "//foundation/communication/socketcmd",
        "//third_party/msgpack-c:msgpackc",
        "//foundation/communication/libflow:flowWrap",
        "//foundation/base/service/imu/libimu:imu",
    ]

    include_dirs = [
        "../can",
        "//applications/idvr/include/idvr",
        "//third_party/rapidjson-1.1.0",
    ]

    ldflags = [ 
        "-ldl",
        "-lpthread",
     ]

}

executable("fat_tool") {
    sources = [
        "fat_tool.cpp",
    ]

    deps = [
        "//foundation/base/core/mystd:mystd",
    ]
    
    ldflags = [ 
        "-ldl",
        "-lpthread",
     ]
}
