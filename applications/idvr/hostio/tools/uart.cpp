#include <termios.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <errno.h>
#include <cstring>

#include "uart.h"
#include "mystd.h"

Uart::Uart(const char *dev_name, int32_t baud) 
{
    mFd = open(dev_name, O_RDWR);
    if(mFd > 0) {
        uartInit(dev_name, baud);
    } else {
        printf("uart open fail\n");
    }
}
Uart::~Uart() 
{
    if(mFd > 0)
        close(mFd);
}

static speed_t speed_convert(unsigned bps)
{
    switch (bps)
    {
        case 0: return B0;
        case 50: return B50;
        case 75: return B75;
        case 110: return B110;
        case 150: return B150;
        case 200: return B200;
        case 300: return B300;
        case 600: return B600;
        case 1200: return B1200;
        case 1800: return B1800;
        case 2400: return B2400;
        case 4800: return B4800;
        case 9600: return B9600;
        case 19200: return B19200;
        case 38400: return B38400;
        case 57600: return B57600;
        case 115200: return B115200;
        case 230400: return B230400;
        case 460800: return B460800;
        case 500000: return B500000;
        case 576000: return B576000;
        case 921600: return B921600;
        case 1000000: return B1000000;
        case 1152000: return B1152000;
        case 1500000: return B1500000;
        case 2000000: return B2000000;
        case 2500000: return B2500000;
        case 3000000: return B3000000;
        case 3500000: return B3500000;
        case 4000000: return B4000000;
        default: return B0;
    }
}

bool Uart::uartInit(const char *dev, int32_t baud)
{
#if 1
    struct termios tty;
    memset(&tty, 0, sizeof tty);
    if (tcgetattr(mFd, &tty) != 0) {
        printf("tcgetattr failed %s", strerror(errno));
        return false;
    }

    speed_t speed = speed_convert(baud);
    cfsetospeed(&tty, speed);
    cfsetispeed(&tty, speed);

    tty.c_cflag = (tty.c_cflag & ~CSIZE) | CS8;     // 8-bit chars
    // disable IGNBRK for mismatched bdrate tests; otherwise receive break
    // as \000 chars
    tty.c_iflag &= ~IGNBRK;         // disable break processing
    tty.c_lflag = 0;                // no signaling chars, no echo,
    // no canonical processing
    tty.c_oflag = 0;                // no remapping, no delays
    tty.c_cc[VMIN]  = 0;            // read doesn't block
    tty.c_cc[VTIME] = 10;            // 0.5 seconds read timeout

    tty.c_iflag &= ~(IXON | IXOFF | IXANY); // shut off xon/xoff ctrl
    tty.c_iflag &= ~(INLCR | ICRNL);

    tty.c_cflag |= (CLOCAL | CREAD);// ignore modem controls,
    // enable reading
    tty.c_cflag &= ~(PARENB | PARODD);      // shut off parity
    tty.c_cflag |= 0;
    tty.c_cflag &= ~CSTOPB;
    tty.c_cflag &= ~CRTSCTS;

    if (tcsetattr(mFd, TCSANOW, &tty) != 0) {
        printf("tcsetattr failed %s", strerror(errno));
        return false;
    }
#else
    struct termios opt;
    memset(&opt, 0, sizeof(opt));

    if(mFd <0)
        return false;

    tcflush(mFd, TCIOFLUSH);

    // ²éÑ¯µ±Ç°ÅäÖÃ
    if (tcgetattr(mFd, &opt)) return -2;

    // ÉèÖÃ²¨ÌØÂÊ
    //speed_t speed = speedof(bps);
    printf("set tty baudrate %d\n", baud);
    speed_t speed = speed_convert(baud);
    cfsetispeed(&opt, speed);
    cfsetospeed(&opt, speed);

    // ÉèÖÃÊý¾ÝÎ»
    opt.c_iflag = 0;
    opt.c_cflag &= ~CSIZE;
    opt.c_cflag |= CS8;

    //opt.c_cflag &= ~CSTOPB;
    //case 2: opt.c_cflag |= CSTOPB; break;

    opt.c_cflag &= ~(PARENB | PARODD);

    opt.c_cflag &= ~CRTSCTS;

    // Ñ¡ÔñÔ­Ê¼ÊäÈë
    opt.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);
    opt.c_oflag = 0;

    opt.c_cc[VMIN]  = 5;            // read doesn't block
    opt.c_cc[VTIME] = 10;            // 0.5 seconds read timeout

    // ÉèÖÃÐÂÅäÖÃ
    //return tcsetattr(mFd, TCSAFLUSH, &opt) < 0 ? -3 : 0;
    //tcsetattr(mFd, TCSAFLUSH, &opt);

    if (tcsetattr(mFd, TCSANOW, &opt) != 0) {
        printf("tcsetattr failed %s", strerror(errno));
        return false;
    }

#endif
    return true;
}

bool Uart::send(const char *data, int32_t len)
{
    int32_t sent = 0;
    
    if (mFd <0)
        return false;

    while (1) {
        int32_t writen = write(mFd, data + sent, len - sent);
        if (writen < 0) {
            if (errno == EINTR || errno == EAGAIN)
                continue;
            else
                return false;
        }
        sent += writen;
        if (sent >= len) {
            return true;
        }
    }
    return false;
}

int32_t Uart::recv(uint8_t *buf, int32_t len, uint32_t timeout_ms)
{
    if (mFd < 0)
        return -1;

    my::uint64 timeout = my::timestamp::utc_milliseconds() + timeout_ms;
    int32_t bufGet = 0;
    my::uint64 now = timeout;
    while (bufGet < len && now <= timeout) {
        int32_t ret = read(mFd, buf + bufGet, len - bufGet);
        bufGet += ret;
        now = my::timestamp::utc_milliseconds();
        usleep(1 * 1000);
    }
    
    return bufGet;

}

