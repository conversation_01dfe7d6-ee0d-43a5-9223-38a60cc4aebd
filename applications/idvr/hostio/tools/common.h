#ifndef __MINIEYE_COM_TOOL_H_
#define __MINIEYE_COM_TOOL_H_


#include <stdint.h>
#include "ipcAgent.h"
#include "McuMessage.h"

bool comm_recv(IpcClient & client, IpcMessage *pMsg);
bool comm_send(IpcClient & client, IpcMessage *pMsg);

void comm_send(IpcClient & client, McuMsgTypeE cmd, const char *data);
void comm_send(IpcClient & client, McuMsgTypeE cmd, uint8_t *data, int32_t len);
int32_t read_msg_timeout(IpcClient &client, McuMsgTypeE cmd, IpcMessage *pMsg, int32_t timeout_ms);



void printbuf(unsigned char *buf, int len);
uint8_t cal_crc(const char *buf, int len);

#endif
