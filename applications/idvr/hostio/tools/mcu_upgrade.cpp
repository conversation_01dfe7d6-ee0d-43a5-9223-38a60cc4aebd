#include <assert.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <stdio.h>

#include "../IOMsg.h"
#include "mcu_upgrade.h"
#include "idvrConfig.h"
#include "common.h"

static IpcClient mUgrade("mcu_upgrade", SH_NAME_HOSTIO);
static uint32_t packet_idx;
static uint32_t packet_total;

static app_header running_header;
static app_header local_header;

uint32_t crc32(uint32_t *addr, int num)
{
#define CRC_POLY                    0x04C11DB7
    int i;
    uint32_t crc=0xffffffff;

    for (; num > 0; num--)
    {
        //printf("0x%08x\n", *addr);
        crc = crc ^ (*addr++);
        for (i = 0; i < 32; i++)
        {
            if (crc & 0x80000000)
                crc = (crc << 1) ^ CRC_POLY;
            else
                crc <<= 1;

        }
        crc &= 0xFFFFFFFF;
    }
    return(crc);
}

bool firmwareCheckCrcOk(void)
{
    uint8_t head[512];
    struct stat st;

    if(access(MCU_FIRWARE_PATH, F_OK)){
        printf("Mcu not found %s\n", MCU_FIRWARE_PATH);
        return false;
    }

    app_header *p = (app_header *)&head[0];

    FILE *fp = fopen(MCU_FIRWARE_PATH, "r");
    if(!fp) {
        return false;
    }
    fseek(fp, 0, SEEK_SET);
    int32_t ret = fread(head, 1, sizeof(head), fp);
    if(ret != sizeof(head)) {
        printf("read upgrade firmware fail\n");
        return false;
    }
    local_header = *p;

    printf("-----------------------------\n");
    printf("Upgrade file's firmware info:\n");
    printf("crc:    0x%08x\n", local_header.crc);
    printf("len:    0x%08x (%d)\n", local_header.len, local_header.len);
    printf("type:   %s\n", local_header.magic);
    printf("commit: %s\n", local_header.commit);
    printf("version:%s\n", local_header.version);
    printf("-----------------------------\n");

    stat(MCU_FIRWARE_PATH, &st);
    //printf("firmware file size = %" FMT_LLD "\n", st.st_size);
    if(st.st_size <= sizeof(head)) {
        return false;
    }
    int body_size = st.st_size - sizeof(head);
    uint8_t *buf = (uint8_t *)malloc(st.st_size);
    if(buf == NULL){
        printf("malloc fail\n");
        return false;
    }
    memset(buf, 0, sizeof(st.st_size));
    int32_t rd_len = fread(buf, 1, body_size, fp);
    uint32_t num = rd_len / 4;
    uint32_t crc = crc32((uint32_t *)buf, num);
    //printf("crc = 0x%08x\n", crc);
    //printf("len = %d\n", p->len);
    free(buf);
    fclose(fp);

    return (crc == p->crc);
}

uint64_t systime(void)
{
    struct timespec times = {0, 0};
    uint64_t time;

    clock_gettime(CLOCK_MONOTONIC, &times);
    //printf("CLOCK_MONOTONIC: %" FMT_LLU ", %" FMT_LLU "\n", times.tv_sec, times.tv_nsec);
    time = times.tv_sec * 1000 + times.tv_nsec / 1000000;
    //printf("time = %" FMT_LLD "\n", time);
    return time;
}

bool upgrade_init(void)
{
    struct stat st;
    if(access(MCU_FIRWARE_PATH, F_OK)){
        printf("Mcu not found %s\n", MCU_FIRWARE_PATH);
        return false;
    }

    if(!stat(MCU_FIRWARE_PATH, &st)) {
        packet_idx = 0;
        packet_total = (st.st_size + MCU_UPGRD_FRAME_SIZE- 1) / MCU_UPGRD_FRAME_SIZE;
        return true;
    }
    return false;
}

bool send_upgrade_frame(void)
{
    FILE *fp;
    uint8_t buf[1024];
    UpgradeHeaderT *p = (UpgradeHeaderT *)&buf[0];

    memset(buf, 0, sizeof(buf));
    p->crc = 0;
    p->packet_idx = packet_idx;
    p->packet_total = packet_total;

    fp = fopen(MCU_FIRWARE_PATH, "r");
    if(!fp) {
        return false;
    }
    fseek(fp, packet_idx*MCU_UPGRD_FRAME_SIZE, 0);
    int ret = fread(p->payload, 1, MCU_UPGRD_FRAME_SIZE, fp);
    if(ret <= 0) {
        printf("read firmware file over, exit!\n");
        return false;
    }

    comm_send(mUgrade, MCU_MSG_TYPE_UPGRADE, buf, MCU_UPGRD_FRAME_SIZE+sizeof(UpgradeHeaderT));
    printf("[MCU]: send frame %d/%d\r", packet_idx, packet_total);
    fflush(stdout);

    fclose(fp);
    packet_idx++;

    return true;
}

uint16_t UpdateCRC16(uint16_t crcIn, uint8_t byte)
{
    uint32_t crc = crcIn;
    uint32_t in = byte|0x100;
    do
    {
        crc <<= 1;
        in <<= 1;
        if (in&0x100)
            ++crc;
        if (crc&0x10000)
            crc ^= 0x1021;
    }
    while (!(in&0x10000));
    return crc&0xffffu;
}

uint16_t Cal_CRC16(const uint8_t* data, uint32_t size)
{
    uint32_t crc = 0;
    const uint8_t* dataEnd = data+size;
    while (data<dataEnd)
        crc = UpdateCRC16(crc,*data++);

    crc = UpdateCRC16(crc,0);
    crc = UpdateCRC16(crc,0);
    return crc&0xffffu;
}

/* 超时等待 CMD*/
int waitCmdAckTimeOut(McuMsgTypeE cmd, IpcMessage* pMsg, uint32_t ms)
{
    return read_msg_timeout(mUgrade, cmd, pMsg, ms);
}

int resetMcu(void)
{
    uint8_t buf[MCU_MSG_MAX_SIZE];
    IpcMessage *pMsg = (IpcMessage *)&buf[0];

    memset(buf, 0, sizeof(buf));
    pMsg->type = MCU_MSG_TYPE_MCU_RST;
    pMsg->len = 0;
    comm_send(mUgrade, pMsg);
    return 0;
}

string get_version(void)
{
    string ver;
    uint8_t buf[MCU_MSG_MAX_SIZE];
    memset(buf, 0, MCU_MSG_MAX_SIZE);
    McuMessage *pMsg = (McuMessage *)&buf[0];
    comm_send(mUgrade, MCU_MSG_TYPE_MCU_VERSION, NULL, 0);
    int ret = waitCmdAckTimeOut(MCU_MSG_TYPE_MCU_VERSION, &(pMsg->ipcMsg), 500);
    if (!ret) {
        printf("mcu version: %s\n", pMsg->u.schar);
        ver = pMsg->u.schar;
    } else {
        printf("read mcu version timeout!\n");
    }

    return ver;
}

int get_firmware_info(void)
{
    uint8_t buf[MCU_MSG_MAX_SIZE];
    memset(buf, 0, MCU_MSG_MAX_SIZE);
    McuMessage *pMsg = (McuMessage *)&buf[0];
    comm_send(mUgrade, MCU_MSG_TYPE_MCU_FIRMWARE_INFO, NULL, 0);
    int ret = waitCmdAckTimeOut(MCU_MSG_TYPE_MCU_FIRMWARE_INFO, &(pMsg->ipcMsg), 500);
    if (!ret) {
        printf("-----------------------------\n");
        printf("mcu current firmware info:\n");
        running_header = *(app_header *)pMsg->u.schar;
        printf("crc:    0x%08x\n", running_header.crc);
        printf("len:    0x%08x (%d)\n", running_header.len, running_header.len);
        printf("type:   %s\n", running_header.magic);
        printf("commit: %s\n", running_header.commit);
        printf("version:%s\n", running_header.version);
        printf("-----------------------------\n");
        return 0;
    } else {
        return -1;
    }
    return 0;
}

void create_version(string &ver)
{
    FILE *fp = fopen(MCU_FIRWARE_VERSION_PATH, "w+");
    if(!fp) {
        printf("create version file fail\n");
        return;
    }
    if(ver.length()>0) {
        fprintf(fp, "%s", ver.c_str());
    }
    fclose(fp);
}

bool firmwareCheckInfoOk(app_header h)
{
    int32_t ret = 0;
    int hundred = 0, ten = 0, unit = 0;
    uint8_t buf[32] = {0};

    if (strcmp(running_header.magic, h.magic)) {
        printf("Upgrade file's firmware type(%s) is not match!\n", h.magic);
        return false;
    }
    return true;
}

int do_mcu_upgrade(void)
{
    char auth_msg[32] = {"m5pro_mcu"};
    string ver;

    ver = get_version();

    if (0 != get_firmware_info()) {
        printf("get mcu current firmware info timeout!\n");
        return -1;
    }

    /* check upgrade file's firmware crc */
    if(!firmwareCheckCrcOk()) {
        printf("Upgrade file's firmware crc check error\n");
        return -1;
    } else {
        printf("Upgrade file's firmware crc check success\n");
    }

    /* check upgrade file's firmware header info */
    if(!firmwareCheckInfoOk(local_header)) {
        printf("Upgrade file's firmware info check error\n");
        return -1;
    } else {
        printf("Upgrade file's firmware info check success\n");
    }

    printf("running_crc 0x%08x local_crc 0x%08x\n", running_header.crc, local_header.crc);
    if (running_header.crc == local_header.crc) {
        printf("header crc is the same!\n");
        create_version(ver);
        return 0;
    }

    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];

    /* reset Mcu */
    resetMcu();
    int ret = waitCmdAckTimeOut(MCU_MSG_TYPE_MCU_BLD_MODE, &(pMsg->ipcMsg), 10000);
    if (ret) { /* rst mcu fail */
        printf("wait mcu reset timeout\n");
        return -2;
    }

    /* authen */
    comm_send(mUgrade, MCU_MSG_TYPE_MCU_UPGRD_AUTHEN, (uint8_t *)auth_msg, strlen(auth_msg));
    ret = waitCmdAckTimeOut(MCU_MSG_TYPE_MCU_UPGRD_AUTHEN, &(pMsg->ipcMsg), 1000);
    if (ret) { /* rst mcu fail */
        printf("mcu authen timeout\n");
        return -2;
    } else {
        if (pMsg->ipcMsg.len > 0 && pMsg->u.uchar[0] == 0) {
            printf("authen ok\n");
        } else {
            printf("authen fail\n");
            return -2;
        }
    }

    upgrade_init();
    while (1)
    {
        if (!send_upgrade_frame()) { /* send fail */
            printf("\nsend package fail!\n");
            return -3;
        }
        int ret = waitCmdAckTimeOut(MCU_MSG_TYPE_UPGRADE, &(pMsg->ipcMsg), 4000);
        if (ret) { /* wait frame ack fail */
            printf("\nwait upgrade ack timeout\n");
            return -4;
        } else {
            //printf("recv ack 0x%x len %d\n", pMsg->u.uchar[0], pMsg->len);
            if(pMsg->u.uchar[0] != 0) {
                printf("\nrecv mcu upgrade ack error!\n");
                return -5;
            }
        }

        if(packet_idx == packet_total) {
            printf("\n[MCU]: send over\n");
            break;
        }
    }

    /* wait app boot */
    ret = waitCmdAckTimeOut(MCU_MSG_TYPE_MCU_APP_MODE, &(pMsg->ipcMsg), 4000);
    if (ret) { /* wait frame ack fail */
        printf("\nwait app boot timeout\n");
        return -4;
    }

    ver = get_version();
    create_version(ver);
    return 0;
}


