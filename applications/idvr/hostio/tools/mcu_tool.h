#ifndef __MINIEYE_MCUTOOL_H_
#define __MINIEYE_MCUTOOL_H_

#include "common.h"

#if 0
int sendPrinterCmd(McuMsgTypeE cmd);
int writeICCardCmd(ICCardWrBuf& card);
int eraseICCardCmd(void);
int writeICCard(void);
void readICCardStr(void);
bool iccardInfoCheck(const uint8_t *buf, int len);
void fxiICCardRst(void);
int readICCardCmd(ICCardWrBuf& card);
int readICCardLoop(void);
int32_t loadICCardJson(ICCardWrBuf& card);

#endif
void gps_raw_data_output_ctrl(bool open);
void test_disp(char *opt);
void ctrl_io(char *opt);
void read_status_loop(void);

void factory_test(char *opt);
void IO_Test(void);
void uart_FeedbackTest(McuMsgTypeE cmd);
void mcu_upgrade(void);
void dump(char *opt);
void getVersion(char *opt);

void send_ctrl_io_cmd(McuIOTypeE type);
bool read_gpio(uint16_t *io);


#endif
