#include <stdio.h>
#include <pthread.h>
#include <unistd.h>
#include <stdlib.h>
#include <stdio.h>

#include <math.h>
#include <unistd.h>
#include <sys/time.h>
#include <string>
#include <string.h>
#include <signal.h>
#include "mcu_upgrade.h"
#include "mcu_tool.h"
#include "DispProto.h"
#include "uart.h"
#include <arpa/inet.h>
#include "../IOMsg.h"
#include "rapidjson.wrap.h"
#include "ttsPlay.h"

#include "CmdListener.h"
#include "properties.h"
#include "imu_client.h"

using namespace rapidjson;

IpcClient mClt("mcu_tool", SH_NAME_HOSTIO);

static bool mLoopExit = false;
static void sigHandler(int s)
{
    if (SIGTERM == s)
        mLoopExit = true;
}

int recvCmdLoop(void)
{
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];
    memset(buf, 0, sizeof(buf));

    while(1) {
        if (comm_recv(mClt, &(pMsg->ipcMsg))) {
            fprintf(stdout,"cmd 0x%x len %d\n",pMsg->ipcMsg.type,  pMsg->ipcMsg.len);
        }
    }
    return 0;
}

void send_ctrl_io_cmd(McuIOTypeE type)
{
    uint8_t cmd[1];
    cmd[0] = type;
    comm_send(mClt, MCU_MSG_TYPE_IO_CTRL, cmd, 1);
}

void ctrl_switch(char *opt)
{
    uint8_t cmd[1];
    if(!strcmp("mcu_rst_soc_on", opt)) {
        cmd[0] = MCU_RST_SOC_ON;
    } else if(!strcmp("mcu_rst_soc_off", opt)) {
        cmd[0] = MCU_RST_SOC_OFF;
    } else if(!strcmp("cap_charing_auto", opt)) {
        cmd[0] = CAP_CHARING_AUTO;
    } else if(!strcmp("cap_charing_manual", opt)) {
        cmd[0] = CAP_CHARING_MANUAL;
    } else if(!strcmp("led_r_on", opt)) {
        cmd[0] = LED_R_ON;
    } else if(!strcmp("led_g_on", opt)) {
        cmd[0] = LED_G_ON;
    } else if(!strcmp("led_r_flash_1s", opt)) {
        cmd[0] = LED_R_FLASH_1S;
    } else if(!strcmp("led_g_flash_1s", opt)) {
        cmd[0] = LED_G_FLASH_1S;
    } else {
        fprintf(stdout,"unknow ctrl cmd.\n");
        return;
    }
    fprintf(stdout,"cmd: %s\n", opt);
    comm_send(mClt, MCU_MSG_TYPE_SWITCH_CTRL, cmd, 1);
}

void ctrl_io(char *opt)
{
    uint8_t cmd[1];
    if(!strcmp("oil_up", opt)) {
        cmd[0] = MINOR_GPIO_OIL_HIGH;
    } else if(!strcmp("oil_down", opt)) {
        cmd[0] = MINOR_GPIO_OIL_LOW;
    } else if(!strcmp("5v_on", opt)) {
        cmd[0] = MINOR_GPIO_PWR_5V_ON;
    } else if(!strcmp("5v_off", opt)) {
        cmd[0] = MINOR_GPIO_PWR_5V_OFF;
    } else if(!strcmp("12v_on", opt)) {
        cmd[0] = MINOR_GPIO_PWR_12V_ON;
    } else if(!strcmp("12v_off", opt)) {
        cmd[0] = MINOR_GPIO_PWR_12V_OFF;
    } else if(!strcmp("sys_pwr_on", opt)) {
        cmd[0] = MINOR_GPIO_PWR_SYS_ON;
    } else if(!strcmp("sys_pwr_off", opt)) {
        cmd[0] = MINOR_GPIO_PWR_SYS_OFF;
    } else if(!strcmp("cap_charing_on", opt)) {
        cmd[0] = MINOR_GPIO_CAP_ENCHAR_ON;
    } else if(!strcmp("cap_charing_off", opt)) {
        cmd[0] = MINOR_GPIO_CAP_ENCHAR_OFF;
    } else if(!strcmp("eth_on", opt)) {
        cmd[0] = MINOR_GPIO_PWR_ETH_ON;
    } else if(!strcmp("eth_off", opt)) {
        cmd[0] = MINOR_GPIO_PWR_ETH_OFF;
    } else if(!strcmp("gps_on", opt)) {
        cmd[0] = MINOR_GPIO_PWR_GPS_ON;
    } else if(!strcmp("gps_off", opt)) {
        cmd[0] = MINOR_GPIO_PWR_GPS_OFF;
    } else if(!strcmp("4g_on", opt)) {
        cmd[0] = MINOR_GPIO_PWR_4G_ON;
    } else if(!strcmp("4g_off", opt)) {
        cmd[0] = MINOR_GPIO_PWR_4G_OFF;
    } else {
        fprintf(stdout,"unknow ctrl cmd.\n");
        return;
    }
    fprintf(stdout,"cmd: %s\n", opt);
    comm_send(mClt, MCU_MSG_TYPE_IO_CTRL, cmd, 1);
}

bool read_gpio(IOStatus *vehi)
{
    McuMsgMcuStatT *ss;
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];

    if (!read_msg_timeout(mClt, MCU_MSG_TYPE_STAT, &(pMsg->ipcMsg), 200)) {
        ss = reinterpret_cast<McuMsgMcuStatT*>(pMsg->u.uchar);
        memcpy(vehi, &ss->vehicle_io, sizeof(IOStatus));
        return true;
    }
    return false;
}

int32_t Uart_FeedbackTest(McuMsgTypeE cmd)
{
    int32_t ret = 0;
    uint8_t data[5] = {1,2,3,4,5};
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];

    int cnt=0;
    while(cnt++ < 3) {
        comm_send(mClt, cmd, data, sizeof(data));

        if (read_msg_timeout(mClt, cmd, &(pMsg->ipcMsg), 300)) {
            fprintf(stdout,"read uart data timeout\n");
            ret = 1;
        } else {
            if(!memcmp(pMsg->u.uchar, data, 5)) {
                ret = 0;
                break;
            } else {
                ret = 2;
                fprintf(stdout,"RS485 test mis-match\n");
                printbuf(pMsg->u.uchar, 5);
            }
        }
    }
    /* 3次失败 */
    if (!ret) {
        fprintf(stdout,"RS485 TEST OK\n");
    } else if(ret == 1) {
        fprintf(stdout,"RS485 TEST FAIL: \"读超时\"\n");
    } else if(ret == 2) {
        fprintf(stdout,"RS485 TEST FAIL: \"读写不一致\"\n");
    }

    return ret;
}


/**********************************
 * 通过 OIL_IO 来驱动其他的IO变化
 *
 * *******************************/
void IO_Test(void)
{
//#define IO_TO_HUMAN(n) ((n==0) ? "低" : "高")
#define IO_TO_HUMAN(n) ((n==0) ? "0" : "1")

#define IO_DUMP(io) do{\
    fprintf(stdout,"近光灯 %s 远光灯 %s 刹车 %s\n"\
            ,IO_TO_HUMAN(io.normal_light)\
            ,IO_TO_HUMAN(io.far_light)\
            ,IO_TO_HUMAN(io.brake));\
}while(0)

    struct timespec ts;
    struct timespec now;
    IOStatus vehi_low, vehi_high;

    send_ctrl_io_cmd(MINOR_GPIO_OIL_LOW);
    clock_gettime(CLOCK_REALTIME, &ts);
    while(1) {
        read_gpio(&vehi_low);
        if (!vehi_low.normal_light\
                && !vehi_low.far_light\
                && !vehi_low.brake) {
            break;
        }

        clock_gettime(CLOCK_REALTIME, &now);
        /* 等待超时 */
        if((now.tv_sec - ts.tv_sec ) * 1000 + now.tv_nsec / 1000000 > ts.tv_nsec / 1000000 + 1000) {
            fprintf(stdout, "IO set low fail\n");
            IO_DUMP(vehi_low);
            fprintf(stdout, "IO TEST FAIL: \"设置低无效\"\n");
            return;
        }
    }
    IO_DUMP(vehi_low);

    send_ctrl_io_cmd(MINOR_GPIO_OIL_HIGH);
    while(1) {
        read_gpio(&vehi_high);
        if (vehi_high.normal_light\
                && vehi_high.far_light\
                && vehi_high.brake) {
            break;
        }

        clock_gettime(CLOCK_REALTIME, &now);
        /* 等待超时 */
        if((now.tv_sec - ts.tv_sec )*1000 + now.tv_nsec/1000000 > ts.tv_nsec/1000000 + 1000) {
            fprintf(stdout, "IO set high fail\n");
            IO_DUMP(vehi_high);
            fprintf(stdout, "IO TEST FAIL: \"设置高无效\"\n");
            return;
        }
    }

    IO_DUMP(vehi_high);

    fprintf(stdout, "IO TEST OK\n");
}

static void onImuDatasRecv(std::vector<GsensorData>& vector) {
    std::vector<GsensorData> imu_data(vector.begin(), vector.end());
    for(int i = 0; i < imu_data.size(); i ++) {
        float deg_temp = 25 + (imu_data.at(i).temp / 256.0f);
        float acc[3];
        float aRes = 8.0/32768;
        acc[0] = imu_data.at(i).accel[0] * aRes;
        acc[1] = imu_data.at(i).accel[1] * aRes;
        acc[2] = imu_data.at(i).accel[2] * aRes;

        double roll = atan2(-acc[1], -acc[2]) * 180/3.14;
        double pitch = atan(acc[0]/sqrt(acc[2]*acc[2] + acc[1]*acc[1])) * 180/3.14;

        fprintf(stdout, "imu_time:%" FMT_LLD " gyro_x:%d gyro_y:%d gyro_z:%d accel_x:%d accel_y:%d accel_z:%d deg_temp:%f roll %.3f pitch %.3f\n",imu_data.at(i).timestamp,
            imu_data.at(i).gyro[0],imu_data.at(i).gyro[1],imu_data.at(i).gyro[2],imu_data.at(i).accel[0],imu_data.at(i).accel[1],imu_data.at(i).accel[2],deg_temp, roll, pitch);
        fflush(stdout);
   }
}

int read_imu_loop(void)
{
    IMUClient imuClient("mcu_tool","127.0.0.1", "12580", "imu.server.test");
    imuClient.start(std::bind(&onImuDatasRecv, std::placeholders::_1));
    while(!mLoopExit) {
        usleep(100*1000);
    }
    imuClient.stop();

    return 0;
}

uint8_t sim_inserted(void)
{
    char value[PROP_VALUE_MAX];
    int len = 0;

    memset(value, 0, PROP_VALUE_MAX);
    len = __system_property_get("rw.minieye.sim_state", value);

    if (len == 0) {
        return 0;
    } else {
        if (value[0] == '0') {
            return 0;
        } else {
            return 1;
        }
    }
}

void sendToDisp(int speed, int left, int right)
{
    DispProto Disp;
    Disp.displine(left, right);
    comm_send(mClt, MCU_MSG_TYPE_RAW_CAN1, (uint8_t *)&Disp.mPkg, sizeof(UrtpCanT));

    int z,m,n;
    z = speed %100;
    m = z/10;
    n = z%10;
    Disp.WarnCar(COLOR_RED, STATE_NOT_SHOW, m, n, STATE_NOT_SHOW, STATE_SHOW, COLOR_WHITE);
    comm_send(mClt, MCU_MSG_TYPE_RAW_CAN1, (uint8_t *)&Disp.mPkg, sizeof(UrtpCanT));
}

void read_status_loop(void)
{
    McuMsgMcuStatT *ss;
    IOStatus *vehi;
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];
    time_t last_time=0, cur_time=0;
    int last_key = 0;
    while(!mLoopExit) {
        if (comm_recv(mClt, &(pMsg->ipcMsg))) {
            //fprintf(stdout,"read type %d\n", pMsg->type);
            if(pMsg->ipcMsg.type == MCU_MSG_TYPE_STAT) {
                if(pMsg->ipcMsg.len != sizeof(McuMsgMcuStatT)) {
                    fprintf(stdout,"McuMsgMcuStatT msg len error!\n");
                    return;
                }
                ss = reinterpret_cast<McuMsgMcuStatT*>(pMsg->u.u8Array);
                vehi = &ss->vehicle_io;

                if(ss->key[0] != 0)
                    last_key = ss->key[0];
                cur_time = time(NULL);
                if(cur_time > last_time) {

                    fprintf(stdout,"SIM卡:%s 模拟车速:%06.2f 模拟转向左:%s 模拟转向右:%s CAN车速:%06.2f CAN转向左:%s CAN转向右:%s SOS:%s 版本号AD:%04d 超级电容:%04.2fV 温度AD:%04.2fV 电源AD:%04.2fv GPS:%s 天线:%s \n"\
                            , sim_inserted() ? "已插入" : "未插入"\
                            , ss->pulses_speed_x10/10.0\
                            , ss->pulses_turnl ? "开" : "关"\
                            , ss->pulses_turnr ? "开" : "关"\
                            , ss->can_speed_x10/10.0\
                            , ss->can_turnl ? "开" : "关"\
                            , ss->can_turnr ? "开" : "关"\
                            , ss->vehicle_io.emergency_alarm ? "开" : "关"\
                            , ss->adcRaw[ADC_CHN_VER]\
                            , ss->adcVol[ADC_CHN_CAP]\
                            , ss->adcVol[ADC_CHN_TEM]\
                            , ss->adcVol[ADC_CHN_VDD]\
                            , ss->lbs.status ? "已定位" : "未定位"\
                            , ss->lbs.antenna == ANTENNA_OK ? "正常" : "异常");
                    fflush(stdout);

                    last_time = cur_time;
                }
#if 0
                fprintf(stdout,"acc %d sos %d near %d far %d "
                        "left %d right %d brake %d \n",\
                        vehi->acc, vehi->emergency_alarm, vehi->normal_light,\
                        vehi->far_light, vehi->left_turn, vehi->right_turn,\
                        vehi->brake);
#endif


                sendToDisp(ss->can_speed_x10/10, ss->can_turnl, ss->can_turnr);
            }
        }
    }
}

/**************************
 * 硬盘，u盘，SD卡 读写测试
 * ************************/
int disk_test(const char *str)
{
    int ret;
    char mount_check[256];
    char cmd[256];
    const char *sd_dir = "/tmp/media_rw/sdcard1";
    const char *ud_dir = "/tmp/media_rw/udisk1";
    const char *hd_dir = "/tmp/media_rw/disk1";
    const char *do_cmd = "dd if=/dev/urandom  of=./randwrite bs=1k count=1 >/dev/null 2>&1 && dd if=./randwrite of=./randread bs=1k count=1 >/dev/null 2>&1 && cmp randwrite randread >/dev/null 2>&1";

#define __CHCK_DIR__(__v, __dir) \
    do { \
        DIR *dir = opendir(__dir); \
        if (dir != nullptr) { \
            closedir(dir); \
            __v = __dir; \
        } \
    } while (0)

    __CHCK_DIR__(sd_dir, "/tmp/media_rw/sdcard0");
    __CHCK_DIR__(ud_dir, "/tmp/media_rw/udisk0");
    __CHCK_DIR__(hd_dir, "/tmp/media_rw/disk0");

    if (!strcmp("ud", str)) {
        snprintf(mount_check, sizeof(mount_check), "mount | grep '%s' >/dev/null 2>&1", ud_dir);
        snprintf(cmd, sizeof(cmd), "cd %s && %s", ud_dir, do_cmd);
    } else if (!strcmp("hd", str)) {
    #if 0   //dfh-a/m5pro不支持硬盘
        snprintf(mount_check, sizeof(mount_check), "mount | grep '%s' >/dev/null 2>&1", hd_dir);
        snprintf(cmd, sizeof(cmd), "cd %s && %s", hd_dir, do_cmd);
    #else
        fprintf(stdout,"%s read/write TEST OK\n", str);
        return 0;
    #endif
    } else if (!strcmp("sd", str)) {
        // 判断SD卡引脚是否拉低, 如果这个功能有问题, 则热插拔就会有问题
        if (system("cat /proc/driver/mgpio | grep gpio-sd0-cdz | busybox awk '{print $2}' | grep high >/dev/null 2>&1") == 0) {
            // 高级写法：只判断被拉高的情况，拉高就是SD卡检测有误
            fprintf(stdout,"TEST FAIL: \"SD卡检测引脚有误\"\n");
            return -1;
        }

        if(system("ls /dev/mmcblk1 >/dev/null 2>&1")) {
            fprintf(stdout,"TEST FAIL: \"未发现SD卡\"\n");
            return -1;
        }
        snprintf(mount_check, sizeof(mount_check), "mount | grep '%s' >/dev/null 2>&1", sd_dir);
        snprintf(cmd, sizeof(cmd), "cd %s && %s", sd_dir, do_cmd);
    } else {
        fprintf(stdout, "unknow disk type: %s\n", str);
        fprintf(stdout,"TEST FAIL: \"非法指令\"\n");
        return -1;
    }

    if (system(mount_check)) {
        fprintf(stdout,"%s not mount\n", str);
        fprintf(stdout,"TEST FAIL: \"未挂载\"\n");
        return -2;
    }

    if(system(cmd)) {
        fprintf(stdout,"%s read/write TEST FAIL: \"读写不一致\"\n", str);
        return -3;
    }

    fprintf(stdout,"%s read/write TEST OK\n", str);
    return 0;
#undef __CHCK_DIR__
}

string getTimestamp(time_t *t)
{
    struct tm* timeinfo;
    struct timeval tv;
    char msglog[128];
    char buffer[64];

    memset(buffer, 0, sizeof buffer);

    if (t) {
        timeinfo = localtime(t);
        strftime(buffer, sizeof buffer, "%Y-%m-%d %H:%M:%S", timeinfo);
        return buffer;
    }

    gettimeofday(&tv, NULL);
    timeinfo = localtime(&tv.tv_sec);
    strftime(buffer, sizeof buffer, "%Y-%m-%d %H:%M:%S.", timeinfo);
    snprintf(msglog, sizeof(msglog), "%s%" FMT_LLD, buffer, tv.tv_usec/1000);

    return msglog;
}

/***************
 * 功能测试
 * *************/
void factory_test(char *opt)
{
    if (!strcmp("gpio", opt)) {
        IO_Test();
    } else if (!strcmp("status", opt)) {
        read_status_loop();
    } else if (!strcmp("rs232_1", opt)) {
        char name[64] = {"/dev/ttyS2"};

        fprintf(stdout, "test %s \n", name);
        Uart com(name);
        char buf[32] = {0};
        int32_t ret;
        com.send("12345", 5);
        ret = com.recv((uint8_t *)&buf[0], 5, 100);
        if (ret > 0) {
            if(!strcmp("12345", buf)) {
                fprintf(stdout, "%s TEST OK\n", opt);
            } else {
                fprintf(stdout, "%s TEST FAIL: \"读写不一致\"\n", opt);
                fprintf(stdout, "buf %s\n", buf);
            }
        } else {
            fprintf(stdout, "%s TEST FAIL: \"读超时\"\n", opt);
        }
    } else if (!strcmp("rs485", opt)) {
        Uart_FeedbackTest(MCU_MSG_TYPE_RS485);
    }
#if 0
    else if (!strcmp("rs232_1", opt) || !strcmp("rs232_2", opt)) {
        char name[64] = {"/dev/ttyS2"};
        if (!strcmp("rs232_1", opt))
            strcpy(name, "/dev/ttyS2");
        else if (!strcmp("rs232_2", opt))
            strcpy(name, "/dev/ttyS3");

        fprintf(stdout, "test %s \n", name);
        Uart com(name);
        char buf[32] = {0};
        int32_t ret;
        com.send("12345", 5);
        ret = com.recv((uint8_t *)&buf[0], 5, 100);
        if (ret > 0) {
            if(!strcmp("12345", buf)) {
                fprintf(stdout, "%s TEST OK\n", opt);
            } else {
                fprintf(stdout, "%s TEST FAIL: \"读写不一致\"\n", opt);
                fprintf(stdout, "buf %s\n", buf);
            }
        } else {
            fprintf(stdout, "%s TEST FAIL: \"读超时\"\n", opt);
        }
    }
#endif
    else if (!strcmp("sd", opt)) {
        disk_test((const char *)opt);
    } else if (!strcmp("record_on_lcd", opt) || !strcmp("record_on_dms", opt)) {
        char cmd[256] = {0};
        int32_t ret;

        if (!strcmp("record_on_lcd", opt)) {
            snprintf(cmd, sizeof(cmd), "mkdir -p /tmp/obb/;record_tool /tmp/obb/record.wav -c 1 -t 4 >/dev/null 2>&1");
        } else if (!strcmp("record_on_dms", opt)){
            snprintf(cmd, sizeof(cmd), "mkdir -p /tmp/obb/;record_tool /tmp/obb/record.wav -c 2 -t 4 >/dev/null 2>&1");
        }

        ret = system(cmd);
        snprintf(cmd, sizeof(cmd), "ndc media cmd play /tmp/obb/record.wav 1 >/dev/null 2>&1");
        ret = system(cmd);
        if (!ret) {
            fprintf(stdout, "%s TEST OK\n", opt);
        } else {
            fprintf(stdout, "%s TEST FAIL\n", opt);
        }
    } else if (!strcmp("record_4ch", opt)) {
#define __AUDIO_REC_STOP() \
    do { \
        std::string cmd = "cmd audioRecTest 0"; \
        logd("%s", cmd.c_str()); \
        std::vector<char> resp; \
        LogCallProxyCmd::sendReq("media", cmd.c_str(), resp); \
        my::thread::msleep(500); \
    } while (0)

#define __AUDIO_REC_START() \
    do { \
        std::string cmd = "cmd audioRecTest 1"; \
        logd("%s", cmd.c_str()); \
        std::vector<char> resp; \
        LogCallProxyCmd::sendReq("media", cmd.c_str(), resp); \
        my::thread::msleep(500); \
        std::string audioData = "录音测试"; \
        ttsPlayRightNow(audioData.c_str()); \
        my::thread::sleep(2); \
    } while (0)

#define __AUDIO_REC_PLAY(__mic) \
    do { \
        std::string audioData = std::string(my::to_string((int32_t) __mic).c_str()); \
        ttsPlayRightNow(audioData.c_str()); \
        my::thread::sleep(1); \
        std::string cmd = "cmd play "; \
        cmd += std::string("/tmp/audio_rec_test_") + my::to_string((int32_t) __mic).c_str() + ".wav" + " 1"; \
        logd("%s", cmd.c_str()); \
        std::vector<char> resp; \
        LogCallProxyCmd::sendReq("media", cmd.c_str(), resp); \
        my::thread::sleep(2); \
    } while (0)

        unlink("/tmp/audio_rec_test_1.wav");
        unlink("/tmp/audio_rec_test_2.wav");
        unlink("/tmp/audio_rec_test_3.wav");
        unlink("/tmp/audio_rec_test_4.wav");

        __AUDIO_REC_STOP();
        __AUDIO_REC_START();
        __AUDIO_REC_STOP();

        __AUDIO_REC_PLAY(1);
        __AUDIO_REC_PLAY(2);
        __AUDIO_REC_PLAY(3);
        __AUDIO_REC_PLAY(4);

        ttsPlayRightNow("录音结束");
        my::thread::sleep(1);

        fprintf(stdout, "%s TEST OK\n", opt);
#undef __AUDIO_REC_STOP
#undef __AUDIO_REC_START
    } else if (!strcmp("inside_speaker", opt) || !strcmp("outside_speaker", opt)) {
        char cmd[256];
        int32_t ret;
        char buffer[64];
        struct tm* timeinfo;
        time_t now = time(NULL);
        timeinfo = localtime(&now);
        strftime(buffer, sizeof buffer, "%Y-%m-%d %H:%M", timeinfo);

        ttsPlayRightNow("%s", buffer);

        if (!strcmp("inside_speaker", opt)) {
            system("echo \"gpio-audio-pwr-l high\" > /proc/driver/mgpio");
            system("echo \"gpio-audio-pwr-r low\" > /proc/driver/mgpio");
            ttsPlayRightNow("内置喇叭播放测试");
        } else if(!strcmp("outside_speaker", opt)) {
            system("echo \"gpio-audio-pwr-r high\" > /proc/driver/mgpio");
            system("echo \"gpio-audio-pwr-l low\" > /proc/driver/mgpio");
            ttsPlayRightNow("外置喇叭播放测试");
        }

        if (!ret) {
            fprintf(stdout,"audio play TEST OK\n");
        } else {
            fprintf(stdout,"audio play TEST FAIL\n");
        }
    } else if (!strcmp("imu", opt)) {
        read_imu_loop();
    } else {
        fprintf(stdout, "unknow cmd TEST FAIL\n");
    }
}

void test_disp(char *opt)
{
    DispProto Disp;
    int cnt = 5;

    if(!strncmp("line", opt, 4)) {
        while(cnt--){
            Disp.displine(1, 1);
            comm_send(mClt, MCU_MSG_TYPE_RAW_CAN1, (uint8_t *)&Disp.mPkg, sizeof(UrtpCanT));
            usleep(200000);
            Disp.displine(0, 0);
            comm_send(mClt, MCU_MSG_TYPE_RAW_CAN1, (uint8_t *)&Disp.mPkg, sizeof(UrtpCanT));
            usleep(200000);
            //Disp.displine(1, 0);
            //comm_send(mClt, MCU_MSG_TYPE_RAW_CAN1, (uint8_t *)&Disp.mPkg, sizeof(UrtpCanT));
            //usleep(100000);
            //Disp.displine(0, 1);
            //comm_send(mClt, MCU_MSG_TYPE_RAW_CAN1, (uint8_t *)&Disp.mPkg, sizeof(UrtpCanT));
            //usleep(100000);
        }
    } else if(!strncmp("1", opt, 1)) {
        fprintf(stdout,"line on\n");
        Disp.displine(1, 1);
        comm_send(mClt, MCU_MSG_TYPE_RAW_CAN1, (uint8_t *)&Disp.mPkg, sizeof(UrtpCanT));
    } else if(!strncmp("2", opt, 1)) {
        fprintf(stdout,"line off\n");
        Disp.displine(0, 0);
        comm_send(mClt, MCU_MSG_TYPE_RAW_CAN1, (uint8_t *)&Disp.mPkg, sizeof(UrtpCanT));
    } else if(!strncmp("hw", opt, 2)) {
        Disp.WarnCar(COLOR_RED, STATE_SHOW, 1, 0, STATE_SHOW, STATE_SHOW, COLOR_WHITE);
        comm_send(mClt, MCU_MSG_TYPE_RAW_CAN1, (uint8_t *)&Disp.mPkg, sizeof(UrtpCanT));
    } else if(!strncmp("fcw", opt, 3)) {
        Disp.WarnCar(COLOR_RED, STATE_BLINK, 0, 9, STATE_SHOW, STATE_NOT_SHOW, COLOR_RED);
        comm_send(mClt, MCU_MSG_TYPE_RAW_CAN1, (uint8_t *)&Disp.mPkg, sizeof(UrtpCanT));
    } else if(!strncmp("loop", opt, 3)) {
        uint32_t digit = 0;
        uint8_t m,n,z;
        while(!mLoopExit)
        {
            z = digit%100;
            m = z/10;
            n = z%10;
            digit++;
            Disp.WarnCar(COLOR_RED, STATE_SHOW, m, n, STATE_SHOW, STATE_SHOW, COLOR_WHITE);
            comm_send(mClt, MCU_MSG_TYPE_RAW_CAN1, (uint8_t *)&Disp.mPkg, sizeof(UrtpCanT));
            fprintf(stdout,"digit: %d%d\n", m, n);
            usleep(30000);
        }
    }

}

void dump(char *opt)
{
    uint8_t data[2];
    #if 0
    if(!strcmp("logs", opt)) {
        comm_send(mClt, MCU_MSG_TYPE_GB19056_LOG_DUMP, NULL, 0);
    } else if(!strcmp("records", opt)) {
        data[0] = LOG_TYPE_GPS;
        comm_send(mClt, MCU_MSG_TYPE_GB19056_RECORD_DUMP, NULL, 0);
    } else if(!strcmp("gb19056", opt)) {
        comm_send(mClt, MCU_MSG_TYPE_MCU_GB19056_FETCH_ALL, NULL, 0);
    } else if(!strcmp("gps_v0", opt) || !strcmp("gps_v1", opt) \
            || !strcmp("gps_v2", opt) || !strcmp("gps_v3", opt)) {
        data[0] = LOG_TYPE_GPS;
        data[1] = atoi((const char *)&opt[strlen(opt)-1]);
        comm_send(mClt, MCU_MSG_TYPE_LOG_CTRL, data, 2);
    } else
    #endif
    if(!strcmp("status_v0", opt) || !strcmp("status_v1", opt)) {
        data[0] = LOG_TYPE_STATUS;
        data[1] = atoi((const char *)&opt[strlen(opt)-1]);
        comm_send(mClt, MCU_MSG_TYPE_LOG_CTRL, data, 2);
    } else if(!strcmp("can_v0", opt) || !strcmp("can_v1", opt)) {
        data[0] = LOG_TYPE_CAN;
        data[1] = atoi((const char *)&opt[strlen(opt)-1]);
        comm_send(mClt, MCU_MSG_TYPE_LOG_CTRL, data, 2);
    } else if(!strcmp("aspeed_v0", opt) || !strcmp("aspeed_v1", opt)) {
        data[0] = LOG_TYPE_ASPEED;
        data[1] = atoi((const char *)&opt[strlen(opt)-1]);
        comm_send(mClt, MCU_MSG_TYPE_LOG_CTRL, data, 2);
    }
}

void getVersion(char *opt)
{
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];

    if(!strcmp("mcu", opt)) {
        comm_send(mClt, MCU_MSG_TYPE_MCU_VERSION, NULL, 0);
        if (!read_msg_timeout(mClt, MCU_MSG_TYPE_MCU_VERSION, &(pMsg->ipcMsg), 500)) {
            fprintf(stdout,"mcu version: %s\n", pMsg->u.schar);
        } else {
            fprintf(stdout,"read mcu version timeout!\n");
        }
    } else if(!strcmp("mcu_info", opt)) {
        get_firmware_info();
    } else if(!strcmp("hostio", opt)) {
        comm_send(mClt, MCU_MSG_TYPE_HOSTIO_VERSION, NULL, 0);
    }
}

int mcuConfig(void)
{
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];

    memset(buf, 0, sizeof(buf));
    pMsg->ipcMsg.type = MCU_MSG_TYPE_MCU_CONFIG;
    pMsg->ipcMsg.len = 0;
    comm_send(mClt, &(pMsg->ipcMsg));
    return 0;
}

static void usage_all(void)
{
    fprintf(stdout,"Usage:mcu_tool [option]\n");
    fprintf(stdout,"%-20s %s\n", "-h help",         "factory test help");
    fprintf(stdout,"%-20s %s\n", "-r <options>",    "reset");
    fprintf(stdout,"%-20s %s\n", "-u",              "upgrade mcu");
    fprintf(stdout,"%-20s %s\n", "-g <reg>",        "get the mcu reg value");
    fprintf(stdout,"%-20s %s\n", "-c <options>",    "mcu can debug");
    fprintf(stdout,"%-20s %s\n", "-S <options>",    "flag switch ctrl");
    fprintf(stdout,"%-20s %s\n", "-O <options>",    "mcu gpio ctrl");
    fprintf(stdout,"%-20s %s\n", "-T <options>",    "factory test");
}

static void usage(const char *exe_name)
{
    if (NULL == exe_name) {
        usage_all();
        return ;
    }

    fprintf(stdout,"Usage:%s <switches> [option]\n", exe_name);
    fprintf(stdout,"\n");

    if(!strcmp("-c", exe_name)) {
        fprintf(stdout,"%-20s %s\n", "-c <options>",    "mcu can debug");
        fprintf(stdout,"%-20s %s\n", "  stat",              "get mcu can statistics");
        fprintf(stdout,"%-20s %s\n", "  clr",               "clear mcu can statistics");
    } else if(!strcmp("-r", exe_name)) {
        fprintf(stdout,"%-20s %s\n", "-r <options>",    "reset");
        fprintf(stdout,"%-20s %s\n", "  mcu",               "manual reset mcu");
        fprintf(stdout,"%-20s %s\n", "  soc",               "manual reset soc");
    } else if (!strcmp("-S", exe_name)) {
        fprintf(stdout,"%-20s %s\n", "-S <options>", "flag switch ctrl");
        fprintf(stdout,"%-20s %s\n", "  mcu_rst_soc_on",    "允许mcu复位soc");
        fprintf(stdout,"%-20s %s\n", "  mcu_rst_soc_off",   "不许mcu复位soc");
        fprintf(stdout,"%-20s %s\n", "  cap_charing_auto",  "电容自动控制充放电");
        fprintf(stdout,"%-20s %s\n", "  cap_charing_manual","电容手动控制充放电");
        fprintf(stdout,"%-20s %s\n", "  led_r_on",          "前面板led红灯亮");
        fprintf(stdout,"%-20s %s\n", "  led_g_on",          "前面板led绿灯灭");
        fprintf(stdout,"%-20s %s\n", "  led_r_flash_1s",    "前面板led红灯闪烁-1Hz");
        fprintf(stdout,"%-20s %s\n", "  led_g_flash_1s",    "前面板led绿灯闪烁-1Hz");
    } else if (!strcmp("-O", exe_name)) {
        fprintf(stdout,"%-20s %s\n", "  oil_up",            "断油电拉高");
        fprintf(stdout,"%-20s %s\n", "  oil_down",          "断油电拉低");
        fprintf(stdout,"%-20s %s\n", "  5v_on",             "5V开");
        fprintf(stdout,"%-20s %s\n", "  5v_off",            "5V关");
        fprintf(stdout,"%-20s %s\n", "  12v_on",            "12V开");
        fprintf(stdout,"%-20s %s\n", "  12v_off",           "12V关");
        fprintf(stdout,"%-20s %s\n", "  sys_pwr_on",        "系统电源开");
        fprintf(stdout,"%-20s %s\n", "  sys_pwr_off",       "系统电源关");
        fprintf(stdout,"%-20s %s\n", "  cap_charing_on",    "电容充电");
        fprintf(stdout,"%-20s %s\n", "  cap_charing_off",   "电容不充电");
        fprintf(stdout,"%-20s %s\n", "  eth_on",            "PHY电源开");
        fprintf(stdout,"%-20s %s\n", "  eth_off",           "PHY电源关");
        fprintf(stdout,"%-20s %s\n", "  gps_on",            "GPS电源开");
        fprintf(stdout,"%-20s %s\n", "  gps_off",           "GPS电源关");
        fprintf(stdout,"%-20s %s\n", "  4g_on",             "4G电源开");
        fprintf(stdout,"%-20s %s\n", "  4g_off",            "4G电源关");
    } else if (!strcmp("-T", exe_name)) {
        fprintf(stdout, "输出结果约定:\n");
        fprintf(stdout, "测试成功,输出的最后一行字符串将包含TEST OK\n");
        fprintf(stdout, "测试失败,输出的最后一行字符串将包含TEST FAIL: \"失败原因\"\n");

        fprintf(stdout, "OPTIONS:\n");
        fprintf(stdout, "-Tgpio                     <IO测试>\n");
        fprintf(stdout, "-Tstatus                   <状态查看>\n");
        fprintf(stdout, "-Trs485                    <RS485测试，接Ehub>\n");
        fprintf(stdout, "-Trs232                    <RS232自环测试>\n");
        //fprintf(stdout, "-Trs232_2                  <第二路RS232自环测试>\n");
        fprintf(stdout, "-Tsd                       <SD卡读写测试>\n");
        fprintf(stdout, "-Trecord_on_dms            <DMS的MIC录音5秒钟>\n");
        fprintf(stdout, "-Trecord_on_lcd            <LCD的MIC录音5秒钟>\n");
        fprintf(stdout, "-Trecord_on_all            <所有MIC录音3秒钟>\n");
        fprintf(stdout, "-Tinside_speaker           <内置喇叭播放录音文件>\n");
        fprintf(stdout, "-Toutside_speaker          <外置喇叭播放录音文件>\n");
        fprintf(stdout, "-Timu                      <读取IMU数据>\n");
    } else {
        usage_all();
    }
    return;
}

__attribute((constructor)) void before_main()
{
    __system_properties_init();
}

#define GETOPT_OPT_STR  "huRCW:S:O:T:D:V:L:c:g:r:"
int main(int argc, char *argv[])
{
    int opt;
    int ret = 0;
    signal(SIGTERM, sigHandler);

    if (1 == argc) {
        usage(NULL);
        return ret;
    }

    while ((opt = getopt(argc, argv, GETOPT_OPT_STR)) != -1) {
        switch (opt) {
            case 'r':
                {
                    if(!strcmp("mcu", optarg)) {
                        fprintf(stdout,"send reset mcu cmd. \n");
                        comm_send(mClt, MCU_MSG_TYPE_MCU_RST, NULL, 0);
                    } else if(!strcmp("soc", optarg)) {
                        fprintf(stdout,"send reset soc cmd. \n");
                        comm_send(mClt, MCU_MSG_TYPE_SOC_RST, NULL, 0);
                    } else {
                        usage(argv[1]);
                    }
                }
                break;
            case 'g':
                {
                    uint32_t addr = 0;
                    char * str;
                    addr = (uint32_t)strtol(optarg, &str, 16);
                    comm_send(mClt, MCU_MSG_TYPE_MCU_REG, (uint8_t *)&addr, sizeof(uint32_t));
                }
                break;
            case 'c':
                {
                    if(!strcmp("stat", optarg)) {
                        fprintf(stdout,"get mcu can statistics. \n");
                        comm_send(mClt, MCU_MSG_TYPE_CAN_STAT, NULL, 0);
                    } else if(!strcmp("clr", optarg)) {
                        fprintf(stdout,"clear mcu can statistics. \n");
                        comm_send(mClt, MCU_MSG_TYPE_CAN_CLR_STAT, NULL, 0);
                    }
                }
                break;
            case 'D':
                {
                    dump(optarg);
                }
                break;
            case 'V':
                {
                    getVersion(optarg);
                }
                break;
#if 0
            case 'G':
                if (optarg[0] == 'u') {
                    gps_sw_upgrade();
                    mcuConfig();
                } else if (optarg[0] == '1') {
                    config_mcu_uart_baud(9600);
                } else if (optarg[0] == '2') {
                    config_mcu_uart_baud(115200);
                } else if(optarg[0] == 'v') {
                    string ver = gps_get_version();
                    if(ver.size() > 0) {
                        printf("%s\n", ver.c_str());
                    }
                } else if(optarg[0] == 'w') {
                    gps_warm_boot();
                } else if(!strcmp("set_bd", optarg)) {
                    set_bd_only();
                } else if(!strcmp("set_gps", optarg)) {
                    set_gps_only();
                } else if(!strcmp("set_all", optarg)) {
                    set_bd_and_gps();
                } else if(optarg[0] == 'b') {
                }
                break;
#endif
            case 'C':
                comm_send(mClt, MCU_MSG_TYPE_CONFIG_CAN, NULL, 0);
                break;
            case 'W':
                test_disp(&optarg[0]);
                break;
            case 'S':
                ctrl_switch(&optarg[0]);
                break;
            case 'O':
                ctrl_io(&optarg[0]);
                break;
            case 'T':
                {
                    factory_test(optarg);
                }
                break;
            case 'u':
                {
                    ret = do_mcu_upgrade();
                    mcuConfig();
                }
                break;
            case 'R':
                {
                    recvCmdLoop();
                }
                break;
            case 'h':
            default:
                usage(argv[1]);
                break;
        }
    }
    return ret;
}

