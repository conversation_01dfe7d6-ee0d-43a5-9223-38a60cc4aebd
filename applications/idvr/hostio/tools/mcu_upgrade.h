#ifndef __MINIEYE_MCU_UPGRADE__
#define __MINIEYE_MCU_UPGRADE__

#include "ipcAgent.h"

#define MCU_UPGRD_FRAME_SIZE    (128)
//#define MCU_FIRWARE_PATH      "/data/minieye/mcu/dfh1935_app.bin.pack"
//#define MCU_FIRWARE_PATH      "/data/minieye/mcu/dfha_mcu_upgrade.bin"
#define MCU_FIRWARE_PATH      "/data/minieye/mcu/m5pro_mcu_upgrade.bin"
#define MCU_FIRWARE_VERSION_PATH      "/data/minieye/mcu/version"

typedef struct {
    uint16_t crc;
    uint16_t packet_idx;
    uint16_t packet_total;
    uint8_t payload[0];
} __attribute__((packed))UpgradeHeaderT;


typedef struct {
    uint32_t crc;
    uint32_t len;
    char     magic[16];
    char     commit[8];
    char     version[32];
}app_header;

static bool onMessage(uint8_t major, uint8_t minor);
static uint32_t crc32(uint32_t *addr, int num);
static bool firmwareCheck(void);
static bool upgrade_init(void);
static bool send_upgrade_frame(void);
static uint64_t systime(void);
static uint16_t UpdateCRC16(uint16_t crcIn, uint8_t byte);
static uint16_t Cal_CRC16(const uint8_t* data, uint32_t size);
static int waitCmdAckTimeOut(uint32_t cmd, IpcMessage* pMsg, uint32_t ms);

int resetMcu(void);

int do_mcu_upgrade(void);
int get_firmware_info(void);

#endif
