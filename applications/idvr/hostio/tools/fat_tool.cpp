/*
    http://elm-chan.org/docs/fat_e.html
*/
#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <vector>
#include <string>
#include <inttypes.h>

#include "mystd.h"

using namespace std;

typedef struct BootCommonField {
    uint8_t BS_JumpBoot[3];
    uint8_t BS_OEM[8];
    uint16_t BPB_BytsPerSec;
    uint8_t BPB_SecPerClus;
    uint16_t BPB_RsvdSecCnt;
    uint8_t BPB_NumFATs;
    uint16_t BPB_RootEntCnt;
    uint16_t BPB_TotSec16;
    uint8_t BPB_Media;
    uint16_t BPB_FATSz16;
    uint16_t BPB_SecPerTrk;
    uint16_t BPB_NumHeads;
    uint32_t BPB_HiddSec;
    uint32_t BPB_TotSec32;
} __attribute__ ((packed)) BootCommonField;

typedef struct BootFat32Field {
    uint32_t BPB_FATSz32;
    uint16_t BPB_ExtFlags;
    uint16_t BPB_FSVer;
    uint32_t BPB_RootClus;
    uint16_t BPB_FSInfo;
    uint16_t BPB_BkBootSec;
    uint8_t BPB_Reserved[12];
    uint8_t BS_DrvNum;
    uint8_t BS_Reserved;
    uint8_t BS_BootSig;
    uint32_t BS_VolID;
    uint8_t BS_VolLab[11];
    uint8_t BS_FilSysType[8];
    uint8_t BS_BootCode32[420];
    uint16_t BS_BootSign;
} __attribute__ ((packed)) BootFat32Field;

typedef struct FSInfo{
#if 0
    FSInfo(uint32_t freeNum, uint32_t lastCluNum)
        : FSI_LeadSig(0x41615252)
        , FSI_Reserved1{0}
        , FSI_StrucSig(0x61417272)
        , FSI_Free_Count(freeNum)
        , FSI_Nxt_Free(lastCluNum)
        , FSI_Reserved2{0}
        , FSI_TrailSig(0xaa550000)
    {

    }
#endif
    uint32_t FSI_LeadSig;
    uint8_t FSI_Reserved1[480];
    uint32_t FSI_StrucSig;
    uint32_t FSI_Free_Count;
    uint32_t FSI_Nxt_Free;
    uint8_t FSI_Reserved2[12];
    uint32_t FSI_TrailSig;
} __attribute__ ((packed)) FSInfo;

#define ATTR_READ_ONLY      0x01 
#define ATTR_HIDDEN         0x02 
#define ATTR_SYSTEM         0x04 
#define ATTR_VOLUME_ID      0x08 
#define ATTR_DIRECTORY      0x10 
#define ATTR_ARCHIVE        0x20 
#define ATTR_LONG_FILE_NAME 0x0F 

typedef struct SFN {
#if 0
    SFN(char *name, uint16_t clusH, uint16_t clusL, uint32_t size)
        : DIR_Attr(ATTR_ARCHIVE)
        , DIR_NTRes(0)
        , DIR_CrtTimeTenth(0)
        , DIR_CrtTime(0)
        , DIR_CrtDate(0)
        , DIR_LstAccDate(0)
        , DIR_FstClusHI(clusH)
        , DIR_WrtTime(0)
        , DIR_WrtDate(0)
        , DIR_FstClusLO(clusL)
        , DIR_FileSize(size)
    {
        strncpy(DIR_Name, name, 11);
    }
#endif
    char DIR_Name[11];
    uint8_t DIR_Attr;
    uint8_t DIR_NTRes;
    uint8_t DIR_CrtTimeTenth;

    uint16_t DIR_CrtTime;
    uint16_t DIR_CrtDate;
    uint16_t DIR_LstAccDate;
    uint16_t DIR_FstClusHI;
    uint16_t DIR_WrtTime;
    uint16_t DIR_WrtDate;
    uint16_t DIR_FstClusLO;
    uint32_t DIR_FileSize;
} __attribute__ ((packed)) SFN;

typedef struct LFN{
    uint8_t LDIR_Ord;
    uint8_t LDIR_Name1[10];
    uint8_t LDIR_Attr[1];
    uint8_t LDIR_Type[1];
    uint8_t LDIR_Chksum[1];

    uint8_t LDIR_Name2[12];
    uint8_t LDIR_FstClusLO[2];
    uint8_t LDIR_Name3[4];
} __attribute__ ((packed)) LFN;

void bootSecParse(BootCommonField *p, BootFat32Field *p32, FSInfo *pfs)
{

    printf("---------------common field--------------------------\n");
    printf("BS_JumpBoot 0x%02x 0x%02x 0x%02x\n", p->BS_JumpBoot[0], p->BS_JumpBoot[1], p->BS_JumpBoot[2]);
    printf("BS_OEM %.8s\n", p->BS_OEM);
    printf("BPB_BytsPerSec 0x%04x\n", p->BPB_BytsPerSec);
    printf("BPB_SecPerClus 0x%02x\n", p->BPB_SecPerClus);
    printf("BPB_RsvdSecCnt 0x%04x\n", p->BPB_RsvdSecCnt);
    printf("BPB_NumFATs 0x%02x\n", p->BPB_NumFATs);
    printf("BPB_RootEntCnt 0x%04x\n", p->BPB_RootEntCnt);
    printf("BPB_TotSec16 0x%04x\n", p->BPB_TotSec16);
    printf("BPB_Media 0x%02x\n", p->BPB_Media);
    printf("BPB_FATSz16 0x%04x\n", p->BPB_FATSz16);
    printf("BPB_SecPerTrk 0x%04x\n", p->BPB_SecPerTrk);
    printf("BPB_NumHeads 0x%04x\n", p->BPB_NumHeads);
    printf("BPB_HiddSec 0x%08x\n", p->BPB_HiddSec);
    printf("BPB_TotSec32 0x%08x\n", p->BPB_TotSec32);
    
    printf("---------------fat32 field--------------------------\n");
    printf("BPB_FATSz32 0x%08x\n", p32->BPB_FATSz32);
    printf("BPB_ExtFlags 0x%04x\n", p32->BPB_ExtFlags);
    printf("BPB_FSVer 0x%04x\n", p32->BPB_FSVer);
    printf("BPB_RootClus 0x%08x\n", p32->BPB_RootClus);
    printf("BPB_FSInfo 0x%04x\n", p32->BPB_FSInfo);
    printf("BPB_BkBootSec 0x%04x\n", p32->BPB_BkBootSec);
    printf("BPB_Reserved %.12s\n", p32->BPB_Reserved);
    printf("BS_DrvNum %02x\n", p32->BS_DrvNum);
    printf("BS_Reserved %02x\n", p32->BS_Reserved);
    printf("BS_BootSig %02x\n", p32->BS_BootSig);
    printf("BS_VolID %08x\n", p32->BS_VolID);
    printf("BS_VolLab %.11s\n", p32->BS_VolLab);
    printf("BS_FilSysType %.8s\n", p32->BS_FilSysType);
    printf("BS_BootCode32 %.420s\n", p32->BS_BootCode32);
    printf("BS_BootSign 0x%04x\n", p32->BS_BootSign);

    printf("-------------------FAT-----------------------\n");
    for(int i=0; i<p->BPB_NumFATs; i++) {
        printf("FAT zone%d address at 0x%x\n", i, p->BPB_BytsPerSec*(p->BPB_RsvdSecCnt + i*p32->BPB_FATSz32));
    }

    uint32_t data_sec = p->BPB_NumFATs*p32->BPB_FATSz32 + p->BPB_RsvdSecCnt;
    uint32_t data_sec_num = p->BPB_TotSec32 - (p->BPB_NumFATs*p32->BPB_FATSz32 + p->BPB_RsvdSecCnt);

    printf("data sector at 0x%x\n", data_sec);
    printf("data sector num 0x%x\n", data_sec_num);
    printf("data addr 0x%x\n", data_sec * p->BPB_BytsPerSec);
    printf("data size 0x%x\n", data_sec_num * p->BPB_BytsPerSec);


    //FSInfo *pfs = (FSInfo *)&data[p32->BPB_FSInfo * p->BPB_BytsPerSec];

    printf("-------------------FSInfo-----------------------\n");
    printf("FSI_LeadSig 0x%04x\n", pfs->FSI_LeadSig);
    printf("FSI_Reserved1 %.480s\n", pfs->FSI_Reserved1);
    printf("FSI_StrucSig 0x%04x\n", pfs->FSI_StrucSig);
    printf("FSI_Free_Count 0x%04x\n", pfs->FSI_Free_Count);
    printf("FSI_Reserved2 %.12s\n", pfs->FSI_Reserved2);
    printf("FSI_Nxt_Free 0x%04x\n", pfs->FSI_Nxt_Free);
    printf("FSI_TrailSig 0x%04x\n", pfs->FSI_TrailSig);

}

int touchFile(const char *name)
{
    FILE *fp = fopen(name, "w+");
    if(!fp) {
        printf("create file fail");
        return -1;
    }

    fclose(fp);
    return 0;
}

void createVolumeFile(uint8_t *root, FSInfo *pfs, uint32_t *pfat, int32_t file_num, int32_t file_size, int32_t cluSize)
{
    SFN *sn = (SFN *)root;

    /* 清除之前可能存在的文件 */
    memset(root, 0, 2*1024*1024);

    /* 使用短文件名，16个字节 */
    int32_t sn_size = file_num * 32;
    int32_t sn_cluCnt = (sn_size + cluSize - 1) / cluSize;
    
    printf("file_num %d sn_size %d sn_cluCnt %d\n", file_num, sn_size, sn_cluCnt);
    
    for(int32_t i=0; i<sn_cluCnt; i++) {
        if(i+1 == sn_cluCnt) {
            if(sn_cluCnt > 1) {
                pfat[2+i] = 0x0FFFFFFF;
            }
            break;
        }
        pfat[2+i] = 2+i+1;
    }

    pfs->FSI_Free_Count -= sn_cluCnt;
    pfs->FSI_Nxt_Free += sn_cluCnt;

    for (int32_t i=0; i<file_num; i++) {
        uint32_t fileUseCluCnt = (file_size + cluSize -1) / cluSize;
        uint32_t nextClu = pfs->FSI_Nxt_Free;
        uint16_t startCluH = (nextClu >> 16) & 0xFFFF;
        uint16_t startCluL = nextClu & 0xFFFF;

        if (pfs->FSI_Free_Count <= 0) {
            printf("no space.\n");
            return;
        }
        if (pfs->FSI_Free_Count < fileUseCluCnt) {
            fileUseCluCnt = pfs->FSI_Free_Count;
            file_size = fileUseCluCnt*cluSize;
        }

        memset(sn[i].DIR_Name, 0, sizeof(sn[i].DIR_Name));
        snprintf(sn[i].DIR_Name, sizeof(sn[i].DIR_Name), "%06d", i);
        sn[i].DIR_Attr = ATTR_ARCHIVE;
        sn[i].DIR_NTRes = 0;
        sn[i].DIR_CrtTimeTenth = 0;
        sn[i].DIR_CrtTime = 0;
        sn[i].DIR_CrtDate = 0;
        sn[i].DIR_LstAccDate = 0;
        sn[i].DIR_FstClusHI = startCluH;
        sn[i].DIR_WrtTime = 0;
        sn[i].DIR_WrtDate = 0;
        sn[i].DIR_FstClusLO = startCluL;
        sn[i].DIR_FileSize = file_size;
        printf("file %d nextClu %d remainClu %d fileUseCluCnt %d\n", i, nextClu, pfs->FSI_Free_Count, fileUseCluCnt);
        
        for(int32_t i=0; i<fileUseCluCnt; i++) {
            if (i+1 == fileUseCluCnt) { // is the last cluster
                pfat[nextClu+i] = 0x0FFFFFFF;
                //printf("fat last set idx %d\n", nextClu+i);
                break;
            }
            pfat[nextClu+i] = nextClu+i+1;//fat write next num
            //printf("fat[%d]=0x%08x\n", nextClu+i, pfat[nextClu+i]);
        }
        pfs->FSI_Free_Count -= fileUseCluCnt;
        pfs->FSI_Nxt_Free += fileUseCluCnt;
    }
}

bool findDentryList(uint32_t *pfat, uint32_t size, uint32_t start, vector<uint32_t> &vFat)
{
    //遍历文件
    printf("fat[0-2] 0x%08x 0x%08x 0x%08x\n", pfat[0], pfat[1], pfat[2]);
    for (uint32_t next=start, cnt=0; cnt<size; cnt++) {
        vFat.push_back(next);
        if (pfat[next] == 0x0FFFFFF8 || pfat[next] == 0x0FFFFFFF) { /* list end */
            printf("find fat end 0x%x cnt %d\n", pfat[next], cnt);
            return true;
        }
        next = pfat[next]; /* fat值为下一个索引 */
    }
    return false;
}

void dentryParse(vector<uint8_t> &vDentry)
{
    uint8_t *dentry = vDentry.data();
    uint32_t offset = 0;

    while (offset < vDentry.size()) {
        char lfn_magic = 0x40;
        uint8_t magic = dentry[0] & lfn_magic;
        uint8_t lfn_idx = 0;

        if (magic == lfn_magic) { /*LFN magic*/
            lfn_idx = dentry[0] & 0x3f;
        }

        SFN *sn = (SFN *)&dentry[offset+lfn_idx*32];
        offset += (lfn_idx + 1) * 32;

        uint8_t empty[32];
        memset(empty, 0, sizeof(empty));
        if(!memcmp(sn, empty, 32)) {
            printf("entry end.\n");
            return;
        }

        printf("entry offet %d lfn %d name %.11s attr 0x%02x\n", offset, lfn_idx, sn->DIR_Name, sn->DIR_Attr);
    }
}

void printbuf(unsigned char *buf, int len)
{
	int i;

	for(i=0; i<len; i++)
	{
		if(i && i%16==0)
			printf("\n");
		printf("0x%02x ", buf[i]);
	}
	printf("\n");
}

void fixFileSize(uint8_t *dentry, uint32_t entrySize, FSInfo *pfs, uint32_t *pfat, uint32_t cluSize, uint32_t file_size)
{
    uint32_t offset = 0;
    printf("cur entry size %d\n", entrySize);
    while (offset < entrySize) {
        char lfn_magic = 0x40;
        uint8_t magic = dentry[0] & lfn_magic;
        uint8_t lfn_idx = 0;

        if (magic == lfn_magic) { /*LFN magic*/
            lfn_idx = dentry[0] & 0x3f;
        }

        SFN *sn = (SFN *)&dentry[offset+lfn_idx*32];
        offset += (lfn_idx + 1) * 32;
        printf("entry offet %d lfn %d name %.11s\n", offset, lfn_idx, sn->DIR_Name);

        uint8_t empty[32];
        memset(empty, 0, sizeof(empty));
        if(!memcmp(sn, empty, 32)) {
            printf("entry end.\n");
            return;
        }

        if (sn->DIR_Attr == ATTR_ARCHIVE) {
            //
        }

        uint32_t fileUseCluCnt = (file_size + cluSize -1) / cluSize;
        uint32_t nextClu = pfs->FSI_Nxt_Free + 1;
        uint16_t startCluH = (nextClu >> 16) & 0xFFFF;
        uint16_t startCluL = nextClu & 0xFFFF;

        if (pfs->FSI_Free_Count <= 0) {
            printf("no space.\n");
            return;
        }

        if (pfs->FSI_Free_Count < fileUseCluCnt) {
            fileUseCluCnt = pfs->FSI_Free_Count;
            file_size = fileUseCluCnt*cluSize;
        }

        /* set dentry */
        sn->DIR_FstClusHI = startCluH;
        sn->DIR_FstClusLO = startCluL;
        sn->DIR_FileSize = file_size;
        printf("nxtFree %d nextClu %d fileUseCluCnt %d\n", pfs->FSI_Nxt_Free, nextClu, fileUseCluCnt);

        /* set fat list */
        for(int32_t i=0; i<fileUseCluCnt; i++) {
            if (i+1 == fileUseCluCnt) { // is the last cluster
                pfat[nextClu+i] = 0x0FFFFFFF;
                printf("fat last set idx %d\n", nextClu+i);
                break;
            }
            pfat[nextClu+i] = nextClu+i+1;//fat write next num
            //printf("fat[%d]=0x%08x\n", nextClu+i, pfat[nextClu+i]);
        }

        /* set fs */
        pfs->FSI_Free_Count -= fileUseCluCnt;
        pfs->FSI_Nxt_Free += fileUseCluCnt;
    }
}

void volumeParse(vector<uint8_t> &vBoot, uint32_t &offsetRoot)
{
    uint8_t *boot = vBoot.data();
    BootCommonField *p = (BootCommonField *)&boot[0];
    BootFat32Field *p32 = (BootFat32Field *)&boot[sizeof(BootCommonField)];
    FSInfo *pfs = (FSInfo *)&boot[512];
    bootSecParse(p, p32, pfs);

    offsetRoot = (p->BPB_NumFATs*p32->BPB_FATSz32 + p->BPB_RsvdSecCnt) * p->BPB_BytsPerSec;
}

void fixDentry(uint8_t *boot, int32_t file_size)
{
    BootCommonField *p = (BootCommonField *)&boot[0];
    BootFat32Field *p32 = (BootFat32Field *)&boot[sizeof(BootCommonField)];
    FSInfo *pfs = (FSInfo *)&boot[512];
    bootSecParse(p, p32, pfs);

    uint32_t offsetRoot = (p->BPB_NumFATs*p32->BPB_FATSz32 + p->BPB_RsvdSecCnt) * p->BPB_BytsPerSec;
    uint32_t cluSize = p->BPB_SecPerClus * p->BPB_BytsPerSec;
    
    printf("------------------add file--------------------\n");
    printf("data offset at 0x%x\n", offsetRoot);
    printf("cluster size %d\n", cluSize);

    uint32_t offsetFat = p->BPB_BytsPerSec * p->BPB_RsvdSecCnt;
    uint32_t *pfat = (uint32_t *)&boot[offsetFat];
    uint32_t fatSize = p32->BPB_FATSz32 * p->BPB_BytsPerSec/4;
    printf("fat offset 0x%x fatNum %d\n", offsetFat, fatSize);

    vector<uint32_t> vFat;
    uint32_t rootDentryClu = 2;
    findDentryList(pfat, fatSize, rootDentryClu, vFat);
    printf("get root entry size %" FMT_LLD "\n", vFat.size());

    vector<uint8_t> vDentry;
    uint8_t *pRoot = boot+offsetRoot; /* root entry start */
    for (uint32_t i=0; i<vFat.size(); i++) {
        /* data size on one fat */
        uint32_t dataOffset = (vFat[i] - 2) * cluSize;
        printf("insert offset %d size %d\n", dataOffset, cluSize);
        vDentry.insert(vDentry.end(), pRoot + dataOffset, pRoot + dataOffset + cluSize);
    }

    dentryParse(vDentry);
    fixFileSize(pRoot, vDentry.size(), pfs, pfat, cluSize, file_size);
}

void fixDentry2(uint8_t *boot, int32_t file_size, int32_t file_num)
{
    BootCommonField *p = (BootCommonField *)&boot[0];
    BootFat32Field *p32 = (BootFat32Field *)&boot[sizeof(BootCommonField)];
    FSInfo *pfs = (FSInfo *)&boot[512];
    bootSecParse(p, p32, pfs);

    uint32_t offsetRoot = (p->BPB_NumFATs*p32->BPB_FATSz32 + p->BPB_RsvdSecCnt) * p->BPB_BytsPerSec;
    uint32_t cluSize = p->BPB_SecPerClus * p->BPB_BytsPerSec;
    
    printf("------------------add file--------------------\n");
    printf("data offset at 0x%x\n", offsetRoot);
    printf("cluster size %d\n", cluSize);

    uint32_t offsetFat = p->BPB_BytsPerSec * p->BPB_RsvdSecCnt;
    uint32_t *pfat = (uint32_t *)&boot[offsetFat];
    uint32_t fatSize = p32->BPB_FATSz32 * p->BPB_BytsPerSec/4;
    printf("fat offset 0x%x fatNum %d\n", offsetFat, fatSize);

    uint8_t *root = &boot[offsetRoot];
    createVolumeFile(root, pfs, pfat, file_num, file_size, cluSize);
}

uint32_t volumeDump(const char *filename)
{
#define RSV_HEAD_SIZE (1024*10)
    vector <uint8_t> vBoot(RSV_HEAD_SIZE, 0);
    uint8_t *boot = vBoot.data();

    FILE *fp = fopen(filename, "r+");
    if(!fp) {
        printf("open file %s fail: %s\n", filename, strerror(errno));
        return -1;
    }
    int32_t ret = fread(boot, 1, RSV_HEAD_SIZE, fp);
    if (ret != RSV_HEAD_SIZE) {
        printf("read ret %d not enough\n", ret);
        return -1;
    }
    fclose(fp);
    
    if (boot[0] != 0xEB && boot[0] != 0xE9) {
        printf("volume invalid\n");
        return -2;
    }

    printf("dump vboot size %" FMT_LLU "\n", vBoot.size());
    uint32_t offsetRoot = 0;
    volumeParse(vBoot, offsetRoot);
    return offsetRoot;
}

int fixVolume(const char *filename, int32_t file_size, int32_t file_num)
{
#define FILE_NAME_SIZE (1024*1024*3)

    uint32_t offsetRoot = volumeDump(filename);

    uint32_t read_size = offsetRoot + FILE_NAME_SIZE;
    printf("read_size %d\n", read_size);

    vector <uint8_t> vBoot(read_size, 0);
    uint8_t *boot = vBoot.data();

    FILE *fp = fopen(filename, "r+");
    if(!fp) {
        printf("open file %s fail: %s\n", filename, strerror(errno));
        return -1;
    }
    int32_t ret = fread(boot, 1, read_size, fp);
    if (ret != read_size) {
        printf("read ret %d not enough\n", ret);
        return -1;
    }
    
    if (boot[0] != 0xEB && boot[0] != 0xE9) {
        printf("volume invalid\n");
        return -2;
    }

    //fixDentry(boot, file_size);
    fixDentry2(boot, file_size, file_num);
    rewind(fp);
    int32_t len = fwrite(boot, 1, read_size, fp);
    printf("write back len %d\n", len);
    fclose(fp);
    return 0;
}

void Usage(void)
{
    printf("-f <volume name>\n");
    printf("-w <write file size>\n");
    printf("-n <write file num>\n");
    printf("-d <dump volume>\n");
}

#define GETOPT_OPT_STR  "hdf:w:n:"
int main(int argc, char *argv[])
{
    int opt;
    int file_size = 0;
    int file_num = 16;
    string volume;
    bool dump = false;
    bool file_size_ok = false;

    while ((opt = getopt(argc, argv, GETOPT_OPT_STR)) != -1) {
        switch (opt) {
            case 'w':
                {
                    if (optarg) {
                        file_size = strtoul(optarg, NULL, 0);
                        file_size_ok = true;
                    }
                }
                break;
            case 'n':
                {
                    if (optarg) {
                        file_num = strtoul(optarg, NULL, 0);
                    }
                }
                break;
            case 'f':
                {
                    if (optarg) {
                        volume += optarg;
                    }
                }
                break;
            case 'd':
                {
                    dump = true;
                }
                break;
            case 'h':
            default:
                Usage();
                break;
        }
    }

    if(dump) {
        volumeDump(volume.c_str());
    }
    if(file_size_ok) {
        fixVolume(volume.c_str(), file_size, file_num);
    }

    return 0;
}


