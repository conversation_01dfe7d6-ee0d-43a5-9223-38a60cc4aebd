#ifndef __MINIEYE_UART_H__
#define __MINIEYE_UART_H__

#include <unistd.h>
#include <stdio.h>
#include <stdint.h>

class Uart
{
    public:
        Uart(const char *dev_name, int32_t baud = 115200);
        ~Uart();

        bool send(const char *data, int32_t len);
        int32_t recv(uint8_t *buf, int32_t len, uint32_t timeout_ms = 0);
    private:
        int32_t mFd;
        bool uartInit(const char *dev, int32_t baud);
};

#endif

