#include "common.h"

void printbuf(unsigned char *buf, int len)
{
	int i;
	for(i=0; i<len; i++){
		if(i && i%16==0)
			fprintf(stdout,"\n");
		fprintf(stdout,"0x%02x ", buf[i]);
	}
	fprintf(stdout,"\n");
}

uint8_t cal_crc(const char *buf, int len)
{
    if (len <=0 ) {
        return 0;
    }

    uint8_t crc = buf[0];
    for (int32_t i=1; i<len; i++) {
        crc ^= buf[i];

    }
    return crc;
}


bool comm_recv(IpcClient &client, IpcMessage *pMsg)
{
    return client.recv(pMsg);
}

bool comm_send(IpcClient &client, IpcMessage *pMsg)
{
    return client.send(pMsg);
}


void comm_send(IpcClient &client, McuMsgTypeE cmd, const char *data)
{
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];
    memset(buf, 0, sizeof(buf));

    if (!data || strlen(data) >= MCU_MSG_MAX_SIZE) {
        return;
    }

    pMsg->ipcMsg.type = cmd;
    pMsg->ipcMsg.len = strlen(data);
    strcpy(pMsg->u.schar, data);
    client.send(&(pMsg->ipcMsg));
}


void comm_send(IpcClient &client, McuMsgTypeE cmd, uint8_t *data, int32_t len)
{
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];
    memset(buf, 0, sizeof(buf));
    pMsg->ipcMsg.type = cmd;
    pMsg->ipcMsg.len = len;
    if(len > 0) {
        memcpy(&pMsg->u.u8Array[0], data, len);
    }
    client.send(&(pMsg->ipcMsg));
}


int32_t read_msg_timeout(IpcClient &client, McuMsgTypeE cmd, IpcMessage *pMsg, int32_t timeout_ms)
{
    uint32_t ms;
    struct timespec ts;
    struct timespec now;

    clock_gettime(CLOCK_REALTIME, &ts);
    while (1) {
        if (!client.recv(pMsg, 100)) {
            //fprintf(stdout, "recv timeout\n");
        } else {
            if (pMsg->type == cmd) { /*get msg*/
                break;
            }
        }

        clock_gettime(CLOCK_REALTIME, &now);
        if((now.tv_sec - ts.tv_sec )*1000 + now.tv_nsec/1000000 > ts.tv_nsec/1000000 + timeout_ms) {
            return -1;
        }
    }
    return 0;
}



