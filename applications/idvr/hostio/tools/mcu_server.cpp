#include "IpcAgent.h"


IpcServer *pSvr = NULL;

void *test_read(void *)
{
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];

    while(1) {
        if(pSvr->recv(pMsg)) {
            printf("server read back, msg len %d\n", pMsg->len);
        } else {
            printf("read fail\n");
        }
    }
}

int main(int argc, char** argv)
{
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];
    uint8_t count = 0;

    pSvr = new IpcServer(SH_NAME_HOSTIO);
    pthread_t pth;
    pthread_create(&pth, NULL, test_read, NULL);
    pthread_detach(pth);
    while (1) {
        sleep(2);
        pMsg->len = count++ % 128;

        if(pSvr->send(pMsg))
            printf("server push msg len %d\n", pMsg->len);
        else
            printf("server send fail!\n");

    }
    delete pSvr;
}



