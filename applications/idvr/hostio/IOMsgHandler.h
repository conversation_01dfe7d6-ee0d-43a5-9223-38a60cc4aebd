﻿#ifndef __IO_MSG_HANDLER_H__
#define __IO_MSG_HANDLER_H__

#include <list>
#include <assert.h>
#include <sys/prctl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <string>

#include "CanCtrl.h"
#include "IOMsg.h"
#include "FileMonitor.h"
#include "GpsSpeedCalib.h"
#include "mystd.h"
#include "FileLog.h"
#include "McuMessage.h"
#include "libflow.h"
#include "CRingBuf.h"
#include "common.h"
#include "EvUart.h"
#include "GpsHandler.h"
#include "FileProp.h"

#define UART_DEVICE "/dev/ttyS1"
#define APPEND_STAT_MSG(msg, key, fmt, val ...) APPEND_STR_MSG(msg, key, 16, fmt, ## val)
#define ADC_PWR_LOW_VAL     (10.0)
#define ADC_PWR_OFF_VAL     (8.9)
#define ADC_BAT_ABSENT_VAL  (6.0)

struct SpeedTurnInfo {
    //选中的速度 左转右转
    int     selectSpeedx10;
    uint8_t selectTurnR;
    uint8_t selectTurnL;

    //模拟速度 左转 右转
    int     pulseSpeedx10;
    uint8_t pluseTurnL;
    uint8_t pluseTurnR;
    float   lastPulseFreq;

    //can速度 左转 右转
    int     canSpeedx10;
    uint8_t canTurnL;
    uint8_t canTurnR;

    //GPS速度
    int     GPSSpeed;
} ;

struct McuInfo {
    bool        mcuInited;          //mcu是否初始化
    bool        mcuVersionOk;       //mcu版本查询是否成功
    char        mcuVersion[20];     //mcu版本
    int16_t     mcuTempture_x100;   //mcu温度
    uint32_t    mcuUptime;
};

typedef enum {
    ADISP_NORMAL,
    ADISP_SPEED,
    ADISP_MAX,
} ADISP_MODE_E;

// IO消息处理机(内置1314协议解析器)
class IOMsgHandler
    : public my::Singleton<IOMsgHandler>,
      public EvUart,
      public GpsObserver,
      public my::thread
{

public:
    IOMsgHandler();
    ~IOMsgHandler();

    class MonitorThead : public my::thread
    {
    public:
        MonitorThead(IOMsgHandler *IOHdl)
            : mIOHdl(IOHdl)
        {
        }

        void run()
        {
            prctl(PR_SET_NAME, "monitor");
            mIOHdl->mFileMonitor.init();
            while(1) {
                // 50ms for timeout, 检测/sdcard/run文件夹
                if (mIOHdl->mFileMonitor.loop()) {
                    if (mIOHdl->mFileMonitor.mCanInputJsonWrEvent) {
                        mIOHdl->mFileMonitor.mCanInputJsonWrEvent = false;
                        mIOHdl->mCan.McuCanConfig();
                    }
                }

                // 检测录像状态并通过ipcAgent广播，10s一次
                mIOHdl->chkRecordStatus();

                // 检测串口，出错重连
                mIOHdl->monitorUart();
            }
        }
    private:
        IOMsgHandler *mIOHdl;
    };

    int init();
    int start();
    void stop();
    std::string dumpStatStr(void);
    std::string logLevel(const char * key, int level);
    // 发送协议包给MCU
    bool sendMsgToMcu(IOMsg& msg);
    bool queryLastGps(double &lat, double &lng);
    // 处理原始数据
    bool onDataRecv(const char* data, int size);

public:
    FileLog *mLogger = NULL;
    bool getAccStatus()
    {
        return mAccOffEvent;
    }
protected:
    // 循环体
    void run();
    //void bat_thres_config(uint16_t disable_thres, uint16_t enable_thres);
    bool monitorUart()
    {
        if (!isEvUartConnected()) {
            evUartOpen(mWorkerHandle, UART_DEVICE, 921600, false);
        }
        return true;
    }

private:
    // 处理原始数据
    void onEvUartDisConnected();
    bool onEvUartRecved(const char *data, int32_t dataLen);

    // 单片机报文处理
    void proc(my::uchar major, my::uchar minor, const char* data1, int size1);
    void mcuAgentServerHandleMsg(void);
    // 初始化配置mcu相关，例如can
    void mcuConfig(void);
    // 查询单片机版本
    void queryMcuVersion(void);

    void accMonitor(void);
    void calcAdcVoltage();
    bool tryPowerOff(bool forcePwrDn = false);

    void chkRecordStatus(void);
    void mcuStatusSync(void);
    void mcuCarInfoSync(void);
    void get_last_gps_location(void);
    void systemPowerMonitor(void);
    bool isCanSig(uint32_t canId, const can_signal &sig);
    uint8_t decodeCanMsg(const UrtpCanT *pCan);
    void updateCurrentSpeed(void);
    void calibAspeed(float freq);
    void updateAspeedCalibParam(void);
    void getLocalProp(void);
    bool setDevId(void);
    void getWorkMode(void);
    void setPowerVol(void);
    void setExtIoStatusToProperty(void);

    void dumpStatusInfo(void);
    bool setLogVerbose(LOG_TYPE_CTRL_E key, int32_t level);

    // GPS模块回调
    void updateLbs(LBS *lbs);

    bool saveTotalMiles(double miles);

    void setTotalMiles(double miles)
    {
        MY_SPINLOCK_X(mTotalMilesLock);
        mTotal_mileage = miles;
    }

    double getTotalMiles()
    {
        MY_SPINLOCK_X(mTotalMilesLock);
        return mTotal_mileage;
    }

    void addTotalMiles(double miles)
    {
        MY_SPINLOCK_X(mTotalMilesLock);
        mTotal_mileage += miles;
    }

    double getDiffMiles()
    {
        MY_SPINLOCK_X(mDiffMilesLock);
        return mDiffMiles;
    }

    void addDiffMiles(double miles)
    {
        MY_SPINLOCK_X(mDiffMilesLock);
        mDiffMiles += miles;
    }

    void clearDiffMiles()
    {
        MY_SPINLOCK_X(mDiffMilesLock);
        mDiffMiles = 0;
    }

    bool mAccOffEvent = false; //连续5次检测到acc off 时为true，连续5次检测到acc on时为false

    // 延时关机相关
    my::spinlock mPwrlock;
    uint32_t mPwrDn = 0; /* 各个线程是否确认关机 */
    uint32_t mPwrDnPending = 0;
    bool mSocRebootEnable = true;
    bool mPwrKeyRstDone = false;

    uint32_t mRecordStatusChkTime = my::getClockSec();
    my::timestamp mPowerDownPendingTime = 0;
    my::timestamp mCanSpeedUpdateTime = 0;
    my::timestamp mCanTurnUpdateTime = 0;
    my::timestamp mCanDoorOpenStatTS = 0;
    my::timestamp mCanMsgUpdateTime = 0;
    my::timestamp mHeartBeatTime = 0;
    my::timestamp mSysOffTime = 0;
    my::timestamp mDumpStatusTS = 0;

    LBS mLbs;
    // 模拟车速相关参数
    bool    mGpsCalibEnable = true;
    float   mGpsCalibRatio;
    double  mGpsCalibStddev;
    GpsSpeedCalib *mGpsSpdCalib;

    float mTotal_mileage = 1.0;
    my::spinlock mTotalMilesLock;

    /*true:则使用speed算里程，false:使用gps算里程*/
    bool mbCalcMileageBySpd = false;
    
    /*总里程 根据gps坐标计算，累加得出*/
    double mDiffMiles = 0.0;
    my::spinlock mDiffMilesLock;

    /* 控制里程的fileprop保存间隔 */
    my::timestamp mTotalMilesTs = 0;

    /* 使用速度计算里程 */
    my::timestamp mSpdListTs = 0;
    std::vector<double> mSpdList;
    
    shared_ptr<FileProp> mFilePropTotalMile;
    
    //uint8_t mKey[2] = {0};
    ADISP_MODE_E mAdisp_mode = ADISP_NORMAL;
    WorkModeE mMode = WORK_MODE_FT;
    uint32_t mAdcRaw[ADC_CHN_MAX] = {0};
    float mAdcAct[ADC_CHN_MAX]= {0.0};

    // hostio获取状态统一广播
    uint32_t mRecStatus = 0;
    uint32_t mDiskStatus = 0;

    McuMsgMcuStatT mSyncMsg;
    SpeedTurnInfo mSpeedTurn;
    IOStatus mVehicleIo = {0};
    DVR_IO_t mDvrIo = {0};
    pwr_set_t mPwrSet = {0};
    flag_sta_t mFlagSta = {0};
    SysStatus_t mSys = {0};
    McuInfo mMCUInfo = {0};
    string mDeviceId;

    /* 打印日志控制 */
    uint8_t mLogVerbose[LOG_TYPE_MAX];

	struct
	{
		int state; // 状态机

		struct
		{
			int len;
			char data[6];
		} header; // 消息头

		my::string body; // 消息体
	} mStack; // 串口1314协议接收栈

    CanCtrl mCan;
    // 检测can.json文件变化
    FileMonitor mFileMonitor;
    MonitorThead *pMonitor;
    GpsHandler mGpsHandler;

    // libevserice
    worker_handle_t   mWorkerHandle = INVALID_HANDLE;

    // 需要固化的中间数据保存的路径
    const char *mDataPath;
    
};


#endif
