#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <mntent.h>

#include <string.h>
#include <fcntl.h>
#include <sys/socket.h>
#include <linux/netlink.h>
 
#include "FileMonitor.h"

FileMonitor::FileMonitor()
    : mIfd(-1)
    , mNfd(-1)
    , mCanInputJsonWrEvent(false)
{
    logd("watcher +");
}
FileMonitor::~FileMonitor()
{
    logd("watcher -");
    if(mIfd > 0) close(mIfd);
    if(mNfd > 0) close(mNfd);
}

void FileMonitor::inotifyEvent(struct inotify_event *i)
{
    //logd("wd = %2d; ",i->wd);

   if (i->wd == mWd[INOTIFY_SDCARD_RUN_DIR]) {
        //logd("%s IN_CLOSE_WRITE\n", inotify_file[INOTIFY_SDCARD_RUN_DIR]);
        if (i->mask & IN_CLOSE_WRITE) {
            if (i->len > 0) {
                if(!strncmp(i->name, "can_input.json", strlen("can_input.json"))) {
                    logd("name = %s\n",i->name);
                    mCanInputJsonWrEvent = true;
                    logd("can_input.json IN_CLOSE_WRITE\n");
                }
            }
        }
    } else {
        logd("unknow ifd\n");
    }

}




int32_t FileMonitor::init(void)
{
    /* monitor file event */
    mIfd = inotify_init();
    if (mIfd == -1) {
        logd("inotify_init fail: %s", strerror(errno));
        return -1;
    }

    /* 只检测文件有些问题， 当文件被删除后， 对文件的检测就失效了.*/
    for (int32_t i = 0; i < INOTIFY_MAX; i++) {
        //int wd = inotify_add_watch(mIfd, inotify_file[i], IN_OPEN | IN_CLOSE_WRITE | IN_DELETE_SELF);
        int wd = inotify_add_watch(mIfd, inotify_file[i], IN_CLOSE_WRITE);
        if (wd == -1) {
            logd("inotify add[%d] %s error %s\n", i, inotify_file[i], strerror(errno));
        }else {
            logd("inotify add[%d] %s success\n", i, inotify_file[i]);
        }
        mWd[i] = wd;
    }

    return 0;
}


#define GET_MAX_FD(max_fd, fd) max_fd = fd > max_fd ? fd : max_fd;

bool FileMonitor::loop(void)
{
    ssize_t ret;
    struct inotify_event *event;
    char buf[2048];
    char *p = buf;
    fd_set rfds;
    struct timeval tv;
    int rv;
    int32_t max_fd = -1;

#define ADD_FD_TO_SET(fd, fds) do{\
    if(fd>0) FD_SET(fd, &fds);\
    GET_MAX_FD(max_fd, fd);\
}while(0)

    FD_ZERO(&rfds);
    ADD_FD_TO_SET(mNfd, rfds);
    ADD_FD_TO_SET(mIfd, rfds);


    //logd("max_fd %d\n", max_fd);

    tv.tv_sec = 0;
    tv.tv_usec = 50000;
    rv = select(max_fd + 1, &rfds, NULL, NULL, &tv);
    if (rv == -1) {
        logd("Monitor select error: %s\n", strerror(errno));
        usleep(10000);
    } else if (rv == 0) {
        //logd("No data within five seconds.\n");
    } else {
        if (FD_ISSET(mIfd, &rfds)) { /* watcher file */
            ret = read(mIfd, buf, sizeof(buf));
            if(ret == -1){
                logd("read error\n");
            }

            //logd("---file watcher notice\n");
            for (p = buf; p < buf + ret;) {
                event = (struct inotify_event *)p;
                inotifyEvent(event);
                p += sizeof(struct inotify_event) + event->len;
            }
        }
    }

    if (mCanInputJsonWrEvent) {
        return true;
    }

    return false;
}



