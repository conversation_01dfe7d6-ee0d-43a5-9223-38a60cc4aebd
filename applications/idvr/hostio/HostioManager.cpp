#include "IOMsgHandler.h"
#include "HostioManager.h"


SINGLETON_STATIC_INSTANCE(HostioManager); // 单例模式


CMD_LISTENER_SINGLETON();
#define THIS_CMD_SOCKET_NAME "hostio"
class HostioCmd : public LogCallProxyCmd
{
    public:
        HostioCmd()    : LogCallProxyCmd("cmd")
        {
            std::string cmdName = getCommand();
            std::string mCmdList[][3] = {
                {cmdName, "help",   "show this usage.\n",},
                {cmdName, "show_status", "show status.\n",},
                {cmdName, "log_level", "get/set log level.\n",},
                {"", "", "",},
            };
            int i = 0;
            mUsage = "\n";

            while (mCmdList[i][0] != "") {
                mUsage += mCmdList[i][0] + " " + mCmdList[i][1] + " - " + mCmdList[i][2];
                setupCmd(THIS_CMD_SOCKET_NAME, mCmdList[i][1].c_str());
                i++;
            }
        }
        virtual ~HostioCmd() {};

        int help(SocketClient *c, int argc, char ** argv)
        {
            sendRsp(c, MMM_CMD_OK, mUsage.c_str(), false);
            return 0;
        }


        int show_status(SocketClient *c, int argc, char ** argv)
        {
            int ret = 0;
            std::string msg;
            int show_time = 0;

            if (argc < 2) {
                ret = -1;

            } else {
                msg = IOMsgHandler::getInstance().dumpStatStr();
            }

            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, msg.c_str(), false);
            return 0;
        }
        int log_level(SocketClient *c, int argc, char ** argv)
        {
            int ret = 0;
            std::string msg;

            const char * key = NULL;
            int32_t level = 0;
            if (argc > 2) {
                key = argv[2];
            }
            if (argc > 3) {
                level = atoi(argv[3]);
            }
            msg = IOMsgHandler::getInstance().logLevel(key, level);

            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, msg.c_str(), false);
            return 0;
        }
        int onRunCommand(SocketClient *c, int argc, char ** argv)
        {
            std::string usage = "\n";

            if ((argc < 2) || !strcmp(argv[1], "help")) {
                return help(c, argc, argv);
            } else if (!strcmp(argv[1], "show_status")) {
                return show_status(c, argc, argv);
            } else if (!strcmp(argv[1], "log_level")) {
                return log_level(c, argc, argv);
            }

            return sendRsp(c, MMM_CMD_INVALID_CMD, NULL, false);
        }
    private:
        std::string mUsage;

};


static HostIOConfigReceiver mConfigReceiver;

bool HostIOConfigReceiver::OnBroadcastCallback(vector<string>& vec)
{
	char section[64]={0};
	char key[64]={0};
	char value[64]={0};

	for(auto it = vec.begin(); it!=vec.end(); it++)
	{
		sscanf((*it).c_str(), "%s %s %s",section,key,value);
		refresh(section,key,value);
	}

	return 0;
}

bool HostIOConfigReceiver::refresh(char* section,char* key,char* value)
{
    conf_t& sys = HostioManager::getInstance().mConfig;

	logi("[refresh] hostio section=%s,key=%s,value=%s",section,key,value);
	if (my::constr("base") == section)
	{
	    if (my::constr("model") == key)
			sys.product.model = value;
		else if (my::constr("ccc") == key)
		    sys.product.ccc = value;
		else if (my::constr("manufacturer") == key)
			sys.product.vendor = value;
		else if (my::constr("manufacture.date") == key)
			sys.product.date = (my::uint)atoi(value);
		else if (my::constr("manufacture.id") == key)
			sys.product.id  = value;
		else if (my::constr("sim") == key)
			sys.sim = value;
		else if (my::constr("init.date") == key)
		    sys.setup.date = (my::uint)atoi(value);
		else if (my::constr("init.mileage") == key)
			sys.setup.mileage_x10 = (float)atof(value);
		else if (my::constr("vehicle.city") == key)
			sys.vehicle.city = (my::ushort)atoi(value);
		else if (my::constr("vehicle.plate_color") == key)
			sys.vehicle.plate_color = (my::ushort)atoi(value);
		else if (my::constr("vehicle.plate_num") == key)
			sys.vehicle.plate_num = value;
		else if (my::constr("vehicle.plate_type") == key)
			sys.vehicle.plate_type = value;
		else if (my::constr("vehicle.province") == key)
			sys.vehicle.province = (my::ushort)atoi(value);
		else if (my::constr("vehicle.vin") == key)
			sys.vehicle.vin = value;
        else if (my::constr("report.default_inteval") == key)
            sys.report.default_inteval = (my::uint)atoi(value);
        else if (my::constr("report.sleep_inteval") == key)
            sys.report.sleep_inteval = (my::uint)atoi(value);
        else if (my::constr("recoder.alarmEnable") == key)
            sys.recoderAlarm.alarmEnable = (bool)atoi(value);
        else if (my::constr("recoder.alarmInterval") == key)
            sys.recoderAlarm.alarmEnable = atoi(value);
	}
    else if (my::constr("media") == section)
    {
        if (my::constr("manufacture.speaker") == key) {
            sys.speaker = (my::uint)atoi(value);
        }
    }
    else if (my::constr("clock") == section)
	{
		if (my::constr("src") == key)
			sys.clock.src = (char)atoi(value);
	}
	else if (my::constr("warn") == section)
	{
		if(my::constr("overspeed.alarm") == key)
			sys.warn.overspeed.alarm = atoi(value);
		else if (my::constr("overspeed.enable") == key)
			sys.warn.overspeed.enable = (char)atoi(value);
		else if (my::constr("overspeed.limit") == key)
			sys.warn.overspeed.limit = atoi(value);
        else if (my::constr("overspeed.delta") == key)
			sys.warn.overspeed.delta = atoi(value);
		else if (my::constr("overtime.alarm") == key)
			sys.warn.overtime.alarm = atoi(value);
		else if (my::constr("overtime.enable") == key)
			sys.warn.overtime.enable = (char)atoi(value);
		else if (my::constr("overtime.limit") == key)
			sys.warn.overtime.limit = atoi(value);
		else if (my::constr("overtime.remainder") == key)
			sys.warn.overtime.remainder = atoi(value);
		else if (my::constr("overtime.rest") == key)
			sys.warn.overtime.rest = atoi(value);
		else if (my::constr("night_spding.datetime_bits") == key)
			sys.warn.night_spding.datetime_bits = (my::uchar)atoi(value);
		else if (my::constr("night_spding.week_bits") == key)
			sys.warn.night_spding.week_bits = (my::uchar)atoi(value);
		else if (my::constr("night_spding.time_bgn") == key)
			sys.warn.night_spding.time_bgn = (int)atoi(value);
		else if (my::constr("night_spding.time_end") == key)
			sys.warn.night_spding.time_end = (int)atoi(value);
		else if (my::constr("night_spding.alarm") == key)
			sys.warn.night_spding.alarm = (int)atoi(value);
		else if (my::constr("night_spding.alarm_spch_minus") == key)
			sys.warn.night_spding.alarm_spch_minus = (int)atoi(value);
		else if (my::constr("night_spding.alarm_spch") == key)
			sys.warn.night_spding.alarm_spch = value;
		else if (my::constr("night_spding.alarm_tms") == key)
			sys.warn.night_spding.alarm_tms = (int)atoi(value);
		else if (my::constr("night_spding.alarm_time_gap") == key)
			sys.warn.night_spding.alarm_time_gap = (int)atoi(value);
	}
	else if (my::constr("conn1") == section)
	{
		if (my::constr("ip1") == key)
			sys.net[0].ip[0] = value;
		else if (my::constr("port.tcp1") == key)
			sys.net[0].port[0] = atoi(value);
		else if (my::constr("ip2") == key)
			sys.net[0].ip[1] = value;
		else if (my::constr("port.tcp2") == key)
			sys.net[0].port[1] = atoi(value);
	}
    else if (my::constr("io") == section)
    {
        if (my::constr("gpio.D0.labels") == key)
            sys.gpio[0].labels = value;
        else if (my::constr("gpio.D1.labels") == key)
            sys.gpio[1].labels = value;
        else if (my::constr("gpio.D2.labels") == key)
            sys.gpio[2].labels = value;
        else if (my::constr("gpio.D3.labels") == key)
            sys.gpio[3].labels = value;
        else if (my::constr("gpio.D4.labels") == key)
            sys.gpio[4].labels = value;
        else if (my::constr("gpio.D5.labels") == key)
            sys.gpio[5].labels = value;
        else if (my::constr("gpio.D6.labels") == key)
            sys.gpio[6].labels = value;
        else if (my::constr("gpio.D7.labels") == key)
            sys.gpio[7].labels = value;
    }

	return true;
}

HostioManager::HostioManager()
{
    CmdListener::getInstance().init(THIS_CMD_SOCKET_NAME);
    CmdListener::getInstance().regCmd(new HostioCmd());
    CmdListener::getInstance().startCmdListener();

    mConfigReceiver.startRecevier();

    IOMsgHandler &tmp = IOMsgHandler::getInstance(); //new IOMsgHandler();
    mMsgHandler = &tmp;

    mMcuAgentServer = new IpcServer(SH_NAME_HOSTIO);

    mAccOffWrite = false;
    mGpsWriteTm = my::timestamp::now();
}

HostioManager::~HostioManager()
{
    delete mMcuAgentServer;
}


bool HostioManager::init()
{
    my::string cfgpath("/data/minieye/idvr/etc/config.ini");
    int ret = mConfig.ldconf(cfgpath.c_str());
    if (ret < 0) {
        loge("HostIO Failed to load config.ini");
        return false;
    }

    mDataSavePath = idvrGetDataPath();
    mMsgHandler->init();


    return true;
}

const char * HostioManager::getDataSavePath()
{
    return mDataSavePath.c_str();
}

int HostioManager::start()
{
    mMsgHandler->start();
    my::thread::start();
    return 0;
}

void HostioManager::stop()
{
    mMsgHandler->stop();
    my::thread::stop();
}

bool HostioManager::mcuAgentServerSendMsg(McuMsgTypeE cmd, const uint8_t *data, uint32_t size)
{
    uint8_t buf[MCU_MSG_MAX_SIZE] = {0};
    McuMessage *pMsg = (McuMessage *)&buf[0];

    if (size > MCU_MSG_PAYLOAD_MAX_SIZE) {
        loge("the len of msg is not allow out of range, size is %d but max is %d", size, MCU_MSG_PAYLOAD_MAX_SIZE);
        return false;
    }
    pMsg->ipcMsg.len = size;
    pMsg->ipcMsg.type = cmd;
    if (size != 0) {
        memcpy(pMsg->u.u8Array, data, size);
    }
    mMcuAgentServer->send(&(pMsg->ipcMsg));
    return true;
}

bool HostioManager::mcuAgentServerRecvMsg(McuMessage *msg)
{
    mMcuAgentServer->recv(&(msg->ipcMsg));
    return true;
}

void HostioManager::debug()
{
    logd("vehicle.sim=[%s]", mConfig.sim.c_str());
    logd("vehicle.vin=[%s]", mConfig.vehicle.vin.c_str());
    logd("vehicle.plate_type=[%s]", mConfig.vehicle.plate_type.c_str());
    logd("vehicle.plate_num=[%s]", mConfig.vehicle.plate_num.c_str());
    logd("vehicle.plate_color=[%d]", mConfig.vehicle.plate_color);
    logd("vehicle.province=[%d]", mConfig.vehicle.province);
    logd("vehicle.city=[%d]", mConfig.vehicle.city);

    logd("product.vendor=[%s]", mConfig.product.vendor.c_str());
    logd("product.model=[%s]", mConfig.product.model.c_str());
    logd("product.ccc=[%s]", mConfig.product.ccc.c_str());
    logd("product.sn=[%d]", mConfig.product.sn);
    logd("product.date=[%d]", mConfig.product.date);
    logd("product.id=[%d]", mConfig.product.id.c_str());
    logd("product.speaker=[%d]", mConfig.speaker);

    logd("setup.date=[%d]", mConfig.setup.date);
    logd("setup.mileage=[%.1f]", mConfig.setup.mileage_x10);

    logd("warn.overtime.enable=[%d]", mConfig.warn.overtime.enable);
    logd("warn.overtime.limit=[%d]", mConfig.warn.overtime.limit);
    logd("warn.overtime.remainder=[%d]", mConfig.warn.overtime.remainder);
    logd("warn.overtime.alarm=[%d]", mConfig.warn.overtime.alarm);
    logd("warn.overtime.rest=[%d]", mConfig.warn.overtime.rest);

    logd("warn.overspeed.enable=[%d]", mConfig.warn.overspeed.enable);
    logd("warn.overspeed.limit=[%d]", mConfig.warn.overspeed.limit);
    logd("warn.overspeed.alarm=[%d]", mConfig.warn.overspeed.alarm);

    logd("clock.src=[%d]", mConfig.clock.src);

    return;
}

int HostioManager::propertySet(const char *key, const char *value)
{
    return __system_property_set(key, value);
}

void HostioManager::writeGpsRecordProperty()
{
    char value[PROP_VALUE_MAX];
    int len = 0;
    memset(value, 0, sizeof(value));
    len = __system_property_get(PROP_RW_MINIEYE_GPSLASTLOCATION, value);
    if(len > 0) {
        propertySet(PROP_PERSIST_MINIEYE_LASTGPSLOCATION, value);
    }
}

void HostioManager::refreshGpsRecord()
{
    IOMsgHandler &ioM = IOMsgHandler::getInstance();
    if (ioM.getAccStatus()) {
        /* acc off 立即写入一次 */
        if (!mAccOffWrite) {
            mAccOffWrite = true;
            /* 写入 */
            writeGpsRecordProperty();
        }
    } else {
        if (mAccOffWrite) {
            mAccOffWrite = false;
            /* acc off 后有写入操作重置写入间隔 */
            mGpsWriteTm = my::timestamp::now();
        }
        if (mGpsWriteTm.elapsed() > 600000) {
            /* 10min 写入一次 */
            mGpsWriteTm = my::timestamp::now();
            writeGpsRecordProperty();
        }
    }
}

void HostioManager::run()
{
    prctl(PR_SET_NAME, "HostioManager");

    while (!exiting()) {
        /* 刷新gps数据到persist      property */
        refreshGpsRecord();
        sleep(3);
    }
}

