#include "json.hpp"
#include "AMessage.h"
#include "CmdListener.h"
#include "system_properties.h"

#include "dps.h"

DPS::DPS(int32_t doorIdx, const std::shared_ptr<minieye::AHandler> & sp, const std::string & camFile)
{
    mDoorIdx = doorIdx;
    mspAhdlr = sp;
    mCamFile = camFile;
}

DPS::~DPS()
{
    mAlgo.Yyt_Uinit();
    mAlgo.Yyt_NetWorkStop();
}
bool DPS::init(CRingBuf * rb1, CRingBuf * rb2)
{
    if (mbInitFlag) {
        return false;
    }

    mRbMap[0] = rb1;
    mRbMap[1] = rb2;

    char path[128];

    snprintf(path, sizeof(path), "/data/dps%d.dat", mDoorIdx);
    mAlgo.Yyt_Init((char*)mCamFile.c_str(), path);
    my::file f;
    int fsize = f.open(path, "r");

    if (fsize == sizeof(mAlgParam)) {
        int r = f.gets((char *)&mAlgParam, sizeof(mAlgParam));

        if (r != fsize) {
            loge("read config fail! %s", path);
            return false;
        }

        mAlgo.Yyt_SetConfig(&mAlgParam);
        f.close();

    } else {
        mAlgParam.nMinFrThred = MINFRTHRED;
        mAlgParam.nDefaultRadius = DEFALRADIUS;
        mAlgParam.nReqMatches = REQMATCHES;
        mAlgParam.nTrackFrame = TRACKFRAME;
        mAlgParam.nLostFrame = LOSTFRAME;
        mAlgParam.nMaxMatchDist = MAXMATCHDIST;
        mAlgParam.nBrightThred = BRIGHTTHRED;
        mAlgParam.bClearNum = 0;
        mAlgParam.nSubsamplingFactor = SUBSAMPLINGFACTOR;
        mAlgParam.nMaxArea = MAXAREA;
        mAlgParam.nMinPointNum = MINPOINTNUM;
        mAlgParam.fMinWhRatio = MINWHRATIO;
        mAlgParam.fMaxWhRatio = MAXWHRATIO;
        mAlgParam.fMinPaRatio = MINPARATIO;
        mAlgParam.nStaticFramNum = STATICFRAMENUM;
        mAlgParam.nDevHigh = DEVHIGH;
        mAlgParam.fDefinition = DEFINITION;

        mAlgParam.sUpBrokenLine.count       = 2;
        mAlgParam.sUpBrokenLine.arr[0].x    = 1;
        mAlgParam.sUpBrokenLine.arr[0].y    = 36;    //96/4.0 + 0.5//96/3.0 + 0.5;
        mAlgParam.sUpBrokenLine.arr[1].x    = 119;  //120-1; //175
        mAlgParam.sUpBrokenLine.arr[1].y    = 36;   //96/4.0 + 0.5;
        mAlgParam.sUpBrokenLine.arr[2].x    = 0;
        mAlgParam.sUpBrokenLine.arr[2].y    = 0;
        mAlgParam.sUpBrokenLine.arr[3].x    = 0;
        mAlgParam.sUpBrokenLine.arr[3].y    = 0;
        mAlgParam.sUpBrokenLine.arr[4].x    = 0;
        mAlgParam.sUpBrokenLine.arr[4].y    = 0;
        mAlgParam.sUpBrokenLine.arr[5].x    = 0;
        mAlgParam.sUpBrokenLine.arr[5].y    = 0;
        mAlgParam.sUpBrokenLine.arr[6].x    = 0;
        mAlgParam.sUpBrokenLine.arr[6].y    = 0;
        mAlgParam.sUpBrokenLine.arr[7].x    = 0;
        mAlgParam.sUpBrokenLine.arr[7].y    = 0;
        mAlgParam.sDownBrokenLine.count = 2;
        mAlgParam.sDownBrokenLine.arr[0].x  = 1;
        mAlgParam.sDownBrokenLine.arr[0].y  = 60;    //96*2/3.0 - 0.5;
        mAlgParam.sDownBrokenLine.arr[1].x  = 119;   //120-1; //175
        mAlgParam.sDownBrokenLine.arr[1].y  = 60;    //96*3/4.0 + 0.5;
        mAlgParam.sDownBrokenLine.arr[2].x  = 0;
        mAlgParam.sDownBrokenLine.arr[2].y  = 0;
        mAlgParam.sDownBrokenLine.arr[3].x  = 0;
        mAlgParam.sDownBrokenLine.arr[3].y  = 0;
        mAlgParam.sDownBrokenLine.arr[4].x  = 0;
        mAlgParam.sDownBrokenLine.arr[4].y  = 0;
        mAlgParam.sDownBrokenLine.arr[5].x  = 0;
        mAlgParam.sDownBrokenLine.arr[5].y  = 0;
        mAlgParam.sDownBrokenLine.arr[6].x  = 0;
        mAlgParam.sDownBrokenLine.arr[6].y  = 0;
        mAlgParam.sDownBrokenLine.arr[7].x  = 0;
        mAlgParam.sDownBrokenLine.arr[7].y  = 0;
        mAlgo.Yyt_SetConfig(&mAlgParam);
    }

    //设置回调函数，使配置软件设置算法时可以把数据保存在flash上
    mAlgo.Yyt_SaveAlgFun(this->cbSetAlgoParam);
    char propName[64];
    snprintf(propName, sizeof(propName), "rw.dps.door%d", mDoorIdx);
    char propValue[64];
    if (__system_property_get(propName, propValue) > 0) {
        int32_t up = 0, down = 0;
        if (2 == sscanf(propValue, "%d %d", &up, &down)) {
            mAlgo.Yyt_ResetCount(up, down);
            logd("door%d init up %d, down %d", mDoorIdx, up, down);
        }
    }
    mbInitFlag = true;
    mAlgo.Yyt_NetWorkStart(6800 + mDoorIdx);
    return true;
}

bool DPS::readChnData(CRingBuf *rb, std::shared_ptr<MMap> & m, int64_t & ts)
{
    DATA_HDR  data;
    uint32_t frameLen = 0;
    RBFrame * pFrame = (RBFrame*)rb->RequestReadFrame(&frameLen);
    int offset = 0;

    if (CRB_VALID_ADDRESS(pFrame) && (frameLen > 0)) {
        int32_t dataType = -1;

        if ((IFrame != pFrame->frameType) &&
            (sizeof(DATA_HDR) != pFrame->dataLen)) {
            loge("Invalid data!");
            return 0;
        }

        ts = pFrame->time;
        data = *(reinterpret_cast<DATA_HDR *>(pFrame->data));
        m = std::make_shared<MMap>((void*)data.phyYAddr, data.dataLen);
    }

    return !!m.get() && !!m->virAddr;
}

void DPS::run()
{
    char thrdName[16];
    snprintf(thrdName, sizeof(thrdName), "dps%d", mDoorIdx);
    prctl(PR_SET_NAME, thrdName);

    while (!exiting()) {
        my::timestamp cur = my::timestamp::now();
        int count = 0, total = 0;
        int64_t max = -1, min = 0x7fffffffffffffff;
        int maxTsCh = -1;

        std::shared_ptr<MMap> spMMap[2] = {nullptr};

        for (auto r : mRbMap) {
            bool ret = false;

            do {
                ret = readChnData(r.second, spMMap[r.first], mTs[r.first]);

                if (!ret) {
                    count++;
                    usleep(10000);
                }
            } while (!ret);

            if (spMMap[r.first] != nullptr && spMMap[r.first]->virAddr) {
                logd("Got %p, len = %d, count %d", spMMap[r.first]->virAddr, spMMap[r.first]->len, count);
                count = 0;
                total++;

                if (mTs[r.first] < min) {
                    min = mTs[r.first];
                }

                if (mTs[r.first] > max) {
                    max = mTs[r.first];
                    maxTsCh = r.first;
                }
            }

            mpVideoData[r.first] = spMMap[r.first]->virAddr;
        }

        if (total < mRbMap.size()) {
            logd("door %d, total %d < %d!", mDoorIdx, total, mRbMap.size());

        } else {
            char test[128];
            snprintf(test, sizeof(test), "/tmp/dps_test%d", mDoorIdx);

            if (!access(test, R_OK)) {
                if (mRbMap.size() == total) {
                    unlink(test);
                }

                for (int i = 0; i < mRbMap.size(); i++) {
                    char rawfile[128];
                    snprintf(rawfile, sizeof(rawfile), "/data/door%d_src%d_raw.dat", mDoorIdx, i);
                    FILE * fp = fopen(rawfile, "w+");

                    if (fp) {
                        fwrite(mpVideoData[i], 720 * 576 * 3 / 2, 1, fp);
                        fclose(fp);
                    }
                }
            }

            if (total == mRbMap.size()) {
                bool ret = mAlgo.Yyt_ConvertCapture(reinterpret_cast<char**>(mpVideoData));

                if (!ret) {
                    loge("Yyt_ConvertCapture error! ret %d, total %d, [%p, %p]\n",
                         ret, total, mpVideoData[0], mpVideoData[1]);

                } else {
                    std::shared_ptr<minieye::AMessage> postMsg = std::make_shared<minieye::AMessage>(0, mspAhdlr);
                    postMsg->setInt32("door", mDoorIdx);
                    my::uint64 ts = (my::uint64)my::timestamp::now();
                    postMsg->setInt64("ts", static_cast<int64_t>(ts));
                    postMsg->post();

                    logd("door %d, Yyt_ConvertCapture succ! cost %f, [%p, %p]\n",
                         mDoorIdx, cur.elapsed(), mpVideoData[0], mpVideoData[1]);
                }

                logd("door %d, max - min = %d, maxTsCh = %d, count %d", mDoorIdx, (int)(max - min), maxTsCh, count);

                for (auto r : mRbMap) {
                    if ((max - min) > 50) {
                        if (r.first == maxTsCh)  {
                            logd("door %d, max - min = %d, maxTsCh = %d read again", mDoorIdx, (int)(max - min), maxTsCh);
                            continue;
                        }
                    }

                    CRingBuf * rb = r.second;
                    int ret = rb->CommitRead();
                    //logd("CommitRead ret = %d", ret);
                }

            } else {
                loge("door %d, no enough channel data!", mDoorIdx);
            }
        }

        for (int i = 0; i < mRbMap.size(); i++) {
            mpVideoData[i] = nullptr;
            mTs[i] = 0;
        }

        if (cur.elapsed() < 10) {
            logd("+door %d, elapsed %fms", mDoorIdx, cur.elapsed());
            usleep(10 * 1000);

        } else {
            logd("-door %d, elapsed %fms", mDoorIdx, cur.elapsed());
        }
    }
}

//一定要完善这个函数的内容，否则算法配置了无法保存，重启了就没有了
//回调函数，当PC配置软件设置算法参数时，会调到这个函数，请完善这个函数的内容
int DPS::cbSetAlgoParam(char* pFilePath, AlgCountParam* pParam, int nSize)
{
    if (sizeof(AlgCountParam) != nSize) {
        loge("error : nSize %d != %d!!!", nSize, sizeof(AlgCountParam));
        return -1;
    }

    my::file f;

    if (f.open(pFilePath) >= 0) {
        int len = sizeof(AlgCountParam);
        int total = len;
        const char * p = (const char *)pParam;

        do {
            len = f.puts(p + total - len, len);
        } while (len > 0);

        f.close();
    }

    logd("dps config file path %s", pFilePath);
    logd("pAlgParam->nLostFrame = %d\n", pParam->nLostFrame);
    logd("pAlgParam->->sUpBrokenLine.arr[0].x = %d\n", pParam->sUpBrokenLine.arr[0].x);

    return 0;
}

bool DPS::process(bool bDoor, AlgOutput* Output)
{
    if (mbInitFlag) {
        return mAlgo.Yyt_Process(bDoor, Output);
    }

    return mbInitFlag;
}

bool DPS::sendVideo(AlgOutput* pOutput)
{
    if (mbInitFlag) {
        mAlgo.Yyt_SendVideo(pOutput);
    }

    return mbInitFlag;
}