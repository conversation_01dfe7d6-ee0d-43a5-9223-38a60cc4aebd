#ifndef _VEHICLE_INFO_H_
#define _VEHICLE_INFO_H_

#include "mystd.h"
#include "libflow.h"

class VehicleInfo
    : public ILibFlowCallback
    , public my::Singleton<VehicleInfo>
{
        friend class my::Singleton<VehicleInfo>;
    private:
        VehicleInfo();
        // 处理libflow数据
        virtual void OnLibFlowCallback(const char *source,  // '\0' terminated string
                                       const char *topic,   // any binary data
                                       const char *data,    // any binary data
                                       size_t size);        // < 2^32
    public:
        ~VehicleInfo();
    private:
        LibflowClient * mLibflowClient = nullptr;
    public:
        bool mbDoorOpen[2] = {true, true};
        std::string mLastOsdCmd;
};

#endif