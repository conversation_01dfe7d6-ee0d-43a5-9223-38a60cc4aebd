


#ifndef _ALGDPS_H__
#define _ALGDPS_H__


typedef struct tagAlgCountResult
{

	unsigned int nUpNum;		//上的总人数
	unsigned int nDownNum;		//下的总人数
	unsigned int nCurUpNum;
	unsigned int nCurDownNum;  

}AlgCountResult;

typedef struct AlgOutput
{
	int nWidth;
	int nHeight;
	unsigned char* pOutBuf1;  //左上图
	unsigned char* pOutBuf2; //右上图
	unsigned char* pDisp; //左下图
	unsigned char* pFrimg; //右下图
	AlgCountResult* pDpsResult; //人数结果

}AlgOutput;



typedef struct tagAlg_short_point
{
	short x;
	short y;
} Alg_short_point;

typedef struct tagAlgCountBrokenLine
{
	int count;											    /* 描述折线点集的个数 */
	Alg_short_point arr[8];			/* 描述折线的点集 */
} AlgCountBrokenLine;


/*算法设置参数*/
typedef struct tagAlgCountParam
{
	int nMinFrThred;  //最小高度
	int nDefaultRadius;	   //目标差异
	int nReqMatches;     //匹配个数
	int nTrackFrame ;  //跟踪帧数
	int nLostFrame ;    //丢失帧数
	int nMaxMatchDist;  //最大匹配距离
	int nBrightThred;    //最低检测亮度
	
	//跟踪
	int	    bClearNum;      //含义：是否清空计数，默认为0，即不清空	范围：0、1
	
	//上计数线设置,至少两个点，至多8个点
	tagAlgCountBrokenLine sUpBrokenLine;
	
	//下计数线设置，至少两个点，至多8个点
	tagAlgCountBrokenLine sDownBrokenLine;
	
	int nSubsamplingFactor;  //子采样概率背景更新速度
	
		
	int nMaxArea;
	int nMinPointNum;
	float fMinWhRatio;
	float fMaxWhRatio;
	float fMinPaRatio;
	int nStaticFramNum;
	int nDevHigh;
	float fDefinition;
	int bNegativeYDetect;

}AlgCountParam;

typedef int (*pFunc)(char* pFilePath,AlgCountParam* pParam,int nSize);


class YytDpsAlg
{
private:

	AlgCountParam m_Parameter;
	int* m_pLeftMapSeat;
	int* m_pRightMapSeat;
	char* m_pWriteBuf;
	char* m_pCaptureData;
	void* m_pFrameList;


	unsigned char* m_pFrImg;
	unsigned char* m_pDispImg;
	unsigned char* m_pReadBuf;
	bool m_nAlgSetConfigFlag;
	int m_nWidth;
	int m_nHeight ;
	void * m_pDPSAlg;
	
	char* m_pFrameDpsBuf;
	void* m_pNetService;
	void* m_pNetMediaSer;
	pFunc m_pCallFun;
	char* m_pCfgFilePath;
public:
	YytDpsAlg();
	~YytDpsAlg();

	int Yyt_Init(char* pEncryptedFile ,char* pCfgFilePath);//初始化
	int Yyt_SetConfig( AlgCountParam *pParam);	//设置参数,只会写进算法，不会写flash
	void  Yyt_GetAlgConfig( AlgCountParam *pParam);//获取算法当前参数

	bool Yyt_ConvertCapture(char**pMemContent );//两个buffer
	bool Yyt_Process(bool bDoor, AlgOutput* Output  );//bDoor门是否打开，Output  算法输出

	void Yyt_GetAlgVer(char* pVerBuf);//获取算法版本号

	void Yyt_Uinit();//算法停止工作

	void Yyt_ResetCount(int nIn,int nOut);//算法初始进出人数，不设置则从0开始


	//当不使用PC调试软件时，可不调用以下函数
	void Yyt_SaveAlgFun(pFunc pCallFun);//输入回调函数名字
	//可以在其它线程中来配置算法参数，可以不调用
	void Yyt_DynamicSetConfig(AlgCountParam* pParam );//参数保存进算法并调用回调函数写flash,可以不调用，PC客户端会调用
	
	void Yyt_NetWorkStart(int nListenPort);//设备为服务器，监听端口，nListenPort+69也会被占用
	void Yyt_SendVideo(AlgOutput* pOutput);//发送视频

	void Yyt_NetWorkStop();//停止网络服务

};





#endif


