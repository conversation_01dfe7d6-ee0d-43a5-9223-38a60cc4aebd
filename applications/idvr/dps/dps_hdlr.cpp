#include "ttsPlay.h"
#include "AMessage.h"
#include "CmdListener.h"
#include "system_properties.h"
#include "vehicle_info.h"

#include "dps_hdlr.h"

DPSHandler::DPSHandler()
{
    mAMsgLooper = std::make_shared<minieye::ALooper>();
    setLooper(mAMsgLooper);
    memset(&mLastAlgoRes, 0, sizeof(mLastAlgoRes));
}

bool DPSHandler::init(int door, int32_t chn[2], const std::string & file)
{
    if (mspDps == nullptr) {
        mpLogger = new FileLog("/data/minieye/idvr/mlog/dpslog/");
        mpLogger->prepare();
        FILE_LOG_W(mpLogger, "dps%d start", door);

        char doorStr[128];
        snprintf(doorStr, sizeof(doorStr), "door%d", door);
        CRingBuf * rb[2] = {nullptr};

        for (int i = 0; i < 2; i++) {
            char rbName[128];
            snprintf(rbName, sizeof(rbName), "raw_ch%d", chn[i]);
            rb[i] = new CRingBuf(doorStr, rbName, sizeof(DATA_HDR) * SCL_PHY_BUF_MAX_NUM, CRB_PERSONALITY_READER, true);

            if (rb[i] == nullptr) {
                loge("CRingBuf create error !");
            }
        }

        mspDps = std::make_shared<DPS>(door, shared_from_this(), file);

        if (mspDps) {
            mspDps->init(rb[0], rb[1]);
        }

        snprintf(doorStr, sizeof(doorStr), "door%dMsgLper", door);
        mAMsgLooper->setName(doorStr);
    }

    return (mspDps != nullptr);
}

DPSHandler::~DPSHandler()
{
    mAMsgLooper->stop();
}

bool DPSHandler::start()
{
    logd("----");

    if (mAMsgLooper) {
        mAMsgLooper->start();

        if (mspDps) {
            mspDps->start();
        }

        return true;
    }

    return false;
}

void DPSHandler::onMessageReceived(const std::shared_ptr<minieye::AMessage> &msg)
{
    VehicleInfo & dvi = VehicleInfo::getInstance();

    switch (msg->what()) {
        case 0: {
                int32_t door = 0;
                int64_t ts   = 0;
                msg->findInt32("door", &door);
                msg->findInt64("ts", &ts);

                my::timestamp curTs = my::timestamp::now();
                int64_t diff = (my::uint64)curTs - ts;

                if (diff >= 300) {
                    logd("process delay too long %" FMT_LLD " !!!!!", diff);
                }

                AlgOutput ao = {0};

                if (mspDps->process(dvi.mbDoorOpen[door], &ao)) {
                    mspDps->sendVideo(&ao);

                    AlgCountResult * pRes = ao.pDpsResult;                    
                    if (pRes) {
                        char cmd[256];

                        if ((mLastAlgoResCur.nCurUpNum != pRes->nCurUpNum) || 
                            (mLastAlgoResCur.nCurDownNum != pRes->nCurDownNum)) {
                            if ((mLastTmOsdSet.elapsed() >= 1000) ||
                                (pRes->nCurUpNum || pRes->nCurDownNum)) {
                                snprintf(cmd, sizeof(cmd), "cmd setUiTips [%s:上%d下%d] %d %d",
                                        door ? "后" : "前",
                                        pRes->nCurUpNum, pRes->nCurDownNum, 0xffffff, door ? 1 : 3);

                                if (LogCallProxyCmd::sendReq("media", cmd)) {
                                }

                                mLastTmOsdSet = my::timestamp::now();
                                mLastAlgoResCur = *pRes;
                            }
                        }

                        if (((mLastAlgoRes.nUpNum != pRes->nUpNum) ||
                              (mLastAlgoRes.nDownNum != pRes->nDownNum))) {

                            snprintf(cmd, sizeof(cmd), "cmd setUiTips %s:上%03d下%03d %d %d",
                                     door ? "后" : "前",
                                     pRes->nUpNum, pRes->nDownNum, 0xffffff, door ? 0 : 2);

                            if (LogCallProxyCmd::sendReq("media", cmd)) {
                            }
                            snprintf(cmd, sizeof(cmd), "cmd setOsdTips %d %s:上%03d下%03d",
                                     door, door ? "后" : "前",
                                     pRes->nUpNum, pRes->nDownNum);

                            if (LogCallProxyCmd::sendReq("media", cmd)) {
                            }
                            if (mpLogger) {
                                snprintf(cmd, sizeof(cmd), "door%d %s:上%03d下%03d",
                                     door, door ? "后" : "前",
                                     pRes->nUpNum, pRes->nDownNum);
                                FILE_LOG_W(mpLogger, "%s", cmd);
                            }
                            char propName[64];
                            snprintf(propName, sizeof(propName), "rw.dps.door%d", door);
                            char propValue[64];
                            snprintf(propValue, sizeof(propValue), "%d %d", pRes->nUpNum, pRes->nDownNum);
                            __system_property_set(propName, propValue);

                            if (!access("/data/dps_tts", R_OK)) {
                                if (!mLastAlgoRes.nCurUpNum && pRes->nCurUpNum) {
                                    ttsPlayRightNow("%s%s", door ? "后" : "前", "上");
                                }

                                if (!mLastAlgoRes.nCurDownNum && pRes->nCurDownNum) {
                                    ttsPlayRightNow("%s%s", door ? "后" : "前", "下");
                                }
                            }
                        }

                        logd("door %d : up %d, down %d, cur up %d, down %d, open %d", door,
                             pRes->nUpNum, pRes->nDownNum, pRes->nCurUpNum, pRes->nCurDownNum, dvi.mbDoorOpen[door]);
                        mLastAlgoRes = *pRes;
                    }

                    logd("door %d, open %d, Yyt_Process cost %f", door,  dvi.mbDoorOpen[door], curTs.elapsed());
                }

                break;
            }

        default: {
                break;
            }
    }
}

