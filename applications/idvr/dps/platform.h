#include "mi_sys.h"
#include "mystd.h"

#define SCL_PHY_BUF_MAX_NUM 6

typedef struct {
    uint64_t    phyYAddr = 0;
    uint64_t    phyUVAddr = 0;
    uint32_t    dataLen = 0;
} DATA_HDR;

struct MMap {
    void * virAddr = nullptr;
    int32_t len = 0;

    MMap(void * phyAddr, int32_t dataLen)
    {
        MI_SYS_Mmap((uint64_t)phyAddr, dataLen, &virAddr, false);
        len = dataLen;
    }
    ~MMap()
    {
        MI_SYS_Munmap(virAddr, len);
    }
};
