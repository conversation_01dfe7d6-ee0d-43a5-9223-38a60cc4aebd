#ifndef _ALGO_DPS_HDLR_H_
#define _ALGO_DPS_HDLR_H_

#include "mystd.h"
#include "CRingBuf.h"
#include "ALooper.h"
#include "AHandler.h"
#include "FileLog.h"
#include "AlgDps.h"
#include "dps.h"

class DPSHandler
    : public minieye::AHandler

{
    public:
        DPSHandler();
        ~DPSHandler();
        bool init(int door, int32_t chn[2], const std::string & file);
        bool start();

    private:
        virtual void onMessageReceived(const std::shared_ptr<minieye::AMessage> &msg);

    private:
        std::shared_ptr<minieye::ALooper> mAMsgLooper;

        std::shared_ptr<DPS> mspDps = nullptr;


        unsigned char mReadBuf[120 * 96 * 4];;

        my::timestamp mLastTmOsdSet;

        AlgCountResult mLastAlgoResCur;
        AlgCountResult mLastAlgoRes;
        FileLog * mpLogger = nullptr;
};


#endif