#ifndef _ALGO_DPS_MNGR_H_
#define _ALGO_DPS_MNGR_H_
#include "mi_sys.h"

#include "mystd.h"
#include "CRingBuf.h"

#include "AlgDps.h"
#include "platform.h"

/* default dps params */
#define MINFRTHRED 80
#define DEFALRADIUS 16
#define REQMATCHES  3  //car 3  //other 2
#define TRACKFRAME  3
#define LOSTFRAME 2
#define MAXMATCHDIST 20
#define BRIGHTTHRED 90
#define SUBSAMPLINGFACTOR  64 //CAR 160 //other 64
#define MAXAREA   8000
#define MINPOINTNUM 150
#define MINWHRATIO  0.2
#define MAXWHRATIO  5
#define MINPARATIO   0.3
#define STATICFRAMENUM  150
#define DEVHIGH    0
#define DEFINITION  3

class DPS
    : public my::thread
{
    public:
        DPS(int32_t doorIdx, const std::shared_ptr<minieye::<PERSON><PERSON><PERSON><PERSON>> & sp, const std::string & camFile);
        ~DPS();

        bool init(CRingBuf * rb1, CRingBuf * rb2);
        bool process(bool bDoor, AlgOutput* Output);

        bool sendVideo(AlgOutput* pOutput);

    private:
        virtual void run();

    private:
        bool readChnData(CRingBuf *rb, std::shared_ptr<MMap> & m, int64_t & ts);
        static int cbSetAlgoParam(char* pFilePath, AlgCountParam* pParam, int nSize);

    private:
        int32_t mDoorIdx = 0;
        YytDpsAlg mAlgo;
        std::string mCamFile;
        bool mbInitFlag = false;
        std::shared_ptr<minieye::AHandler> mspAhdlr = nullptr;
        std::map<int32_t, CRingBuf *> mRbMap;

        AlgCountParam mAlgParam;
        void * mpVideoData[2] = {nullptr};
        int32_t mOutLen[2] = {0};
        int64_t mTs[2] = {0};
};

#endif
