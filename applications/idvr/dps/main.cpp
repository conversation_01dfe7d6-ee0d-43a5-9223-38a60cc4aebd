#include "dps_hdlr.h"
#include "CmdListener.h"
#include "system_properties.h"

#ifndef ANDROID
__attribute((constructor)) void before_main()
{
    __system_properties_init();
}
#endif
int main(int argc, const char * argv[])
{
    if (argc < 3) {
        loge("usage : %s [cam0ParamFile] [cam1ParamFile]!", argv[0]);
        return -1;
    }

    trace_signal(SIGSEGV);
    trace_signal(SIGFPE);
    trace_signal(SIGABRT);
    trace_signal(SIGBUS);

    char value[PROP_VALUE_MAX] = {0};
    const char * p = nullptr;
    char prop[PROP_NAME_MAX]   = {0};
    snprintf(prop, sizeof(prop), "persist.dps.channels");

    if (__system_property_get(prop, value) > 0) {
        if (value[0]) {
            p = value;
        }
    }

    int32_t chn[4] = {0, 1, 2, 3};
    int32_t ret = 4;

    if (p) {
        ret = sscanf(p, "%d,%d,%d,%d", &chn[0], &chn[1], &chn[2], &chn[3]);

        if (4 != ret) {
            ret = sscanf(p, "%d,%d", &chn[0], &chn[1]);
        }

        if ((4 != ret) && (2 != ret)) {
            loge("get channel tbl fail!! %s", prop);
            return -1;
        }
    }

    std::shared_ptr<DPSHandler> sp[2] = {nullptr};

    for (int i = 0; i < ret / 2; i++) {
        sp[i] = std::make_shared<DPSHandler>();

        //logd("%d : %d", i, chn[i * 2]);
        if (sp[i] && chn[i * 2] >= 0) {
            char path[128];
            snprintf(path, sizeof(path), "%s", argv[1 + i]);
            if (access(path, R_OK)) {
                snprintf(path, sizeof(path), "/system/etc/minieye/dps%d.txt", i);
            }
            sp[i]->init(i, &chn[i * 2], path);
            sp[i]->start();
        }
    }

    bool cfg = true;

    while (true) {
        usleep(10000000);

        if (cfg) {
            cfg = false;

            for (int i = 0; i < 4; i++) {
                char cmd[256];
                snprintf(cmd, sizeof(cmd), "cmd saveconf media.ch%d ai.func dps",       i + 1);
                LogCallProxyCmd::sendReq("config", cmd);
                snprintf(cmd, sizeof(cmd), "cmd saveconf media.ch%d ai.enable true",    i + 1);
                LogCallProxyCmd::sendReq("config", cmd);
                snprintf(cmd, sizeof(cmd), "cmd saveconf media.ch%d ai.fps 12",         i + 1);
                //LogCallProxyCmd::sendReq("config", cmd);
                snprintf(cmd, sizeof(cmd), "cmd saveconf media.ch%d ai.resl 720x576",   i + 1);
                LogCallProxyCmd::sendReq("config", cmd);
            }
        }
    }

    return 0;
}