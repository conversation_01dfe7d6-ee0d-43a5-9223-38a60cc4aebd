#include "json.hpp"
#include "vehicle_info.h"
#include "CmdListener.h"

using json = nlohmann::json;

SINGLETON_STATIC_INSTANCE(VehicleInfo);

VehicleInfo::VehicleInfo()
{
    mLibflowClient = new LibflowClient("127.0.0.1", "23666", "MINIEYE.VehicleInfo.v1");

    if (mLibflowClient) {
        mLibflowClient->start(this);
    }
}
VehicleInfo::~VehicleInfo()
{

}
void VehicleInfo::OnLibFlowCallback(const char *source,     // '\0' terminated string
                                    const char *topic,   // any binary data
                                    const char *data,    // any binary data
                                    size_t size)         // < 2^32
{
    std::string recv_str(data, size);

    try {
        //以json格式解析协议
        json recv_json = json::parse(recv_str);
        json vi = recv_json.at("vehicle_info");
        float speed = vi.at("speed_kmph");
        if (!access("/data/turnlamp_as_door", R_OK)) {/*默认开门，配置转向测试用*/
            if (vi.find("turn_lamp") != vi.end()) {
                int turn = vi.at("turn_lamp");
                mbDoorOpen[0] = !!(turn & 1) && (speed < 1e-3);/*左转*/
                mbDoorOpen[1] = !!(turn & 2) && (speed < 1e-3);
            }
        } else if (access("/data/dps_test", R_OK)){
            bool door0 = vi.at("far_light");
            bool door1 = vi.at("dipped_light");
            mbDoorOpen[0] = door0 && (speed < 1e-3);
            mbDoorOpen[1] = door1 && (speed < 1e-3);
        } else {
            uint32_t doorBits = vi.at("door_open");
            mbDoorOpen[0] = !!(doorBits & 1);
            mbDoorOpen[1] = !!(doorBits & 2);
        }
        char cmd[128];
        snprintf(cmd, sizeof(cmd), "cmd setOsdTips %d 门信号:%d,%d",
                    2, mbDoorOpen[0], mbDoorOpen[1]);
        if (mLastOsdCmd != cmd) {
            mLastOsdCmd = cmd;
            if (LogCallProxyCmd::sendReq("media", cmd)) {
            }
        }
    } catch (json::exception e) {
        loge("topic:%s json : %s!\n", topic, data);
    }

}