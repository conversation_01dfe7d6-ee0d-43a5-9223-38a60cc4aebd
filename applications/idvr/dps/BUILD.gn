
executable("dps") {
    sources = [
        "main.cpp",
        "dps.cpp",
        "dps_hdlr.cpp",
        "vehicle_info.cpp",
    ]

    include_dirs = [
        ".",
        "./lib",
        "//third_party/n<PERSON><PERSON>_json",
        "//applications/idvr/include/idvr",
        "//applications/vendor/8838_sdk4/include",
    ]

    deps = [
        "//foundation/base/core/mystd",
        "//foundation/base/core/filelog",
        "//foundation/base/core/json-c-util:jsonUtil",
        "//foundation/base/service/tts/libtts",
        "//foundation/communication/message",
        "//foundation/communication/ringbuf",
        "//foundation/communication/ipcAgent",
        "//foundation/communication/property",
        "//foundation/communication/socketcmd",
        "//foundation/communication/libflow:flowWrap",
        "//third_party/libyuv",
    ]

    libs = [
    ]

    defines = [
        "LOG_TAG_STR=${target_name}",
    ]

    thirdPartyAlgoLib = rebase_path("//applications/idvr/dps/lib")
    sigmastar_sdk_dir = rebase_path("//applications/vendor/8838_sdk4/lib/share/")
    ldflags = [
        "-L${thirdPartyAlgoLib}",
        "-L${sigmastar_sdk_dir}",
        "-lcam_os_wrapper",
        "-lmi_sys",
        "-ldl",
        "-lalg",
        "-O0",
        "-g3",
    ]

    cflags_cc = [
        "-Wno-psabi",
        "-Wno-unused-parameter",
        "-fPIC",
    ]
}
