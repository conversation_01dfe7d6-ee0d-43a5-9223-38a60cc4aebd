
executable("frame_extrocter") {
    sources = [
        "main.cpp",
    ]

    include_dirs = [
        ".",
        "//applications/idvr/include/idvr",
    ]

    deps = [
        "//foundation/base/core/filelog",
        "//foundation/base/core/mystd",
        "//foundation/base/core/json-c-util:jsonUtil",
        "//foundation/communication/socketcmd",
    ]

    libs = [
    ]

    defines = [
        "LOG_TAG_STR=${target_name}",
    ]

    ldflags = [
        "-ldl",
    ]

    cflags_cc = [
        "-Wno-psabi",
        "-Wno-unused-parameter",
    ]
}
