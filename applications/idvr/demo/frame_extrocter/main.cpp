#include "CmdListener.h"
#include "mystd.h"

int32_t main(int32_t argc, char **argv)
{
    my::log::setLogTag(LOG_NAME);

    int32_t offset = 0;

    printf("usage: %s [id] [ch] [strmIdx] [w] [h] [qualityLevel] [bgnUsTimestamp] [intervalUs0,intervalUs1,...,intervalUsn,]. \n", argv[offset]);

    offset ++;

    char *id = argv[offset]; offset ++;
    int32_t chn = atoi(argv[offset]); offset ++;
    int32_t strmidx = atoi(argv[offset]); offset ++;
    int32_t w = atoi(argv[offset]); offset ++;
    int32_t h = atoi(argv[offset]); offset ++;
    int32_t q = atoi(argv[offset]); offset ++;
    int64_t bgn = atoll(argv[offset]); offset ++;
    char *invervs = argv[offset]; offset ++;

    my::string attCmd;
    attCmd.assignf("cmd frameExtract %s %d %d %d %d %d %lld %s", 
        id, chn, strmidx, w, h, q, bgn, invervs);

    logd("len %d > %s", attCmd.length(), (const char *) attCmd);
    std::vector<char> resp;
    LogCallProxyCmd::sendReqWithTimeOut("media", (const char *) attCmd, resp, 1000);
    logd("%s", resp.data());

    return 0;
}
