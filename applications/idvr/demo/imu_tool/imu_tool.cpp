#include "imu.h"

void printfData(unsigned char *buf, unsigned char len)
{
    printf("data are as follows: \r\n");
    for(int i = 0; i < len; i++) {
        printf("0x%02X ", buf[i]);
        if(!((i+1)%8)) printf("| ");
        if(!((i+1)%16)) printf("\r\n");
    }
    printf("\r\n");
}

unsigned int strToNum(char * str)
{
    char * p = str;
    if (('0' == *p) && ('x' == *(p++))) {
        // 按十六进制规则转换
    } else {
        // 按十进制规则转换
    }
    return 0;
}

static int imu_cmd_get_wai(int fd, void * argv)
{
    unsigned char wai = 0;
    int ret = 0;
    IMU_INFO("who am i?");
    ret = ioctl(fd, IMU_IOCTL_CMD_GET_WHO_AM_I, &wai);
    if(ret) {
        IMU_ERR("read imu register failure.");
    } else {
        IMU_INFO("i am 0x%X.", wai);
    }
    return ret;
}

static void usage_set_odr(void)
{
    IMU_INFO("%6s %s",  " |-1 ", "1.6 Hz");
    IMU_INFO("%6s %s",  " |-2 ", "12.5 Hz");
    IMU_INFO("%6s %s",  " |-3 ", "26 Hz");
    IMU_INFO("%6s %s",  " |-4 ", "52 Hz");
    IMU_INFO("%6s %s",  " |-5 ", "104 Hz");
    IMU_INFO("%6s %s",  " |-6 ", "208 Hz");
    IMU_INFO("%6s %s",  " |-7 ", "416 Hz");
    IMU_INFO("%6s %s",  " |-8 ", "833 Hz");
    IMU_INFO("%6s %s",  " |-9 ", "1.66 kHz");
    IMU_INFO("%6s %s",  " |-10", "3.33 kHz");
    IMU_INFO("%6s %s",  " |-11", "6.66 kHz");
    return ;
}

static int imu_cmd_set_sample_rate(int fd, void * argv)
{
    int ret = 0;
    enum imu_output_data_rate odr = IMU_ODR_INVALID;

    IMU_DBG("imu_cmd_set_sample_rate");

    if (NULL == argv) {
        IMU_ERR("pls input the imu odr para");
        usage_set_odr();
        return ret;
    }

    odr = (imu_output_data_rate)atoi((const char *)argv);

    if (odr >= IMU_ODR_MAX) {
        IMU_ERR("imu odr para is invalid");
        return -1;
    }

    ret = ioctl(fd, IMU_IOCTL_CMD_SET_SAMPLE_RATE, &odr);
    if(ret) {
        IMU_ERR("imu_cmd_set_sample_rate failure.");
    }
    return ret;
}

static int imu_cmd_get_sample_rate(int fd, void * argv)
{
    int ret = 0;
    enum imu_output_data_rate odr = IMU_ODR_INVALID;

    IMU_DBG("imu_cmd_get_sample_rate");

    ret = ioctl(fd, IMU_IOCTL_CMD_GET_SAMPLE_RATE, &odr);
    if(ret) {
        IMU_ERR("failure.");
        return ret;
    }

    switch (odr) {
        case IMU_ODR_12P5HZ: IMU_INFO("sample rate is 12.5 Hz");  break;
        case IMU_ODR_26HZ:   IMU_INFO("sample rate is 26 Hz");    break;
        case IMU_ODR_52HZ:   IMU_INFO("sample rate is 52 Hz");    break;
        case IMU_ODR_104HZ:  IMU_INFO("sample rate is 104 Hz");   break;
        case IMU_ODR_208HZ:  IMU_INFO("sample rate is 208 Hz");   break;
        case IMU_ODR_416HZ:  IMU_INFO("sample rate is 416 Hz");   break;
        case IMU_ODR_833HZ:  IMU_INFO("sample rate is 833 Hz");   break;
        case IMU_ODR_1K66HZ: IMU_INFO("sample rate is 1.66 kHz"); break;
        case IMU_ODR_3K33HZ: IMU_INFO("sample rate is 3.33 kHz"); break;
        case IMU_ODR_6K66HZ: IMU_INFO("sample rate is 6.66 kHz"); break;
        default:             IMU_INFO("sample rate is invalid");  break;
    }
    return ret;
}

static int imu_cmd_get_gyro_sensitivity(int fd, void * argv)
{
    int ret = 0;
    struct imu_arg_sensitivity arg;

    IMU_DBG("imu_cmd_get_gyro_sensitivity");

    ret = ioctl(fd, IMU_IOCTL_CMD_GET_GYRO_SENSITIVITY, &arg);
    if(ret) {
        IMU_ERR("failure.");
    }

    IMU_INFO("gyro sensitivity is %0.4f.", (float)arg.val/arg.divisor);
    return ret;
}

static int imu_cmd_get_accel_sensitivity(int fd, void * argv)
{
    int ret = 0;
    struct imu_arg_sensitivity arg;

    IMU_DBG("imu_cmd_get_accel_sensitivity");

    ret = ioctl(fd, IMU_IOCTL_CMD_GET_ACCEL_SENSITIVITY, &arg);
    if(ret) {
        IMU_ERR("failure.");
    }

    IMU_INFO("accel sensitivity is %0.4f.", (float)arg.val/arg.divisor);
    return ret;
}

const char * strAccelScale(enum imu_accel_scale ias)
{
    switch (ias) {
        case IMU_ACCEL_SCALE_2G:  return "2g";
        case IMU_ACCEL_SCALE_4G:  return "4g";
        case IMU_ACCEL_SCALE_8G:  return "8g";
        case IMU_ACCEL_SCALE_16G: return "16g";
        default:                  return "is invalid";
    }
}

static int imu_cmd_set_accel_scale(int fd, void * argv)
{
    int ret = 0;
    enum imu_accel_scale ias = IMU_ACCEL_SCALE_INVALID;

    IMU_DBG("imu_cmd_set_accel_scale");

    if (NULL == argv) {
        IMU_INFO("pls input the accel scale.");
        IMU_INFO("  |-1 - 2G");
        IMU_INFO("  |-2 - 4G");
        IMU_INFO("  |-3 - 8G");
        IMU_INFO("  |-4 - 16G");
        return ret;
    }

    ias = (enum imu_accel_scale)atoi((char *)argv);
    if (ias == IMU_ACCEL_SCALE_INVALID || ias >= IMU_ACCEL_SCALE_MAX) {
        IMU_INFO("pls input the accel scale.");
        IMU_INFO("  |-1 - 2G");
        IMU_INFO("  |-2 - 4G");
        IMU_INFO("  |-3 - 8G");
        IMU_INFO("  |-4 - 16G");
        return ret;
    }

    IMU_INFO("set accel scale %s.", strAccelScale(ias));

    ret = ioctl(fd, IMU_IOCTL_CMD_SET_ACCEL_SCALE, &ias);
    if(ret) {
        IMU_ERR("failure.");
    }
    return ret;
}

static int imu_cmd_get_accel_scale(int fd, void * argv)
{
    int ret = 0;
    enum imu_accel_scale ias = IMU_ACCEL_SCALE_INVALID;

    IMU_DBG("imu_cmd_get_accel_scale");

    ret = ioctl(fd, IMU_IOCTL_CMD_GET_ACCEL_SCALE, &ias);
    if(ret) {
        IMU_ERR("failure.");
    }

    IMU_INFO("get accel scale %s.", strAccelScale(ias));
    return ret;
}

const char * strGyroScale(enum imu_gyro_scale igs)
{
    switch (igs) {
        case IMU_GYRO_SCALE_125:  return "125 dps";
        case IMU_GYRO_SCALE_245:  return "245 dps";
        case IMU_GYRO_SCALE_500:  return "500 dps";
        case IMU_GYRO_SCALE_1000: return "1000 dps";
        case IMU_GYRO_SCALE_2000: return "2000 dps";
        default:                  return "is invalid";
    }
}

static int imu_cmd_set_gyro_scale(int fd, void * argv)
{
    int ret = 0;
    enum imu_gyro_scale igs = IMU_GYRO_SCALE_INVALID;

    IMU_DBG("imu_cmd_set_gyro_scale");
    if (NULL == argv) {
        IMU_INFO("pls input the accel scale.");
        IMU_INFO("  |-1 - 125 dps");
        IMU_INFO("  |-2 - 245 dps");
        IMU_INFO("  |-3 - 500 dps");
        IMU_INFO("  |-4 - 1000 dps");
        IMU_INFO("  |-5 - 2000 dps");
        return ret;
    }

    igs = (enum imu_gyro_scale)atoi((char *)argv);
    if (igs == IMU_GYRO_SCALE_INVALID || igs >= IMU_GYRO_SCALE_MAX) {
        IMU_INFO("pls input the gyro scale.");
        IMU_INFO("  |-1 - 125 dps");
        IMU_INFO("  |-2 - 245 dps");
        IMU_INFO("  |-3 - 500 dps");
        IMU_INFO("  |-4 - 1000 dps");
        IMU_INFO("  |-5 - 2000 dps");
        return ret;
    }

    IMU_INFO("set gyro scale %s.", strGyroScale(igs));

    ret = ioctl(fd, IMU_IOCTL_CMD_SET_GYRO_SCALE, &igs);
    if(ret) {
        IMU_ERR("failure.");
    }
    return ret;
}

static int imu_cmd_get_gyro_scale(int fd, void * argv)
{
    int ret = 0;
    enum imu_gyro_scale igs = IMU_GYRO_SCALE_INVALID;

    IMU_DBG("imu_cmd_get_gyro_scale");

    ret = ioctl(fd, IMU_IOCTL_CMD_GET_GYRO_SCALE, &igs);
    if(ret) {
        IMU_ERR("failure.");
    }

    IMU_INFO("get gyro scale %s.", strGyroScale(igs));
    return ret;
}

static int imu_cmd_read_gyro_xyz(int fd, void * argv)
{
    int ret = 0;
    struct imu_arg_gyro_xyz igxyz;

    IMU_DBG("imu_cmd_read_gyro_xyz");

    memset(&igxyz, 0, sizeof(struct imu_arg_gyro_xyz));
    ret = ioctl(fd, IMU_IOCTL_CMD_READ_GYRO_XYZ, &igxyz);
    if(ret) {
        IMU_ERR("failure.");
    }

    IMU_INFO("get gyro x:%d y:%d z:%d.", igxyz.x, igxyz.y, igxyz.z);
    return ret;
}

static int imu_cmd_read_accel_xyz(int fd, void * argv)
{
    int ret = 0;
    struct imu_arg_accel_xyz iaxyz;

    IMU_DBG("imu_cmd_read_accel_xyz");

    memset(&iaxyz, 0, sizeof(struct imu_arg_accel_xyz));
    ret = ioctl(fd, IMU_IOCTL_CMD_READ_ACCEL_XYZ, &iaxyz);
    if(ret) {
        IMU_ERR("failure.");
    }

    IMU_INFO("get accel x:%d y:%d z:%d.", iaxyz.x, iaxyz.y, iaxyz.z);
    return ret;
}

static int imu_cmd_read_temp(int fd, void * argv)
{
    int ret = 0;
    struct imu_arg_temp itemp;

    IMU_DBG("imu_cmd_read_temp");

    memset(&itemp, 0, sizeof(struct imu_arg_temp));
    ret = ioctl(fd, IMU_IOCTL_CMD_READ_TEMP, &itemp);
    if(ret) {
        IMU_ERR("failure.");
    }

    IMU_INFO("temp is %0.2f℃(%d-0x%04X)", 25 + (itemp.temp / 256.0f), itemp.temp, itemp.temp);
    return ret;
}

static int imu_cmd_reset_fifo(int fd, void * argv)
{
    int ret = 0;

    IMU_DBG("imu_cmd_reset_fifo");

    ret = ioctl(fd, IMU_IOCTL_CMD_RESET_FIFO, NULL);
    if(ret) {
        IMU_ERR("failure.");
    }
    return ret;
}

static int imu_cmd_set_fifo_threshold(int fd, void * argv)
{
    int ret = 0;

    IMU_DBG("imu_cmd_set_fifo_threshold");

    ret = ioctl(fd, IMU_IOCTL_CMD_SET_FIFO_THRESHOLD, NULL);
    if(ret) {
        IMU_ERR("failure.");
    }
    return ret;
}

static int imu_cmd_get_fifo_threshold(int fd, void * argv)
{
    int ret = 0;

    IMU_DBG("imu_cmd_get_fifo_threshold");

    ret = ioctl(fd, IMU_IOCTL_CMD_GET_FIFO_THRESHOLD, NULL);
    if(ret) {
        IMU_ERR("failure.");
    }
    return ret;
}

static int imu_cmd_read_fifo_cnt(int fd, void * argv)
{
    int ret = 0;

    IMU_DBG("imu_cmd_read_fifo_cnt");

    ret = ioctl(fd, IMU_IOCTL_CMD_READ_FIFO_CNT, NULL);
    if(ret) {
        IMU_ERR("failure.");
    }
    return ret;
}

static int imu_cmd_read_fifo_data(int fd, void * argv)
{
    int ret = 0;

    IMU_DBG("imu_cmd_read_fifo_data");

    ret = ioctl(fd, IMU_IOCTL_CMD_READ_FIFO_DATA, NULL);
    if(ret) {
        IMU_ERR("failure.");
    }
    return ret;
}

static int imu_cmd_init(int fd, void * argv)
{
    int ret = 0;
    unsigned char mode = 0;

    IMU_DBG("imu_cmd_init");
    if (NULL == argv) {
        IMU_INFO("pls input the init mode.");
        IMU_INFO("  |-0 - 6 axis + timestamp x 80");
        IMU_INFO("  |-1 - 6 axis");
        IMU_INFO("  |-2 - debug");
        IMU_INFO("  |-3 - 6 axis + timestamp x 10");
        return ret;
    }

    mode = (unsigned char)atoi((char *)argv);

    IMU_INFO("mode is %d.", mode);

    ret = ioctl(fd, IMU_IOCTL_CMD_INIT, &mode);
    if(ret) {
        IMU_ERR("failure.");
    }
    return ret;
}

static int imu_cmd_read_sensor_data(int fd, void * argv)
{
    int ret = 0;
    struct imu_data_package_80 datap;

    IMU_DBG("imu_cmd_read_sensor_data");

    memset(&datap, 0, sizeof(struct imu_data_package_80));
    ret = ioctl(fd, IMU_IOCTL_CMD_READ_SENSOR_DATA, &datap);
    if(ret) {
        IMU_ERR("failure.");
    }

    IMU_INFO("sensor data cnt: %d", datap.cnt);
    for (int i=0; i<datap.cnt; i++) {
        printf("%02d: gyro x:%4d y:%4d z:%4d accel x:%4d y:%4d z:%4d timestamp:%12lld temp:%0.2f℃\n", i,
            datap.idata[i].gyro[0], datap.idata[i].gyro[1], datap.idata[i].gyro[2],
            datap.idata[i].accel[0], datap.idata[i].accel[1], datap.idata[i].accel[2],
            datap.idata[i].timestamp, 25 + (datap.idata[i].temp / 256.0f));
    }
    return ret;
}

static int imu_cmd_read_6_axis_timestamp_10_data(int fd, void * argv)
{
    int ret = 0;
    struct imu_data_package_10 datap;

    IMU_DBG("imu_cmd_read_6_axis_timestamp_10_data");

    memset(&datap, 0, sizeof(struct imu_data_package_10));
    ret = ioctl(fd, IMU_IOCTL_CMD_READ_6_AXIS_TIMESTAMP_10_DATA, &datap);
    if(ret) {
        IMU_ERR("failure.");
    }

    IMU_INFO("sensor data cnt: %d", datap.cnt);
    for (int i=0; i<datap.cnt; i++) {
        printf("%02d: gyro x:%4d y:%4d z:%4d accel x:%4d y:%4d z:%4d timestamp:%12lld temp:%0.2f℃\n", i,
            datap.idata[i].gyro[0], datap.idata[i].gyro[1], datap.idata[i].gyro[2],
            datap.idata[i].accel[0], datap.idata[i].accel[1], datap.idata[i].accel[2],
            datap.idata[i].timestamp, 25 + (datap.idata[i].temp / 256.0f));
    }
    return ret;
}

static int imu_cmd_read_a_set_of_data(int fd, void * argv)
{
    int ret = 0;
    struct GsensorData gdata;

    IMU_DBG("imu_cmd_read_sensor_datas");

    memset(&gdata, 0, sizeof(struct GsensorData));
    ret = ioctl(fd, IMU_IOCTL_CMD_READ_A_SET_OF_DATA, &gdata);
    if(ret) {
        IMU_ERR("failure.");
    }

    printf("gyro x:%d y:%d z:%d accel x:%d y:%d z:%d temp:%0.2f℃ timestamp:%ld \n",
        gdata.gyro[0], gdata.gyro[1], gdata.gyro[2], gdata.accel[0], gdata.accel[1], gdata.accel[2],
        25 + (gdata.temp / 256.0f), gdata.timestamp);
    return ret;
}

static int imu_cmd_read_6_axis_data(int fd, void * argv)
{
    int ret = 0;
    struct six_axis_data_package datap;

    IMU_DBG("imu_cmd_read_6_axis_data");

    memset(&datap, 0, sizeof(struct six_axis_data_package));
    ret = ioctl(fd, IMU_IOCTL_CMD_READ_6_AXIS_DATA, &datap);
    if(ret) {
        IMU_ERR("failure.");
    }

    IMU_DBG("6 axis data cnt: %d", datap.cnt);
    for (int i=0; i<datap.cnt; i++) {
        printf("%3d: gyro x:%d y:%d z:%d accel x:%d y:%d z:%d\n", i,
            datap.idata[i].gyro[0], datap.idata[i].gyro[1], datap.idata[i].gyro[2],
            datap.idata[i].accel[0], datap.idata[i].accel[1], datap.idata[i].accel[2]);
    }
    return ret;
}

static int imu_cmd_read_data_dbg(int fd, void * argv)
{
    int ret = 0;
    struct imu_data_package_80 datap;

    IMU_DBG("imu_cmd_read_data_dbg");

    memset(&datap, 0, sizeof(struct imu_data_package_80));
    ret = ioctl(fd, IMU_IOCTL_CMD_READ_DATA_DEBUG, &datap);
    if(ret) {
        IMU_ERR("failure.");
    }
#if 1
    IMU_DBG("sensor data cnt: %d", datap.cnt);
    for (int i=0; i<datap.cnt; i++) {
        printf("%02d: gyro x:%4d y:%4d z:%4d accel x:%4d y:%4d z:%4d timestamp:%8lld temp:%0.2f℃\n", i,
            datap.idata[i].gyro[0], datap.idata[i].gyro[1], datap.idata[i].gyro[2],
            datap.idata[i].accel[0], datap.idata[i].accel[1], datap.idata[i].accel[2],
            datap.idata[i].timestamp, 25 + (datap.idata[i].temp / 256.0f));
    }
#endif
    return ret;
}

static int imu_cmd_deinit(int fd, void * argv)
{
    int ret = 0;

    IMU_DBG("imu_cmd_deinit");

    ret = ioctl(fd, IMU_IOCTL_CMD_DEINIT, NULL);
    if(ret) {
        IMU_ERR("failure.");
    }
    return ret;
}

static int imu_cmd_software_reset(int fd, void * argv)
{
    int ret = 0;

    IMU_DBG("imu_cmd_software_reset");

    ret = ioctl(fd, IMU_IOCTL_CMD_SOFTWARE_RESET, NULL);
    if(ret) {
        IMU_ERR("failure.");
    }
    return ret;
}

enum imu_cmd_op{
    IMU_CMD_GET_WAI,
    IMU_CMD_SET_SAMPLE_RATE,
    IMU_CMD_GET_SAMPLE_RATE,
    IMU_CMD_GET_GYRO_SENSITIVITY,
    IMU_CMD_GET_ACCEL_SENSITIVITY,
    IMU_CMD_SET_ACCEL_SCALE,
    IMU_CMD_GET_ACCEL_SCALE,
    IMU_CMD_SET_GYRO_SCALE,
    IMU_CMD_GET_GYRO_SCALE,
    IMU_CMD_READ_GYRO_XYZ,
    IMU_CMD_READ_ACCEL_XYZ,
    IMU_CMD_READ_TEMP,
    IMU_CMD_RESET_FIFO,
    IMU_CMD_SET_FIFO_THRESHOLD,
    IMU_CMD_GET_FIFO_THRESHOLD,
    IMU_CMD_GET_FIFO_CNT,
    IMU_CMD_READ_FIFO_DATA,
    IMU_CMD_INIT,
    IMU_CMD_READ_SENSOR_DATA,
    IMU_CMD_READ_A_SET_OF_DATA,
    IMU_CMD_READ_6_AXIS_DATA,
    IMU_CMD_READ_DATA_DBG,
    IMU_CMD_DEINIT,
    IMU_CMD_SOFTWARE_RESET,
    IMU_CMD_READ_6_AXIS_TIMESTAMP_10_DATA,
    IMU_CMD_COUNT,
};

const char* strCmdOp(enum imu_cmd_op cmd)
{
    switch (cmd) {
        case IMU_CMD_GET_WAI:                       return "get who am i";
        case IMU_CMD_SET_SAMPLE_RATE:               return "set sample rate";
        case IMU_CMD_GET_SAMPLE_RATE:               return "get sample rate";
        case IMU_CMD_GET_GYRO_SENSITIVITY:          return "get gyro sensitivity";
        case IMU_CMD_GET_ACCEL_SENSITIVITY:         return "get accel sensitivity";
        case IMU_CMD_SET_ACCEL_SCALE:               return "set accel scale";
        case IMU_CMD_GET_ACCEL_SCALE:               return "get accel scale";
        case IMU_CMD_SET_GYRO_SCALE:                return "set gyro scale";
        case IMU_CMD_GET_GYRO_SCALE:                return "get gyro scale";
        case IMU_CMD_READ_GYRO_XYZ:                 return "read gyro xyz";
        case IMU_CMD_READ_ACCEL_XYZ:                return "read accel xyz";
        case IMU_CMD_READ_TEMP:                     return "read temp";
        case IMU_CMD_RESET_FIFO:                    return "reset fifo";
        case IMU_CMD_SET_FIFO_THRESHOLD:            return "set fifo threshold";
        case IMU_CMD_GET_FIFO_THRESHOLD:            return "get fifo threshold";
        case IMU_CMD_GET_FIFO_CNT:                  return "read fifo cnt";
        case IMU_CMD_READ_FIFO_DATA:                return "read fifo data";
        case IMU_CMD_INIT:                          return "init";
        case IMU_CMD_READ_SENSOR_DATA:              return "read sensor data";
        case IMU_CMD_READ_A_SET_OF_DATA:            return "read a set of data";
        case IMU_CMD_READ_6_AXIS_DATA:              return "read 6 axis data";
        case IMU_CMD_READ_DATA_DBG:                 return "read data debug";
        case IMU_CMD_DEINIT:                        return "deinit";
        case IMU_CMD_SOFTWARE_RESET:                return "software reset";
        case IMU_CMD_READ_6_AXIS_TIMESTAMP_10_DATA: return "read 6axis+timesmtap x10 data";
        default:                                    return "unknown cmd";
    }
}

static int (*imu_ioctl_ops[IMU_CMD_COUNT])(int, void *) = {
    imu_cmd_get_wai,
    imu_cmd_set_sample_rate,
    imu_cmd_get_sample_rate,
    imu_cmd_get_gyro_sensitivity,
    imu_cmd_get_accel_sensitivity,
    imu_cmd_set_accel_scale,
    imu_cmd_get_accel_scale,
    imu_cmd_set_gyro_scale,
    imu_cmd_get_gyro_scale,
    imu_cmd_read_gyro_xyz,
    imu_cmd_read_accel_xyz,
    imu_cmd_read_temp,
    imu_cmd_reset_fifo,
    imu_cmd_set_fifo_threshold,
    imu_cmd_get_fifo_threshold,
    imu_cmd_read_fifo_cnt,
    imu_cmd_read_fifo_data,
    imu_cmd_init,
    imu_cmd_read_sensor_data,
    imu_cmd_read_a_set_of_data,
    imu_cmd_read_6_axis_data,
    imu_cmd_read_data_dbg,
    imu_cmd_deinit,
    imu_cmd_software_reset,
    imu_cmd_read_6_axis_timestamp_10_data,
};

static void usage(const char *exe_name)
{
    IMU_INFO("Usage:%s <switches> [option]\n", exe_name);

    IMU_INFO("-H/-h   imu tool help");
    IMU_INFO("-R/-r   imu tool read reg, <reg> [num:1]");
    IMU_INFO("-W/-w   imu tool write reg, <reg> <1 byte data>");
    IMU_INFO("-I/-i   imu tool ioctl, <cmd> <custom para>");
    for (int i=0; i<IMU_CMD_COUNT; i++) {
        IMU_INFO("  |-%02d    %s", i, strCmdOp((enum imu_cmd_op)i));
    }
    return;
}

#define GETOPT_OPT_STR  "HhR:r:W:w:I:i:"
int main(int argc, char *argv[])
{
    int opt;
    int ret = 0;
    IMUHandle imuhandle;

    IMU_DBG("imu tool");

    ret = imuhandle.Init();
    if(ret < 0)
    {
        IMU_ERR("Imu_Init error!,ret=%d", ret);
	    return ret;
    }

    if (1 == argc) {
        usage(argv[0]);
        return 0;
    }

    while ((opt = getopt(argc, argv, GETOPT_OPT_STR)) != -1) {
        if (2 == argc) {
            usage(argv[0]);
            return 0;
        }
        switch (opt) {
            case 'R':
            case 'r':
                {
                    struct RegOpData op;
                    IMU_DBG("Read Imu register...");
                    op.reg = atoi(argv[2]) & 0x7F;
                    op.len = (argc == 3) ? 1 : (atoi(argv[3]) & 0x7F);

                    ret = ioctl(imuhandle.mFd, IMU_IOCTL_CMD_READ_REGISTER, &op);
                    if (ret) {
                        IMU_ERR("Read Imu register failure.");
                    } else {
                        printfData(op.buf, op.len);
                    }
                }
                break;

            case 'W':
            case 'w':
                {
                    if (3 == argc) {
                        IMU_INFO("pls input a 1-byte data.");
                        return 0;
                    }
                    struct RegOpData op;
                    IMU_DBG("Write Imu register...");
                    op.reg = atoi(argv[2]) & 0x7F;
                    op.len = 1;
                    op.buf[0] = atoi(argv[3]) & 0xFF;

                    ret = ioctl(imuhandle.mFd, IMU_IOCTL_CMD_WRITE_REGISTER, &op);
                    if (ret) {
                        IMU_ERR("Write Imu register failure.");
                    } else {
                        printfData(op.buf, op.len);
                    }
                }
                break;

            case 'I':
            case 'i':
                {
                    unsigned int cmd = atoi(argv[2]);
                    int ret = 0;
                    if (cmd >= IMU_CMD_COUNT) {
                        IMU_ERR("command is invalid.");
                        return 0;
                    }
                    IMU_DBG("Ioctl Imu cmd op...");
                    ret = imu_ioctl_ops[cmd](imuhandle.mFd, argv[3]);
                    if (ret) {
                        IMU_ERR("op is return err(%d).", ret);
                    }
                }
                break;

            case 'H':
            case 'h':
            default:
                usage(argv[0]);
                break;
        }
    }
    return ret;
}

