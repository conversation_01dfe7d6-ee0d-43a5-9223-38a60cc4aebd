#ifndef __IMU_H__
#define __IMU_H__

#include "mystd.h"

#define VERION_MAJOR                4
#define VERION_MINOR                18

//#define IMU_DEBUG_ON

#define IMU_DEVICE_NAME     "/dev/imu"

#define __FILENAME__                (strrchr("/" __FILE__, '/') + 1)

#define IMU_INFO(fmt, args...) \
    do { \
        printf("[%s-%s:%d] V%d.%d-info: " fmt "\n", IMU_DEVICE_NAME, __FILENAME__, __LINE__, VERION_MAJOR, VERION_MINOR, ##args); \
    } while(0)

#define IMU_ERR(fmt, args...) \
    do { \
        printf("[%s-%s:%d] V%d.%d-error: " fmt "\n", IMU_DEVICE_NAME, __FILENAME__, __LINE__, VERION_MAJOR, VERION_MINOR, ##args); \
    } while(0)

#ifdef IMU_DEBUG_ON
#define IMU_DBG(fmt, args...) \
    do { \
        printf("[%s-%s:%d] V%d.%d-debug: " fmt "\n", IMU_DEVICE_NAME, __FILENAME__, __LINE__, VERION_MAJOR, VERION_MINOR, ##args); \
    } while(0)
#endif

#ifndef IMU_DBG
#define IMU_DBG(fmt, args...) {}
#endif

#define IMU_IOCTL_MAGIC                         'I'
#define IMU_IOCTL_CMD_READ_REGISTER                 _IO(IMU_IOCTL_MAGIC, 0x01)
#define IMU_IOCTL_CMD_WRITE_REGISTER                _IO(IMU_IOCTL_MAGIC, 0x02)
#define IMU_IOCTL_CMD_GET_WHO_AM_I                  _IO(IMU_IOCTL_MAGIC, 0x03)
#define IMU_IOCTL_CMD_SET_SAMPLE_RATE               _IO(IMU_IOCTL_MAGIC, 0x04)
#define IMU_IOCTL_CMD_GET_SAMPLE_RATE               _IO(IMU_IOCTL_MAGIC, 0x05)
#define IMU_IOCTL_CMD_GET_GYRO_SENSITIVITY          _IO(IMU_IOCTL_MAGIC, 0x06)
#define IMU_IOCTL_CMD_GET_ACCEL_SENSITIVITY         _IO(IMU_IOCTL_MAGIC, 0x07)
#define IMU_IOCTL_CMD_SET_ACCEL_SCALE               _IO(IMU_IOCTL_MAGIC, 0x08)
#define IMU_IOCTL_CMD_GET_ACCEL_SCALE               _IO(IMU_IOCTL_MAGIC, 0x09)
#define IMU_IOCTL_CMD_SET_GYRO_SCALE                _IO(IMU_IOCTL_MAGIC, 0x0A)
#define IMU_IOCTL_CMD_GET_GYRO_SCALE                _IO(IMU_IOCTL_MAGIC, 0x0B)
#define IMU_IOCTL_CMD_READ_GYRO_XYZ                 _IO(IMU_IOCTL_MAGIC, 0x0C)
#define IMU_IOCTL_CMD_READ_ACCEL_XYZ                _IO(IMU_IOCTL_MAGIC, 0x0D)
#define IMU_IOCTL_CMD_READ_TEMP                     _IO(IMU_IOCTL_MAGIC, 0x0E)
#define IMU_IOCTL_CMD_RESET_FIFO                    _IO(IMU_IOCTL_MAGIC, 0x0F)
#define IMU_IOCTL_CMD_SET_FIFO_THRESHOLD            _IO(IMU_IOCTL_MAGIC, 0x10)
#define IMU_IOCTL_CMD_GET_FIFO_THRESHOLD            _IO(IMU_IOCTL_MAGIC, 0x11)
#define IMU_IOCTL_CMD_READ_FIFO_CNT                 _IO(IMU_IOCTL_MAGIC, 0x12)
#define IMU_IOCTL_CMD_READ_FIFO_DATA                _IO(IMU_IOCTL_MAGIC, 0x13)
#define IMU_IOCTL_CMD_INIT                          _IO(IMU_IOCTL_MAGIC, 0x14)
#define IMU_IOCTL_CMD_READ_SENSOR_DATA              _IO(IMU_IOCTL_MAGIC, 0x15)
#define IMU_IOCTL_CMD_READ_A_SET_OF_DATA            _IO(IMU_IOCTL_MAGIC, 0x16)
#define IMU_IOCTL_CMD_READ_6_AXIS_DATA              _IO(IMU_IOCTL_MAGIC, 0x17)
#define IMU_IOCTL_CMD_READ_DATA_DEBUG               _IO(IMU_IOCTL_MAGIC, 0x18)
#define IMU_IOCTL_CMD_DEINIT                        _IO(IMU_IOCTL_MAGIC, 0x19)
#define IMU_IOCTL_CMD_SOFTWARE_RESET                _IO(IMU_IOCTL_MAGIC, 0x1A)
#define IMU_IOCTL_CMD_READ_6_AXIS_TIMESTAMP_10_DATA _IO(IMU_IOCTL_MAGIC, 0x1B)

#define IMU_IOCTL_CMD_COUNT                     0x1C


struct RegOpData
{
    unsigned char reg;
    unsigned char len;
    unsigned char buf[127];
};

struct imu_arg_gyro_xyz {
    short x;
    short y;
    short z;
};

struct imu_arg_accel_xyz {
    short x;
    short y;
    short z;
};

struct imu_arg_temp {
    short temp;
};

struct imu_arg_sensitivity {
    unsigned short val;
    unsigned short divisor;
};

struct GsensorData {
    short gyro[3];
    short accel[3];
    short temp;
    uint64_t timestamp;
};

struct imu_data_package_80 {
    struct GsensorData idata[80];
    unsigned char cnt;
};

struct imu_data_package_10 {
    struct GsensorData idata[10];
    unsigned char cnt;
};

struct sixAxisData
{
    short gyro[3];
    short accel[3];
};

struct six_axis_data_package {
    struct sixAxisData idata[160];
    unsigned char cnt;
};

// Also known as: sample rate
enum imu_output_data_rate {
    IMU_ODR_INVALID,
    IMU_ODR_1P6HZ,
    IMU_ODR_12P5HZ,
    IMU_ODR_26HZ,
    IMU_ODR_52HZ,
    IMU_ODR_104HZ,
    IMU_ODR_208HZ,
    IMU_ODR_416HZ,
    IMU_ODR_833HZ,
    IMU_ODR_1K66HZ,
    IMU_ODR_3K33HZ,
    IMU_ODR_6K66HZ,
    IMU_ODR_MAX,
};

// Also known as: gyro range
enum imu_gyro_scale {
    IMU_GYRO_SCALE_INVALID,
    IMU_GYRO_SCALE_125,
    IMU_GYRO_SCALE_245,
    IMU_GYRO_SCALE_500,
    IMU_GYRO_SCALE_1000,
    IMU_GYRO_SCALE_2000,
    IMU_GYRO_SCALE_MAX,
};

// Also known as: accel range
enum imu_accel_scale {
    IMU_ACCEL_SCALE_INVALID,
    IMU_ACCEL_SCALE_2G,
    IMU_ACCEL_SCALE_4G,
    IMU_ACCEL_SCALE_8G,
    IMU_ACCEL_SCALE_16G,
    IMU_ACCEL_SCALE_MAX,
};

class IMUHandle
{
    public:
        IMUHandle()
        {
            mFd = 0;
        }
        ~IMUHandle()
        {
            close(mFd);
        }

        int Init()
        {
            int ret = 0;
            mFd = open(IMU_DEVICE_NAME, O_RDONLY);
            if (mFd < 0) {
                IMU_ERR("Error! IMUHandle : Failed to open %s, %s\n", IMU_DEVICE_NAME, strerror(errno));
                return mFd;
            }

            return ret;
        }

    public:
        int mFd;
};

#endif

