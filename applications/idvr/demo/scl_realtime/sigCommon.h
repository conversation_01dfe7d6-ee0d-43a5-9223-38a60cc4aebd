#ifndef __SIG_COMMON_H__
#define __SIG_COMMON_H__

#include "mi_sys.h"
#include "mi_scl.h"
#include "mi_vif.h"
#include "mi_sensor.h"
#include "mi_disp.h"
#include "mi_venc.h"
#include "mi_vdec.h"
#include "mi_rgn.h"
#include "mi_fb.h"
#include "mi_ai.h"
#include "mi_ao.h"

#include "mi_sensor_datatype.h"
#include "mi_vif_datatype.h"
#include "mi_scl_datatype.h"
#include "mi_disp_datatype.h"
#include "mi_fb_datatype.h"

#include "mi_rgn_datatype.h"

#include "mi_sys_datatype.h"
#include "mi_venc_datatype.h"
#include "mi_vdec_datatype.h"

#include "mi_ai_datatype.h"
#include "mi_ao_datatype.h"
#include "mi_aio_datatype.h"

#include "mystd.h"

#define SIG_VIF_PER_GROUP_DEV_MAX_NUM           (4)
#define SIG_VIF_GROUP_MAX_NUM                   (4)
#define SIG_VIF_DEV_MAX_NUM                     (SIG_VIF_PER_GROUP_DEV_MAX_NUM * SIG_VIF_GROUP_MAX_NUM)

#define SIG_SENSOR_PAD_MAX_NUM                  (4)
#define SIG_SENSOR_PLANE_MAX_NUM                (4)

#define SIG_SCL_DEV_MAX_NUM                     (4)
#define SIG_SCL_CHN_MAX_NUM                     (16)
#define SIG_SCL_OUT_PORT_MAX_NUM                (6)

#define SIG_SCL_BUFF_HANDLE_MAX_NUM             (6)

#define SIG_VENC_VIDEO_CHN_MAX_NUM              (32)
#define SIG_VENC_JPEG_CHN_MAX_NUM               SIG_VENC_VIDEO_CHN_MAX_NUM

#define SIG_VDEC_VIDEO_CHN_MAX_NUM              (32)

#define SIG_DISP_DEV_MAX_NUM                    (2)
#define SIG_DISP_LAYER_MAX_NUM                  (2)
#define SIG_DISP_INPORT_MAX_NUM                 (16)

#define SIG_OSD_MAX_NUM                         (32)

#define SIG_AUDIO_REC_CHN_MAX_NUM               (4)

/// data type unsigned char, data length 1 byte
typedef unsigned char MI_U8; // 1 byte
/// data type unsigned short, data length 2 byte
typedef unsigned short MI_U16; // 2 bytes
/// data type unsigned int, data length 4 byte
typedef unsigned int MI_U32; // 4 bytes
/// data type unsigned int, data length 8 byte
typedef unsigned long long MI_U64; // 8 bytes
/// data type signed char, data length 1 byte
typedef signed char MI_S8; // 1 byte
/// data type signed short, data length 2 byte
typedef signed short MI_S16; // 2 bytes
/// data type signed int, data length 4 byte
typedef signed int MI_S32; // 4 bytes
/// data type signed int, data length 8 byte
typedef signed long long MI_S64; // 8 bytes
/// data type float, data length 4 byte
typedef float MI_FLOAT; // 4 bytes
/// data type 64bit physical address
typedef unsigned long long MI_PHY; // 8 bytes
/// data type pointer content
typedef unsigned long MI_VIRT; // 4 bytes when 32bit toolchain, 8 bytes when 64bit toolchain.

typedef unsigned char MI_BOOL;

#define PIXEL8888ALPHA(pixelval)	(((pixelval) >> 24) & 0xff)
#define PIXEL8888RED(pixelval)  	(((pixelval) >> 16) & 0xff)
#define PIXEL8888GREEN(pixelval)	(((pixelval) >> 8) & 0xff)
#define PIXEL8888BLUE(pixelval) 	((pixelval) & 0xff)

#define ARGB888_BLACK               ARGB2PIXEL8888(128, 0, 0, 0)
#define ARGB888_RED                 ARGB2PIXEL8888(128, 255, 0, 0)
#define ARGB888_GREEN               ARGB2PIXEL8888(128, 0, 255, 0)
#define ARGB888_BLUE                ARGB2PIXEL8888(128, 0, 0, 255)

#define ALIGN_UP(x, align)          (((x) + ((align) - 1)) & ~((align) - 1))
#define ALIGN_BACK(x, a)            (((x) / (a)) * (a))
#define ALIGN_FRONT(x, a)           ((((x) + (a) / 2) / (a)) * (a))

#define STCHECKRESULT(_func_)                                                   \
    do                                                                             \
    {                                                                              \
        MI_S32 s32Ret = MI_SUCCESS;                                                \
        s32Ret        = _func_;                                                    \
        if (s32Ret != MI_SUCCESS)                                                  \
        {                                                                          \
            loge("exec function (%s) failed ! err:%x", #_func_, s32Ret); \
            return s32Ret;                                                         \
        }                                                                          \
        else                                                                       \
        {                                                                          \
        }                                                                          \
    } while (0)

#define DISP_TO_SYS_CHN_ID(layerid, portid)                      \
    ({                                                           \
        int32_t chnid = 0;                                           \
        if ((layerid == 0 || layerid == 2) && portid <= 15)      \
        {                                                        \
            chnid = portid;                                      \
        }                                                        \
        else if ((layerid == 1 || layerid == 3) && portid == 0)  \
        {                                                        \
            chnid = 16;                                          \
        }                                                        \
        else                                                     \
        {                                                        \
            loge("invalid layer%d port%d", layerid, portid); \
            chnid = 0;                                           \
        }                                                        \
        chnid;                                                   \
    })

#define ExecFunc(_func_, _ret_) \
    do{ \
        MI_S32 s32Ret = MI_SUCCESS; \
        s32Ret = _func_; \
        if (s32Ret != _ret_) \
        { \
            loge("exec function (%s) failed ! error:%x", #_func_, s32Ret); \
            return s32Ret; \
        } \
        else \
        { \
            logd("exec function (%s) pass", #_func_); \
        } \
    } while(0)

typedef struct SigSysBindInfo
{
    MI_SYS_ChnPort_t    stSrcChnPort;
    MI_SYS_ChnPort_t    stDstChnPort;
    MI_U32              u32SrcFrmrate;
    MI_U32              u32DstFrmrate;
    MI_SYS_BindType_e   eBindType;
    MI_U32              u32BindParam;
} sigSysBindInfo_t;

typedef enum SigFrameDataType
{
    E_SIG_FRAME_DATA_TYPE_UNKOWN = -100,
    E_SIG_FRAME_DATA_TYPE_H264_P = E_MI_VENC_H264E_NALU_PSLICE,
    E_SIG_FRAME_DATA_TYPE_H264_I = E_MI_VENC_H264E_NALU_ISLICE,
    E_SIG_FRAME_DATA_TYPE_H265_P = E_MI_VENC_H265E_NALU_PSLICE,
    E_SIG_FRAME_DATA_TYPE_H265_I = E_MI_VENC_H265E_NALU_ISLICE,
    E_SIG_FRAME_DATA_TYPE_AUDIO = 200,
} sigFrameDataType_t;

typedef enum SigVencModType
{
    E_SIG_VENC_MOD_TYPE_UNKOWN = -100,
    E_SIG_VENC_MOD_TYPE_H264 = E_MI_VENC_MODTYPE_H264E,
    E_SIG_VENC_MOD_TYPE_H265 = E_MI_VENC_MODTYPE_H265E,
    E_SIG_VENC_MOD_TYPE_JPEG = E_MI_VENC_MODTYPE_JPEGE,
} sigVencModType_t;

MI_S32 sigSysModuleInit();
MI_S32 sigSysModuleUnInit();

MI_S32 sigSysBind(sigSysBindInfo_t *pstBindInfo);
MI_S32 sigSysUnBind(sigSysBindInfo_t *pstBindInfo);

MI_SNR_PADID getSnrPadIdByGroupId(MI_VIF_GROUP groupId);
MI_VIF_DEV getVifDevIdBySnrPadId(MI_SNR_PADID snrPadId, MI_U32 planId);
MI_VIF_GROUP getVifGroupIdByVifDevId(MI_VIF_DEV vifDevId);
MI_U32 getPlanIdByVifDevId(MI_VIF_DEV vifDevId);
MI_SCL_DEV getSclDevIdByVifDevId(MI_VIF_DEV vifDevId);

MI_S32 sigCheckFd(const MI_S32 *fds, MI_S32 fdCount, MI_BOOL *status, int32_t timeOutMs = 100);

#endif
