#include "sigCommon.h"

MI_S32 sigSysModuleInit()
{
    MI_SYS_Version_t miVersion;

    STCHECKRESULT(MI_SYS_Init(0));

    memset(&miVersion, 0x0, sizeof(MI_SYS_Version_t));
    STCHECKRESULT(MI_SYS_GetVersion(0, &miVersion));
    logd("u8Version:%s", miVersion.u8Version);

    // MI_U64 pts = 0;
    // STCHECKRESULT(MI_SYS_GetCurPts(0, &pts));
    // STCHECKRESULT(MI_SYS_InitPtsBase(0, pts));
    // STCHECKRESULT(MI_SYS_SyncPts(0, pts));

    return MI_SUCCESS;
}

MI_S32 sigSysModuleUnInit()
{
    STCHECKRESULT(MI_SYS_Exit(0));

    return MI_SUCCESS;
}

MI_S32 sigSysBind(sigSysBindInfo_t *pstBindInfo)
{
    ExecFunc(MI_SYS_BindChnPort2(0, &pstBindInfo->stSrcChnPort, &pstBindInfo->stDstChnPort,
        pstBindInfo->u32SrcFrmrate, pstBindInfo->u32DstFrmrate, pstBindInfo->eBindType, pstBindInfo->u32BindParam),
        MI_SUCCESS);
    logd("------------");
    logd("src(%d-%d-%d-%d) dst(%d-%d-%d-%d)  %d...", pstBindInfo->stSrcChnPort.eModId, pstBindInfo->stSrcChnPort.u32DevId,
        pstBindInfo->stSrcChnPort.u32ChnId, pstBindInfo->stSrcChnPort.u32PortId,
        pstBindInfo->stDstChnPort.eModId, pstBindInfo->stDstChnPort.u32DevId, pstBindInfo->stDstChnPort.u32ChnId,
        pstBindInfo->stDstChnPort.u32PortId, pstBindInfo->eBindType);
    logd("------------");
    return MI_SUCCESS;
}

MI_S32 sigSysUnBind(sigSysBindInfo_t *pstBindInfo)
{
    ExecFunc(MI_SYS_UnBindChnPort(0, &pstBindInfo->stSrcChnPort, &pstBindInfo->stDstChnPort), MI_SUCCESS);

    return MI_SUCCESS;
}

MI_SNR_PADID getSnrPadIdByGroupId(MI_VIF_GROUP groupId)
{
    if (groupId == 0) return 0;
    else if (groupId == 1) return 2;
    else if (groupId == 2) return 1;
    else if (groupId == 3) return 3;
    else return -1;
}

MI_VIF_DEV getVifDevIdBySnrPadId(MI_SNR_PADID snrPadId, MI_U32 planId)
{
    if (snrPadId == 0) return planId;
    else if (snrPadId == 1) return 8 + planId;
    else if (snrPadId == 2) return 4 + planId;
    else if (snrPadId == 3) return 12 + planId;
    else return -1;
}

MI_VIF_GROUP getVifGroupIdByVifDevId(MI_VIF_DEV vifDevId)
{
    return vifDevId / SIG_VIF_PER_GROUP_DEV_MAX_NUM;
}

MI_U32 getPlanIdByVifDevId(MI_VIF_DEV vifDevId)
{
    return vifDevId % SIG_VIF_PER_GROUP_DEV_MAX_NUM;
}

MI_SCL_DEV getSclDevIdByVifDevId(MI_VIF_DEV vifDevId)
{
    switch (vifDevId) {
        case 0 ... 3: return 0;
        case 4 ... 7: return 2;
        case 8 ... 11: return 1;
        case 12 ... 15: return 3;
        default: return -1;
    }
}

MI_S32 sigCheckFd(const MI_S32 *fds, MI_S32 fdCount, MI_BOOL *status, int32_t timeOutMs)
{
    fd_set rfds;

    MI_U32 n = 0;

    MI_S32 ret = 0;
    MI_S32 fdMax = -1;

    fdCount = MIN(fdCount, 16);

    struct timeval timeout;
    timeout.tv_sec = timeOutMs / 1000;
    timeout.tv_usec = (timeOutMs % 1000) * 1000;

    FD_ZERO(&rfds);
    for (MI_S32 i = 0; i < fdCount; i ++) {
        status[i] = FALSE;
        if (fds[i] > 0) {
            FD_SET(fds[i], &rfds);
            if (fds[i] > fdMax) {
                fdMax = fds[i];
            }
        }
    }

    if (fdMax < 0) {
        return 0;
    }

    ret = select(fdMax + 1, &rfds, nullptr, nullptr, &timeout);
    if (ret < 0) {
        logpe("select error !");
        return -1;
    } else if (ret == 0) {
        // logpe("select time out .");
        return 0;
    } else {
        for (MI_S32 i = 0; i < fdCount; i ++) {
            if (fds[i] > 0) {
                if (FD_ISSET(fds[i], &rfds)) {
                    status[i] = TRUE;
                }
            }
        }
    }

    return 1;
}
