#include "sigCommon.h"

#define VIF_DEV			8

#define SNR_PAD_ID		1

#define GROUP_ID        2

#define SCL_DEV_ID		2
#define SCL_CHN_ID		0
#define SCL_OUTPORT_ID	0

MI_S32 snrInit()
{
    MI_U32 miResCount = 0;
    MI_U8 miResIndex = 0;
    MI_S8 selectIdx = -1;
    MI_SNR_Res_t miSnrRes;

	MI_SNR_PADID snrPadId = SNR_PAD_ID;

	MI_VIF_DEV vifDevId = VIF_DEV;
	MI_U32 planeId = vifDevId % 4;

	MI_SNR_Anadec_SrcAttr_t miAnadecSrcAttr;

    memset(&miAnadecSrcAttr, 0, sizeof(MI_SNR_Anadec_SrcAttr_t));
    miAnadecSrcAttr.stRes.u16Width = 1920;
    miAnadecSrcAttr.stRes.u16Height = 1080;
    miAnadecSrcAttr.u32Fps = 25;
    miAnadecSrcAttr.eTransferMode = E_MI_SNR_ANADEC_TRANSFERMODE_AHD;

    STCHECKRESULT(MI_SNR_SetAnadecSrcAttr(snrPadId, planeId, &miAnadecSrcAttr));

	//------------------

    STCHECKRESULT(MI_SNR_SetPlaneMode(snrPadId, FALSE));
    STCHECKRESULT(MI_SNR_QueryResCount(snrPadId, &miResCount));

    for (miResIndex = 0; miResIndex < miResCount; miResIndex ++) {

        memset(&miSnrRes, 0, sizeof(MI_SNR_Res_t));
        STCHECKRESULT(MI_SNR_GetRes(snrPadId, miResIndex, &miSnrRes));

        logd("index %d, Crop(%d, %d, %d, %d), outputsize(%d, %d), maxfps %d, minfps %d, ResDesc %s",
            miResIndex, miSnrRes.stCropRect.u16X, miSnrRes.stCropRect.u16Y,
            miSnrRes.stCropRect.u16Width, miSnrRes.stCropRect.u16Height,
            miSnrRes.stOutputSize.u16Width, miSnrRes.stOutputSize.u16Height,
            miSnrRes.u32MaxFps, miSnrRes.u32MinFps, miSnrRes.strResDesc);
    }

    selectIdx = 0;
    if (selectIdx < 0) {
        loge("no suitable parameters were matched ! snrPadId: %d", snrPadId);
        return -1;
    }

    STCHECKRESULT(MI_SNR_SetRes(snrPadId, selectIdx));

	//------------

	STCHECKRESULT(MI_SNR_Enable(snrPadId));

	return MI_SUCCESS;
}

MI_S32 snrUnInit()
{
	MI_SNR_PADID snrPadId = SNR_PAD_ID;

	STCHECKRESULT(MI_SNR_Disable(snrPadId));

	return MI_SUCCESS;
}

//--------------------

MI_S32 vifInit()
{
	MI_VIF_GROUP groupId = GROUP_ID;
	MI_SNR_PADID snrPadId = SNR_PAD_ID;

	MI_VIF_DEV vifDevId = VIF_DEV;

	MI_U32 planeId = vifDevId % 4;

	MI_VIF_PORT vifPortId = 0;

    MI_SNR_PADInfo_t miPadInfo;
    MI_VIF_GroupAttr_t miGroupAttr;

    MI_VIF_DevAttr_t miVifDevAttr;
    MI_SNR_PlaneInfo_t miSnrPlaneInfo;

	MI_VIF_OutputPortAttr_t miVifPortInfo;

    memset(&miPadInfo, 0, sizeof(MI_SNR_PADInfo_t));
    memset(&miGroupAttr, 0, sizeof(MI_VIF_GroupAttr_t));

    memset(&miVifDevAttr, 0, sizeof(MI_VIF_DevAttr_t));
    memset(&miSnrPlaneInfo, 0, sizeof(MI_SNR_PlaneInfo_t));

	memset(&miVifPortInfo, 0, sizeof(MI_VIF_OutputPortAttr_t));

	STCHECKRESULT(MI_SNR_GetPadInfo(snrPadId, &miPadInfo));

    miGroupAttr.eIntfMode = (MI_VIF_IntfMode_e) miPadInfo.eIntfMode;
    miGroupAttr.eWorkMode = E_MI_VIF_WORK_MODE_4MULTIPLEX;
    miGroupAttr.eHDRType = E_MI_VIF_HDR_TYPE_OFF;
    if (miGroupAttr.eIntfMode == E_MI_VIF_MODE_BT656) {
        miGroupAttr.eClkEdge = (MI_VIF_ClkEdge_e) miPadInfo.unIntfAttr.stBt656Attr.eClkEdge;
    }

    STCHECKRESULT(MI_VIF_CreateDevGroup(groupId, &miGroupAttr));

	//--------------

	STCHECKRESULT(MI_SNR_GetPlaneInfo(snrPadId, planeId, &miSnrPlaneInfo));

    miVifDevAttr.stInputRect.u16X = 0;
    miVifDevAttr.stInputRect.u16Y = 0;
    miVifDevAttr.stInputRect.u16Width = 1920;
    miVifDevAttr.stInputRect.u16Height = 1080;
    miVifDevAttr.bEnH2T1PMode = 0;

    if (miSnrPlaneInfo.eBayerId >= E_MI_SYS_PIXEL_BAYERID_MAX) {
        miVifDevAttr.eInputPixel = miSnrPlaneInfo.ePixel;
    } else {
        miVifDevAttr.eInputPixel = (MI_SYS_PixelFormat_e) RGB_BAYER_PIXEL(miSnrPlaneInfo.ePixPrecision, miSnrPlaneInfo.eBayerId);
    }

    miVifDevAttr.eInputPixel = E_MI_SYS_PIXEL_FRAME_YUV422_UYVY;
    logd("setchnportattr (%d, %d, %d, %d)", miVifDevAttr.stInputRect.u16X,
        miVifDevAttr.stInputRect.u16Y, miVifDevAttr.stInputRect.u16Width,
        miVifDevAttr.stInputRect.u16Height);

    STCHECKRESULT(MI_VIF_SetDevAttr(vifDevId, &miVifDevAttr));
    STCHECKRESULT(MI_VIF_EnableDev(vifDevId));

	//----------------------

    miVifPortInfo.stCapRect.u16X = 0;
    miVifPortInfo.stCapRect.u16Y = 0;
    miVifPortInfo.stCapRect.u16Width = 1920;
    miVifPortInfo.stCapRect.u16Height = 1080;
    miVifPortInfo.stDestSize.u16Width = 1920;
    miVifPortInfo.stDestSize.u16Height = 1080;

    miVifPortInfo.ePixFormat = E_MI_SYS_PIXEL_FRAME_YUV422_UYVY; //E_MI_SYS_PIXEL_FRAME_YUV_SEMIPLANAR_420
    miVifPortInfo.eFrameRate = E_MI_VIF_FRAMERATE_FULL; //帧率1比1输出

	return MI_SUCCESS;
}

MI_S32 vifUnInit()
{
	MI_VIF_GROUP groupId = 1;

	MI_VIF_DEV vifDevId = 8;
	MI_VIF_PORT vifPortId = 0;

	STCHECKRESULT(MI_VIF_DisableOutputPort(vifDevId, vifPortId));

	//-----------

	STCHECKRESULT(MI_VIF_DisableDev(vifDevId));

	//------------

	STCHECKRESULT(MI_VIF_DestroyDevGroup(groupId));

	//--------------

	return MI_SUCCESS;
}

//----------------

MI_S32 sclInit()
{
	MI_SCL_DEV sclDevId = SCL_DEV_ID;
	MI_SCL_CHANNEL sclChnId = SCL_CHN_ID;
	MI_SCL_PORT sclOutPortId = SCL_OUTPORT_ID;

	MI_SCL_DevAttr_t miCreateDevAttr;

    MI_SCL_ChannelAttr_t miSclChnAttr;
    MI_SYS_WindowRect_t miInputCropWin;
    MI_SCL_ChnParam_t miSclChnParam;

    MI_SCL_OutPortParam_t miSclOutputParam;
    MI_SYS_WindowRect_t miOrigPortCrop;
    MI_SYS_WindowSize_t miOrigPortSize;
    MI_SYS_ChnPort_t miSysChnPort;

	sigSysBindInfo_t miBindInfo;

	MI_VIF_DEV vifDevId = VIF_DEV;

	memset(&miCreateDevAttr, 0, sizeof(MI_SCL_DevAttr_t));
	memset(&miSclChnAttr, 0, sizeof(MI_SCL_ChannelAttr_t));
	memset(&miInputCropWin, 0, sizeof(MI_SYS_WindowRect_t));
	memset(&miSclChnParam, 0, sizeof(MI_SCL_ChnParam_t));

    memset(&miSclOutputParam, 0, sizeof(MI_SCL_OutPortParam_t));
    memset(&miOrigPortCrop, 0, sizeof(MI_SYS_WindowRect_t));
    memset(&miOrigPortSize, 0, sizeof(MI_SYS_WindowSize_t));
    memset(&miSysChnPort, 0, sizeof(MI_SYS_ChnPort_t));

    memset(&miBindInfo, 0, sizeof(sigSysBindInfo_t));

    miCreateDevAttr.u32NeedUseHWOutPortMask = E_MI_SCL_HWSCL0;
    STCHECKRESULT(MI_SCL_CreateDevice(sclDevId, &miCreateDevAttr));

	//--------------

	STCHECKRESULT(MI_SCL_CreateChannel(sclDevId, sclChnId, &miSclChnAttr));

	//---------------

	// miInputCropWin.u16X = 0;
    // miInputCropWin.u16Y = 0;
    // miInputCropWin.u16Width = 1920;
    // miInputCropWin.u16Height = 1080;

	// STCHECKRESULT(MI_SCL_SetInputPortCrop(sclDevId, sclChnId, &miInputCropWin));

	//-------------------

    miSclChnParam.eRot = E_MI_SYS_ROTATE_NONE;//rotate;
    STCHECKRESULT(MI_SCL_SetChnParam(sclDevId, sclChnId, &miSclChnParam));
    STCHECKRESULT(MI_SCL_StartChannel(sclDevId, sclChnId));

	//--------------------

    miOrigPortCrop.u16X = 0;
    miOrigPortCrop.u16Y = 0;
    miOrigPortCrop.u16Width = 1920;
    miOrigPortCrop.u16Height = 1080;

    miOrigPortSize.u16Width = 1920;
    miOrigPortSize.u16Height = 1080;

    memcpy(&miSclOutputParam.stSCLOutCropRect, &miOrigPortCrop, sizeof(MI_SYS_WindowRect_t));
    memcpy(&miSclOutputParam.stSCLOutputSize, &miOrigPortSize, sizeof(MI_SYS_WindowSize_t));

    miSclOutputParam.ePixelFormat = E_MI_SYS_PIXEL_FRAME_YUV_SEMIPLANAR_420;// YYU420_NV12
    miSclOutputParam.bMirror = 0;
    miSclOutputParam.bFlip = 0;

    miSclOutputParam.stSCLOutputSize.u16Width = 1920;
    miSclOutputParam.stSCLOutputSize.u16Height = 1080;

    miSysChnPort.eModId = E_MI_MODULE_ID_SCL;
    miSysChnPort.u32DevId = sclDevId;
    miSysChnPort.u32ChnId = sclChnId;
    miSysChnPort.u32PortId = sclOutPortId;

    STCHECKRESULT(MI_SCL_SetOutputPortParam(sclDevId, sclChnId, sclOutPortId, &miSclOutputParam));
    STCHECKRESULT(MI_SYS_SetChnOutputPortDepth(0, &miSysChnPort, SIG_SCL_BUFF_HANDLE_MAX_NUM, SIG_SCL_BUFF_HANDLE_MAX_NUM + 3));

	//-----------------------

	STCHECKRESULT(MI_SCL_EnableOutputPort(sclDevId, sclChnId, sclOutPortId));

	//------------------------

    miBindInfo.stSrcChnPort.eModId = E_MI_MODULE_ID_VIF;
    miBindInfo.stSrcChnPort.u32DevId = vifDevId;
    miBindInfo.stSrcChnPort.u32ChnId = 0;
    miBindInfo.stSrcChnPort.u32PortId = 0;

    miBindInfo.stDstChnPort.eModId = E_MI_MODULE_ID_SCL;
    miBindInfo.stDstChnPort.u32DevId = sclDevId;
    miBindInfo.stDstChnPort.u32ChnId = sclChnId;
    miBindInfo.stDstChnPort.u32PortId = sclOutPortId;

	miBindInfo.u32SrcFrmrate = 25;
    miBindInfo.u32DstFrmrate = 25;
    miBindInfo.eBindType = sclDevId == 2 ? E_MI_SYS_BIND_TYPE_REALTIME : E_MI_SYS_BIND_TYPE_FRAME_BASE;

	STCHECKRESULT(sigSysBind(&miBindInfo));

	return MI_SUCCESS;
}

MI_S32 sclUnInit()
{
	MI_VIF_DEV vifDevId = VIF_DEV;

	MI_SCL_DEV sclDevId = SCL_DEV_ID;
	MI_SCL_CHANNEL sclChnId = SCL_CHN_ID;
	MI_SCL_PORT sclOutPortId = SCL_OUTPORT_ID;

	sigSysBindInfo_t miBindInfo;

	memset(&miBindInfo, 0, sizeof(sigSysBindInfo_t));

    miBindInfo.stSrcChnPort.eModId = E_MI_MODULE_ID_VIF;
    miBindInfo.stSrcChnPort.u32DevId = vifDevId;
    miBindInfo.stSrcChnPort.u32ChnId = 0;
    miBindInfo.stSrcChnPort.u32PortId = 0;

    miBindInfo.stDstChnPort.eModId = E_MI_MODULE_ID_SCL;
    miBindInfo.stDstChnPort.u32DevId = sclDevId;
    miBindInfo.stDstChnPort.u32ChnId = sclChnId;
    miBindInfo.stDstChnPort.u32PortId = sclOutPortId;

	miBindInfo.u32SrcFrmrate = 25;
    miBindInfo.u32DstFrmrate = 25;
    miBindInfo.eBindType = sclDevId == 2 ? E_MI_SYS_BIND_TYPE_REALTIME : E_MI_SYS_BIND_TYPE_FRAME_BASE;

    STCHECKRESULT(sigSysUnBind(&miBindInfo));

	//--------------------

	STCHECKRESULT(MI_SCL_DisableOutputPort(sclDevId, sclChnId, sclOutPortId));

	//--------------------

    STCHECKRESULT(MI_SCL_StopChannel(sclDevId, sclChnId));
    STCHECKRESULT(MI_SCL_DestroyChannel(sclDevId, sclChnId));

	//--------------------

	STCHECKRESULT(MI_SCL_DestroyDevice(sclDevId));

	return MI_SUCCESS;
}

int32_t main(int32_t argc, char *argv[])
{
    logd("");
    logd("******************************************");
    logd("");

	STCHECKRESULT(snrInit());
	STCHECKRESULT(vifInit());
	STCHECKRESULT(sclInit());

	//---------------------

	sclUnInit();
	vifUnInit();
	snrUnInit();

	return 0;
}
