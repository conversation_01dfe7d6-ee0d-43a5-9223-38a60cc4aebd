
executable("scl_realtime") {
    sources = [
        "sigCommon.cpp",
        "main.cpp",
    ]

    include_dirs = [
        ".",
        "//applications/idvr/include/idvr",
        "//applications/vendor/8838_sdk4/include",
    ]

    deps = [
        "//foundation/base/core/filelog",
        "//foundation/base/core/mystd",
        "//foundation/base/core/json-c-util:jsonUtil",
        "//foundation/communication/socketcmd",
        "//foundation/communication/property",
        "//foundation/communication/ringbuf",
        "//foundation/communication/libevservice",
        "//foundation/communication/ipcAgent",
        "//foundation/multimedia/libsrc/libaud.enc",
        "//foundation/multimedia/libsrc/libaud.dec",
        "//foundation/multimedia/libsrc/libfallocate",
        "//foundation/multimedia/libsrc/libstrmts",
        "//foundation/multimedia/libsrc/librtp",
        "//foundation/multimedia/libsrc/libmdraw",
        "//foundation/multimedia/libsrc/libmjpeg",
        "//third_party/libevent/libevent-2.1.12-stable:libevent",
        "//third_party/libevent/libevent-2.1.12-stable:libevent_pthreads",
        "//third_party/faac/faac-********:faac",
        "//third_party/faad/faad2-2.8.8:faad",
        "//third_party/mp4v2/mp4v2_2.0.0:mp4v2",
        "//third_party/libyuv",
        "//third_party/libpng",
        "//third_party/freetype2/freetype-2.11.1:freetype2",
    ]

    libs = [
    ]

    defines = [
        "LOG_TAG_STR=${target_name}",
    ]

    sigmastar_sdk_dir = rebase_path("//applications/vendor/8838_sdk4/lib/share/")

    ldflags = [
        "-L${sigmastar_sdk_dir}",
        "-L${sigmastar_sdk_dir}/opencv_9.1.0",
        "-ldl",
        "-lpthread",
        "-lopencv_imgcodecs",
        "-lopencv_imgproc",
        "-lopencv_core",
        "-lcam_fs_wrapper",
        "-lcam_os_wrapper",
        "-lmi_ai",
        "-lmi_ao",
        "-lmi_common",
        "-lmi_disp",
        "-lmi_panel",
        "-lmi_fb",
        "-lmi_rgn",
        "-lmi_scl",
        "-lmi_sensor",
        "-lmi_sys",
        "-lmi_vdec",
        "-lmi_venc",
        "-lmi_vif",
    ]

    cflags_cc = [
        "-Wno-psabi",
        "-Wno-unused-parameter",
    ]
}
