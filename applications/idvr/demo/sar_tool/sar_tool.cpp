#include "sar.h"

static void usage(const char *exe_name)
{
    SAR_INFO("Usage:%s <switches> [option]\n", exe_name);

    SAR_INFO("-H/-h   sar tool help");
    SAR_INFO("-R/-r   sar tool read adc, <ch> [0:3]");
    return;
}

#define GETOPT_OPT_STR  "HhR:r:"
int main(int argc, char *argv[])
{
    int opt;
    int ret = 0;
    SARHandle sarHandle;

    SAR_DBG("sar tool");

    ret = sarHandle.Init();
    if (ret < 0) {
        SAR_ERR("Sar Init error!,ret=%d", ret);
        return ret;
    }

    if (1 == argc) {
        usage(argv[0]);
        return 0;
    }

    while ((opt = getopt(argc, argv, GETOPT_OPT_STR)) != -1) {
        if (2 == argc) {
            usage(argv[0]);
            return 0;
        }
        switch (opt) {
            case 'R':
            case 'r':
                {
                    SAR_CONFIG_READ_ADC stSarCfg;
                    SAR_DBG("Read Sar channel adc value...");

                    memset(&stSarCfg, 0, sizeof(SAR_CONFIG_READ_ADC));
                    stSarCfg.ch = atoi(argv[2]) & 0x7F;
                    if (stSarCfg.ch >= SAR_CHN_MAX) {
                        SAR_ERR("Input the channel num within the range of [0, 3].");
                        ret = -1;
                        break;
                    }

                    ret = ioctl(sarHandle.mFd, MS_SAR_SET_CHANNEL_READ_VALUE, &stSarCfg);
                    if (ret) {
                        SAR_ERR("Read sar adc failure.");
                    } else {
                        SAR_INFO("get success, chn=%d, value=%d, calVal=%0.2fV\n",
                            stSarCfg.ch, stSarCfg.adcRawVal, stSarCfg.adcRawVal * 3.3 / 0x3FF);
                    }
                }
                break;

            case 'H':
            case 'h':
            default:
                usage(argv[0]);
                break;
        }
    }
    return ret;
}

