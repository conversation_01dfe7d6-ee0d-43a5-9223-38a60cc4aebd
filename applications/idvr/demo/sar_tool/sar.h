#ifndef __SAR_H__
#define __SAR_H__

#include "mystd.h"

#define VERION_MAJOR                1
#define VERION_MINOR                3

//#define SAR_DEBUG_ON

#define SAR_DEVICE_NAME     "/dev/sar"

#define __FILENAME__                (strrchr("/" __FILE__, '/') + 1)

#define SAR_INFO(fmt, args...) \
    do { \
        printf("[%s-%s:%d] V%d.%d-info: " fmt "\n", SAR_DEVICE_NAME, __FILENAME__, __LINE__, VERION_MAJOR, VERION_MINOR, ##args); \
    } while(0)

#define SAR_ERR(fmt, args...) \
    do { \
        printf("[%s-%s:%d] V%d.%d-error: " fmt "\n", SAR_DEVICE_NAME, __FILENAME__, __LINE__, VERION_MAJOR, VERION_MINOR, ##args); \
    } while(0)

#ifdef SAR_DEBUG_ON
#define SAR_DBG(fmt, args...) \
    do { \
        printf("[%s-%s:%d] V%d.%d-debug: " fmt "\n", SAR_DEVICE_NAME, __FILENAME__, __LINE__, VERION_MAJOR, VERION_MINOR, ##args); \
    } while(0)
#endif

#ifndef SAR_DBG
#define SAR_DBG(fmt, args...) {}
#endif

#define SARADC_IOCTL_MAGIC                          'a'
#define MS_SAR_INIT                                 _IO(SARADC_IOCTL_MAGIC, 0x00)
#define MS_SAR_SET_CHANNEL_READ_VALUE               _IO(SARADC_IOCTL_MAGIC, 0x01)
#define SAR_IOCTL_CMD_COUNT                         0x01

typedef struct
{
    unsigned int ch;            // 0~3
    unsigned int adcRawVal;     // 读取到的值
}SAR_CONFIG_READ_ADC;

typedef enum {
    SAR_CHN_0,
    SAR_CHN_1,
    SAR_CHN_2,
    SAR_CHN_3,
    SAR_CHN_MAX,
}SAR_CHN_NUM;

class SARHandle
{
    public:
        SARHandle()
        {
            mFd = 0;
        }
        ~SARHandle()
        {
            close(mFd);
        }

        int Init()
        {
            int ret = 0;
            mFd = open(SAR_DEVICE_NAME, O_RDWR);
            if (mFd < 0) {
                SAR_ERR("Error! SARHandle : Failed to open %s, %s\n", SAR_DEVICE_NAME, strerror(errno));
                return -1;
            }

            ret = ioctl(mFd, MS_SAR_INIT);
            if (ret < 0) {
                SAR_ERR("ioctl MS_SAR_INIT error");
                close(mFd);
                return -2;
            }

            return mFd;
        }

    public:
        int mFd;
};

#endif

