#include <alloca.h>
#include <limits.h>

#include <errno.h>
#include <strings.h>
#include <fcntl.h>
#include <sys/types.h>
#include <termios.h>

#include <sys/time.h>

#include <math.h>
#include <malloc.h>

#include <time.h> 
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <stdbool.h>
#include <stdarg.h>
#include <stddef.h>
#include <signal.h>

#include <sys/file.h>
#include <unistd.h>
#include <stdint.h>

#include <sys/types.h>
#include <sys/stat.h>

/**
 * @brief 
 * YUV画框相关代码
 */

#define MIN(_x, _y)   ((_x) < (_y) ? (_x) : (_y))
#define MAX(_x, _y)   ((_x) > (_y) ? (_x) : (_y))

typedef struct YUVColor
{
    YUVColor() : y(0), u(0), v(0)
    {

    }

    YUVColor(uint8_t y_, uint8_t u_, uint8_t v_)
        : y(y_)
        , u(u_)
        , v(v_)
    {

    }

    uint8_t         y;
    uint8_t         u;
    uint8_t         v;
} YUVColor_t;

#define YUV_COLOR_RED       YUVColor(0x51, 0x5a, 0xfa)
#define YUV_COLOR_BLUE      YUVColor(0x28, 0xff, 0x00)

typedef struct Point
{
    Point() : x(0), y(0)
    {

    }

    Point(int32_t x_, int32_t y_)
        : x(x_)
        , y(y_)
    {

    }

    int32_t         x;
    int32_t         y;
} point_t;

typedef struct Line
{
    point_t         p0;
    point_t         p1;
    YUVColor_t      color;
    int32_t         lineWidth = 2;
} line_t;

typedef struct Rect
{
    Rect() : x(0), y(0), w(0), h(0)
    {

    }

    Rect(int32_t x_, int32_t y_, int32_t w_, int32_t h_)
        : x(x_)
        , y(y_)
        , w(w_)
        , h(h_)
    {

    }

    int32_t         x;
    int32_t         y;
    int32_t         w;
    int32_t         h;
} rect_t;

static inline bool _YUVDrawPoint(uint8_t *yuvData, int32_t yuvlen, int32_t iw, int32_t ih, const point_t& point, const YUVColor_t& color)
{
    int32_t yOffset = 0;
    int32_t uOffset = 0;
    int32_t vOffset = 0;

    if (yuvData == nullptr ||
        iw <= 0 ||
        ih <= 0 ||
        point.x < 0 ||
        point.y < 0 ||
        point.x >= iw ||
        point.y >= ih ||
        (iw * ih + iw * ih / 2) != yuvlen
    ) {
        return false;
    }

    yOffset = point.y * iw + point.x;
    uOffset = ih * iw + (point.y / 2) * iw + point.x / 2 * 2;
    vOffset = uOffset + 1;

    yuvData[yOffset] = color.y;
    yuvData[uOffset] = color.u;
    yuvData[vOffset] = color.v;

    return true;
}

static inline bool _YUVDrawLine(uint8_t *yuvData, int32_t yuvlen, int32_t iw, int32_t ih, line_t& line)
{
    if (yuvData == nullptr ||
        iw <= 0 ||
        ih <= 0 ||
        line.p0.x < 0 ||
        line.p0.y < 0 ||
        line.p1.x < 0 ||
        line.p1.y < 0 ||
        line.p0.x >= iw ||
        line.p0.y >= ih ||
        line.p1.x >= iw ||
        line.p1.y >= ih ||
        line.lineWidth <= 0
    ) {
        return false;
    }

    line.lineWidth = line.lineWidth <= 0 ? 2 : line.lineWidth;

    line.p0.x = (line.p0.x + line.lineWidth >= iw) ? (iw - line.lineWidth) : (line.p0.x < 0 ? 0 : line.p0.x);
    line.p1.x = (line.p1.x + line.lineWidth >= iw) ? (iw - line.lineWidth) : (line.p1.x < 0 ? 0 : line.p1.x);
    line.p0.y = (line.p0.y + line.lineWidth >= ih) ? (ih - line.lineWidth) : (line.p0.y < 0 ? 0 : line.p0.y);
    line.p1.y = (line.p1.y + line.lineWidth >= ih) ? (ih - line.lineWidth) : (line.p1.y < 0 ? 0 : line.p1.y);

    int32_t dx = (line.p0.x > line.p1.x) ? (line.p0.x - line.p1.x) : (line.p1.x - line.p0.x);
    int32_t dy = (line.p0.y > line.p1.y) ? (line.p0.y - line.p1.y) : (line.p1.y - line.p0.y);
    int32_t xstep = (line.p0.x < line.p1.x) ? 1 : -1;
	int32_t ystep = (line.p0.y < line.p1.y) ? 1 : -1;
    int32_t nstep = 0, eps = 0;
	int32_t pointX = line.p0.x;
	int32_t	pointY = line.p0.y;

    if (dx > dy) {
        while (nstep <= dx) {
            if (_YUVDrawPoint(yuvData, yuvlen, iw, ih, Point(pointX, pointY), line.color) == false) {
                return false;
            }

            eps += dy;
            if ((eps << 1) >= dx) {
                pointY += ystep;
                eps -= dx;
            }

            pointX += xstep;
            nstep ++;
        }
    } else {
        while (nstep <= dy) {
            if (_YUVDrawPoint(yuvData, yuvlen, iw, ih, Point(pointX, pointY), line.color) == false) {
                return false;
            }

            eps += dx;
            if ((eps << 1) >= dy) {
                pointX += xstep;
                eps -= dy;
            }

            pointY += ystep;
            nstep ++;
        }
    }

    return true;
}

/**
 * @brief 
 * YUV画矩形框接口
 * @param yuvData yuv原始数据
 * @param yuvlen yuv数据长度
 * @param iw    yuv宽度
 * @param ih    yuv高度
 * @param rect 需要画的矩形区域
 * @param color 矩形边框颜色，使用宏 YUV_COLOR_RED/YUV_COLOR_BLUE
 * @return true 
 * @return false 
 */
bool YUVDrawRect(uint8_t *yuvData, int32_t yuvlen, int32_t iw, int32_t ih, const rect_t& rect, const YUVColor_t& color)
{
    line_t left;
    line_t right;
    line_t top;
    line_t bot;

    //左边竖线
    left.p0.x = rect.x;
    left.p0.y = rect.y;
    left.p1.x = left.p0.x;
    left.p1.y = left.p0.y + rect.h;
    left.color = color;
    left.lineWidth = 2;

    //右边竖线
    right.p0.x = rect.x + rect.w;
    right.p0.y = rect.y;
    right.p1.x = right.p0.x;
    right.p1.y = right.p0.y + rect.h;
    right.color = color;
    right.lineWidth = 2;

    //上面横线
    top.p0.x = rect.x;
    top.p0.y = rect.y;
    top.p1.x = rect.x + rect.w;
    top.p1.y = top.p0.y;
    top.color = color;
    top.lineWidth = 2;

    //下面横线
    bot.p0.x = rect.x;
    bot.p0.y = rect.y + rect.h;
    bot.p1.x = rect.x + rect.w;
    bot.p1.y = bot.p0.y;
    bot.color = color;
    bot.lineWidth = 2;

    printf("left %d %d %d %d\n", left.p0.x, left.p0.y, left.p1.x, left.p1.y);
    printf("right %d %d %d %d\n", right.p0.x, right.p0.y, right.p1.x, right.p1.y);
    printf("top %d %d %d %d\n", top.p0.x, top.p0.y, top.p1.x, top.p1.y);
    printf("bot %d %d %d %d\n", bot.p0.x, bot.p0.y, bot.p1.x, bot.p1.y);

    if (_YUVDrawLine(yuvData, yuvlen, iw, ih, left) == false) {
        printf("_YUVDrawLine left error !\n");
        return false;
    }

    if (_YUVDrawLine(yuvData, yuvlen, iw, ih, right) == false) {
        printf("_YUVDrawLine right error !\n");
        return false;
    }

    if (_YUVDrawLine(yuvData, yuvlen, iw, ih, top) == false) {
        printf("_YUVDrawLine top error !\n");
        return false;
    }

    if (_YUVDrawLine(yuvData, yuvlen, iw, ih, bot) == false) {
        printf("_YUVDrawLine bottom error !\n");
        return false;
    }

    return true;
}

//-----------------------------------------------------------------------------------------------------------
//-----------------------------------------------------------------------------------------------------------
//-----------------------------------------------------------------------------------------------------------

/**
 * @brief 
 *  以下是测试代码
 */

static uint64_t _startMs = 0ULL;
static inline void _clockInit()
{
    struct timespec startTime = { 0 };
    clock_gettime(CLOCK_MONOTONIC, &startTime);
    if (_startMs <= 0) {
        _startMs = (startTime.tv_sec * 1000 + (startTime.tv_nsec / (1000 * 1000)));
    }
}

static inline uint64_t _getClockMs()
{
    struct timespec now = { 0 };
    uint64_t ms = 0;
    uint64_t current = 0;

    clock_gettime(CLOCK_MONOTONIC, &now);
    current = (now.tv_sec * 1000 + (now.tv_nsec / (1000 * 1000)));
    ms = current - _startMs;
    return (uint64_t) ms;
}

static inline int32_t _YUVLoadFileSize(const char *file)
{
    struct stat s;
    if (file == nullptr ||
        file[0] == '\0' ||
        access(file, F_OK) != 0
    ) {
        return -1;
    }

    stat(file, &s);
    return s.st_size;
}

static inline bool _YUVLoadData(const char *file, int32_t iw, int32_t ih, uint8_t *& yuvData, int32_t& yuvlen)
{
    int32_t n = 0;
    int32_t offset = 0;

    uint8_t *yuvData_ = nullptr;
    int32_t yuvlen_ = 0;
    int32_t yuvlenTmp_ = 0;

    FILE *fp = nullptr;
    
    yuvData = nullptr;
    yuvlen = 0;

    yuvlen_ = _YUVLoadFileSize(file);
    if (yuvlen_ <= 0) {
        printf("%s is empty or not exist !\n", file);
        return false;
    }

    if ((iw * ih + iw * ih / 2) != yuvlen_) {
        printf("just support YUV NV12 !\n");
        return false;
    }

    yuvData_ = (uint8_t *) calloc(1, yuvlen_);
    if (yuvData_ == nullptr) {
        printf("calloc error !\n");
        return false;
    }

    fp = fopen(file, "rb");
    if (fp == nullptr) {
        printf("fopen '%s' error !\n", file);
        goto error;
    }

    yuvlenTmp_ = yuvlen_;
    printf("yuvlen: %d\n", yuvlenTmp_);

    for ( ; yuvlenTmp_ > 0; ) {
        n = fread(yuvData_ + offset, 1, MIN(1024, yuvlenTmp_), fp);
        if (n <= 0) {
            printf("fread '%s' error !\n", file);
            goto error;
        }

        offset += n;
        yuvlenTmp_ -= n;
    }
    fclose(fp);

    yuvData = yuvData_;
    yuvlen = yuvlen_;

    return true;

error:
    if (fp)         fclose(fp);
    if (yuvData_)    free(yuvData_);
    return false;
}

static inline bool _YUVSaveData(const uint8_t *yuvData, int32_t yuvlen, const char *file)
{
    FILE *fp = nullptr;
    int32_t n = 0;
    int32_t offset = 0;

    if (yuvData == nullptr ||
        yuvlen <= 0 ||
        file == nullptr ||
        file[0] == '\0'
    ) {
        return false;
    }

    unlink(file);

    fp = fopen(file, "w+");
    if (fp == nullptr) {
        printf("fopen '%s' error !\n", file);
        return false;
    }

    for ( ; yuvlen > 0; ) {
        n = fwrite(yuvData + offset, 1, MIN(1024, yuvlen), fp);
        if (n <= 0) {
            printf("fwrite error !\n");
            goto error;
        }

        offset += n;
        yuvlen -= n;
    }

    fclose(fp);
    return true;

error:
    if (fp)         fclose(fp);
    return false;
}

int32_t main(int32_t argc, char **argv)
{
    int32_t offset = 0;
    int32_t filesize = 0;

    uint8_t *yuvData = nullptr;
    int32_t yuvlen = 0;

    uint64_t clkMs = 0;

    _clockInit();

    if (argc != 9) {
        printf("usage: %s [yuv_file] [yuv_w] [yuv_h] [rect_x] [rect_y] [rect_w] [rect_h] [save_path].\n", argv[offset]);
        return -1;
    }

    offset ++;

    const char *yuvFilePath = argv[offset]; offset ++;

    int32_t iw = atoi(argv[offset]); offset ++;
    int32_t ih = atoi(argv[offset]); offset ++;

    int32_t x = atoi(argv[offset]); offset ++;
    int32_t y = atoi(argv[offset]); offset ++;
    int32_t w = atoi(argv[offset]); offset ++;
    int32_t h = atoi(argv[offset]); offset ++;

    const char *savePath = argv[offset]; offset ++;

    printf(
        "yuv file: %s\n"
        "      iw: %d\n"
        "      ih: %d\n"
        "  rect x: %d\n"
        "  rect y: %d\n"
        "  rect w: %d\n"
        "  rect h: %d\n"
        "save path: %s\n"
        , yuvFilePath
        , iw
        , ih
        , x
        , y
        , w
        , h
        , savePath
    );

    if (_YUVLoadData(yuvFilePath, iw, ih, yuvData, yuvlen) == false) {
        printf("_YUVLoadData error !\n");
        return -1;
    }

    /**
     * @brief
     * 使用YUVDrawRect画框
     */
    clkMs = _getClockMs();
    if (YUVDrawRect(yuvData, yuvlen, iw, ih, Rect(x, y, w, h), YUV_COLOR_RED) == false) {
        printf("_YUVDrawRect error !\n");
        goto error;
    }
    printf("YUVDrawRect run time %llu ms\n", _getClockMs() - clkMs);

    clkMs = _getClockMs();
    if (_YUVSaveData(yuvData, yuvlen, savePath) == false) {
        printf("_YUVSaveData error !\n");
        goto error;
    }
    printf("_YUVSaveData run time %llu ms\n", _getClockMs() - clkMs);

    free(yuvData);

    printf("success .\n");

    return 0;

error:
    if (yuvData)    free(yuvData);
    return -1;
}

/**
 * @brief 
 * e.g. 画一个 Rect(100 100 400 200)的框
 *  yuv_draw /tmp/1666063660223_1280_720_1_front.yuv 1280 720 100 100 400 200 /tmp/1.yuv
 */
