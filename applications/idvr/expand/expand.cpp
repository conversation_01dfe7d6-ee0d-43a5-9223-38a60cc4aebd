#include <time.h>
#include <arpa/inet.h>
#include <net/if.h>
#include <string.h>
#include "expand.h"
#include "expand.message.h"
#include "mystd.h"
#include "prot.manager.h"
#include "transporter.slot.h"
#include "rapidjson.wrap.h"

SINGLETON_STATIC_INSTANCE(ExpandSet);

namespace minieye {

#define EXPAND_JSON_PATH "/data/minieye/idvr/etc/expand.json"
    
static const char *uartDev[EX_RS_MAX] = {
    "/dev/null",    /* dummy rs485*/
    "/dev/ttyS2", /* RS232_1 对应com版中间的rs232 */
    "/dev/ttyHSL3",
};



ExpandSet::ExpandSet()
{
    logd("expand +");
}

ExpandSet::~ExpandSet()
{
    logd("expand -");
}

const char *uartName[3] = {
    "RS485",
    "RS232_1",
    "RS232_2",
};

int32_t ExpandSet::load_expand_json(void)
{
    #if 1
    rapidjson::Document doc;

    RAPIDJSON_LOAD(EXPAND_JSON_PATH);
    
    for (int i=EX_RS485; i<EX_RS_MAX; i++) {
        RAPIDJSON_GET_JSON_INT(doc[uartName[i]], "baudrate", mUartConfig[i].baud);
        RAPIDJSON_GET_JSON_INT(doc[uartName[i]], "databit", mUartConfig[i].databit);
        RAPIDJSON_GET_JSON_INT(doc[uartName[i]], "stopbit", mUartConfig[i].stopbit);
        RAPIDJSON_GET_JSON_STRING(doc[uartName[i]], "parity", mUartConfig[i].parity);
        RAPIDJSON_GET_JSON_STRING(doc[uartName[i]], "protocol", mUartConfig[i].protName);
        RAPIDJSON_GET_JSON_BOOL(doc[uartName[i]], "enable", mUartConfig[i].enable);
    }

    RAPIDJSON_GET_JSON_STRING(doc["CAN1"], "baudrate", mCanConfig[CAN1].baud);
    RAPIDJSON_GET_JSON_STRING(doc["CAN1"], "protocol", mCanConfig[CAN1].protName);
    RAPIDJSON_GET_JSON_BOOL(doc["CAN1"], "enable", mCanConfig[CAN1].enable);

    RAPIDJSON_GET_JSON_STRING(doc["CAN2"], "baudrate", mCanConfig[CAN2].baud);
    RAPIDJSON_GET_JSON_STRING(doc["CAN2"], "protocol", mCanConfig[CAN2].protName);
    RAPIDJSON_GET_JSON_BOOL(doc["CAN2"], "enable", mCanConfig[CAN2].enable);

    RAPIDJSON_GET_JSON_STRING(doc["NET"], "url", mNetConfig.url);
    RAPIDJSON_GET_JSON_STRING(doc["NET"], "ip", mNetConfig.ip);
    RAPIDJSON_GET_JSON_STRING(doc["NET"], "local_ip", mNetConfig.ip);
    RAPIDJSON_GET_JSON_INT(doc["NET"], "port", mNetConfig.port);
    RAPIDJSON_GET_JSON_STRING(doc["NET"], "protocol", mNetConfig.protName);
    RAPIDJSON_GET_JSON_BOOL(doc["NET"], "enable", mNetConfig.enable);

    RAPIDJSON_GET_JSON_STRING(doc["USB"], "protocol", mUsbConfig.protName);
    RAPIDJSON_GET_JSON_BOOL(doc["USB"], "enable", mUsbConfig.enable);
    #endif
    expand_config_dump();
    return 0;
}

int32_t ExpandSet::expand_config_dump(void)
{
    logd("------------------%s dump---------------", EXPAND_JSON_PATH);
    for (int i=EX_RS485; i<EX_RS_MAX; i++) {
        logd("uart %d baud %d databit %d stopbit %d parity %s protocol %s enable %d"\
                , i
                , mUartConfig[i].baud
                , mUartConfig[i].databit
                , mUartConfig[i].stopbit
                , mUartConfig[i].parity.c_str()
                , mUartConfig[i].protName.c_str()
                , mUartConfig[i].enable);
    }

    logd("can1 baud %s protocol %s enable %d", \
            mCanConfig[CAN1].baud.c_str(), mCanConfig[CAN1].protName.c_str(), mCanConfig[CAN1].enable);
    logd("can2 baud %s protocol %s enable %d", \
            mCanConfig[CAN2].baud.c_str(), mCanConfig[CAN2].protName.c_str(), mCanConfig[CAN2].enable);

    logd("url: %s", mNetConfig.url.c_str());
    logd("ip: %s", mNetConfig.ip.c_str());
    logd("local_ip: %s", mNetConfig.ip.c_str());
    logd("port: %d", mNetConfig.port);
    logd("protocol: %s", mNetConfig.protName.c_str());
    logd("enable: %d", mNetConfig.enable);

    logd("usb protocl %s enable %d", mUsbConfig.protName.c_str(), mUsbConfig.enable);
    logd("------------------%s dump over-----------", EXPAND_JSON_PATH);

    return 0;
}




int32_t ExpandSet::start(void)
{
    load_expand_json();
    mLibflowServer = new LibflowServer("127.0.0.1", EXPAND_LIBFLOW_PORT, "MINIEYE.EXPAND.V1");
    mLibflowRcver = new LibflowRcver();
    if (mLibflowRcver && mLibflowServer) {
        mLibflowServer->add_receiver(mLibflowRcver);
    }

    for (int i = EX_RS485; i < EX_RS_MAX; i++) {
        if (mUartConfig[i].enable) {
            shared_ptr<Protocol> sp_prot;
            sp_prot.reset(protmanager::findProto(mUartConfig[i].protName));
            if(!sp_prot) {
                continue;
            }
            shared_ptr<ClientFd> client_fd;
            if(i == EX_RS485) {
                client_fd.reset(new RS485Client(sp_prot.get(), mUartConfig[i].baud));
                logd("init rs485 %s baud %d", uartDev[i], mUartConfig[i].baud);
            } else {
                client_fd.reset(new TTYClient(sp_prot.get(), uartDev[i], mUartConfig[i].baud));
                logd("init tty %s baud %d", uartDev[i], mUartConfig[i].baud);
            }
            logd("create proto %s", mUartConfig[i].protName.c_str());
            {
                std::lock_guard<std::mutex> Lock(expandDevsLock);
                mExpandDevs[mUartConfig[i].protName] = make_shared<ExpandDev>();
                mExpandDevs[mUartConfig[i].protName]->init(mUartConfig[i].protName, sp_prot, client_fd);
                mLibflowServer->add_receiver(sp_prot.get());
            }
            logd("setupCmdList proto %s", mUartConfig[i].protName.c_str());
        }
    }
    logd("end for protos");

    for (int i = CAN1; i <= CAN2; i++) {
        if (mCanConfig[i].enable) {
            shared_ptr<Protocol> sp_prot;
            sp_prot.reset(protmanager::findProto(mCanConfig[i].protName));
            if(!sp_prot) {
                continue;
            }
            shared_ptr<ClientFd> client_fd;
            client_fd.reset(new CanClient(sp_prot.get(), i, mCanConfig[i].baud));
            logd("create proto %s", mCanConfig[i].protName.c_str());
            {
                std::lock_guard<std::mutex> Lock(expandDevsLock);
                mExpandDevs[mCanConfig[i].protName] = make_shared<ExpandDev>();
                mExpandDevs[mCanConfig[i].protName]->init(mCanConfig[i].protName, sp_prot, client_fd);
                mLibflowServer->add_receiver(sp_prot.get());
            }
            logd("setupCmdList proto %s", mCanConfig[i].protName.c_str());
        }
    }

    // 启动LibflowServer
    mLibflowServer->start();

    return 0;
}

bool ExpandSet::isDevSupport(std::string name)
{
    
    std::lock_guard<std::mutex> Lock(expandDevsLock);
    auto iter = mExpandDevs.find(name);
    if (iter != mExpandDevs.end()) {
        return true;
    }
    return false;

    return 0;
}


int32_t ExpandSet::sendLibFlow(const char* topic, const char* data, size_t size)
{

    mLibflowServer->send(topic, data, size);
    return 0;
}


ExpandDev::ExpandDev()
{
    logd("ExpandDev +");
}

ExpandDev::~ExpandDev()
{
    logd("ExpandDev -");
}


int32_t ExpandDev::init(string name, shared_ptr<Protocol> prot, shared_ptr<ClientFd> client)
{
    if (name == "" || !prot || !client) {
        logd("ExpandDev init failed!!");
        return -1;
    }
    mName = name, 
    mProt = prot;
    mClient = client;
    mWorkLooper = make_shared<WorkLooper>(mName.c_str());
    mWorkLooper->addClient(mClient.get());
    mWorkLooper->start();
    mCmdListener.init(mName.c_str());
    mCmdListener.regCmd(new Cmdline(mProt.get(), "cmd"));
    prot->setupCmdList(mName.c_str());
    mCmdListener.startCmdListener();
    return 0;
}
void LibflowRcver::recv(const char* source,  // '\0' terminated string
                        const char* topic,   // any binary data
                        const char* data,    // any binary data
                        size_t size)     // < 2^32
{
    logd("topic %s!\n", topic);

    ExpandSet & es = ExpandSet::getInstance();
    es.sendData2dev(topic, data, size);
}

} /* end of minieye namespace */


