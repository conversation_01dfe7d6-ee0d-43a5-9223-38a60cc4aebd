#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <fcntl.h>

#include "cmdline.h"

Cmdline::Cmdline(Protocol *prot, const char *name)
    : LogCallProxyCmd(name)
    , m<PERSON><PERSON>(prot)
    , mName(name)
{
    
}

int Cmdline::onRunProt(SocketClient *c, int argc, char ** argv) {
    if (argc >= 2) {
        string ack;
        bool ret = mProt->runCmd(argc-1, &argv[1], ack);
        if (ret) {
            c->sendMsg(MMM_CMD_OK, ack.c_str(), false);
        } else {
            c->sendMsg(MMM_CMD_OPERATION_FAILED, ack.c_str(), false);
        }
        return 0;
    }
    
    return 0;
}

int Cmdline::onRunCommand(SocketClient *c, int argc, char ** argv)
{
    string usage = "\n";
    usage += mName;
    usage += " help\n";

    if (argc >= 2 && 0 == strcmp(mName.c_str(), argv[0])) {
       onRunProt(c, argc, argv); 
    }

    c->sendMsg(MMM_CMD_INVALID_CMD, usage.c_str(), false);
    return 0;
}


