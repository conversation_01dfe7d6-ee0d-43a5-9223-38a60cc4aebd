#ifndef __MINIEYE_WORK_LOOPER_H__
#define __MINIEYE_WORK_LOOPER_H__

#include <arpa/inet.h>
#include <netdb.h>
#include <string>
#include "transporter.h"

#include <sys/prctl.h>

using namespace std;

namespace minieye
{

class WorkLooper
    : public my::thread
{
public:
    WorkLooper(const char *name = "expand_poller")
        : mName(name)
    {

    }
    virtual ~WorkLooper(){};
    void addClient(ClientFd *res) { mClient.push_back(res); }
private:
    bool pollFd(void);
    bool mainloop(void);
    void run(void)
    {
        prctl(PR_SET_NAME, mName.c_str());
        while(!exiting())
        {
            mainloop();
        }
    }
private:
    string mName;
    vector<ClientFd *> mClient;
};

} //namespace minieye
#endif
