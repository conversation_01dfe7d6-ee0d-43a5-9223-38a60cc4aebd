#ifndef __MINIEYE_TRANS_SLOT_H__
#define __MINIEYE_TRANS_SLOT_H__

#include <arpa/inet.h>
#include <netdb.h>
#include <string>
#include <sys/prctl.h>
#include "transporter.h"
#include "protocol.h"


using namespace std;

namespace minieye
{


class RS485Client
    : public SocketPairClient
{
public:
    RS485Client(Protocol *p, uint32_t baudrate);
    bool onReConfig(int32_t socketpair);

private:
    uint32_t    mBaudrate;
    int32_t     mSocketpair;

};

class CanClient
    : public SocketPairClient
{
    public:
        CanClient(Protocol *p, int channelId, string baudrate);
        bool onReConfig(int32_t socketpair);
    private:
        string          mBaudrate;
        int32_t         mSocketpair = -1;
        uint32_t        mChannelId = 0;    
};
} //namespace minieye
#endif
