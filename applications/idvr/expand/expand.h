#ifndef __MINIEYE_EXPAND_H__
#define __MINIEYE_EXPAND_H__

#include <stdint.h>
#include "transporter.h"
#include "cmdline.h"
#include "CmdListener.h"
#include "worklooper.h"
#include "libflow.h"


using std::string;

namespace minieye {


typedef struct UartConf {
    UartConf()
        : enable(false)
    {
    }
    int baud;
    int databit;
    int stopbit;
    string parity;
    string protName;
    bool enable;
}UartConf;

typedef struct CanConf {
    CanConf()
        : enable(false)
    {
    }
    string baud;
    string protName;
    bool enable;
}CanConf;

typedef struct NetConf {
    NetConf()
        : enable(false)
    {
    }
    string url;
    string ip;
    uint16_t port;
    string local_ip;
    string protName;
    bool enable;
}NetConf;

typedef struct UsbConf {
    UsbConf()
        : enable(false)
    {
    }
    string protName;
    bool enable;
}UsbConf;

typedef enum {
    EX_RS485,
    EX_RS232_1,
    EX_RS232_2,
    EX_RS_MAX,
}UartE;
enum {
    CAN1,
    CAN2,
};


/************************
 * 外设
 * **********************/
class ExpandDev
{
    public:
        ExpandDev();
        virtual ~ExpandDev();
        int32_t init(string name, shared_ptr<Protocol> prot, shared_ptr<ClientFd> client);
        int32_t send(const char * data, size_t size)
        {
            if (mProt.get()) {
                mProt->msgEnque((void*)data, size);
            }
            return size;
        }

    private:
        CmdListener mCmdListener;
        shared_ptr<WorkLooper> mWorkLooper;
        shared_ptr<Protocol> mProt;
        shared_ptr<ClientFd> mClient;
        string mName;
};

class LibflowRcver
    : public flow::Receiver
{
    public:
        virtual void recv(const char* source,  // '\0' terminated string
                          const char* topic,   // any binary data
                          const char* data,    // any binary data
                              size_t size);    // < 2^32
};


/************************
 *外设合集
 * **********************/
class ExpandSet
    : public my::Singleton<ExpandSet>
{
    friend class my::Singleton<ExpandSet>;

    public:
        ExpandSet();
        virtual ~ExpandSet();
        int32_t start(void);
        int32_t sendLibFlow(const char* topic, const char* data, size_t size);
        int32_t sendData2dev(const char * protName, const char * data, size_t size)
        {
            auto pdev = mExpandDevs.find(protName);
            if (pdev != mExpandDevs.end()) {
                return pdev->second->send(data, size);
            }
            return -1;
        }
        bool isDevSupport(std::string name);

 
    private:

        int32_t load_expand_json(void);
        int32_t expand_config_dump(void);

    private:

        UartConf mUartConfig[EX_RS_MAX];
        CanConf mCanConfig[2];
        NetConf mNetConfig;
        UsbConf mUsbConfig;
        LibflowServer *mLibflowServer;
        LibflowRcver * mLibflowRcver;
        std::mutex expandDevsLock;
        map<string, shared_ptr<ExpandDev>> mExpandDevs;

        //PipeInterface mRs485Fd;
};




int32_t getC4Id(string &id);

} /* end of minieye namespace */

#endif

