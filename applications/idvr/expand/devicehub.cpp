#include <unistd.h>
#include <sys/file.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <termios.h>
#include "devicehub.h"
#include "expand.h"


using namespace minieye;

SINGLETON_STATIC_INSTANCE(DeviceHub);

int32_t DeviceHub::init(void)
{
    logi("DeviceHub init");
    mcuHandler = make_shared<McuMsgHandler>();
    mcuHandler->init(this);
    do {
        std::lock_guard<std::mutex> lg(mSockMutex);
        for (int32_t i = DEVICE_TYPE_UNKNOWN + 1; i < DEVICE_TYPE_MAX; i++) {
            mSock[i] = -1;
        }
    } while (0);
    my::thread::start();
    return 0;

}

int32_t DeviceHub::registerDevice(DeviceType type, int32_t socket)
{
    std::lock_guard<std::mutex> lg(mSockMutex);
    mSock[type] = socket;

    return 0;
}

int32_t DeviceHub::writeStream(DeviceType type, uint8_t *buf, const int32_t size)
{
    if (mSock[type] < 0) {
        loge("mSock[%d] error !", mSock);
        return -1;
    }
    McuMsgTypeE cmd = MCU_MSG_TYPE_INVALID;
    uint32_t maxPayloadSize = MCU_MSG_PAYLOAD_MAX_SIZE;
    uint32_t remain = size;
    uint32_t offset = 0;

    if (type == DEVICE_TYPE_485) {
        cmd = MCU_MSG_TYPE_RS485;
    } else if (type == DEVICE_TYPE_CAN0) {
        cmd = MCU_MSG_TYPE_RAW_CAN0;
        maxPayloadSize = sizeof(canFrame_t); /*一帧帧发送，规避mcu不处理粘包导致丢包问题*/
    } else if (type == DEVICE_TYPE_CAN1) {
        cmd = MCU_MSG_TYPE_RAW_CAN1;
        maxPayloadSize = sizeof(canFrame_t);
    }
    /* 每次发送不能超过 MCU_MSG_PAYLOAD_MAX_SIZE */
    do {
        int32_t len = remain <= maxPayloadSize ? remain : maxPayloadSize;
        mcuHandler->comm_send(cmd, &buf[offset], len);
        offset += len;
        remain = remain - len;
    } while (remain > 0);

    return size;
}

bool DeviceHub::setBuadRate(DeviceType type, uint32_t baudrate)
{
    if (mSock[type] < 0) {
        loge("mSock[%d] error !", mSock);
        return false;
    }
    McuMsgTypeE cmd = MCU_MSG_TYPE_INVALID;

    if (type == DEVICE_TYPE_485) {
        cmd = MCU_MSG_TYPE_RS485_CONFIG;
    }
    UrtpUartConfT conf(baudrate, 1, 'N');
    
    logd("send buadrate, %d", baudrate);
    mcuHandler->comm_send(cmd, (uint8_t *)&conf, sizeof(UrtpUartConfT));

    return true;
}

bool DeviceHub::setCanMode(DeviceType type, std::string baudrate, std::string mode)
{
    if (mSock[type] < 0) {
        loge("mSock[%d] error !", type);
        return false;
    }
    McuMsgTypeE cmd = MCU_MSG_TYPE_CONFIG_CAN;
    McuMsgCanConfigT conf;
    if (DEVICE_TYPE_CAN0 == type) {
        conf.canx = MCU_CAN_IDX_CAN0_SPEED;
    } else if (DEVICE_TYPE_CAN1 == type) {
        conf.canx = MCU_CAN_IDX_CAN1_DISP;
    } else {
        logd("type %d invalid!\n", type);    
        return false;    
    }
    if (baudrate == "1M" || baudrate == "1m") {
        conf.speed = MCU_CAN_SPEED_1M;
    } else if (baudrate == "800K" || baudrate == "800k") {
        conf.speed = MCU_CAN_SPEED_800K;
    } else if (baudrate == "500K" || baudrate == "500k" ) {
        conf.speed = MCU_CAN_SPEED_500K;
    } else if (baudrate == "250K" || baudrate == "250k") {
        conf.speed = MCU_CAN_SPEED_250K;
    } else if (baudrate == "125K" || baudrate == "125k") {
        conf.speed = MCU_CAN_SPEED_125K;
    } else if (baudrate == "100K" || baudrate == "100k") {
        conf.speed = MCU_CAN_SPEED_100K;
    } else if (baudrate == "50K" || baudrate == "50k") {
        conf.speed = MCU_CAN_SPEED_50K;
    } else if (baudrate == "20K" || baudrate == "20k") {
        conf.speed = MCU_CAN_SPEED_20K;
    } else if (baudrate == "10K" || baudrate == "10k") {
        conf.speed = MCU_CAN_SPEED_10K;
    } else if (baudrate == "5K" || baudrate == "5k") {
        conf.speed = MCU_CAN_SPEED_5K;
    } else {
        logd("send buadrate, %s failed!", baudrate.c_str());    
        return false;
    }
    if (mode == "normal") {
        conf.mode = MCU_CAN_WORKING_MODE_NORMAL;
    } else {
        conf.mode = MCU_CAN_WORKING_MODE_SILENT;
    }
    conf.use_json = 0;
    mcuHandler->comm_send(cmd, (uint8_t *)&conf, sizeof(McuMsgCanConfigT));
    return true;
}
bool DeviceHub::setCanFilter(DeviceType type,uint32_t canId) {
    if (mSock[type] < 0) {
        loge("mSock[%d] error !", mSock);
        return false;
    }
    McuMsgTypeE cmd = MCU_MSG_TYPE_INVALID;
    if (0 == canId) {
        cmd = MCU_MSG_TYPE_CLEAR_ALL_FILTER;
        mcuHandler->comm_send(cmd, NULL, 0);
    } else {
        cmd = MCU_MSG_TYPE_SET_CAN_FILTER;
         McuMsgCanFilterT filter;
        if (DEVICE_TYPE_CAN0 == type) {
            filter.canIdx = MCU_CAN_IDX_CAN0_SPEED;
        } else if (DEVICE_TYPE_CAN1 == type) {
            filter.canIdx = MCU_CAN_IDX_CAN1_DISP;
        } else {
            logd("type %d invalid!\n", type);
            return false;    
        }
        filter.canIds[0] = canId;
        filter.useListMode = true;
        mcuHandler->comm_send(cmd, (uint8_t *)&filter, sizeof(McuMsgCanFilterT));
    }
    return true;
}

bool DeviceHub::setCanFilter(DeviceType type,uint32_t canId[], uint32_t canIdNum) {
    if (mSock[type] < 0) {
        loge("mSock[%d] error !", mSock);
        return false;
    }
    McuMsgTypeE cmd = MCU_MSG_TYPE_INVALID;
    if (0 == canId[0] || 0 == canIdNum) {
        cmd = MCU_MSG_TYPE_CLEAR_ALL_FILTER;
        mcuHandler->comm_send(cmd, NULL, 0);
    } else {
        cmd = MCU_MSG_TYPE_SET_CAN_FILTER;
         McuMsgCanFilterT filter;
        if (DEVICE_TYPE_CAN0 == type) {
            filter.canIdx = MCU_CAN_IDX_CAN0_SPEED;
        } else if (DEVICE_TYPE_CAN1 == type) {
            filter.canIdx = MCU_CAN_IDX_CAN1_DISP;
        } else {
            logd("type %d invalid!\n", type);    
            return false;    
        }
        if (canIdNum > CAN_ID_ARRAY_SIZE) {
            canIdNum = CAN_ID_ARRAY_SIZE;
        }
        for (int i = 0; i < canIdNum; i++) {
            filter.canIds[i] = canId[i];
        }
        filter.useListMode = true;
        mcuHandler->comm_send(cmd, (uint8_t *)&filter, sizeof(McuMsgCanFilterT));
    }
    return true;
}

bool DeviceHub::setGpsLbs(LBS lbs)
{
    McuMsgTypeE cmd = MCU_MSG_TYPE_F9KGPS_LIBS;
    uint8_t buf[MCU_MSG_MAX_SIZE] = {0};
    size_t size = sizeof(lbs);
    mcuHandler->comm_send(cmd, (uint8_t *)&lbs, size);
    mLBS = lbs;
    return true;
}

bool DeviceHub::getGpsLbs(LBS & lbs)
{
    std::lock_guard<std::mutex> _l(mMutex);
    lbs = mLBS;
    return true;
}
void DeviceHub::setIOStatus(IOStatus ioStatus)
{
    std::lock_guard<std::mutex> _l(mMutex);
    mIOStatus = ioStatus;
}

void DeviceHub::getIOStatus(IOStatus& ioStatus)
{
    std::lock_guard<std::mutex> _l(mMutex);
    ioStatus = mIOStatus;
}

void DeviceHub::setSysStatus(SysStatus_t sys)
{
    std::lock_guard<std::mutex> _l(mMutex);
    mSys = sys;
}

void DeviceHub::getSysStatus(SysStatus_t& sys)
{
    std::lock_guard<std::mutex> _l(mMutex);
    sys = mSys;
}

void DeviceHub::setRecordStatus(int32_t record)
{
    std::lock_guard<std::mutex> _l(mMutex);
    mRecord = record;
}

void DeviceHub::getRecordStatus(int32_t & status)
{
    std::lock_guard<std::mutex> _l(mMutex);
    status = mRecord;
}

void DeviceHub::setMcuMsgStat(McuMsgMcuStatT s)
{
    std::lock_guard<std::mutex> _l(mMutex);
    mMcuMsgStat = s;
}
void DeviceHub::getMcuMsgStat(McuMsgMcuStatT & s)
{
    std::lock_guard<std::mutex> _l(mMutex);
    s = mMcuMsgStat;
}

int32_t DeviceHub::onSocketDataRecv(DeviceType type)
{
    uint8_t buf[1024];

    std::lock_guard<std::mutex> lg(mSockMutex);
    int32_t got = read(mSock[type], buf, sizeof(buf));
    if (got > 0) { /*recv data*/
        writeStream(type, buf, got);
        logd("write stream type %d %s\n", type, my::hex(my::constr((const char *)buf, got)).c_str());

        // for loop test case
        // onDeviceHubRecv(type, buf, got);
    } else if (!got || ((got < 0) && (errno != EINTR && errno != EAGAIN && errno != EWOULDBLOCK))) { /*close*/
        if (!got) {
            loge("socket close: got = %d\n", got);
        }
        if (got == -1) {
            loge("socket error: %s, got = %d\n", strerror(errno), got);
        }
        mSock[type] = -1;
        return -1;
    } else {
        loge("socket %d : %s", mSock[type], strerror(errno));
        usleep(10);
    }
    return 0;
}


void DeviceHub::run()
{
    while (!exiting()) {

        struct timeval timeout = {0, 10000};
        fd_set rfds, wfds;
        FD_ZERO(&rfds);
        FD_ZERO(&wfds);
        int32_t fdMax = -1;

        for (int32_t i = DEVICE_TYPE_UNKNOWN + 1; i < DEVICE_TYPE_MAX; i++) {
            if (mSock[i] > 0) {
                FD_SET(mSock[i], &rfds);
                if (mSock[i] > fdMax) {
                    fdMax = mSock[i];
                }
            }
        }

        // 如果还没有设备注册
        if (fdMax < 0) {
            usleep(100 * 1000);
            continue;
        }

        int32_t ret = select(fdMax + 1, &rfds, NULL, NULL, &timeout);
        if (ret < 0) {
            loge("selected failed %s", strerror(errno));
            // return false;
        } else if (ret == 0) {
            // timeout!
        } else {
            //logd("select ret %d...........", ret);
            for (int32_t i = DEVICE_TYPE_UNKNOWN + 1; i < DEVICE_TYPE_MAX; i++) {
                if (FD_ISSET(mSock[i], &rfds)) {
                    logd("on data recv fd %d ...........", mSock[i]);
                    onSocketDataRecv(DeviceType(i));
                }
            }
        }
    }
}


int32_t DeviceHub::onDeviceHubRecv(DeviceType type, uint8_t *buf, const int32_t size)
{
    if (mSock[type] < 0 || size < 1) {
        return -1;
    }

    if (DEVICE_TYPE_CAN0 == type || DEVICE_TYPE_CAN1 == type) {
#if 0        
        uint8_t send[size + sizeof(IpcMessage)] = {0};
        McuMessage *pMsg = (McuMessage *)&(send[0]);
        if (DEVICE_TYPE_CAN0 == type) {
            pMsg->ipcMsg.type = MCU_MSG_TYPE_RAW_CAN0;
        } else if (DEVICE_TYPE_CAN1 == type) {
            pMsg->ipcMsg.type = MCU_MSG_TYPE_RAW_CAN1;
        }
        pMsg->ipcMsg.len = size;
        memcpy(pMsg->u.uchar, buf, size);
#else
        uint8_t send[sizeof(canFrame_t)] = {0};
        canFrame_t *pFrame = (canFrame_t *)&(send[0]);
        McuMsgCanT *pCanData = (McuMsgCanT *)buf;
        pFrame->frameId = pCanData->id;
        pFrame->len = pCanData->len;
        memcpy(pFrame->data, pCanData->data, 8);
#endif

        int32_t writen = write(mSock[type], send, sizeof(send));
        if (writen != sizeof(send)) {
            loge("write fd %d error writen %d!", mSock[type], writen);
            return -2;
        }
    } else {
        int32_t writen = write(mSock[type], buf, size);
        if (writen != size) {
            loge("write fd %d error writen %d!", mSock[type], writen);
            return -2;
        }
    }

    logd("send device type %d %s\n", type, my::hex(my::constr((const char *)buf, size)).c_str());
    
    return 0;
}

bool DeviceHub::McuMsgHandler::on_recv(McuMessage * msg)
{
    switch(msg->ipcMsg.type) {
        case MCU_MSG_TYPE_RS485: {
            mDeviceHub->onDeviceHubRecv(DEVICE_TYPE_485, msg->u.uchar, msg->ipcMsg.len);
            break;
        }
        case MCU_MSG_TYPE_STAT: {
            if (MCU_MSG_SIZE_STAT == msg->ipcMsg.len) {
                // for speedled
                mDeviceHub->setCarSpeed(msg->u.stat[0].speed_x10 / 10.0);
                mDeviceHub->setTurnRight(msg->u.stat[0].turnr);
                mDeviceHub->setTurnLeft(msg->u.stat[0].turnl);
                // for f9k display
                mDeviceHub->setIOStatus(msg->u.stat[0].vehicle_io);
                mDeviceHub->setSysStatus(msg->u.stat[0].sys);
                // for alinkalertor
                McuMsgCarInfoT &carInfo = msg->u.carInfo[0];
                mDeviceHub->setReverse(carInfo.canReverse);

                mDeviceHub->setMcuMsgStat(msg->u.stat[0]);
            }
            break;
        }
        case MCU_MSG_TYPE_RAW_CAN0: {
            mDeviceHub->onDeviceHubRecv(DEVICE_TYPE_CAN0, msg->u.uchar, msg->ipcMsg.len);
            break;
        }
        case MCU_MSG_TYPE_RAW_CAN1: {
            mDeviceHub->onDeviceHubRecv(DEVICE_TYPE_CAN1, msg->u.uchar, msg->ipcMsg.len);
            break;
        }
        case MCU_MSG_TYPE_RECORD_STATUS: {
            mDeviceHub->setRecordStatus((int32_t)msg->u.int32[0]);
            break;
        }
        default: {
            break;
        }
    }
    return true;
}

