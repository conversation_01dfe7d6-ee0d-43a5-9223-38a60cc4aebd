#include <sys/types.h> 
#include <sys/wait.h>
#include <sys/prctl.h>
#include "properties.h"


#include "mystd.h"
#include "expand.h"
#include "devicehub.h"
#include "idvrProperty.h"

static bool isSystemWorkMode()
{
    char propValue[PROP_VALUE_MAX] = {0};
    memset(propValue, 0, sizeof(propValue));
    int len = __system_property_get(PROP_RW_MINIEYE_WORK_MODE, propValue);
    const char *p = propValue;
    if(!strcmp(p, "normal")) {
        return true;
    }

    return false;
}

__attribute((constructor)) void before_main()
{
    __system_properties_init();
}


int main(int argc, char** argv)
{
    trace_signal(SIGSEGV);
    trace_signal(SIGFPE);
    trace_signal(SIGABRT);

    bool runFlag = isSystemWorkMode();

    if (runFlag) {
        DeviceHub::getInstance().init();
        ExpandSet::getInstance().start();
    }


    while(true) {
        usleep(1000 * 1000 * 1000);
    }
	return 0;
}

