#include <unistd.h>
#include <sys/file.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <termios.h>
#include "transporter.slot.h"
#include "devicehub.h"

using namespace minieye;


RS485Client::RS485Client(Protocol *p, uint32_t baudrate)
    : SocketPairClient(p)
    , mBaudrate(baudrate)
    , mSocketpair(-1)
{
    logi("RS485Client() %d", mBaudrate);
}

bool RS485Client::onReConfig(int32_t socketpair)
{
    mSocketpair = socketpair;

    /* 注册485设备监听和回调数据 */
    DeviceHub::getInstance().registerDevice(DeviceHub::DEVICE_TYPE_485, mSocketpair);

    //TODO: 设置baudrate
    DeviceHub::getInstance().setBuadRate(DeviceHub::DEVICE_TYPE_485, mBaudrate);
    
    return true;
}

CanClient::CanClient(Protocol *p, int channelId, string baudrate)
    : SocketPairClient(p)
    , mBaudrate(baudrate)
    , mChannelId(channelId)
    , mSocketpair(-1)
{
    logi("CanClient() %d", channelId);
    p->setChannelId(channelId);
}
bool CanClient::onReConfig(int32_t socketpair)
{
    mSocketpair = socketpair;
    if (0 == mChannelId) {
        DeviceHub::getInstance().registerDevice(DeviceHub::DEVICE_TYPE_CAN0, mSocketpair);
    } else {
        DeviceHub::getInstance().registerDevice(DeviceHub::DEVICE_TYPE_CAN1, mSocketpair);
    }
    return true;
}
