    
executable("expand") {
    sources = [
        "main.cpp",
        "cmdline.cpp",
        "devicehub.cpp",
        "expand.cpp",
        "transporter.cpp",
        "transporter.slot.cpp",
        "worklooper.cpp",
        "prot/algoOutputCanProt.cpp",
        "prot/alertor.cpp",
        "prot/displed.cpp",
        "prot/speedled.cpp",
        "prot/tahsensor.cpp",
        "prot/tpms.haoyue.cpp",
        "prot/weightSensor.cpp",
        "prot/f9kGPS.cpp",
        "prot/zfRadar.cpp",
        "prot/f9kDisp.cpp",
        "prot/f9kGpsNovatelMsg.cpp",
        "prot/Display.xunjian.cpp",
        "prot/canDisplay.cpp",
        "prot/FuelMeter.ChangRun.cpp",
        "prot/FuelMeter.FuTai.cpp",
        "prot/LiquidMeter.FuTai.cpp",
        "prot/WeightSensor.FuTai.cpp",
        "prot/AnKaiCanProt.cpp",
        "prot/passenger.flow.meter.cpp",
        "prot/bsdAlert.XiaMenCiBei.cpp",
        "prot/SuZhouJinLong.cpp",
        "prot/AlinkAlertor.RongSheng.cpp",
        "prot/LongAnAlertor.RongSheng.cpp",
        "prot/BsdCanInfo.RongSheng.cpp",
        "prot/AlgoCanDisplay.cpp",
        "prot/NormalCan1Out.cpp",
    ]

    include_dirs = [
        ".",
        "//applications/protocol/prot.jtt808-1078/io.service",
        "//applications/protocol/algo",
        "//applications/idvr/expand/prot/",
        "//applications/idvr/include/idvr",
        "//third_party/rapidjson-1.1.0/",
        "//third_party/nlohmann_json/",
        "//third_party/msgpack-c/include/",
        "//foundation/communication/libflow/include/",
        "//foundation/communication/libflow/flowWrap/",
        "//foundation/communication/message/",
        "//foundation/communication/property/",
    ]


    deps = [
        "//applications/protocol/algo",
        "//foundation/base/core/mystd",
        "//foundation/base/core/filelog",
        "//foundation/base/service/tts/libtts",
        "//foundation/base/core/nmea_decode:nmea_decode",
        "//foundation/communication/ipcAgent",
        "//foundation/communication/ipcAgent",
        "//foundation/communication/socketcmd",
        "//foundation/communication/ringbuf",
        "//foundation/communication/libflow:flowWrap",
        "//foundation/communication/property:property",
        "//foundation/communication/message:message",
        "//foundation/communication/libflow:flowWrap",
        "//third_party/msgpack-c:msgpackc",
    ]

    cflags_cc = [
        "-Wno-unused-parameter",
        "-Wno-psabi",
        "-std=gnu++2a",
    ]

    defines = [ 
        "LOG_TAG_STR=${target_name}",
     ]

    libs = [

     ]

    sigmastar_sdk_dir = rebase_path("//applications/vendor/8838_sdk4/lib/share/")
    ldflags = [
        "-L${sigmastar_sdk_dir}/opencv_9.1.0",
        "-lopencv_imgcodecs",
        "-lopencv_imgproc",
        "-lopencv_core",
        "-ldl",
        "-pthread",
    ]
}
