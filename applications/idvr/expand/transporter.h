#ifndef __MINIEYE_TRANS_H__
#define __MINIEYE_TRANS_H__

#include <arpa/inet.h>
#include <netdb.h>
#include <string>
#include "protocol.h"
#include <sys/prctl.h>

using namespace std;

namespace minieye
{

class ClientFd
{
    public:
        ClientFd(Protocol *prot, int32_t fd)
            : mFd(fd)
            , mProt(prot)
            , mConnected(false)
            , mPendingMsg(NULL)
            , mLastRecvDataTime(0)
            , mLastTryConnectTime(0)
        {
        }

        Protocol            *mProt;
        int32_t             mFd;
        bool                mConnected;
        Protocol::Message * mPendingMsg;
        int32_t             mLastRecvDataTime;
        int32_t             mLastTryConnectTime;

        bool onDataRecv(void);
        bool sendQueNotEmpty(void);
        bool onRecvTimeOut(void);
        int readData(void * buf, int len){
            return read(mFd, buf, len);
        }
        void sendOutputMessage(void);
        virtual bool onConnect(void)
        {
            mConnected = true;
            return true;
        }
};

class TcpClient
    : public ClientFd
{
public:
    TcpClient(Protocol *p, const char *server, uint16_t port);
    bool onConnect(void);

private:
    string      mServer;
    uint16_t    mPort;
};

class TTYClient
    : public ClientFd
{
public:
    TTYClient(Protocol *p, const char *ttyDev, uint32_t baudrate);
    bool onConnect(void);
    bool setBaudrate(int speed);

private:
    string      mTTYDev;
    uint32_t    mBaudrate;
};


class SocketPairClient
    : public ClientFd
{
public:
    SocketPairClient(Protocol *p);
    bool onConnect(void);
    virtual bool onReConfig(int32_t socketpair)
    {
        return true;
    }

private:
    int32_t mPairFds[2]; // SocketPair
};

} //namespace minieye
#endif
