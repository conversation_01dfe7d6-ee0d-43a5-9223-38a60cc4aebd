# DFH2 外设说明

### DFH2 接口板

  1.  2路RS232

  2.  1路RS485

  3.  2路CAN

  4.  1路IPC

  5.  1路USB host

​     

### 接口可配置

  ```json
  {
       "RS485":{
           "baudrate":115200,
           "databit":8,
           "stopbit":1,
           "parity":"none",
           "protocol":"alertor",
           "enable":false
       },  
       "RS232_1":{
           "baudrate":115200,
           "parity":"none",
           "databit":8,
           "stopbit":1,
           "protocol":"alertor",
           "enable":true
       },  
       "RS232_2":{
           "baudrate":115200,
           "parity":"none",
           "databit":8,
           "stopbit":1,
           "protocol":"alertor",
           "enable":false
       },  
       "CAN1":{
           "baudrate":"500k",
           "protocol":"alertor",
           "enable":false
       },  
       "CAN2":{
           "baudrate":"500k",
           "protocol":"alertor",
           "enable":false
       },  
       "NET":{
           "url":"setup.minieye.cc",
           "ip":"***********",
           "port":10240,
           "local_ip":"*************",
           "protocol":"alertor",
           "enable":false
       },  
       "USB":{
           "protocol":"alertor",
           "enable":false
       }   
   }
  
  ```

  

### 代码框架

  ```cpp
  Transporter.h
  
  /× 创建FD ×/
  class ClientFd
   {
       public:
           ClientFd(Protocol *prot, int32_t fd);
           bool onDataRecv(void);
           bool sendQueNotEmpty(void);
           bool onRecvTimeOut(void);
           int readData(void * buf, int len);
           void sendOutputMessage(void);
           virtual bool onConnect(void);
   };
   
   /× 创建QueBuf ×/
   class ClientBuf
   {
   public:
       ClientBuf(Protocol *p);
       bool polling(void);
       MsgArray mArray;
       Protocol *mProt;
   };
  
  /× 对多路ClientFd, ClientBuf轮循 ×/
  class FdSelectIO
       : public my::thread
   {
   public:
       FdSelectIO(const char *name = "expand_poller");
       virtual ~FdSelectIO(){};
       void addClient(ClientFd *res) { mClient.push_back(res); }
       void addClient(ClientBuf *res) { mBuf.push_back(res); }
   private:
       bool pollFd(void);
       bool pollBuf(void);
       bool mainloop(void);
       void run(void);
   public:
       vector<ClientBuf *> mBuf;
   private:
       string mName;
       vector<ClientFd *> mClient;
   };
  
  /× 协议基类 ×/
  class Protocol
   {
   public:
       Protocol(void)
       virtual ~Protocol()
       virtual const char * name();
       virtual string info() = 0;
       virtual string trigger();
       
       /* interface */
       virtual int32_t onServerConnected(void) = 0;
       virtual int32_t onServerDisconnected(void) = 0;
       virtual int32_t onDataRecevied(const char *p, uint32_t len)=0;
  }
  
  ```

  

### 声光报警器协议

  ```cpp
  /×指令定义×/
  #define ATCMD_VOL_GET              0x20
  #define ATCMD_VOL_SET              0x21
  #define ATCMD_LED_LUMI             0x43
  #define ATCMD_LED_FRE              0x44 
  #define ATCMD_WRITE_MUSIC          0x60
   /* 报警指令，持续4秒后停止 */
  #define ATCMD_AUDIO_LED_START      0x80
  #define ATCMD_AUDIO_LED_STOP       0x81 
  #define ATCMD_GET_VERSION          0x90
  #define ATCMD_MCU_UPGRADE          0xA0
  
  /× 通信协议 ×/
  typedef struct {
       uint16_t    magic;
       uint8_t     cmd;
       uint8_t     len;
       uint8_t     payload[0];
       //uint8_t     checksum;
  } __attribute__((packed)) UartProtHeaderT;
  
  /× 协议类 ×/
  class Alertor : public Protocol
   {
       public:
           Alertor(void);
           ~Alertor();
           string info(void);
           bool upgradeInit(void);
           bool sendMusicFileInfo(void);
           /* 发送命令 */
           string runCmd(int argc, char **argv);
           int32_t onServerConnected(void);
           int32_t onServerDisconnected(void);
           int32_t onDataRecevied(const char *p, uint32_t len);
   }
  
  ```

### 报警响应过程

```sequence
  Title:alertor
  host -> stm32: alert cmd
  Note right of stm32: blink
  stm32 -> audioMcu: play audio cmd
```



### 升级过程




  ```sequence
  Title:alertor upgrade seq
  host -> alertor: reset
  Note right of alertor: enter bootloader
alertor --> host: boot frame
  host -> alertor:  frame
  alertor -> host: save and ack
  host -> alertor:  last frame
  Note right of alertor: check crc and copy to boot zone
  alertor --> host: resart
  ```

  

