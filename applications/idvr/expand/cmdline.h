#ifndef __EXPAND_CMD_LISTENER_H__
#define __EXPAND_CMD_LISTENER_H__

#include <time.h>
#include <string>


#include "CmdListener.h"
#include "protocol.h"

using namespace minieye;
#define EXPAND_CMD_SOCKET_NAME "expand"
#define INVALID_PROT_CMD -1
typedef struct CmdStr {
    const char * mName;
    uint32_t mCmd;
    const char * mText;

    CmdStr(const char * n = NULL, uint32_t c = 0, const char * t = NULL)
    {
        mName = n;
        mCmd  = c;
        mText = t;
    }

    static uint32_t strToCmd(const std::vector<struct CmdStr> & cmdTbl, const char *str)
    {
        for(auto c : cmdTbl) {
            if(!strcmp(c.mName, str)) {
                return c.mCmd;
            }
        }

        return INVALID_PROT_CMD;
    }

    static std::string setupCmdList(const std::vector<struct CmdStr> & cmdTbl, const char * protName)
    {
        std::string usage = "\n";

        for (auto c : cmdTbl) {
            usage += protName;
            usage += " ";
            usage += c.mName;
            usage += " - ";
            if (c.mText) {
                usage += c.mText;
            } else {
                usage += c.mName;
                usage += "\n";
            }
            LogCallProxyCmd::setupCmd(protName, "cmd", c.mName);
        }

        return usage;
    }
} CmdStrT;
class Cmdline 
    : public LogCallProxyCmd
{
public:

    Cmdline(Protocol *prot, const char *name);
    virtual ~Cmdline() {};
    int onRunCommand(SocketClient *c, int argc, char ** argv);

    Protocol *mProt;
private:
    int onRunProt(SocketClient *c, int argc, char ** argv);
    string mName;
};

#endif
