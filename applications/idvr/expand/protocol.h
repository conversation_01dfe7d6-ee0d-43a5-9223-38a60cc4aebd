#ifndef __MINIEYE_PROTOCOL_H__
#define __MINIEYE_PROTOCOL_H__

#include <stdio.h>
#include <stdint.h>
#include <list>
#include <vector>
#include <mutex>
#include <assert.h>
#include <unistd.h>
#include <functional>

#include "mystd.h"
#include "CmdListener.h"
#include "flow.hpp"

using namespace std;
namespace minieye
{
#define Mutex std::mutex
#define AutoMutex std::lock_guard<std::mutex>
#define _l lck
#define HEART_BEAT_THRESHOLD  (120)

class Protocol
    : public flow::Receiver
{
public:

    Protocol(void)
    {
        logd("Protocl +");
    }
    virtual ~Protocol()
    {
        logd("~Protocl +");
    }
    virtual const char * name() {
        return "protocol";
    }

    class Message
    {
    public:
        Message(void *p, uint32_t len)
            : mLen(len)
            , mBuf(NULL)
            , mSent(0)
        {
            assert(len <= 64 * 1024);
            mBuf = malloc(len);
            memcpy(mBuf, p, len);
        }
        ~Message(void)
        {
            if (mBuf) {
                free(mBuf);
            }
        }
        void * data(void)
        {
            return mBuf;
        }
        uint32_t len(void)
        {
            return mLen;
        }

        bool send2Fd(int32_t fd, int32_t &err)
        {
            if (mSent >= mLen) {
                loge("Message already send over");
                return true;
            }
            while (1) {
                int32_t writen = write(fd, static_cast<uint8_t *>(mBuf) + mSent, mLen - mSent);
                if (writen < 0) {
                    err = errno;
                    if (errno == EINTR || errno == EAGAIN)
                        continue;
                    else
                        return false;
                }
                mSent += writen;
                if (mSent >= mLen) {
	                //logd("send %s\n", my::hex(my::constr((const char *)mBuf, mSent)).c_str());
                    //loge("send msg over.....");
                    return true;
                }
            }
            return false;
        }
    private:
        uint32_t    mLen;
        void        *mBuf;
        uint32_t    mSent;
    };

    /* interface */
    virtual int32_t onServerConnected(void) = 0;
    virtual int32_t onServerDisconnected(void) = 0;
    virtual int32_t onDataRecevied(const char *p, uint32_t len) = 0;
    //virtual bool onMessage(uint8_t *p, uint32_t len) = 0;

    virtual std::string setupCmdList(const char * cmdName) = 0;
    /* socketcmd线程，返回值为应答 */ 
    virtual  bool runCmd(int argc, char **argv, string &ack) = 0;
	virtual  uint32_t getTimeOutVal(void) 
	{
		return HEART_BEAT_THRESHOLD;
	}
    virtual void setChannelId(int channelId){;}
    virtual uint32_t messageNum(void)
    {
        AutoMutex _l(mOutputMsgListLock);
        return mOutputMsgList.size();
    }

    virtual Message * msgDeque(void)
    {
        AutoMutex _l(mOutputMsgListLock);
        if (0 == mOutputMsgList.size()) {
            return NULL;
        }
        Message *m = mOutputMsgList.front();
        mOutputMsgList.pop_front();
        return m;
    }
    void msgQueueClear(void){
        AutoMutex _l(mOutputMsgListLock);
        Message *m = NULL;
        while(mOutputMsgList.size()){
            logd("list clear erase");
            mOutputMsgList.pop_front();
            delete m;
        }
    }
    /*libflow recv*/
    virtual void recv(const char* source,  // '\0' terminated string
                    const char* topic,   // any binary data
                    const char* data,    // any binary data
                    size_t size) {    // < 2^32
                    }

//protected:
    void msgEnque(void *p, uint32_t len, bool front = false)
    {
        Message *m = new Message(p, len);
        AutoMutex _l(mOutputMsgListLock);
        if (front) {
            mOutputMsgList.push_front(m);
        } else {
            mOutputMsgList.push_back(m);
        }
    }

private:
    std::mutex           mOutputMsgListLock;
    list<Message *>      mOutputMsgList;
};

// 创建协议对象
template<class _type_>
static Protocol * protConstructor(void)
{
    return new _type_();
}
typedef std::function<Protocol* (void)> ProtConstructorFn;

} //namespace minieye
#endif

