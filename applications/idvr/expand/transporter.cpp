#include <unistd.h>
#include <sys/file.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <termios.h>
#include "transporter.h"

using namespace minieye;

void ClientFd::sendOutputMessage(void)
{
    Protocol::Message *m = NULL;
    if (mPendingMsg) {
        m = mPendingMsg;
    } else {
        m = mProt->msgDeque();
    }
    if (!m) {
        loge("No message available");
        return;
    }
    int32_t err = 0;
    bool done = m->send2Fd(mFd, err);
    if (0 != err) {
        loge("Write to %d failed %s", mFd, strerror(err));
        if (EPIPE == err) {
            mProt->onServerDisconnected();
            mConnected = false;
            close(mFd);
            mFd = -1;
        }
    }
    if (done && mPendingMsg) {
        mPendingMsg = NULL;
    }
    if (!done && !mPendingMsg) {
        mPendingMsg = m;
    }
    if (done) {
        delete m;
    }
}

bool ClientFd::onRecvTimeOut(void)
{
    int val = my::timestamp::system_seconds() - mLastRecvDataTime;
    if (mProt && (val > mProt->getTimeOutVal())) {
        mConnected = false;
        close(mFd);
        mFd = -1;
        mProt->onServerDisconnected();
        logd("client recv data timetout %d sec!\n", val);
        return true;
    }
    return false;
}

bool ClientFd::onDataRecv(void)
{
    uint8_t buf[4 * 1024];
    mLastRecvDataTime = my::timestamp::system_seconds();;
    int32_t got = readData(buf, sizeof(buf));
    if (got > 0) { /*recv data*/
        mProt->onDataRecevied((const char *)buf, got);
    } else if (!got || ((got < 0) && (errno != EINTR && errno != EAGAIN && errno != EWOULDBLOCK))) { /*close*/
        if (!got) {
            loge("socket close: got = %d\n", got);
        }
        if (got == -1) {
            loge("socket error: %s, got = %d\n", strerror(errno), got);
        }
        mConnected = false;
        close(mFd);
        mProt->onServerDisconnected();
        mFd = -1;
        return false;
    } else {
        loge("socket %d : %s", mFd, strerror(errno));
        usleep(10);
    }
    
    return true;
}

bool ClientFd::sendQueNotEmpty(void)
{
    return (mProt->messageNum() > 0); 
}

TTYClient::TTYClient(Protocol *p, const char *ttyDev, uint32_t baudrate)
    : ClientFd(p, open(ttyDev, O_RDWR | O_NOCTTY | O_NDELAY))
    , mTTYDev(ttyDev)
    , mBaudrate(baudrate)
{
}

bool TTYClient::setBaudrate(int speed)
{
    int speed_arr[] = {B115200, B57600, B38400, B19200, B9600, B4800, B2400, B1200, B600};
    int name_arr[] = {115200, 57600, 38400, 19200, 9600, 4800, 2400, 1200, 600};

    uint32_t i;
    int status;
    struct termios Opt;

    if (mFd < 0) {
        loge("tty %s open failed", mTTYDev.c_str());
        return false;
    }

    tcgetattr(mFd, &Opt);
    if (tcgetattr( mFd,&Opt)  !=  0) {
        loge("tcgetattr error");
        return false;
    }
    for (i= 0; i < sizeof(speed_arr) / sizeof(int); i++) {
        if( speed == name_arr[i]) {
            loge("set mBaudrate = %d", speed);
            tcflush(mFd, TCIOFLUSH);
            cfsetispeed(&Opt, speed_arr[i]);
            cfsetospeed(&Opt, speed_arr[i]);
            status = tcsetattr(mFd, TCSANOW, &Opt);
            if  (status != 0){
                perror("tcsetattr error");
            }
            tcflush(mFd,TCIOFLUSH);
            loge("set speed %d ok.\n", speed);
            return true;
        }
    }
    tcflush(mFd,TCIOFLUSH);
    loge("set speed %d fail.\n", speed);
    return false;
}

bool TTYClient::onConnect(void)
{

    if (mFd < 0) {
        loge("tty %s open failed", mTTYDev.c_str());
        mFd = open(mTTYDev.c_str(), O_RDWR | O_NOCTTY | O_NDELAY);
        if (mFd < 0)
            return false;
    }

    struct termios tty;
    memset(&tty, 0, sizeof tty);
    if (tcgetattr(mFd, &tty) != 0) {
        loge("tcgetattr failed %s", strerror(errno));
        return false;
    }

    //cfsetospeed(&tty, mBaudrate);
    //cfsetispeed(&tty, mBaudrate);

    tty.c_cflag = (tty.c_cflag & ~CSIZE) | CS8;     // 8-bit chars
    // disable IGNBRK for mismatched bdrate tests; otherwise receive break
    // as \000 chars
    tty.c_iflag &= ~IGNBRK;         // disable break processing
    tty.c_lflag = 0;                // no signaling chars, no echo,
    // no canonical processing
    tty.c_oflag = 0;                // no remapping, no delays
    tty.c_cc[VMIN]  = 0;            // read doesn't block
    tty.c_cc[VTIME] = 10;            // 0.5 seconds read timeout

    tty.c_iflag &= ~(IXON | IXOFF | IXANY); // shut off xon/xoff ctrl
    tty.c_iflag &= ~(INLCR | ICRNL);

    tty.c_cflag |= (CLOCAL | CREAD);// ignore modem controls,
    // enable reading
    tty.c_cflag &= ~(PARENB | PARODD);      // shut off parity
    tty.c_cflag |= 0;
    tty.c_cflag &= ~CSTOPB;
    tty.c_cflag &= ~CRTSCTS;

    if (tcsetattr(mFd, TCSANOW, &tty) != 0) {
        loge("tcsetattr failed %s", strerror(errno));
        return false;
    }

    setBaudrate(mBaudrate);

    return true;
}


TcpClient::TcpClient(Protocol *p, const char *server, uint16_t port)
    : ClientFd(p, socket(AF_INET, SOCK_STREAM, 0))
    , mServer(server)
    , mPort(port)
{
}

bool TcpClient::onConnect(void)
{
    int32_t ret = -1;
    if (mFd < 0) {
        logd("fd is %d\n", mFd);
        mFd = socket(AF_INET, SOCK_STREAM, 0);
    }
    sockaddr_in addr;
    addr.sin_family = AF_INET;
    addr.sin_port = htons(mPort);
    if (mServer == "") {
        return false;
    }
#if 1
    struct hostent * pHost = gethostbyname(mServer.c_str());
    if (!pHost) {
        logd("onConnect gethostbyname %s failed %s", mServer.c_str(), hstrerror(h_errno));
        return false;
    }
    addr.sin_addr = *((struct in_addr *)pHost->h_addr);
#else
    ret = inet_aton(mServer.c_str(), &addr.sin_addr);
    if (1 != ret) {
        logd("inet_aton %s failed %s", mServer.c_str(), strerror(errno));
        return false;
    }
#endif
    ret = connect(mFd, (sockaddr*) &addr, sizeof(sockaddr_in));
    if (0 == ret) {
        int32_t flag = fcntl(mFd, F_GETFL);
        flag |= O_NONBLOCK;
        fcntl(mFd, F_SETFL, flag);
        logd("Connect to %s:%d success", mServer.c_str(), mPort);
        return true;
    } else {
        loge("Connect to %s:%d failed %s", mServer.c_str(), mPort, strerror(errno));
        return false;
    }
}


SocketPairClient::SocketPairClient(Protocol *p)
    : ClientFd(p, -1)
{
    mPairFds[0] = -1;
    mPairFds[1] = -1;
}

bool SocketPairClient::onConnect(void)
{
    int32_t ret = -1;

	// mPairFds[0]在ClientFd::onRecvTimeOut关闭
	if (mPairFds[1] >= 0) {
		close(mPairFds[1]);
		mPairFds[1] = -1;
	}
	
    ret = socketpair(AF_UNIX, SOCK_STREAM, 0, mPairFds);
    if (0 != ret) {
        loge("create socketpair failed %s", strerror(errno));
        return false;
    }

    fcntl(mPairFds[0], F_SETFL, O_NONBLOCK);
    fcntl(mPairFds[1], F_SETFL, O_NONBLOCK);
    mFd = mPairFds[0];
    onReConfig(mPairFds[1]);

    return true;
}

