#include <unistd.h>
#include <sys/file.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <termios.h>
#include "transporter.h"
#include "worklooper.h"
#include "mystd.h"
#include "properties.h"
#include "idvrProperty.h"

using namespace minieye;

bool WorkLooper::mainloop(void)
{
    for (auto it = mClient.begin(); it != mClient.end(); ++it) {
        ClientFd *cli = *it;
        if (!cli->mConnected) {
            time_t now = my::timestamp::system_seconds();
            if (now < cli->mLastTryConnectTime + 2) { /* limit connect rate*/
                continue;
            }
            cli->mLastTryConnectTime = my::timestamp::system_seconds();
            cli->mConnected = cli->onConnect();
            if (cli->mConnected) {
                logd("%s connect.", cli->mProt->name());
                cli->mLastRecvDataTime = my::timestamp::system_seconds();
                cli->mProt->msgQueueClear();
                cli->mProt->onServerConnected();
            }
        }
    }
    pollFd();
    return true;
}

bool WorkLooper::pollFd(void)
{
    bool bHaveOutput = false;
    struct timeval timeout = {0, 10000};
    fd_set rfds, wfds;
    FD_ZERO(&rfds);
    FD_ZERO(&wfds);
    
    int32_t mFdMax = 0;
    for (auto it = mClient.begin(); it != mClient.end(); ++it) {
        ClientFd *cli = *it;
        if (cli->mConnected) {
            FD_SET(cli->mFd, &rfds);
            FD_SET(cli->mFd, &wfds);
            if (cli->mFd > mFdMax) {
                mFdMax = cli->mFd;
            }
        }
        if(cli->sendQueNotEmpty()){
            logd("send queue not empty........");
            bHaveOutput = true;
        }
    }

    // 数据接收
    int32_t ret = select(mFdMax + 1, &rfds, bHaveOutput? &wfds : NULL, NULL, &timeout);
    if (ret < 0) {
        loge("selected failed %s", strerror(errno));
        return false;
    } else if (ret == 0) {
        char propValue[PROP_VALUE_MAX] = {0};
        if (__system_property_get(PROP_PERSIST_MINIEYE_EXPAND_TIMEOUT_ENABLE, propValue) > 0) {
            if (strcmp(propValue, "false") == 0) {
                return true;
            }
        }
        for (auto it = mClient.begin(); it != mClient.end(); ++it) {
            ClientFd *cli = *it;
			cli->onRecvTimeOut();
        }
    } else {
        //logd("select ret %d...........", ret);
        for (auto it = mClient.begin(); it != mClient.end(); ++it) {
            ClientFd *cli = *it;
            if (FD_ISSET(cli->mFd, &rfds)) {
                //logd("on data recv fd %d ...........", cli->mFd);
                cli->onDataRecv();
            }
            if (bHaveOutput && (cli->mFd >= 0) && FD_ISSET(cli->mFd, &wfds)) {
                cli->sendOutputMessage();
            }
        }
    }

    return true;
}


