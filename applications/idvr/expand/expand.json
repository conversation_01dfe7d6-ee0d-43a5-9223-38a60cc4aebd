{"RS485": {"baudrate": 115200, "databit": 8, "stopbit": 1, "parity": "none", "protocol": "alertor", "enable": false}, "RS232_1": {"baudrate": 9600, "parity": "none", "databit": 8, "stopbit": 1, "protocol": "alertor", "enable": true}, "RS232_2": {"baudrate": 115200, "parity": "none", "databit": 8, "stopbit": 1, "protocol": "alertor", "enable": false}, "CAN1": {"baudrate": "500k", "protocol": "alertor", "enable": false}, "CAN2": {"baudrate": "500k", "protocol": "alertor", "enable": false}, "NET": {"url": "setup.minieye.cc", "ip": "***********", "port": 10240, "local_ip": "*************", "protocol": "alertor", "enable": false}, "USB": {"protocol": "alertor", "enable": false}}