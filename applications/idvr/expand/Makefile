include ../mk_include
TARGET_NAME=expand
TARGET=$(OUT_BIN_DIR)/$(TARGET_NAME)

LIB_FLAGS=-ldl -lflow -lmystd -lflowWrap -lfilelog -lmsgpackc -lmcuAgent -lsocketcmd -lproperty
CXX_FLAGS+=-DLOG_TAG_STR=$(TARGET_NAME) -Wno-psabi
LIB_FLAGS+=\
	-L$(LIB_DIR)/system \
	-L$(LIB_DIR)/thirdparty

INCS=	-I$(TOP_DIR)/include/mystd \
        -I$(TOP_DIR)/include/libflowWrap \
        -I$(TOP_DIR)/include/system/mcuAgent \
        -I$(TOP_DIR)/include/system/socketcmd \
        -I$(TOP_DIR)/include/system/sysutils \
        -I$(TOP_DIR)/include/system/idvr \
        -I$(TOP_DIR)/include/system/filelog \
        -I$(TOP_DIR)/include/system/ \
        -I$(TOP_DIR)/include/thirdparty/nlohmann_json/ \
        -I$(TOP_DIR)/include/thirdparty/rapidjson-1.1.0/ \
        -I$(TOP_DIR)/include/thirdparty/msgpack-c/ \
        -I$(TOP_DIR)/include/system/libflowWrap \
        -I$(TOP_DIR)/include/system/libflow \
		-I$(TOP_DIR)/include/system/libproperty \
        -I prot/ \
        -I export/ \
        -I.

SRCS=	$(wildcard *.cpp)

VPATH+=prot
VPATH+=export
PROT_SRC=$(wildcard prot/*.cpp)
SRCS+=$(notdir $(PROT_SRC))

OUT_OBJ_DIR=$(OUT_DIR)/obj/$(TARGET_NAME)

OBJECTS = $(patsubst %.cpp,%.o, $(SRCS))
OBJS_OUT = $(addprefix $(OUT_OBJ_DIR)/,$(notdir $(OBJECTS)))
DEP = $(OBJS_OUT:%.o=%.d)

.PHONY:all

all:$(TARGET)

$(TARGET):$(OBJS_OUT)
	$(CXX) $(CXX_FLAGS) $(BIN_FLAGS) $(OBJS_OUT) $(LIB_FLAGS) -o $@
ifneq ($(DEBUG), y)
	$(STRIP) $(TARGET)
endif
	cp -uv export/*.h $(TOP_DIR)/include/system/expand/

-include $(DEP)

$(OUT_OBJ_DIR)/%.o:%.cpp
	-mkdir -p $(OUT_BIN_DIR);mkdir -p $(OUT_OBJ_DIR)
	$(CXX) $(CXX_FLAGS) $(INCS) -MM -MT $@ -MF $(patsubst %.o, %.d, $@) $<
	$(CXX) $(CXX_FLAGS) -DEXECUTABLE $(INCS) -c $< -o $@

clean:
	rm $(OUT_OBJ_DIR)/*;rm $(TARGET);
