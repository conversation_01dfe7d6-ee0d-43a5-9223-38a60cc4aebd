#include "ttsPlay.h"
#include "properties.h"
#include "idvrProperty.h"
#include "rapidjson.wrap.h"

#include "expand.h"
#include "expand.message.h"
#include "devicehub.h"

#include "zfRadar.h"

namespace minieye
{
#define  CAR_INFO_INTERVAL_MS          20  // 20 MS

enum CAL_PARAM_BOUNDARY : int {
    EM_CAL_BD_DISTANCE_MIN = 80,
    EM_CAL_BD_DISTANCE_MAX = 250,
    EM_CAL_BD_ANGLE_MIN = 3500,
    EM_CAL_BD_ANGLE_MAX  = 5500,
    EM_CAL_BD_ANGLE_DEFUALT = 4000,
};


enum CAL_STATUS : int {
    EM_CAL_ST_UNKNOWN = 0,
    EM_CAL_ST_ING,
    EM_CAL_ST_SUCCESS,
    EM_CAL_ST_FAIL,
};

enum CAL_LOOP : int {
    EM_LOOP_START = 0,
    EM_LOOP_AUTO_ANGLE_IN,
    EM_LOOP_AUTO_ANGLE_OUT,
    EM_LOOP_W_ANGLE_IN,
    EM_LOOP_W_ANGLE_OUT,
    EM_LOOP_W_DISTANCE_IN,
    EM_LOOP_W_DISTANCE_OUT,
    EM_LOOP_FAIL,
};

enum RADAR_CAN_ID : int {
    EM_CAN_CAL_ID       = 0x0EF,        /* 向雷达发送校准报文 */
    EM_CAN_CAR_INFO_ID  = 0x130,  /* 向雷达发送车速、转向信息报文 */
    EM_CAN_ALARM_ID     = 0x2F0      /* 雷达上报报警状态信息 */
};

enum RADAR_VENDOR_E : int {
    E_RADAR_VENDOR_ZHUHAI_SHANGFU = 0,  /*珠海上富*/
    E_RADAR_VENDOR_CHENGTAI_TECH,       /*承泰科技*/
    E_RADAR_VENDOR_NUM_MAX
};
typedef canFrame_t (*CarInfoDataFn)(int canId, int spd);
static canFrame_t shangfuCarInfo(int canId, int speedX100)
{
    canFrame_t canData;
    canData.frameId = canId;
    canData.len = 8;
    memset(canData.data, 0, sizeof(canData.data));
    canData.data[0] = 0;//1 << 7;
    canData.data[1] = (uint8_t)((uint16_t)speedX100 >> 8);
    canData.data[2] = (uint8_t)((uint16_t)speedX100);
    return canData;
}
static canFrame_t chengtaiCarInfo(int canId, int speedX100)
{
    uint16_t speed = speedX100 / 100;
    canFrame_t canData;
    canData.frameId = canId;
    canData.len = 8;
    memset(canData.data, 0, sizeof(canData.data));
    canData.data[6] = (uint8_t)((uint16_t)speed >> 8);
    canData.data[7] = (uint8_t)((uint16_t)speed);
    return canData;
}

struct {
    int32_t calib_can_id;
    int32_t car_info_can_id;
    int32_t alarm_can_id;

    calParam_t calib_param;
    int calib_stat;

    CarInfoDataFn carInfoFn;
} gRadarParam[E_RADAR_VENDOR_NUM_MAX] = {
    [E_RADAR_VENDOR_ZHUHAI_SHANGFU] = {
        .calib_can_id       = 0x0EF,
        .car_info_can_id    = 0x130,
        .alarm_can_id       = 0x2F0,

        .calib_stat = EM_CAL_ST_UNKNOWN,
        .carInfoFn = shangfuCarInfo,
    },
    [E_RADAR_VENDOR_CHENGTAI_TECH] = {
        .calib_can_id       = 0,
        .car_info_can_id    = 0x0CFE6CEE,
        .alarm_can_id       = 0x18DACDB5,

        .calib_stat = EM_CAL_ST_SUCCESS,
        .carInfoFn = chengtaiCarInfo,
    },
};

ZfRadarProt::ZfRadarProt()
{
    mLastCarInfSnd = my::timestamp::now();
    mLastRadarMsgRcvTm = my::timestamp::now();
    mLastOsdSnd = my::timestamp::now();
    mAngle = EM_CAL_BD_ANGLE_DEFUALT;
    mDistance = EM_CAL_BD_DISTANCE_MIN;
    mCalLoop = EM_LOOP_W_DISTANCE_OUT;
}

ZfRadarProt::~ZfRadarProt()
{

}

/*
    校准状态|雷达到车头的距离|角度
    校准状态：1:标定成功 2:标定中 3:标定失败
    雷达到车头的距离:float
    角度:float
*/
bool ZfRadarProt::initCalibrationInfo(void)
{
    char value[PROP_VALUE_MAX] = {0};
    int        status = EM_CAL_ST_UNKNOWN;
    calParam_t param;

    if (__system_property_get(PROP_PERSIST_MINIEYE_RADAR_CALIBRATION, value) > 0) {
        sscanf(value, "S[%d]D[%.1f]A[%.2f]", &status, &(param.distance), &(param.angle));

        if (status == EM_CAL_ST_SUCCESS) {
            gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_stat = status;
            gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param = param;

        } else {
            gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_stat = status;
        }

    } else {
        gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_stat = EM_CAL_ST_UNKNOWN;
        gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param = param;
    }

    return true;
}

int32_t ZfRadarProt::onServerConnected(void)
{
    /* 获取校正状态 */
    initCalibrationInfo();
    loadCalParam();

    start();
    setCanMode("normal");
    uint32_t canId[4];
    uint32_t count = 0;
    for (int i = 0; i < E_RADAR_VENDOR_NUM_MAX; i++) {
        canId[i] = gRadarParam[i].alarm_can_id;
        count++;
    }
    setFilter(canId, count);

    /* 获取语音 */

    return 0;
}
int32_t ZfRadarProt::onServerDisconnected(void)
{
    stop();
    return 0;
}

int32_t ZfRadarProt::onDataRecevied(const char *p, uint32_t len)
{
    logd("len %d, recv  %s", len, my::hex(my::constr(p, len)).c_str());
    mLastRadarMsgRcvTm = my::timestamp::now();
    onCmdData(mRcvArray, p, len);
    return 0;
}

void ZfRadarProt::setCanMode(std::string mode)
{
    DeviceHub & dh = DeviceHub::getInstance();
    if ((mode != mCanMode || mbCanRcvFail) && mCanEnable) {
        logd("can speed %s", mCanBaudRate.c_str());
        if (mode == "normal") {
            dh.setCanMode(DeviceHub::DEVICE_TYPE_CAN1, mCanBaudRate, mode);

        } else {
            dh.setCanMode(DeviceHub::DEVICE_TYPE_CAN1, mCanBaudRate, mode);
        }

        mCanMode = mode;
    }
}

void ZfRadarProt::clearFilter()
{
    if (0 == mCanChId) {
        DeviceHub::getInstance().setCanFilter(DeviceHub::DEVICE_TYPE_CAN0, 0);

    } else if (1 == mCanChId) {
        DeviceHub::getInstance().setCanFilter(DeviceHub::DEVICE_TYPE_CAN1, 0);
    }
}

void ZfRadarProt::setFilter(uint32_t canId[], uint32_t canIdNum)
{
    if (0 == mCanChId) {
        DeviceHub::getInstance().setCanFilter(DeviceHub::DEVICE_TYPE_CAN0, canId, canIdNum);

    } else if (1 == mCanChId) {
        DeviceHub::getInstance().setCanFilter(DeviceHub::DEVICE_TYPE_CAN1, canId, canIdNum);
    }
}

void ZfRadarProt::radarConnectCheck()
{
    char cmd[256] = {0};

    if (mLastRadarMsgRcvTm.elapsed() >= 3 * 1000) {
        /* 3s无雷达数据则判定雷达故障 */
        if (mLastOsdSnd.elapsed() >= 3 * 1000) {
            /* 3s刷新一次osd避免osd发送过于频繁导致media故障 */
            mLastOsdSnd = my::timestamp::now();
            snprintf(cmd, sizeof(cmd), "cmd setUiTips Radar:error %d radar %d", 0xffffff, 0xff0000);
            LogCallProxyCmd::sendReq("media", cmd);
            mbCanRcvFail = true;
            //logd("%s", cmd);
        }

    } else if (mLastOsdSnd.elapsed() >= 3 * 1000) {
        mLastOsdSnd = my::timestamp::now();
        snprintf(cmd, sizeof(cmd), "cmd clearUiTips radar");
        LogCallProxyCmd::sendReq("media", cmd);
        //logd("%s", cmd);
    }
}

void ZfRadarProt::startAngleCal()
{
    canFrame_t canData;
    canData.frameId = EM_CAN_CAL_ID;
    canData.len = 8;
    /* 动态标定报文内容:ff 00 00 00 00 00 00 00 */
    memset(canData.data, 0, sizeof(canData.data));
    canData.data[0] = 0xFF;
    minieye::Protocol::msgEnque((void*)&canData, sizeof(canData));
    std::lock_guard<std::mutex> lock(mLock);
    mCalLoop = EM_LOOP_AUTO_ANGLE_IN;
}

void ZfRadarProt::sendCalParam(int32_t angle, int32_t distance)
{
    int       loop = 0;
    canFrame_t canData;
    canData.frameId = EM_CAN_CAL_ID;
    canData.len = 8;
    loge("angle:%d, distance:%d\n", angle, distance);
    /* 动态标定报文内容:ff 00 00 00 00 00 00 00 */
    memset(canData.data, 0, sizeof(canData.data));

    if (angle > EM_CAL_BD_ANGLE_MIN && angle < EM_CAL_BD_ANGLE_MAX) {
        canData.data[1] |= 0x1 << (8 - 8); /* 起始字节1，起始位8 */
        canData.data[2] = angle >> 8;
        canData.data[3] = angle & 0xff;
        loop = EM_LOOP_W_ANGLE_IN;
    }

    if (distance > EM_CAL_BD_DISTANCE_MIN && distance < EM_CAL_BD_DISTANCE_MAX) {
        canData.data[1] |= 0x1 << (9 - 8); /* 起始字节1，起始位9 */
        canData.data[4] = (uint8_t)distance;
        mCalDisTm = my::timestamp::now();
        loop = EM_LOOP_W_DISTANCE_IN;
    }

    if (loop) {
        my::string msgData;
        msgData.append((const char *)canData.data, 8);
        my::hexdump(msgData, true);
        minieye::Protocol::msgEnque((void*)&canData, sizeof(canData));
        std::lock_guard<std::mutex> lock(mLock);
        mCalTm = my::timestamp::now();
        mCalLoop = loop;

    }
}

void ZfRadarProt::startCal(int32_t angle, int32_t distance)
{
    loge("angle:%d, distance:%d!\n", angle, distance);

    if (angle <= EM_CAL_BD_ANGLE_MIN || angle >= EM_CAL_BD_ANGLE_MAX) {
        /* 无效角度使用动态标定 */
        startAngleCal();

    } else {
        /* 写入标定角度 */
        sendCalParam(angle, distance);
    }
}

void ZfRadarProt::calCheck()
{
    bool calFlg = true;

    if (mCalReq.angle > EM_CAL_BD_ANGLE_MIN && mCalReq.angle < EM_CAL_BD_ANGLE_MAX) {
        if (mCalReq.angle != gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.angle) {
            calFlg = false;
        }
    }

    if (mCalReq.distance > EM_CAL_BD_DISTANCE_MIN && mCalReq.distance < EM_CAL_BD_DISTANCE_MAX) {
        if (mCalReq.distance != gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.distance) {
            calFlg = false;
        }
    }

    if (!calFlg) {
        gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_stat = EM_CAL_ST_FAIL;

    } else {
        gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_stat = EM_CAL_ST_SUCCESS;
    }

    setRadarCalProperty(gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_stat, gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.distance, gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.angle);
    std::lock_guard<std::mutex> lock(mLock);
}

void ZfRadarProt::calLoop()
{
    int loopCase = EM_LOOP_START;
    {
        std::lock_guard<std::mutex> lock(mLock);
        loopCase = mCalLoop;
    }
    loge("loopCase:%d!\n", loopCase);

    switch (loopCase) {
        case EM_LOOP_START: {
                startCal(mCalReq.angle, mCalReq.distance);
                break;
            }

        case EM_LOOP_AUTO_ANGLE_IN: {
                break;
            }

        case EM_LOOP_AUTO_ANGLE_OUT: {
                int32_t distance;
                {
                    std::lock_guard<std::mutex> lock(mLock);
                    distance = mCalReq.distance;
                }

                if (distance <= EM_CAL_BD_DISTANCE_MIN || distance >= EM_CAL_BD_DISTANCE_MAX) {
                    /* 无效距离结束标定 */
                    mCalLoop = EM_LOOP_FAIL;

                } else {
                    sendCalParam(EM_CAL_BD_ANGLE_MIN, distance);
                }

                break;
            }

        case EM_LOOP_W_ANGLE_IN: {
                break;
            }

        case EM_LOOP_W_ANGLE_OUT: {
                int32_t distance;
                {
                    std::lock_guard<std::mutex> lock(mLock);
                    distance = mCalReq.distance;
                }

                if (distance <= EM_CAL_BD_DISTANCE_MIN || distance >= EM_CAL_BD_DISTANCE_MAX) {
                    /* 无效距离结束标定 */
                    mCalLoop = EM_LOOP_FAIL;

                } else {
                    sendCalParam(EM_CAL_BD_ANGLE_MIN, distance);
                }

                break;
            }

        case EM_LOOP_W_DISTANCE_IN: {
                if (mCalDisTm.elapsed() > 20000) {
                    /* 20s还未成功则判定结束 */
                    mCalLoop = EM_LOOP_W_DISTANCE_OUT;
                }

                break;
            }

        case EM_LOOP_W_DISTANCE_OUT: {
                calCheck();
                break;
            }

        case EM_LOOP_FAIL: {
                std::lock_guard<std::mutex> lock(mLock);
                gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_stat = EM_CAL_ST_FAIL;
                setRadarCalProperty(gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_stat, gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.distance, gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.angle);
                break;
            }

        default: {
                break;
            }
    }
}

void ZfRadarProt::run()
{
    bool norLoop = true;

    while (!exiting()) {
        // todo send can pkg
        {
            std::lock_guard<std::mutex> lock(mLock);

            if (gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_stat != EM_CAL_ST_ING) {
                norLoop = true;

            } else {
                norLoop = false;
            }
        }

        sendCarInfo();
        radarConnectCheck();

        if (!norLoop) {
            /* 标定 */
            calLoop();
        }

        usleep(10 * 1000);
    }
}

/* 同步动态标定结果 */
void ZfRadarProt::calMsgDeal(uint8_t angleCalFlg, int32_t distance, int32_t angle)
{
    loge("angle:%d\n", angle);
    gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.angle = angle;

    if (angleCalFlg) {
        /* 动态标定成功 */
        mCalLoop = EM_LOOP_AUTO_ANGLE_OUT;

    } else {
        /* 动态标定失败;结束标定 */
        mCalLoop = EM_LOOP_FAIL;
    }
}

void ZfRadarProt::calMsgDeal(int32_t distance, int32_t angle)
{
    loge("distance:%d, angle:%d,mCalLoop:%d!\n", distance, angle, mCalLoop);
    gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.angle = angle;
    gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.distance = distance;

    if (mCalLoop == EM_LOOP_W_ANGLE_IN) {
        if (angle == mCalReq.angle) {
            /* 写入完成 */
            mCalLoop = EM_LOOP_W_ANGLE_OUT;
        }

        return;
    }

    if (mCalLoop == EM_LOOP_W_DISTANCE_IN) {
        if (distance == mCalReq.distance) {
            mCalLoop = EM_LOOP_W_DISTANCE_OUT;

        } else {
            loge("distance cal failed!\n");

            if (mCalDisTm.elapsed() > 20000) {
                /* 20s还未成功则判定结束 */
                mCalLoop = EM_LOOP_W_DISTANCE_OUT;
            }
        }
    }
}

int ZfRadarProt::shangfuCanAlarmDataDeal(const uint32_t canID, const uint8_t *canData)
{
    uint8_t radarSysStatus = canData[1] & 0x03; /*0x0: 雷达关闭;0x1: 雷达正常工作;0x2: 雷达错误;0x3: Reserved */
    uint8_t radarWorkMode = (canData[1] >> (10 - 8)) & 0x1;  /* 0x1 active; 0x0 off */
    uint8_t radarEnableFlag = (canData[1] >> (14 - 8)) & 0x01; /* 0x0: OFF;0x1: Active */
    uint8_t radarAlarmFlag = (canData[1] >> (12 - 8)) & 0x03; /* 0x0: No Warning;0x1: Warning level 1;0x2: Warning level 2;0x3: Reserved*/

    uint8_t radarAngleCalFlg = (canData[5] >> (40 - (5 * 8))) & 0x1; /* 0:雷达安装角度未标定 1:雷达安装角度已标定 */
    uint8_t radarAngleCalStatus = (canData[5] >> (41 - (5 * 8))) & 0x1; /* 0:雷达未开始进行角度标定 1:雷达正在进行角度标定 */

    uint8_t distance = canData[2];
    int32_t angle = (int32_t)(canData[3]) << 8 | (int32_t)(canData[4]);

    loge("radarAngleCalStatus:%d, radarAngleCalFlg:%d, distance:%d, angle:%d!\n",
         radarAngleCalStatus, radarAngleCalFlg, distance, angle);

    if (gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_stat == EM_CAL_ST_ING) {
        if (mCalTm.elapsed() < 5000) {
            /* 5s内不解析标定结果防止标定前的消息干扰标定结果 */
            return 0;
        }

        if (mCalTm.elapsed() > 240000) {
            /* 4min标定未结束强制终止任务 */
            std::lock_guard<std::mutex> lock(mLock);
            loge("time out\n");
            mCalLoop = EM_LOOP_W_DISTANCE_OUT;
            return 0;
        }

        std::lock_guard<std::mutex> lock(mLock);

        if (gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_stat == EM_CAL_ST_ING) {
            if (mCalLoop == EM_LOOP_AUTO_ANGLE_IN) {
                if (radarAngleCalStatus != 1) {
                    calMsgDeal(radarAngleCalFlg, distance, angle);
                }

            } else {
                if (radarAngleCalFlg) {
                    calMsgDeal(distance, angle);

                } else {
                    /* 标定完成未置位 */
                    if (angle == mCalReq.angle) {
                        calMsgDeal(distance, angle);
                    }
                }
            }

            return 0;
        }
    }

    DeviceHub &devHub = DeviceHub::getInstance();
    double speed = devHub.getCarSpeed();

    if (radarSysStatus == 0x1 && radarWorkMode == 0x1 && radarEnableFlag == 0x1) {
        loge("radarAlarmFlag:%d, speed:%d", radarAlarmFlag, (int)speed);

        if (radarAlarmFlag == 0x1 || radarAlarmFlag == 0x2) {
            if (speed < 30 && mWarnLastTs.elapsed() > 500) {
                mWarnLastTs = my::timestamp::now();
                /* 500ms 上报一次报警事件 */
                sendFlowMessage();

            } else {
                //logd("speed < 30");
            }
        }
    } else {
        loge("radarSysStatus:%d, radarWorkMode:%d, radarEnableFlag:%d\n", radarSysStatus, radarWorkMode, radarEnableFlag);
    }

    return 0;
}


int ZfRadarProt::chengtaiCanAlarmDataDeal(const uint32_t canID, const uint8_t *canData)
{
    bool bAlarm = !!(canData[0] & (1 << 7)); //bit8
    int level = (canData[0] & 0x70) >> 4;
    int type = canData[0] & 0xf; //1-BSD、2-LCA

    if (bAlarm) {
        DeviceHub &devHub = DeviceHub::getInstance();
        double speed = devHub.getCarSpeed();
        loge("radarAlarm, level %d, speed %d, type %d", level, (int)speed, type);

        if ((1 == type) && (speed < 30) && (mWarnLastTs.elapsed() > 500)) {
            mWarnLastTs = my::timestamp::now();
            /* 500ms 上报一次报警事件 */
            sendFlowMessage();

        } else {
            //logd("speed < 30");
        }
    }

    return 0;
}


int32_t ZfRadarProt::onDataParse(const uint8_t *p, uint32_t size)
{
    if (size < sizeof(canFrame_t)) {
        loge("size:%d < canFrame_t:%d!\n", size, sizeof(canFrame_t));
        return -1;
    }

    canFrame_t *pMsg = (canFrame_t *)p;
    logd("canId:%8xH, canData: %02x-%02x-%02x-%02x-%02x-%02x-%02x-%02x, size %d\n",
         pMsg->frameId, pMsg->data[0], pMsg->data[1], pMsg->data[2],
         pMsg->data[3], pMsg->data[4], pMsg->data[5], pMsg->data[6], pMsg->data[7], size);

    for (int i = 0; i < E_RADAR_VENDOR_NUM_MAX; i++) {
        if (pMsg->frameId != gRadarParam[i].alarm_can_id) {
            continue;
        }

        switch (i) {
            case E_RADAR_VENDOR_ZHUHAI_SHANGFU: {
                    shangfuCanAlarmDataDeal(pMsg->frameId, pMsg->data);
                    break;
                }

            case E_RADAR_VENDOR_CHENGTAI_TECH: {
                    chengtaiCanAlarmDataDeal(pMsg->frameId, pMsg->data);
                    break;
                }

            default: {
                    loge("no impl idx %d!", i);
                    break;
                }
        }
    }

    return sizeof(canFrame_t);
}

int32_t ZfRadarProt::onCmdData(vector<uint8_t> &recvArray, const char *p, uint32_t len)
{
    recvArray.insert(recvArray.end(), p, p + len);
    int32_t parsed = 0;

    do {
        parsed = onDataParse(recvArray.data(), recvArray.size());

        if (recvArray.size() >= (1 << 20)) {
            parsed = recvArray.size();
            logd("frame too long!!\n");
        }

        if (parsed < 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + recvArray.size());
            parsed = 0;

        } else if (parsed > 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + parsed);

        } else {
            logd("alertor frame not complete!!\n");
        }

        if (recvArray.size() <= 0 || parsed <= 0) {
            break;
        }
    } while (1);

    return parsed;
}

bool ZfRadarProt::loadConfig()
{
    if (access("/data/minieye/idvr/etc/expand.json", F_OK)) {
        return false;
    }

    rapidjson::Document doc;
    RAPIDJSON_LOAD("/data/minieye/idvr/etc/expand.json");

    if (0 == mCanChId) {
        RAPIDJSON_GET_JSON_STRING(doc["CAN1"], "baudrate", mCanBaudRate);
        RAPIDJSON_GET_JSON_BOOL(doc["CAN1"], "enable", mCanEnable);
        RAPIDJSON_GET_JSON_STRING(doc["CAN1"], "mode", mCanMode);

    } else if (1 == mCanChId) {
        RAPIDJSON_GET_JSON_STRING(doc["CAN2"], "baudrate", mCanBaudRate);
        RAPIDJSON_GET_JSON_BOOL(doc["CAN2"], "enable", mCanEnable);
        RAPIDJSON_GET_JSON_STRING(doc["CAN2"], "mode", mCanMode);
    }

    return true;
}

bool ZfRadarProt::loadCalParam()
{
    if (access("/data/minieye/idvr/etc/bsd_radar_setup.json", F_OK)) {
        return false;
    }

    bool valid = true;
    rapidjson::Document doc;
    RAPIDJSON_LOAD("/data/minieye/idvr/etc/bsd_radar_setup.json");
    loge("load json success!\n");
    float val = 30.00;
    RAPIDJSON_GET_JSON_DOUBLE(doc["CAL"], "angle_radar_to_body", val);
    mAngle = (int32_t)(val * 100);

    if (EM_CAL_BD_ANGLE_MIN <= mAngle || mAngle >= EM_CAL_BD_ANGLE_MAX) {
        valid = false;
    }

    val = 10.0;
    RAPIDJSON_GET_JSON_DOUBLE(doc["CAL"], "dist_radar_to_front", val);
    mDistance = (int32_t)(val * 10);

    if (mDistance <= EM_CAL_BD_DISTANCE_MIN || mDistance >= EM_CAL_BD_DISTANCE_MAX) {
        valid = false;
    }

    std::string str;
    RAPIDJSON_GET_JSON_STRING(doc["CAL"], "language", str);

    if (str.length() > 0) {
        gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.language = str.c_str();
    }

    loge("language:%s", gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.language.c_str());
    std::lock_guard<std::mutex> lock(mLock);

    if (gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_stat != EM_CAL_ST_ING && valid) {
        if (mAngle != gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.angle || mDistance != gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.distance) {
            mCalReq.angle = mAngle;
            mCalReq.distance = mDistance;
            gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_stat = EM_CAL_ST_ING;
            /* 更新property */
            setRadarCalProperty(gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_stat, gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.distance, gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.angle);
        }
    }

    return true;
}

void ZfRadarProt::sendCarInfo()
{
    DeviceHub & dh = DeviceHub::getInstance();

    if (mLastCarInfSnd.elapsed() >= CAR_INFO_INTERVAL_MS) {
        int speedX100 = dh.getCarSpeed() * 100;

        for (int i = 0; i < E_RADAR_VENDOR_NUM_MAX; i++) {
            canFrame_t canData = gRadarParam[i].carInfoFn(gRadarParam[i].car_info_can_id, speedX100);
            minieye::Protocol::msgEnque((void*)&canData, sizeof(canData));
#if 1
            logd("canid[0x%x] spd %d msg:%02X %02X %02X %02X %02X %02X %02X %02X", gRadarParam[i].car_info_can_id, speedX100,
                 canData.data[0], canData.data[1], canData.data[2], canData.data[3],
                 canData.data[4], canData.data[5], canData.data[6], canData.data[7]);
#endif
        }

        mLastCarInfSnd = my::timestamp::now();

    } else {
        usleep(10 * 1000);
    }
}

void ZfRadarProt::ttsGBK(const my::string spch, int32_t tmGapLimit)
{
    if (tmGapLimit) {
        auto last = mTtsMsgList.find(spch.c_str());

        if (last != mTtsMsgList.end()) {
            if ((time(NULL) - last->second) < tmGapLimit) {
                logd("Too many tts : %s", spch.c_str());
                return ;
            }
        }

        mTtsMsgList[spch.c_str()] = (uint32_t)time(NULL);
    }


    /*gbk to utf8*/
    if (my::is_str_utf8((char*) spch.c_str())) {
        loge("%s\n", spch.c_str());
        ttsPlayRightNow("%s", (char*) spch.c_str());

    } else {
        loge("%s\n", spch.c_str());
        char * tmp = new char[spch.length() << 2];
        my::gbkToUtf8((char*) spch.c_str(), tmp);
        ttsPlayRightNow("%s", tmp);
        delete [] tmp;
    }
}

bool ZfRadarProt::runCmd(int argc, char **argv, string &ack)
{
    if (argc < 1) {
        loge("argc:%d\n", argc);
        return false;
    }

    if (!strcmp(argv[0], "Calibration")) {
        {
            std::lock_guard<std::mutex> lock(mLock);

            if (gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_stat == EM_CAL_ST_ING) {
                if (gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.language == "zh") {
                    ttsGBK("标定中", 10);

                } else {
                    ttsGBK("Incalibration", 10);
                }

                return false;
            }
        }

        calParam_t param;

        if (argc == 2) {
            param.distance = (int32_t)(atof(argv[1]) * 10);
            param.angle = EM_CAL_BD_DISTANCE_MIN;

        } else if (argc >= 3) {
            param.distance = (int32_t)(atof(argv[1]) * 10);
            param.angle = (int32_t)(atof(argv[2]) * 100);
            loge("cmd calibration distance:%d,angle:%d!\n", param.distance, param.angle);

        } else {
            param.angle = mAngle;
            param.distance = mDistance;
            loge("use bsd_radar_setup.json calibration\n");
        }

        /* 距离无效必定会标定失败直接返回 */
        if (param.distance <= EM_CAL_BD_DISTANCE_MIN || param.distance >= EM_CAL_BD_DISTANCE_MAX) {
            loge("Calibration distance invalid!\n");
            return false;
        }

        mCalTm = my::timestamp::now();
        {
            std::lock_guard<std::mutex> lock(mLock);
            mCalReq = param;
            gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_stat = EM_CAL_ST_ING;
            mCalLoop = EM_LOOP_START;
        }
        /* 更新property */
        setRadarCalProperty(gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_stat, gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.distance, gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.angle);
    }

    return true;
}

void ZfRadarProt::playCalResult(int32_t status)
{
    loge("language:%s\n", gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.language.c_str());

    switch (status) {
        case 2: {
                if (gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.language == "zh") {
                    ttsGBK("标定成功", 10);

                } else {
                    ttsGBK("Calibrationsuccess", 10);
                }

                break;
            }

        case 3: {
                if (gRadarParam[E_RADAR_VENDOR_ZHUHAI_SHANGFU].calib_param.language == "zh") {
                    ttsGBK("标定失败", 10);

                } else {
                    ttsGBK("Calibrationfailure", 10);
                }

                break;
            }
    }
}

bool ZfRadarProt::setRadarCalProperty(int32_t status, int32_t distance, int32_t angle)
{
    playCalResult(status);

    char value[PROP_VALUE_MAX] = {0};
    snprintf(value, PROP_VALUE_MAX, "S[%d]D[%.1f]A[%.2f]", status, distance / 10.0, angle / 100.00);
    __system_property_set(PROP_PERSIST_MINIEYE_RADAR_CALIBRATION, value);
    loge("set %s %s!distance:%d,angle:%d\n", PROP_PERSIST_MINIEYE_RADAR_CALIBRATION, value, distance, angle);
    return true;
}

bool ZfRadarProt::sendFlowMessage(void)
{
    expand::ZfRadarMessage message;
    message.mCalStatus   = true;
    message.mAlarmStatus = true;

    msgpack::sbuffer sbuf;
    msgpack::pack(sbuf, message);
    ExpandSet::getInstance().sendLibFlow(ZFRADAR_LIBFLOW_TOPIIC, sbuf.data(), sbuf.size());

    return true;
}


}
