#ifndef __MINIEYE_ALGO_OUTPUT_CAN_PROT_H__
#define __MINIEYE_ALGO_OUTPUT_CAN_PROT_H__
/*
    算法输出CAN协议定制
*/
#include "mystd.h"
#include "cmdline.h"
#include "algo.h"
#include "canDisplay.h"
#include "adas_alert.h"

#define ENABLE_EXPIRE_PROTO 0
namespace minieye
{

struct SwayHighCanDisplayWarningPkt;
struct SwayHighCanDisplayStatusPkt;
struct SwayHighCanDisplayErrorPkt;

class AlgoOutputCanProt
    : public CanDisplay
    , public IALGO_OBSERVER
    , public my::thread
{
    public:
        AlgoOutputCanProt();
        ~AlgoOutputCanProt();

        virtual int32_t onServerConnected(void);
        virtual int32_t onServerDisconnected(void);
        virtual int32_t onDataRecevied(const char *p, uint32_t len);
        virtual std::string setupCmdList(const char * cmdName);
        virtual bool runCmd(int argc, char **argv, string &ack);
    protected:
        virtual bool onAlgoEvent(std::shared_ptr<Event> evt);

    private:
#if ENABLE_EXPIRE_PROTO
        bool onBsdEventWoTeJia(std::shared_ptr<Event> evt);
        bool outputCanMsgWoTeJia(const McuMsgMcuStatT & s);
#endif

        bool checkBsdAlgoWork();
        bool checkCamWork();
        bool checkMaintenance();

        bool onBsdEventSuZhouShuWei(std::shared_ptr<Event> evt);
        bool outputCanMsgSuZhouShuWei();

        bool onBsdEventXiaMenCiBei(std::shared_ptr<Event> evt);
        bool outputOilIOXiaMenCiBei();

        bool onAdasEventZhongChi(std::shared_ptr<Event> evt);
        bool onDmsEventZhongChi(std::shared_ptr<Event> evt);
        bool checkVehicleMonitoring();
        bool outputCanMsgZhongChi();

        bool onBsdEventJinLongSingleBsd(std::shared_ptr<Event> evt);
        bool outputCanMsgJinLongSingleBsd();

        bool onBsdEventWanXiang(std::shared_ptr<Event> evt);
        bool outputCanMsgWanXiang();

        bool onBsdEventSwayHighCanDisplay(std::shared_ptr<Event> evt);
        bool outputCanMsgSwayHighCanDisplay();

    private:
        virtual void run();
        int32_t onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len);
        int32_t onDataParse(const char *p, uint32_t size);

     private:
        bool mbStart = false;
        string mCanProtName;
        string mCmdUsage;
        vector<uint8_t> mRcvArray;
        int mCamRunSec = 0;

        ADAS_CAN700 mAdasCan700Pkt;

        int32_t mCarFlicAlarmExitCnt = 0;

        std::mutex  mEvtRcdMapMtx;
        std::map<int32_t/*event*/, std::pair<my::timestamp, int32_t/*level*/>> mEvtRcdMap;
        std::pair<EVT_TYPE, my::timestamp> mLastDmsEvt = {EVT_TYPE::EVT_TYPE_INVALID, my::timestamp()};
        std::pair<EVT_TYPE, my::timestamp> mLastBsdEvt = {EVT_TYPE::EVT_TYPE_INVALID, my::timestamp()};

        std::mutex  mCanMsgTsMtx;
        std::map<int32_t/*canId*/, my::timestamp> mCanMsgTs;

        int32_t mLastLBsdEventLevel = 0;
        int32_t mLastRBsdEventLevel = 0;
        my::timestamp mLastSwayHighRefreshTs;
        my::timestamp mLastSysDetectTs;
        std::shared_ptr<SwayHighCanDisplayWarningPkt> mSwayHighCanDisplayWarningPkt;
        std::shared_ptr<SwayHighCanDisplayStatusPkt> mSwayHighCanDisplayStatusPkt;
        std::shared_ptr<SwayHighCanDisplayErrorPkt> mSwayHighCanDisplayErrorPkt;

};
};
#endif

#undef ENABLE_EXPIRE_PROTO