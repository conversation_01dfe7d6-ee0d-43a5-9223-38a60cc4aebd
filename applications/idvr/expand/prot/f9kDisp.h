#ifndef __F9KDISP_H__
#define __F9KDISP_H__

#include "protocol.h"
#include "nmea/nmea.h"
#include "McuMessage.h"
#include "FileLog.h"

namespace minieye {

#define SHOW    1   // 显示
#define HIDE    0   // 隐藏
#define FICK    2   // 闪烁
#define CMD_SHOW_PED    0x7
#define CMD_SHOW_CAR    0x5
#define CMD_SHOW_LINE   0x1
#define DISPLAY_CAN_ID  0x780
#define SHOW_E  0xE
#define SHOW_CAR    SHOW
//#define
//#define
//#define

class F9KDisPlay: public Protocol, public my::thread 
{
public:
        F9KDisPlay();
        ~F9KDisPlay();

        int32_t onServerConnected(void);
        int32_t onServerDisconnected(void);
        int32_t onDataRecevied(const char *p, uint32_t len);
        std::string setupCmdList(const char * cmdName) { return "F9KDisPlay";}
        bool runCmd(int argc, char **argv, string &ack);
        void run();
private:
        int32_t onDataParse(const char *p, uint32_t size);
private:
        vector<uint8_t>     mRcvArray;
        uint8_t             mLastLineShow; 
        float               mSpdThres;
};
}
#endif
