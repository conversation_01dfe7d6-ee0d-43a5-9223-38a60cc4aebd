#ifndef __F9KGPS_H__
#define __F9KGPS_H__

#include "protocol.h"
#include "nmea/nmea.h"
#include "McuMessage.h"
#include "FileLog.h"
#include "expand.h"
#include "mystd.h"
#include "f9kGpsNovatelMsg.h"

#define NTRIP_CFG_PATH  "/data/minieye/idvr/etc/NTRIP.conf"     // ntrip server 登录账号和密码

// 127.0.0.1 
// 23877
#define RTK_LOCTION_LIBFLOW_TOPIIC  "rtk.location.topic"

struct LocationInfo {
    uint8_t         status;     // 定位状态 0 未定位 1已定位
    uint8_t         mode;       // 定位模式：0=Invalid, 1=2D/3D, 2=DGNSS, 4=Fixed RTK, 5=FloatRTK, 6=Dead）
    double          direction;  // 方向
    double          hdop;       // 水平精度
    double          latitude;   // 纬度 
    double          longitude;  // 经度
    double          altitude;   // 高度
    double          speed;      // 速度
    // MSGPACK_DEFINE(status, mode, direction, hdop, latitude, longitude, altitude, speed);
}; 


namespace minieye {

    class F9KGPS: public Protocol, public my::thread 
    {
        using ClientCallback = std::function<void (char const* _buffer, int _size)>;
        class NtripClient : public my::thread
        {
        public:
            NtripClient() = default;
            NtripClient(NtripClient const& ) = delete;
            NtripClient(NtripClient&& ) = delete;
            NtripClient& operator = (NtripClient const& ) = delete;
            NtripClient& operator = (NtripClient&&) = delete;
            NtripClient(std::string const& ip, int port, std::string const& user_, std::string const& passwd_,
                            std::string const& mountpoint_, F9KGPS* gps);
            ~NtripClient() { stop(); }

            void getCustomConf(std::string path = NTRIP_CFG_PATH);
            void init(std::string const& ip, int port, std::string const& user_, std::string const& passwd_,
                        std::string const& mountpoint_, F9KGPS* gps);

            // 更新发送的GGA语句.
            // 根据ntrip账号的要求, 如果距离服务器位置过远, 服务器不会返回差分数据.
            void setGgaBuffer(std::string const& gga);
            
            // 设置GGA上报时间间隔, 单位秒(s).
            void setReportInterval(int32_t intv)
            {
                reportInterval_ = intv;
            }

            bool start(void);
            void stop(void);

        protected:
            void run();

        public:
            bool                        isServiceRunning = false;
            bool                        isGgaUpdate = false;  // 外部更新GGA数据标志.
            int32_t                     reportInterval_ = 1;  // GGA数据上报时间间隔.
            std::string                 serverIp;
            int32_t                     serverPort = -1;
            std::string                 user;
            std::string                 passwd;
            std::string                 mountpoint;
            std::string                 ggaBuffer;
            int32_t                     socketFd_ = -1;
            std::mutex                  mutex_;
            F9KGPS*                     mF9kGps;
            FileLog                     *mNtripLogger = NULL;
        };

    public:
        F9KGPS();
        ~F9KGPS();
        int32_t onServerConnected(void);
        int32_t onServerDisconnected(void);
        int32_t onDataRecevied(const char *p, uint32_t len);
        std::string setupCmdList(const char * cmdName) { return "F9KGPS";}
        bool runCmd(int argc, char **argv, string &ack) {return true;}
        bool setDebugLevel(int32_t level);
        bool getStatus() {return mLbs.status;}
        bool getInit() {return mF9KInit;}

    private:
        int32_t onDataParse(const char *p, uint32_t size);
        void getGpsSignalLevel(void);
        void getLastGpsLocatoin(void);
        void updataLastGpsLocation(void);
        void getGpsTime(void);
        void dumpGpsInfo(void);
        void notiyLibs(void);
        void configF9k(void);
        bool getNetWorkStatus();

    protected:
        void run();

    private:
        string mCmdUsage;
        std::vector<uint8_t>            mRcvArray;
        nmeaINFO                        mNmeaInfo;
        nmeaPARSER                      mNmeaParser;
        my::string                      mNmeaBuf;
        my::string                      mNovAtelBuf;
        LBS                             mLbs = { 0 };
        double                          mDegLat = 0.0;
        double                          mDegLon = 0.0;
        int32_t                         mDebugLevel = 0;
        FileLog                         *mGpsLogger = NULL;
        FileLog                         *mNmeaLogger = NULL;
        NtripClient                     *mNtripClient;
        my::timestamp                   mGpsUpdateTime = 0;                 // GPS超时清零
        my::timestamp                   mNtripCheckTime = 0;                // Ntrip Client 断线重连
        bool                            mLastPlay = false;
        //CmdIndex                      mBaudRate = BAUDRATE_INDEX_115200;  // 波特率
        //CmdIndex                      mWorkHz = WORK_FREQ_INDEX_5HZ;      // 工作频率
        bool                            mF9KInit = false;
        bool                            mExpandExit = false;
        my::timestamp                   mLastPktTs; 
        my::timestamp                   mLocatMonitor;
        std::mutex                      mMutex;
        char mWaitMsgTyp = 0;
        BestposData mBestposData;
        InspvaxData mInspvaxData;
    };

}



#endif


