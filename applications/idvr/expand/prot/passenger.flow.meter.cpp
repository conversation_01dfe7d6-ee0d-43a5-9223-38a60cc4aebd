
#include <assert.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <sys/time.h>
#include "system_properties.h"
#include "passenger.flow.meter.h"
#include "expand.h"

namespace minieye
{
#define     PFM_PROT_MAGIC          (0x7E)

#define     MAGIC                   (0x0)
#define     ESC_CHAR                (0x1)
#define     MAGIC_APPEND            (0x2)
#define     ESC_APPEND              (0x3)

typedef enum : uint16_t
{
    PFM_READ_MODE_RESP          = 0xe000,
    PFM_TEST_MODE_ENTER_RESP    = 0xe001,

    PFM_QUERY_DEV_INFO_RESP     = 0xe100,
    PFM_SET_DEV_INFO_RESP       = 0xe101,
    PFM_QUERY_NETWORK_RESP      = 0xe102,
    PFM_SET_NETWORK_RESP        = 0xe103,

    PFM_QUERY_SETTINGS_RESP     = 0xe104,
    PFM_SET_SETTINGS_RESP       = 0xe105,

    PFM_QUERY_TIME_RESP         = 0xe106,
    PFM_SET_TIME_RESP           = 0xe107,
    PFM_QUERY_RUN_STAT_RESP     = 0xe108,

    PFM_SEND_DOOR_STAT_RESP     = 0xe610,
    PFM_QUERY_PASSEGER_NUM_RESP = 0xe611,
    PFM_CLEAR_PASSEGER_NUM_RESP = 0xe612,

    PFM_READ_MODE       = 0xf000,
    PFM_TEST_MODE_ENTER = 0xf001,
    PFM_TEST_MODE_EXIT  = 0xf002,
    PFM_KEEP_HEARTBEAT  = 0xf003,

    PFM_QUERY_DEV_INFO  = 0xf100,
    PFM_SET_DEV_INFO    = 0xf101,
    PFM_QUERY_NETWORK   = 0xf102,
    PFM_SET_NETWORK     = 0xf103,
    PFM_QUERY_SETTINGS  = 0xf104,
    PFM_SET_SETTINGS    = 0xf105,
    PFM_QUERY_TIME      = 0xf106,
    PFM_SET_TIME        = 0xf107,
    PFM_QUERY_RUN_STAT  = 0xf108,

    PFM_BROADCAST_TIME  = 0xf303,
    PFM_BROADCAST_DOOR  = 0xf601,

    PFM_SEND_DOOR_STAT  = 0Xf610,

    PFM_QUERY_PASSEGER_NUM = 0xf611,
    PFM_CLEAR_PASSEGER_NUM = 0xf612,
} PFM_PROT_E;

template<class _type_>
_type_ bcd2value(_type_ rawMSB)
{
    char tmp[128] = {0};
    memcpy(tmp, &rawMSB, sizeof(rawMSB));
    return (_type_)strtoul(tmp, NULL, 16);
}
typedef struct PFMMsg
{
    uint8_t     magicBgn    = PFM_PROT_MAGIC;
    uint16_t    cmd         = 0;
    uint8_t     addr        = 1;
    uint16_t    length      = 0;
    my::string  payload;
    uint8_t     chkXor      = 0;
    uint8_t     magicEnd    = PFM_PROT_MAGIC;

    PFMMsg(uint8_t command = 0) {
        cmd = command;
    }
    int32_t encode(my::string & msg);
    int32_t decode(uint8_t * msg, int32_t len);
    uint8_t checkXor(uint8_t *p, uint32_t len);
} PFMMsg;

int32_t PFMMsg::encode(my::string & msg)
{
    msg << my::hton;
    length = payload.length();
    msg << magicBgn << cmd << addr << length;
    msg << payload;
    msg << chkXor;
    msg << magicEnd;
    msg[msg.length() - 2] = checkXor((uint8_t *)msg.c_str(), msg.length());
    return msg.length();
}
int32_t PFMMsg::decode(uint8_t * msg, int32_t len)
{
    if ((msg[0] != PFM_PROT_MAGIC) && (msg[0] != msg[len - 1])) {
        return -1;
    }
    uint8_t cs = checkXor(msg, len);
    if (cs != msg[len - 2]) {
        loge("checksum error! 0x%02x != 0x%02x", cs, msg[len - 2]);
        return -2;
    }
    my::constr msgStr((const char *)msg, len);
    msgStr >> my::ntoh;
    msgStr >> magicBgn >> cmd >> addr >> length;
    payload = my::string((const char *)msgStr, msgStr.length() - 2);
    msgStr += payload.length();
    msgStr >> chkXor;
    msgStr >> magicEnd;

    return 0;
}

uint8_t PFMMsg::checkXor(uint8_t * p, uint32_t len)
{
    uint8_t * start = p + sizeof(magicBgn);
    uint8_t chksum = start[0];
#define CHKSUM_LEN(__len__) (__len__ - (sizeof(magicBgn) + sizeof(chkXor) + sizeof(magicEnd)))

    for (uint32_t i = 1; i < CHKSUM_LEN(len); i++) {
        chksum ^= start[i];
    }

    return chksum;
}


PassengerFlowMeter::PassengerFlowMeter()
{
    __system_property_set(PASSENGER_FLOW_METER_RUN_PROPERTY, "true");
    mpFileLog = new FileLog("/data/minieye/idvr/mlog/pfm/", 5 * 1024 * 1024, 10);
    mpFileLog->prepare();
    start();
}
int32_t PassengerFlowMeter::onServerConnected(void)
{
    return 0;
}
int32_t PassengerFlowMeter::onServerDisconnected(void)
{
    return 0;
}

int32_t PassengerFlowMeter::onDataRecevied(const char *p, uint32_t len)
{
    //logd("on cmd data %d", len);
    //logd("recv %s", my::hex(my::constr(p, len)).c_str());

    onCmdData(mRcvArray, p, len);
    return 0;
}

int32_t PassengerFlowMeter::onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len)
{
    recvArray.insert(recvArray.end(), p, p + len);
    my::hexdump(my::constr((const char*)recvArray.data(), recvArray.size()), true, "expand.onCmdData");
    int32_t parsed = 0;

    do {
        parsed = onDataParse((const char *)recvArray.data(), recvArray.size());

        if (recvArray.size() >= (1 << 20)) {
            parsed = recvArray.size();
            logd("frame too long!!\n");
        }

        if (parsed < 0) {
            
            recvArray.clear();
            logd("parse fail, clear all!!\n");
    
        } else if (parsed > 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + parsed);
            //logd("erase size %d\n", parsed);

        } else {
            //logd("alertor frame not complete!!\n");
        }

        if (recvArray.size() <= 0 || parsed <= 0) {
            break;
        }

    } while (1);

    return parsed;
}

bool PassengerFlowMeter::onEscape(uint8_t *p, uint32_t len, std::vector<uint8_t> &v)
{
    uint32_t i = 0;

    v.push_back(p[0]);
    for (i = 1; i < len - 1; i++) {
        if (mEscape[MAGIC] == p[i]) {
            v.push_back(mEscape[ESC_CHAR]);
            v.push_back(mEscape[MAGIC_APPEND]);
        }else if (mEscape[ESC_CHAR] == p[i]) {
            v.push_back(mEscape[ESC_CHAR]);
            v.push_back(mEscape[ESC_APPEND]);
        } else {
            v.push_back(p[i]);
        }
    }
    v.push_back(p[len - 1]);

    return true;
}

bool PassengerFlowMeter::onUnEscape(uint8_t *p, uint32_t len, std::vector<uint8_t> &v)
{
    if (mEscape[MAGIC] != p[len - 1]) {
        return false;
    }

    uint32_t i = 0;
    for (i = 0; i < len - 1;) {
        if (mEscape[ESC_CHAR] == p[i]) {
            if (mEscape[MAGIC_APPEND] == p[i + 1]) {
                v.push_back(mEscape[MAGIC]);
            } else if (mEscape[ESC_APPEND] == p[i + 1]) {
                v.push_back(mEscape[ESC_CHAR]);
            }
            i += 2;
        } else {
            v.push_back(p[i]);
            i++;
        }
    }
    v.push_back(p[len - 1]);
    return true;
}
bool PassengerFlowMeter::onValidate(uint8_t *p, uint32_t len)
{
    if (mEscape[MAGIC] != p[0] || mEscape[MAGIC] != p[len - 1]) {
        return false;
    }

    PFMMsg msg;
    uint8_t cs = msg.checkXor(p, len);
    return (cs == p[len - 2]);
}
int32_t PassengerFlowMeter::onDataParse(const char *p, uint32_t size)
{
    int32_t start = -1;
    int32_t end = -1;
    uint32_t offset = 0;

    for (int i = 0; i < static_cast<int32_t>(size); i++) {
        if (mEscape[MAGIC] == p[i]) {
            if (start < 0) {
                start = i;
            } else if (end < 0) {
                end = i;
                break;
            }
        }
    }

    if ((start >= 0) && (end >= 0)) {
        uint8_t* tmp = (uint8_t*)(p + start);
        uint32_t len = end - start + 1;
        std::vector<uint8_t> protMsg;

        if (onUnEscape(tmp, len, protMsg)) {
            if (onValidate(protMsg.data(), protMsg.size())) {
                logd("PassengerFlowMeter");
                onMessage(protMsg.data(), protMsg.size());
            } else {
                loge("invalid data:\n");
                //my::hexdump(my::constr((char*)protMsg.data(), protMsg.size()), true, "expand.onDataParse");
                /*to skip dirty data, such as "xx xx xx 7E 7E xx xx 7E"*/
                if ((tmp[0] == 0x7e) && (tmp[1] == 0x7e)) {
                    end = -1;
                    start += 1;
                }
            }

        } else {
            return 0;
        }

    } else {
        logd("******start =  %d, end = %d\n", start, end);
    }

    return (end >= 0) ? (end + 1) : start;
}


bool PassengerFlowMeter::onMessage(uint8_t *p, uint32_t len)
{
    mLastDataUtcTime = time(NULL);
    PFMMsg msg;
    msg.decode(p, len);
    my::hexdump(my::constr((const char*)p, len), true, "expand.onMsg");

    switch (msg.cmd) {
        case PFM_QUERY_DEV_INFO_RESP: {
            std::lock_guard<std::mutex> _l_(mDevinfoMtx);
            mSndCmdBuf.erase(PFM_QUERY_DEV_INFO);
            mDevInfo.decode((uint8_t *)msg.payload.c_str(), msg.payload.length());
            break;
        }
        case PFM_SEND_DOOR_STAT_RESP: { /*发送开关门信号应答*/
            std::lock_guard<std::mutex> _l_(mSndCmdBufMtx);
            mSndCmdBuf.erase(PFM_SEND_DOOR_STAT);
            if (msg.payload.length()) {
                if (!msg.payload[0]) {
                    logd("PFM_SEND_DOOR_STAT_RESP succ!");
                    
                } else {
                    loge("PFM_SEND_DOOR_STAT_RESP fail! %d", msg.payload[0]);
                }
            } else {
                loge("PFM_SEND_DOOR_STAT_RESP invalid payload!");
            }
            break;
        }

        case PFM_QUERY_PASSEGER_NUM_RESP: { /*查询客流人数应答*/
            std::lock_guard<std::mutex> _l_(mSndCmdBufMtx);
            if (msg.payload.length() >= 8) {
                uint16_t onBoardNum = 0, offBoardNum = 0;
                uint32_t errCode = 0;
                my::constr pl(msg.payload);
                pl >> my::ntoh >> onBoardNum >> offBoardNum >> errCode;
                expand::PassengerFlowMeterMsg message;
                message.mUtcTime = my::timestamp::utc_milliseconds();
                message.raw = "resp_passenger_num_query";
                message.onBoardNum = onBoardNum;
                message.offBoardNum = offBoardNum;
                message.errCode = errCode;
                msgpack::sbuffer  sbuf;
                msgpack::pack(sbuf, message);
                ExpandSet::getInstance().sendLibFlow(PASSENGER_FLOW_METER_LIBFLOW_TOPIC, sbuf.data(), sbuf.size());
                if (mpFileLog) {
                    auto it = mSndCmdBuf.find(PFM_QUERY_PASSEGER_NUM);
                    PFMMsg sndMsg;
                    int32_t ret = -1;
                    if (it != mSndCmdBuf.end()) {
                        ret = sndMsg.decode((uint8_t *)it->second.first.c_str(), it->second.first.length());
                    }

                    if (ret == 0) {
                        mpFileLog->mlog(":idx_%02x :on %d :off %d :errCode 0x%x", sndMsg.addr, onBoardNum, offBoardNum,
                                        errCode);
                    } else {
                        mpFileLog->mlog(":idx_unknown :on %d :off %d :errCode 0x%x", onBoardNum, offBoardNum, errCode);
                    }
                }
                mSndCmdBuf.erase(PFM_QUERY_PASSEGER_NUM);
            } else {
                loge("PFM_QUERY_PASSEGER_NUM_RESP invalid payload size %d", msg.payload.length());
            }
            break;
        }
        case PFM_CLEAR_PASSEGER_NUM_RESP: {/*清除客流人数应答*/
            std::lock_guard<std::mutex> _l_(mSndCmdBufMtx);
            mSndCmdBuf.erase(PFM_CLEAR_PASSEGER_NUM);
            if (msg.payload.length()) {
                if (!msg.payload[0]) {
                    logd("PFM_CLEAR_PASSEGER_NUM_RESP succ!");
                } else {
                    loge("PFM_CLEAR_PASSEGER_NUM_RESP fail! %d", msg.payload[0]);
                }
            } else {
                loge("PFM_CLEAR_PASSEGER_NUM_RESP invalid payload!");
            }
            break;
        }
        default: {
                break;
            }
    }

    return true;
}
/*libflow recv*/
void PassengerFlowMeter::recv(const char* source,  // '\0' terminated string
            const char* topic,   // any binary data
            const char* data,    // any binary data
            size_t size)
{
    if (!strcmp(topic, PASSENGER_FLOW_METER_LIBFLOW_TOPIC)) {
        auto curDoorbits = (uint8_t)data[0];
        if (curDoorbits != 0) {
            logd("door bits %02xh", curDoorbits);
        }
        
        uint8_t reverseBits = mLastDoorBits ^ curDoorbits;

        constexpr int32_t doorCnt = 2;  // 门的数量
        constexpr int32_t doorCntBits = (1 << (doorCnt << 1)) - 1;
        if (reverseBits & doorCntBits) {
            uint8_t op = 0;
            for (int i = 0; i < doorCnt; i++) {
                const int32_t openDoorBit = (1 << (i << 1));
                const int32_t closeDoorBit = (1 << ((i << 1) + 1));
                if ((reverseBits & openDoorBit) && (curDoorbits & openDoorBit)) {  // 之前没信号，现在有信号的情况
                    logd("open door %d", i + 1);
                    mpFileLog->mlog("open :idx_%02x", i + 1);
                    op = 1;
                } else if ((reverseBits & closeDoorBit) && (curDoorbits & closeDoorBit)) {
                    logd("close door %d", i + 1);
                    // mpFileLog->mlog("close door %d", i + 1);
                    op = 2;
                } else {
                    op = 0;
                }

                if (op != 0) {
                    PFMMsg msg;
                    msg.cmd = PFM_SEND_DOOR_STAT;
                    msg.addr = i + 1;
                    msg.payload << my::hton << op;
                    my::string s;
                    msg.encode(s);

                    msgEnque((void *)s.c_str(), s.length());
                }
            }
        }

        mLastDoorBits = curDoorbits;
    }
}



#define PFM_CMD_HELP            0
#define PFM_CMD_SHOW            1
#define PFM_CMD_GET_PFM         2
#define PFM_CMD_DEVINFO         3
#define PFM_CMD_SEND_DOOR_STAT  4

static const std::vector<CmdStrT> gPFMCmds = {
    {
        "help",
        PFM_CMD_HELP,
        "show this usage.\n"
    },
    {
        "show",
        PFM_CMD_SHOW,
        "show last LIQUID data.\n"
    },
    {
        "get",
        PFM_CMD_GET_PFM,
        "get passenger flow data.\n"
    },
    {
        "devinfo",
        PFM_CMD_DEVINFO,
        "get device info.\n"
    },
    {
        "door",
        PFM_CMD_SEND_DOOR_STAT,
        "send door status.\n"
    }
};

std::string PassengerFlowMeter::setupCmdList(const char * cmdName)
{
    mCmdUsage = CmdStrT::setupCmdList(gPFMCmds, cmdName);
    return mCmdUsage;
}
static void bcd2str(const uint8_t * bcd, int32_t len, my::string & str)
{
    str.capacity(len * 2 + 1);
    char * p = (char*)str.c_str();
    for (int32_t i = 0; i < len; i++) {
        snprintf(p + i * 2, 3, "%02x", bcd[i]);
    }
}
bool PassengerFlowMeter::runCmd(int argc, char **argv, string &ack)
{
    uint32_t cmd = CmdStrT::strToCmd(gPFMCmds, argv[0]);

    switch (cmd) {
        case PFM_CMD_HELP: {
            ack = mCmdUsage;
            return true;
        }

        case PFM_CMD_SHOW: {
            ack = "\n";
            APPEND_STR_MSG(ack, "Msg UTC", 16, "%ld", mLastDataUtcTime);
            APPEND_STR_MSG(ack, "chipNum", 16, "%d", mDevInfo.chipNum);
            for (auto r : mDevInfo.chipInfo) {
                my::string ci, di;
                bcd2str((const uint8_t *)r.chipId, sizeof(r.chipId) - 1, ci);
                bcd2str((const uint8_t *)r.devId, sizeof(r.devId) - 1, di);
                APPEND_STR_MSG(ack, "chipId",   4, "%s", ci.c_str());
                APPEND_STR_MSG(ack, "devId",    4, "%s", di.c_str());
                APPEND_STR_MSG(ack, "slaveId",  4, "%d", r.slaveId);
                APPEND_STR_MSG(ack, "pcb",      4, "%d", r.pcbVersion);
                APPEND_STR_MSG(ack, "sys",      4, "%d", r.sysVersion);
                APPEND_STR_MSG(ack, "app",      4, "%d", r.appVersion);
                APPEND_STR_MSG(ack, "ini",      4, "%d", r.iniVersion);
                APPEND_STR_MSG(ack, "algo",     4, "%d", r.algoVersion);
                APPEND_STR_MSG(ack, "mod",      4, "%d", r.modVersion);
                APPEND_STR_MSG(ack, "qtt",      4, "%d", r.qttVersion);
                APPEND_STR_MSG(ack, "platId",   4, "%s", r.platId);
                APPEND_STR_MSG(ack, "undef",    4, "%s", r.undefine);
            }
            return true;
        }
        case PFM_CMD_GET_PFM: {
            ack = "\n";
            PFMMsg msg;
            msg.cmd = PFM_QUERY_PASSEGER_NUM;
            msg.addr = 1;
            if (argc > 1) {
                msg.addr = atoi(argv[1]);
            }
            uint8_t data = 1;
            msg.payload << my::hton << data;
            my::string s;
            msg.encode(s);
            msgEnque((void*)s.c_str(), s.length());
            {
                std::lock_guard<std::mutex> _l_(mSndCmdBufMtx);
                mSndCmdBuf[msg.cmd] = std::pair<my::string, my::timestamp>(s, my::timestamp::now());
            }
            return true;
        }
        case PFM_CMD_DEVINFO: {
            ack = "\n";
            PFMMsg msg;
            msg.cmd = PFM_QUERY_DEV_INFO;
            msg.addr = 1;
            if (argc > 1) {
                msg.addr = atoi(argv[1]);
            }
            uint8_t data = 1;
            msg.payload << my::hton << data;
            my::string s;
            msg.encode(s);
            msgEnque((void*)s.c_str(), s.length());
            {
                std::lock_guard<std::mutex> _l_(mSndCmdBufMtx);
                mSndCmdBuf[msg.cmd] = std::pair<my::string, my::timestamp>(s, my::timestamp::now());
            }
            return true;
        }
        case PFM_CMD_SEND_DOOR_STAT: {
            ack = "\n";
            PFMMsg msg;
            msg.cmd = PFM_SEND_DOOR_STAT;
            msg.addr = 1;
            if (argc > 2) {
                msg.addr = atoi(argv[1]);
                uint8_t data = atoi(argv[2]);

                if (data != 1 && data != 2) {
                    ack += "door status should be 1 or 2\n";
                    return false;
                }
                msg.payload << my::hton << data;
            }
            my::string s;
            msg.encode(s);
            msgEnque((void *)s.c_str(), s.length());
            return true;
        }
        default: {
            ack = mCmdUsage;
        }
    }

    return false;
}

void PassengerFlowMeter::run()
{
    prctl(PR_SET_NAME, "pfm");

    while (!exiting()) {
        do {
            std::lock_guard<std::mutex> _l_(mSndCmdBufMtx);
            auto it = mSndCmdBuf.begin();
            while (it != mSndCmdBuf.end()) {
                if (it->second.second.elapsed() >= 1000) {
                    it->second.second = my::timestamp::now();
                    msgEnque((void *)it->second.first.c_str(), it->second.first.length());
                }
                it++;
            }
        } while (0);
        usleep(1000000);
    }
}

} //namespace minieye


