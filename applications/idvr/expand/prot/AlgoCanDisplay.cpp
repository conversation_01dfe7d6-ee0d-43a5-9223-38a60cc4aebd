
#include "AlgoCanDisplay.h"

#include <assert.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>

#include "adas_alert.h"
#include "devicehub.h"
#include "expand.h"
#include "system_properties.h"

namespace minieye {

AlgoCanDisplay::AlgoCanDisplay() {
    AlgoManager &am = AlgoManager::getInstance();
    char tmp[256];
    snprintf(tmp, sizeof(tmp), "AlgoCanDisplay%p", this);
    am.addObserver(tmp, this);
    __system_property_set(ALGO_CAN_DISPLAY_OUTPUT_PROC_PROPERTY, "expand");

    start();
}
AlgoCanDisplay::~AlgoCanDisplay() noexcept {
    AlgoManager &am = AlgoManager::getInstance();
    char tmp[256];
    snprintf(tmp, sizeof(tmp), "AlgoCanDisplay%p", this);
    am.delObserver(tmp);
}
int32_t AlgoCanDisplay::onServerConnected(void) {
    return 0;
}
int32_t AlgoCanDisplay::onServerDisconnected(void) {
    return 0;
}

int32_t AlgoCanDisplay::onDataRecevied(const char *p, uint32_t len) {
    // logd("on cmd data %d", len);
    // logd("recv %s", my::hex(my::constr(p, len)).c_str());

    return 0;
}

bool AlgoCanDisplay::onAlgoEvent(std::shared_ptr<Event> e) {
    switch (e->type()) {
        case EVT_TYPE_ADAS_CAN700_MSG: {
            do {
                std::lock_guard<std::mutex> lg(mUpdateLock);
                mEnableAdas = true;
                my::string data = e->mEvtData["data"];
                ADAS_CAN700 *pAdasMsg = (ADAS_CAN700 *)data.c_str();

                auto &dh = DeviceHub::getInstance();
                int speed = dh.getCarSpeed();
                mCurSpeed = speed;
                mCurAdasCan700 = *pAdasMsg;

            } while (0);
            mUpdateCond.notify_one();
            logd("on ADAS CAN700 msg");
            break;
        }

        case EVT_TYPE_BSD_Front:  /*左前方*/
        case EVT_TYPE_BSD_Behind: /*右前方*/
        case EVT_TYPE_BSD_Left:
        case EVT_TYPE_BSD_Right: {
            do {
                std::lock_guard<std::mutex> lg(mUpdateLock);
                mEnableBsd = true;
                if (e->type() == EVT_TYPE_BSD_Front || e->type() == EVT_TYPE_BSD_Left) {
                    mLastBsdAlarmTs[E_DEVICE_LEFT] = my::timestamp::now();
                } else {
                    mLastBsdAlarmTs[E_DEVICE_RIGHT] = my::timestamp::now();
                }
            } while (0);
            mUpdateCond.notify_one();
            logd("on BSD alarm %d", e->type());
            break;
        }
        default: {
            break;
        }
    }
    return true;
}

#define ALGO_CAN_DISPLAY_PROT_CMD_HELP    0
#define ALGO_CAN_DISPLAY_PROT_CMD_SHOW    1
#define ALGO_CAN_DISPLAY_PROT_CMD_TRIGGER 2

static const std::vector<CmdStrT> gAlgoCanDisplayProtCmds = {
    {"help", ALGO_CAN_DISPLAY_PROT_CMD_HELP, "show this usage.\n"},
    {"show", ALGO_CAN_DISPLAY_PROT_CMD_SHOW, "show last  data.\n"},
    {"trigger", ALGO_CAN_DISPLAY_PROT_CMD_TRIGGER, "fake to trigger event.\n"},
};

std::string AlgoCanDisplay::setupCmdList(const char *cmdName) {
    mCmdUsage = CmdStrT::setupCmdList(gAlgoCanDisplayProtCmds, cmdName);
    return mCmdUsage;
}

bool AlgoCanDisplay::runCmd(int argc, char **argv, string &ack) {
    uint32_t cmd = CmdStrT::strToCmd(gAlgoCanDisplayProtCmds, argv[0]);

    switch (cmd) {
        case ALGO_CAN_DISPLAY_PROT_CMD_HELP: {
            ack = mCmdUsage;
            return true;
        }

        case ALGO_CAN_DISPLAY_PROT_CMD_SHOW: {
            ack = "\n";

            return true;
        }
        case ALGO_CAN_DISPLAY_PROT_CMD_TRIGGER: {
            if (argc >= 3) {
                DeviceHub &devHub = DeviceHub::getInstance();
                AlgoManager &am = AlgoManager::getInstance();
                am.triggerEvent(argv[1], argv[2], devHub.getCarSpeed());
                return true;
            }
        }
        default: {
            ack = mCmdUsage;
        }
    }

    return false;
}

void AlgoCanDisplay::run() {
    prctl(PR_SET_NAME, "AlgoCanDisplay");

    auto handleAdasCan700 = [this]() {
        bool updateFlag = false;
        float time = mCurAdasCan700.headway_measurement * 0.1;
        int hw0 = mCurAdasCan700.headway_measurement / 10;
        int hw1 = mCurAdasCan700.headway_measurement % 10;
        int hw = hw0 * 16 + hw1;

        if (mCurAdasCan700.left_ldw != mLastAdasCan700.left_ldw) {
            logd("left trigger %d ...", mCurAdasCan700.left_ldw);
            mAdasCanDisplay.leftLine = mCurAdasCan700.left_ldw ? E_DISPLAY_SHOW : E_DISPLAY_HIDE;
            updateFlag = true;
        }
        if (mCurAdasCan700.right_ldw != mLastAdasCan700.right_ldw) {
            logd("right trigger %d ...", mCurAdasCan700.right_ldw);
            mAdasCanDisplay.rightLine = mCurAdasCan700.right_ldw ? E_DISPLAY_SHOW : E_DISPLAY_HIDE;
            updateFlag = true;
        }

        if (mCurAdasCan700.peds_in_dz != mLastAdasCan700.peds_in_dz) {
            logd("peds trigger...");
            mAdasCanDisplay.pedestrian = mCurAdasCan700.peds_in_dz ? E_DISPLAY_SHOW : E_DISPLAY_HIDE;
            updateFlag = true;
        }

        if (mCurAdasCan700.peds_fcw != mLastAdasCan700.peds_fcw) {
            logd("peds_fcw trigger...");
            mAdasCanDisplay.pedestrian = mCurAdasCan700.peds_fcw ? E_DISPLAY_FICK : E_DISPLAY_HIDE;
            updateFlag = true;
        }

        if (mCurAdasCan700.fcw_on && !mLastAdasCan700.fcw_on) {
            logd("fcw trigger...");
            mAdasCanDisplay.vehicle = Vehicle{E_DISPLAY_RED, E_DISPLAY_FICK};
            mAdasCanDisplay.segNum = SegNum{(uint8_t)hw, true, E_DISPLAY_RED, E_DISPLAY_HIDE};
            updateFlag = true;
        }
        if (mCurAdasCan700.fcw_on) {  // highest priority
            goto out;
        }

        if (mCurAdasCan700.headway_warning_level != mLastAdasCan700.headway_warning_level ||
            mCurAdasCan700.headway_measurement != mLastAdasCan700.headway_measurement || mLastSpeed != mCurSpeed ||
            mCurAdasCan700.headway_valid != mLastAdasCan700.headway_valid) {
            if (time == 0) {
                mAdasCanDisplay.segNum.state = E_DISPLAY_HIDE;
            } else {
                mAdasCanDisplay.segNum.state = E_DISPLAY_SHOW;
            }
            mAdasCanDisplay.segNum.num = (uint8_t)hw;
            mAdasCanDisplay.segNum.isDot = true;
            mAdasCanDisplay.segNum.color = E_DISPLAY_WHITE;
            if (mCurAdasCan700.headway_valid) {
                mAdasCanDisplay.vehicle.state = E_DISPLAY_SHOW;
            } else {
                mAdasCanDisplay.vehicle.state = E_DISPLAY_HIDE;
            }

            if (mCurAdasCan700.headway_warning_level == 2) {
                mAdasCanDisplay.vehicle.color = E_DISPLAY_RED;
            } else {
                mAdasCanDisplay.vehicle.color = E_DISPLAY_WHITE;
            }
            updateFlag = true;
        }

    out:
        mLastAdasCan700 = mCurAdasCan700;
        mLastSpeed = mCurSpeed;
        return updateFlag;
    };

    auto handleBsdAlarm = [this]() {
        auto leftLine = E_DISPLAY_HIDE;
        auto rightLine = E_DISPLAY_HIDE;
        auto pedestrian = E_DISPLAY_HIDE;

        if (mLastBsdAlarmTs[E_DEVICE_LEFT].elapsed() <= 2000) {
            leftLine = E_DISPLAY_SHOW;
            pedestrian = E_DISPLAY_SHOW;
        }

        if (mLastBsdAlarmTs[E_DEVICE_RIGHT].elapsed() <= 2000) {
            rightLine = E_DISPLAY_SHOW;
            pedestrian = E_DISPLAY_SHOW;
        }

        if (leftLine != mBsdCanDisplay.leftLine || rightLine != mBsdCanDisplay.rightLine ||
            pedestrian != mBsdCanDisplay.pedestrian) {
            mBsdCanDisplay.leftLine = leftLine;
            mBsdCanDisplay.rightLine = rightLine;
            mBsdCanDisplay.pedestrian = pedestrian;
            return true;
        }
        return false;
    };

    auto handleStationary = [this]() {
        DeviceHub &dh = DeviceHub::getInstance();
        int speed = dh.getCarSpeed();

        auto segNum = mStationaryDisplay.segNum;
        if (speed == 0) {
            segNum.state = E_DISPLAY_SHOW;
            segNum.isDot = true;
            segNum.color = E_DISPLAY_WHITE;
            segNum.num = 0xAA;
        } else {
            segNum.state = E_DISPLAY_HIDE;
            segNum.isDot = true;
            segNum.color = E_DISPLAY_WHITE;
            segNum.num = 0x00;
        }

        if (segNum.state != mStationaryDisplay.segNum.state || segNum.isDot != mStationaryDisplay.segNum.isDot ||
            segNum.color != mStationaryDisplay.segNum.color || segNum.num != mStationaryDisplay.segNum.num) {
            mStationaryDisplay.segNum = segNum;
            return true;
        }

        return false;
    };

    std::unique_lock<std::mutex> lck(mUpdateLock);
    while (!exiting()) {
        bool updateFlag = (mLastUpdateTs.elapsed() >= 2000) | handleStationary() |
                          (mEnableBsd && handleBsdAlarm()) | (mEnableAdas && handleAdasCan700());  
        if (updateFlag) {
            mLastUpdateTs = my::timestamp::now();
            mCanDisplay = mStationaryDisplay | mAdasCanDisplay | mBsdCanDisplay;
            setLeftLineStatus(mCanDisplay.leftLine);
            setRightLineStatus(mCanDisplay.rightLine);
            setPedestrianStatus(mCanDisplay.pedestrian);
            setSegNumStatus(mCanDisplay.segNum);
            setVehicleStatus(mCanDisplay.vehicle);
            refreshDispStatePack();
        }
        mUpdateCond.wait_for(lck, std::chrono::milliseconds(50));
    }
}

}  // namespace minieye
