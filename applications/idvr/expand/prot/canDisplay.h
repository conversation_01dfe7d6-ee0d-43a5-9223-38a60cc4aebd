#ifndef __CAN_DISP_H__
#define __CAN_DISP_H__

#include "protocol.h"
#include "McuMessage.h"


namespace minieye {

#define CMD_SHOW_PED            0x7
#define CMD_SHOW_CAR            0x5
#define CMD_SHOW_LINE           0x1
#define CMD_SHOW_LINE_CAR_REG   0x3

#define DISPLAY_CAN_ID  0x780
#define SHOW_E  0xE
#define SHOW_CAR    SHOW

typedef enum DisplayStatus
{
    E_DISPLAY_HIDE = 0,
    E_DISPLAY_SHOW,
    E_DISPLAY_FICK
}E_DisplayStatus_t;

typedef enum ColorStatus
{
    E_DISPLAY_RED = 0,
    E_DISPLAY_WHITE
}E_ColorStatus;

typedef struct SegNum
{
    uint8_t num;  // 16进制
    bool isDot;
    E_ColorStatus color;
    E_DisplayStatus_t state;
}E_SegNum_t;

typedef struct Vehicle
{

    E_ColorStatus color;
    E_DisplayStatus_t state;
}E_Vehicle_t;


typedef struct DispStatePack
{
    E_DisplayStatus_t leftLine;
    E_DisplayStatus_t rightLine;
    E_DisplayStatus_t pedestrian;
    E_Vehicle_t vehicle;
    E_SegNum_t segNum;
    
    DispStatePack() {
        leftLine = E_DISPLAY_HIDE;
        rightLine = E_DISPLAY_HIDE;
        pedestrian = E_DISPLAY_HIDE;
        vehicle.color = E_DISPLAY_WHITE;
        vehicle.state = E_DISPLAY_HIDE;
        segNum.num = 0;
        segNum.isDot = false;
        segNum.color = E_DISPLAY_WHITE;
        segNum.state = E_DISPLAY_HIDE;
    }

    DispStatePack operator|(const DispStatePack& other) const {
        DispStatePack pack;
        pack.leftLine = std::max(leftLine, other.leftLine);
        pack.rightLine = std::max(rightLine, other.rightLine);
        pack.pedestrian = std::max(pedestrian, other.pedestrian);
        pack.vehicle.color = std::min(vehicle.color, other.vehicle.color);  // 红色优先
        pack.vehicle.state = std::max(vehicle.state, other.vehicle.state);
        pack.segNum.num = std::max(segNum.num, other.segNum.num);
        pack.segNum.isDot = segNum.isDot || other.segNum.isDot;
        pack.segNum.color = std::min(segNum.color, other.segNum.color);  // 红色优先
        pack.segNum.state = std::max(segNum.state, other.segNum.state);
        return pack;
    }
}DispStatePack_t;


/*需要操控can小屏幕，可以继承这个类*/
class CanDisplay : public Protocol
{
public:
    CanDisplay();
    ~CanDisplay();

    void sentCanData(uint8_t *data);
    void fillVehicleStruct(E_Vehicle_t& vehicle, E_ColorStatus color, E_DisplayStatus_t state);
    void fillSegNumStruct(E_SegNum_t& segNum, uint8_t num, bool isDot, E_ColorStatus color, E_DisplayStatus_t state);  // num为16进制
    bool isEeqSegNumStruct(E_SegNum_t& segNum1, E_SegNum_t& segNum2);
    bool isEeqVehicleStruct(E_Vehicle_t& vehicle1, E_Vehicle_t& vehicle2);

    //下面的函数调用后立即生效
    void drawPedestrian(E_DisplayStatus_t status);
    void drawLine(E_DisplayStatus_t leftLineStatus, E_DisplayStatus_t rightLineStatus);
    void drawCarSeg(E_Vehicle_t vehicle, E_SegNum_t seg);
    // void drawLineCarSeg(E_Vehicle_t vehicle, E_SegNum_t seg, E_DisplayStatus_t leftLineStatus, E_DisplayStatus_t rightLineStatus);

    //下面的函数调用后不会立即生效,只是往mStatusPack填充数据,通过调用refreshDispStatePack()将生效
    void setLeftLineStatus(E_DisplayStatus_t status);
    void setRightLineStatus(E_DisplayStatus_t status);
    void setPedestrianStatus(E_DisplayStatus_t status);
    void setVehicleStatus(E_Vehicle_t vehicle);
    void setSegNumStatus(E_SegNum_t segNum);
    void refreshDispStatePack();

private:
    DispStatePack_t    mStatusPack;
    DispStatePack_t    mLastStatusPack;
    my::timestamp      mRefreshStamp;
};

}
#endif
