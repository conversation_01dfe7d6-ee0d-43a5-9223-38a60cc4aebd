
#include "LongAnAlertor.RongSheng.h"

#include <assert.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <numeric>

#include "devicehub.h"
#include "expand.h"
#include "system_properties.h"

#define ALINK_ALERTOR_ALARM_MODE "persist.minieye.rs.alarm.mode" // BASIC（默认）:启动bsd； ADVANCE:启动转向，倒车，bsd

#define BSD_CAM_ID_PROT "rw.objp2bsd.right"
#define BSD_CAM_ID_VALUE "2"

namespace minieye {
// --- Alink Alertor protocol begin ---

#define ALERTOR_PROT_MAGIC (0x7E)

struct LongAnAlertor::LongAnAlertorMsg {
    uint8_t magicBgn = ALERTOR_PROT_MAGIC;
    uint16_t msgId = 0xF001;      // 报警id，固定
    uint16_t msgAttr = 0;         // 未使用
    uint16_t sequenceNumber = 0;  // 未使用
    uint8_t playContent = 0x1;    // 播放内容
    uint16_t crc = 0;
    uint8_t magicEnd = ALERTOR_PROT_MAGIC;

    int32_t encode(my::string &msg);
    // int32_t decode(uint8_t *msg, int32_t len);
    // static bool onValidate(uint8_t *p, int32_t len);
};

int32_t LongAnAlertor::LongAnAlertorMsg::encode(my::string &msg) {
    msg << my::hton;
    msg << magicBgn;
    msg << msgId << msgAttr << sequenceNumber << playContent << crc;
    msg << magicEnd;
    return msg.length();
}

my::string LongAnAlertor::getVolumeCmd(uint8_t volume) {
    if (volume > 10 || volume < 0) {
        loge("rs volume is out of range, return default value 5\n");
        volume = 5;
    }

    uint8_t magic = ALERTOR_PROT_MAGIC;
    uint16_t msgId = 0xF002;  // 设置音量
    uint8_t unknown = 0x00;
    my::string cmd;
    cmd << my::hton;
    cmd << magic << msgId;
    for (int i = 0; i < 5; i++) {
        cmd << unknown;
    }
    cmd << volume;
    for (int i = 0; i < 3; i++) {
        cmd << unknown;
    }
    cmd << magic;
    return cmd;
}

// --- Long An Alertor protocol end ---

LongAnAlertor::LongAnAlertor()
/* , mIpcClient(make_shared<IpcClient>("expand", "/mnt/obb/alink_alertor"))*/ {
    AlgoManager &am = AlgoManager::getInstance();
    char tmp[256];
    snprintf(tmp, sizeof(tmp), "LongAnAlertor%p", this);
    am.addObserver(tmp, this);
    __system_property_set(BSD_CAM_ID_PROT, BSD_CAM_ID_VALUE);
    mLastChkVolumeTs = my::timestamp::now();

    start();
}
LongAnAlertor::~LongAnAlertor() noexcept {
    AlgoManager &am = AlgoManager::getInstance();
    char tmp[256];
    snprintf(tmp, sizeof(tmp), "LongAnAlertor%p", this);
    am.delObserver(tmp);
}
int32_t LongAnAlertor::onServerConnected(void) {
    return 0;
}
int32_t LongAnAlertor::onServerDisconnected(void) {
    return 0;
}

int32_t LongAnAlertor::onDataRecevied(const char *p, uint32_t len) {
    // logd("on cmd data %d", len);
    // logd("recv %s", my::hex(my::constr(p, len)).c_str());

    return 0;
}

bool LongAnAlertor::onAlgoEvent(std::shared_ptr<Event> e) {
    switch (e->type()) {
        case EVT_TYPE_BSD_Left:
        case EVT_TYPE_BSD_Right: {
            if (e->c.level != 3) {
                mLastBsdAlarmTs = my::timestamp::now();
            }
            break;
        }
        default: {
            break;
        }
    }
    return true;
}

#define LONG_AN_485_PROT_CMD_HELP    0

static const std::vector<CmdStrT> gLongAn485ProtCmds = {
    {"help", LONG_AN_485_PROT_CMD_HELP, "show this usage.\n"},
};

std::string LongAnAlertor::setupCmdList(const char *cmdName) {
    mCmdUsage = CmdStrT::setupCmdList(gLongAn485ProtCmds, cmdName);
    return mCmdUsage;
}

bool LongAnAlertor::runCmd(int argc, char **argv, string &ack) {
    uint32_t cmd = CmdStrT::strToCmd(gLongAn485ProtCmds, argv[0]);

    switch (cmd) {
        case LONG_AN_485_PROT_CMD_HELP: {
            ack = mCmdUsage;
            return true;
        }
        default: {
            ack = mCmdUsage;
        }
    }

    return false;
}

#define BSD_WORK_STATUS_PROP  "init.svc.bsd"
#define OBJP_WORK_STATUS_PROP "init.svc.objp"

void LongAnAlertor::run() {
    prctl(PR_SET_NAME, "LongAnAlertor");
    auto lastPlayContent = E_CONTENT_NONE;
    // bool updateMcuMsgWorking = true;

    // auto updateMcuMsg = [this, &updateMcuMsgWorking]() {
    //     uint8_t buf[MCU_MSG_MAX_SIZE];
    //     McuMessage *pMsg = (McuMessage *)&buf[0];

    //     while (updateMcuMsgWorking) {
    //         if (mIpcClient->recv(&(pMsg->ipcMsg))) {
    //             switch (pMsg->ipcMsg.type) {
    //                 case MCU_MSG_TYPE_CAR_INFO: {
    //                     if (MCU_MSG_SIZE_CAR_INFO == pMsg->ipcMsg.len) {
    //                         McuMsgCarInfoT &carInfo = pMsg->u.carInfo[0];
    //                         mIsReverseOn = carInfo.canTurn;
    //                         mIsTurnOn = carInfo.canReverse;
    //                     }
    //                 } break;
    //                 default: {
    //                     // loge("unknown mcu msg type %d, len %d", msg->type, msg->len);
    //                     break;
    //                 }
    //             }
    //         } else {
    //             loge("mcu client read fail!\n");
    //         }
    //     }
    // };
    // auto updateMcuMsgThread = std::thread(updateMcuMsg);

    while (!exiting()) {
        auto playContent = E_CONTENT_NONE;
        if (mLastBsdAlarmTs.elapsed() <= 2000) {
            playContent = E_BSD_ALERT;
        } else {
            char propValue[PROP_VALUE_MAX] = {0};
            if (__system_property_get(ALINK_ALERTOR_ALARM_MODE, propValue) > 0) {
                if (strcmp(propValue, "advance") == 0) {
                    auto &dh = DeviceHub::getInstance();
                    if (dh.getReverse() != 0) {
                        playContent = E_REVERSE;
                    } else if (dh.getTurnLeft() != 0) {
                        playContent = E_TURN_LEFT;
                    } else if (dh.getTurnRight() != 0) {
                        playContent = E_TURN_RIGHT;
                    }
                }
            }
        }

        if (mLastChkVolumeTs.elapsed() >= 1000) {
            char propValue[PROP_VALUE_MAX] = {0};
            int volume = 5;

            if (__system_property_get("persist.minieye.rs.la.volume", propValue) > 0) {
                try {
                    volume = std::stoi(propValue);
                } catch (...) {
                }
            }

            if (volume != mCurVolume) {
                mSetVolumeCnt = 0;
            }

            if (mCurVolume == -1 || mSetVolumeCnt < 3) {
                auto cmd = getVolumeCmd(volume);
                // logd("send volume cmd %s", my::hex(cmd).c_str());
                // mIpcClient->send(cmd.c_str(), cmd.length());
                msgEnque((void *)cmd.c_str(), cmd.length());
                mCurVolume = volume;
                logi("set LongAnAlertor volume %d", mCurVolume);
                mSetVolumeCnt++;
            }
            mLastChkVolumeTs = my::timestamp::now();
        }

        if (mLastAlertUpdateTs.elapsed() >= 2000 || playContent != lastPlayContent) {
            LongAnAlertorMsg msg;
            msg.playContent = playContent;

            my::string payload;
            msg.encode(payload);
            msgEnque((void *)payload.c_str(), payload.length());
            logi("set LongAnAlertor alertor content %d", playContent);

            // 龙安报警器特有，需要触发后1s发关闭的指令
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
            msg.playContent = E_CONTENT_NONE;
            payload.clear();
            msg.encode(payload);
            msgEnque((void *)payload.c_str(), payload.length());

            lastPlayContent = playContent;
            mLastAlertUpdateTs = my::timestamp::now();  // 两个命令触发的间隔为3s
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
    // updateMcuMsgWorking = false;
    // updateMcuMsgThread.join();
}

}  // namespace minieye
