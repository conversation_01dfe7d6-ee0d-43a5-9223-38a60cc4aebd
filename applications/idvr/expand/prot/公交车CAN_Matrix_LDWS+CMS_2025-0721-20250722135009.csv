"Msg Name报文名称",报文解释,"Msg Type报文类型","Msg ID报文标识符","PGN参数组编号","Msg Send Type报文发送类型","Msg Cycle Time (ms)报文周期时间","Msg Length (Byte)报文长度","Signal Name信号名称","Signal Description信号描述","Start Byte起始字节","Start Bit起始位","SPN可疑参数编号","Signal Send Type信号发送类型","Bit Length (Bit)信号长度","Date Type数据类型","Resolution精度","Offset偏移量","Signal Min. Value (phys)物理最小值","Signal Max. Value (phys)物理最大值","Signal Min. Value (Hex)总线最小值","Signal Max. Value (Hex)总线最大值","Initial Value (Hex)初始值","Invalid Value(Hex)无效值","Error Value(hex)错误值","Inactive Value (Hex)非使能值","Unit单位","Signal Value Description信号值描述",,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
AEBS1,CMS状态，摄像头发送,Normal,0x0CF02FA0,61487,Cycle,50,8,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,Advanced_emergency_braking_system_state,"Advanced emergency braking system state
系统当前状态——报警/事件类型",0,0,5676,Cycle,4,Unsigned,1,0,0,15,0x0,0xF,0x0,0xE,0xE,/,,"0x0:system is not ready (initialization not finished)系统初始化
0x1:system is temporarily not available系统起效车速未到
0x2:system is deactivated by driver驾驶员关闭系统
0x3:system is ready (no warning and no braking active)系统准备好
0x4:driver overrides system驾驶员接管系统
0x5:collision warning active (not affecting vehicle dynamics)系统一级报警——AEB预警，等级一级
0x6:collision warning with braking (e.g. brake jerk or partial braking)系统二级报警（点亮制动灯）——AEB预警，等级二级
0x7:emergency braking active系统紧急制动（点亮制动灯）——AEB制动，等级一级
0x8~0xD: Not Available
0xE:error indication系统故障
0xF:not available / not installed",,,
,,,,,,,,Collision_warning_level,"Collision warning level
碰撞报警等级",0,4,5677,Cycle,4,Unsigned,1,0,0,15,0x0,0xF,0x0,0xE,0xE,/,,"0x0:no warning
0x1:warning level 1 - lowest车辆一级报警
0x2:warning level 2行人一级报警
0x3:warning level 3车辆二级报警
0x4:warning level 4行人二级报警
0x5~0xD: Not Available
0xE:error indication
0xF:not available / not installed",,,
,,,,,,,,Relevant_Object_Detected_For_Advanced_Emergency_Braking_System,"Relevant object detected for advanced emergency braking
system
AEBS监控相关目标",1,8,5678,Cycle,3,Unsigned,1,0,0,7,0x0,0x7,0x0,0x6,0x6,/,,"0x0:no relevant object monitored
0x1:relevant object is being monitored
0x2:relevant object is not being detected reliably
0x3~0x5: Not Available
0x6:error indication
0x7:not available / not installed",,,
,,,,,,,,TimeToCollisionWithRObject,"Time to collision with relevant object
碰撞时间——TTC碰撞时间",2,16,5680,Cycle,8,Unsigned,0.05,0,0,12.5,0x0,0xFA,0x0,0x0,0x0,/,,,,,
AEBS2_27,CMS开关使能信号,Normal,0x0C0BA027,2816,Cycle,50,8,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,AEBS2_27_Drvractivationdemand,"Driver activation demand for Advanced Emergency Braking System
AEBS开关使能——AEB 制动开关状态",0,0,5681,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x1,0x0,/,/,,"0x0:the driver does not want the Advanced EmergencyBraking System to warn or intervene at any time(deactivation of system)AEBS开关
0x1:the driver wants the Advanced Emergency BrakingSystem to warn or intervene if necessary(no deactivation of system)
0x2:error
0x3:don’t' care / take no action",,,
FLI1,车道偏离状态报文，摄像头发送,Normal,0x10F007E8,61447,Cycle,50,8,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,Lane_Departure_Imminent_Right_Side,"Lane Departure Imminent, Right Side
车辆已向右偏离车道——车道偏离预警（AEB）、2：右边界预警",0,4,1701,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x0,0x2,0x2,/,,"0x0:Not imminent
0x1:Imminent
0x2:Error indicator
0x3:Not Available",,,
,,,,,,,,Lane_Departure_Imminent_Left_ Side,"Lane Departure Imminent, Left Side
车辆已向左偏离车道车辆已向右偏离车道——车道偏离预警（AEB）、1：左边界预警",0,6,1700,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x0,0x2,0x2,/,,"0x0:Not imminent
0x1:Imminent
0x2:Error indicator
0x3:Not Available",,,
FLI2,车道偏离状态报文，摄像头发送,Normal,0x18FE5BE8,65115,Cycle,100,8,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,Lane_Tracking_Status_Right_Side,"Lane Tracking Status Right Side
是否检测到右边车道线",0,2,1711,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x3,0x3,0x3,/,,"0x0:Not tracking right side
0x1:Tracking right side
0x2:Reserved
0x3:Don’t care/take no action",,,
,,,,,,,,Lane_Tracking_Status_Left_Side,"Lane Tracking Status Left Side
是否检测到左边车道线",0,4,1710,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x3,0x3,0x3,/,,"0x0:Not tracking left side
0x1:Tracking left side
0x2:Reserved
0x3:Don’t care/take no action",,,
,,,,,,,,Lane_Departure_Indication_Enable_Status,"Lane Departure Indication Enable Status
LDWS功能开启状态",0,6,1702,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x1,0x3,0x3,/,,"0x0:Lane departure indication disabled
0x1:Lane departure indication enabled
0x2:Reserved
0x3:Not used",,,
WEDR,数据存储报文，摄像头发送,Normal,0x0CF0EEA0,61678,Cycle,200,8,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,Advanced_emergency_braking_system_state,"Advanced emergency braking system state
系统当前状态",0,0,5676,Cycle,4,Unsigned,1,0,0,15,0x0,0xF,0x0,0xE,0xE,,,"0x0:system is not ready (initialization not finished)系统初始化
0x1:system is temporarily not available系统起效车速未到
0x2:system is deactivated by driver驾驶员关闭系统
0x3:system is ready (no warning and no braking active)系统准备好
0x4:driver overrides system驾驶员接管系统
0x5:collision warning active (not affecting vehicle dynamics)系统一级报警
0x6:collision warning with braking (e.g. brake jerk or partial braking)系统二级报警（点亮制动灯）
0x7:emergency braking active系统紧急制动（点亮制动灯）
0x8~0xD: Not Available
0xE:error indication系统故障
0xF:not available / not installed",,,
,,,,,,,,Lane_Departure_Indication_Enable_Status,"Lane Departure Indication Enable Status
LDWS功能开启状态",1,8,1702,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x3,0x3,0x3,,,"0x0:Lane departure indication disabled
0x1:Lane departure indication enabled
0x2:Reserved
0x3:Not used",,,
,,,,,,,,Lane_Departure_Imminent_Right_Side,"Lane Departure Imminent, Right Side
车辆已向右偏离车道",1,10,1701,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x0,0x2,0x2,,,"0x0:Not imminent
0x1:Imminent
0x2:Error
0x3:Not Available",,,
,,,,,,,,Lane_Departure_Imminent_Left_Side,"Lane Departure Imminent, Left Side
车辆已向左偏离车道",1,12,1700,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x0,0x2,0x2,,,"0x0:Not imminent
0x1:Imminent
0x2:Error
0x3:Not Available",,,
,,,,,,,,Distancetomaintargetahead,前方主要目标距离——纵向相对距离,2,16,,Cycle,8,Unsigned,1,0,0,250,0x0,0xFA,0x0,0xFF,0xFE,,,,,,
,,,,,,,,Relativevelocitytomaintargetahead,前方主要目标相对速度——纵向相对速度,3,24,,Cycle,8,Unsigned,1,-125,-125,125,0x0,0xFA,0x0,0xFF,0xFE,,,,,,
,,,,,,,,Amber_Warning_Lamp_Status_LDW,LDW故障灯,4,32,624,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x0,0x3,0x3,,,"0x0: Lamp off
0x1: Lamp on
0x2: Reserved
0x3: Not available",,,
,,,,,,,,Amber_Warning_Lamp_Status_FCW,FCW故障灯,4,36,624,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x0,0x3,0x3,,,"0x0: Lamp off
0x1: Lamp on
0x2: Reserved
0x3: Not available",,,
,,,,,,,,AEB_CollisionRisk_trigEDR,AEB碰撞风险触发EDR,6,48,,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x3,,,,,"0x0:AEB Collision Risk no trig Mode
0x1:AEB Collision Risk trig Mode
0x2:Reserved
0x3:LDW & FCW Mode",,,
,,,,,,,,less_than_20_second_after_last_AEBtrig,距离上次AEB触发小于20s,6,50,,Cycle,1,Unsigned,1,0,0,1,0x0,0x1,0x0,,,,,"0x0: 20s or more since the last AEB trigger
0x1: Less than 20s since the last AEB trigger",,,
DM1_0x18FECAA0,数据存储报文，摄像头发送,Normal,0x18FECAA0x,65226,Cycle,1000,8,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,DM1_A0_Amber_Warning_Lamp_Status,"This lamp is used to relay trouble code information that is reporting a problem with the vehicle system but the vehicle need
not be immediately stopped.故障灯",0,2,624,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x0,0x3,,,,"0x0: Lamp off
0x1: Lamp on
0x2: Reserved
0x3: Not available",,,
,,,,,,,,DM1_A0_DM1_SPN_Low_8,,2,16,1214,Cycle,8,Unsigned,1,0,0,255,0x0,0xFF,0x0,,,,,,,,
,,,,,,,,DM1_A0_DM1_SPN_Second_Byte,,3,24,1214,Cycle,8,Unsigned,1,0,0,255,0x0,0xFF,0x0,,,,,,,,
,,,,,,,,DM1_A0_FMI,,4,32,1215,Cycle,5,Unsigned,1,0,0,31,0x0,0x1F,0x0,,,,,,,,
,,,,,,,,DM1_A0_DM1_SPN_High_3,,4,37,1214,Cycle,3,Unsigned,1,0,0,7,0x0,0x7,0x0,,,,,,,,
,,,,,,,,DM1_A0_OccurrenceCount,,5,40,1216,Cycle,7,Unsigned,1,0,0,127,0x0,0x7F,0x0,,,,,,,,
,,,,,,,,DM1_A0_SPNConversionMethod,,5,47,1706,Cycle,1,Unsigned,1,0,0,1,0x0,0x1,0x0,,,,,,,,
TP_CM_DM1_A0,CMS故障报文，摄像头发送,Normal,0x18ECFFA0x,60416,IfActive,,8,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,DM1_CM_A0_ControlByte,传输控制状态,0,0,2556,IfActive,8,Unsigned,1,0,0,255,0x0,0xFF,0x20,,,,,,,,
,,,,,,,,DM1_CM_A0_TotalNumberOfBbytes,传输总字节数,1,8,2567,IfActive,16,Unsigned,1,0,0,65535,0x0,0xFFFF,0x0,,,,,,,,
,,,,,,,,DM1_CM_A0_TotalNumberOfPackets,传输总包数,3,24,2568,IfActive,8,Unsigned,1,0,0,255,0x0,0xFF,0x0,,,,,,,,
,,,,,,,,DM1_CM_A0_ParameterGroupNumber,需要传输报文的PGN,5,40,2569,IfActive,24,Unsigned,1,0,0,16777215,0x0,0xFFFFFF,0x0,,,,,,,,
TP_DT_DM1_A0,CMS故障报文，摄像头发送,Normal,0x18EBFFA0x,60160,IfActive,,8,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,DM1_DT_A0_SequenceNumber,序列号,0,0,2572,IfActive,8,Unsigned,1,0,0,255,0x0,0xFF,0x0,,,,,,,,
,,,,,,,,DM1_DT_A0_PacketisedData,,1,8,2573,IfActive,56,Unsigned,1,0,0,7.20576E+16,0x0,0xFFFFFFFFFFFFFF,0x0,,,,,,,,
DM1_0x18FECAE8,车道偏离故障码报文，摄像头发送,Normal,0x18FECAE8x,65226,Cycle,1000,8,,,,,,,,,,,,,,,,,,,,,,,
TP_CM_DM1_E8,车道偏离故障码报文，摄像头发送,Normal,0x18ECFFE8x,60416,IfActive,,8,,,,,,,,,,,,,,,,,,,,,,,
TP_DT_DM1_E8,车道偏离故障码报文，摄像头发送,Normal,0x18EBFFE8x,60160,IfActive,,8,,,,,,,,,,,,,,,,,,,,,,,
EBC1,ABS相关报文,Normal,0x18F0010B,61441,Cycle,100,8,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,ASREngCtrlActive,State signal which indicates that ASR engine control has been commanded to be active.ASR发动机控制激活,0,0,561,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x0,0x3,,,,"0x0: ASREngCtrlPassiveButInstalled
0x1: ASREngCtrlActive
0x2: Reserved
0x3: NotAvailable",,,
,,,,,,,,ASRBrakeCtrlActive,State signal which indicates that ASR brake control is active.ASR制动控制激活,0,2,562,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x0,0x3,,,,"0x0: ASRBrakeCtrlPassiveButInstalled
0x1: ASRBrakeCtrlActive
0x2: Reserved
0x3: NotAvailable",,,
,,,,,,,,AntiLockBrakingActive,State signal which indicates that the ABS is active.ABS激活,0,4,563,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x0,0x3,,,,"0x0: ABSPassiveButInstalled
0x1: ABSActive
0x2: Reserved
0x3: NotAvailable",,,
,,,,,,,,EBS_BrakeSwitch,"EBS Brake Switch
制动开关状态",0,6,1121,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x0,0x3,0x2,,,"0x0:Brake pedal is not being pressed
0x1:Brake pedal is being pressed
0x2:Error
0x3:Not available",,,
,,,,,,,,EBS_BrakePedalPosition,"Brake Pedal Position
制动踏板位置——刹车踏板开度",1,8,521,Cycle,8,Unsigned,0.4,0,0,100,0x0,0xFA,0,0x0,,,,,,,
,,,,,,,,ABSFullyOperational,Signal which indicates whether an ABS system is fully operational or whether its functionality is reduced by a defect or by an intended action.ABS全功能信号,5,40,1243,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x0,0x3,,,,"0x0: NotFullyOperational
0x1: FullyOperational
0x2: Reserved
0x3: NotAvailable",,,
,,,,,,,,ABS_EBSAmberWarningSignal,This parameter commands the ABS/EBS amber/yellow optical warning signal。ABS故障灯,5,44,1438,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x0,0x3,,,,"0x0: Off
0x1: On
0x2: Reserved
0x3: TakeNoAction",,,
,,,,,,,,EBS_RedWarningState,用于显示EBS红灯状态,5,42,1439,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x0,0x3,,,,"0x0:Off
0x1:On
0x2: Reserved
0x3:Take no action",,,
EBC2,车速报文,Normal,0x18FEBF0B,65215,Cycle,50,8,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,Mean_Front_Axle_Speed,The average speed of the two front wheels.Invalid Value:0xFB00~0xFFFF前轴速,0,0,904,Cycle,16,Unsigned,1/256,0,0,251,0x0,0xFFFF,0x0,0xFFFF,0xFEFE,,km/h,"0xFB00-0xFDFF:Invalid
0xFE00-0xFEFF:Error Indicator
0xFF00-0xFFF:Not Available ",,,
EBC 5,EBS相关,Normal,0x18FDC40B,64964,Cycle,100,8,,,,,,,,,,,,,,,,,,,,,,,
VDC1,ESC相关,Normal,0X18FE4F0Bx,65103,Cycle,100,8,,,,,,,,,,,,,,,,,,,,,,,
VDC2,ESC相关,Normal,0x18F0090Bx,61449,Cycle,10,8,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,SteerWheelAngle,"Steering Wheel Angle
方向盘转角——方向盘角度",0,0,1807,Cycle,16,Unsigned,0.000976563,-31.374,-31.374,31.374,0x0,0xF9EB,0xFFFF,0xFF00~0xFFFF,0xFE00,,,"0xFE00~0xFEFF:Error
0xFF00~0xFFFF: Invalid",,,
,,,,,,,,YawRate,"Yaw Rate
横摆角速度",3,24,1808,Cycle,16,Unsigned,0.00012207,-3.92,-3.92,3.92,0x0,0xFAE1,0xFFFF,,,,,,,,
,,,,,,,,LateralAcceleration,"Lateral Acceleration
横向加速度",5,40,1809,Cycle,16,Unsigned,0.000488281,-15.687,-15.687,15.687,0x0,0xFAFE,0xFFFF,,,,,,,,
,,,,,,,,LongitudinalAcceleration,"Longitudinal Acceleration
纵向加速度",7,56,1810,Cycle,8,Unsigned,0.1,-12.5,-12.5,12.5,0x0,0xFA,0xFF,,,,,,,,
EEC2,油门踏板位置报文,Normal,0xCF00300,61443,Cycle,50,8,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,Accel_Pedal_Position1 ,The ratio of actual position of the analog engine speed/torque request input device。加速踏板百分比,1,8,91,Cycle,8,Unsigned,0.4,0,0,100,0x0,0xFA,0x0,/,0xFE,/,%,,,,
ETC2,档位信号报文,Normal,0x18F00503,61445,Cycle,100,8,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,Current_Gear ,"The gear currently engaged in the transmission or the last gear engaged while the transmission is in the process of
shifting to the new or selected gear. Transitions toward a destination gear will not be indicated. Once the selected gear
has been engaged then Transmission Current Gear (SPN 523) will reflect that gear.当前档位信号",3,24,523,Cycle,8,Unsigned,1,-125,-125,125,0x0,0xFF,0xFF,0xFF,0xFE,,,0xFB: Park,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
OEL_27,转向灯开关报文,Normal,0x0CFDCC27,64972,CA,1000,8,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,OEL_27_Turn_Signal_Switch,A 4 bit parameter to indicate the selected position of the operator's turn signal switch.转向开关状态,1,8,2876,Cycle,4,Unsigned,1,0,0,15,0x0,0xF,0x0,0x0,/,/,,"0x0:No Turn being signaled
0x1:Left Turn to be Flashing
0x2:Right turn to be Flashing
0x3-0xD:Reserved
0xE:Error
0xF:Not available",,,
,,,,,,,,Amber_Warning_Lamp_Status_FCW,FCW故障灯,4,36,624,Cycle,2,Unsigned,1,0,0,3,0x0,0x3,0x0,0x3,0x3,,,"0x0: Lamp off
0x1: Lamp on
0x2: Reserved
0x3: Not available",,,
,,,,,,,,DM1_2A_SPNConversionMethod,,5,47,1706,Cycle,1,Unsigned,1,0,0,1,0x0,0x1,0x0,,,,,,,,
UDAS,,Normal,0x0CF901B2,63745,Cycle,100,8,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,RA,后右,0,0,,Cycle,8,Unsigned,2,0,0,255,0x0,0xFF,,,,,cm,"0x0:没有距离
0x1~0xFA:对应检测距离（目标障碍物小于45cm时，保持0x17；计算障碍物距离：将CAN信息转成10进制*2）
0xFB~0xFC:无效
0xFD:雷达偶发性故障
0xFE:雷达故障
0xFF:无效",,,
,,,,,,,,RB,后右中,1,8,,Cycle,8,Unsigned,2,0,0,255,0x0,0xFF,,,,,cm,"0x0:没有距离
0x1~0xFA:对应检测距离（目标障碍物小于45cm时，保持0x17；计算障碍物距离：将CAN信息转成10进制*2）
0xFB~0xFC:无效
0xFD:雷达偶发性故障
0xFE:雷达故障
0xFF:无效",,,
,,,,,,,,RC,后左中,2,16,,Cycle,8,Unsigned,2,0,0,255,0x0,0xFF,,,,,cm,"0x0:没有距离
0x1~0xFA:对应检测距离（目标障碍物小于45cm时，保持0x17；计算障碍物距离：将CAN信息转成10进制*2）
0xFB~0xFC:无效
0xFD:雷达偶发性故障
0xFE:雷达故障
0xFF:无效",,,
,,,,,,,,RD,后左,3,24,,Cycle,8,Unsigned,2,0,0,255,0x0,0xFF,,,,,cm,"0x0:没有距离
0x1~0xFA:对应检测距离（目标障碍物小于45cm时，保持0x17；计算障碍物距离：将CAN信息转成10进制*2）
0xFB~0xFC:无效
0xFD:雷达偶发性故障
0xFE:雷达故障
0xFF:无效",,,
,,,,,,,,FA,前左,4,32,,Cycle,8,Unsigned,2,0,0,255,0x0,0xFF,,,,,cm,"0x0:没有距离
0x1~0xFA:对应检测距离（目标障碍物小于45cm时，保持0x17；计算障碍物距离：将CAN信息转成10进制*2）
0xFB~0xFC:无效
0xFD:雷达偶发性故障
0xFE:雷达故障
0xFF:无效",,,
,,,,,,,,FB,前左中,5,40,,Cycle,8,Unsigned,2,0,0,255,0x0,0xFF,,,,,cm,"0x0:没有距离
0x1~0xFA:对应检测距离（目标障碍物小于45cm时，保持0x17；计算障碍物距离：将CAN信息转成10进制*2）
0xFB~0xFC:无效
0xFD:雷达偶发性故障
0xFE:雷达故障
0xFF:无效",,,
,,,,,,,,FC,前右中,6,48,,Cycle,8,Unsigned,2,0,0,255,0x0,0xFF,,,,,cm,"0x0:没有距离
0x1~0xFA:对应检测距离（目标障碍物小于45cm时，保持0x17；计算障碍物距离：将CAN信息转成10进制*2）
0xFB~0xFC:无效
0xFD:雷达偶发性故障
0xFE:雷达故障
0xFF:无效",,,
,,,,,,,,FD,前右,7,56,,Cycle,8,Unsigned,2,0,0,255,0x0,0xFF,,,,,cm,"0x0:没有距离
0x1~0xFA:对应检测距离（目标障碍物小于45cm时，保持0x17；计算障碍物距离：将CAN信息转成10进制*2）
0xFB~0xFC:无效
0xFD:雷达偶发性故障
0xFE:雷达故障
0xFF:无效",,,
