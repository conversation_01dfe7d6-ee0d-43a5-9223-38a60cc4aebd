

# 声光报警器RS232通信协议

1. 串口配置： 9600_8N1

2. 协议格式：(小端字节序)

| OFFSET | LENGTH |                 CONTENT                  |
| :----: | :----: | :--------------------------------------: |
|   0    |   2    |            帧头：固定=0x55AA             |
|   2    |   1    |                  命令字                  |
|   3    |   1    |           数据长度N, [0, 255]            |
|   4    |   N    |                   数据                   |
|  4+N   |   1    | 校验和（校验范围是校验和之前的所有字节） |

3. 命令字定义

| 命令字 |   定义   |      |
| :----: | :------: | ---- |
|  0x80  | 报警触发 |      |
|  0x81  | 报警停止 |      |
|  0x82  | 告警级别 |      |

4. 指令报文样例：

|   功能   |         报文          |                         说明                         |
| :------: | :-------------------: | :--------------------------------------------------: |
| 报警触发 |    AA 55 80 00  7F    |               数据长度为0， 校验和0x7F               |
| 告警级别 | AA 55 82 02  00 03 86 | 数据长度为1，默认告警事件0 告警级别为 3级 校验和0x86 |
|          |                       |                                                      |

5. 校验和代码


```c
uint8_t checkSum(uint8_t * data, uint16_t len)
{
    uint32_t sum = 0; 
    for (uint16_t i = 0; i < len; i++) {
        sum += data[i];
    }   
    return (sum & 0xFF);
}
```

