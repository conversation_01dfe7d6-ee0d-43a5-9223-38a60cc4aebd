#include <assert.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <sys/time.h>
#include <fstream>
#include <set>

#include "expand.h"
#include "devicehub.h"
#include "algoOutputCanProt.h"
#include "system_properties.h"
#include "CRingBuf.h"
#include "RingBufFrame.h"

namespace minieye
{
enum ZC_ADAS_SOUND_TYPE {
    ZC_ADAS_SOUND_NONE = 0,
    ZC_ADAS_SOUND_FOLLOW,
    ZC_ADAS_SOUND_SKIDDING,
    ZC_ADAS_SOUND_LDW_LEFT,
    ZC_ADAS_SOUND_LDW_RIGHT,
    ZC_ADAS_SOUND_HW,
    ZC_ADAS_SOUND_PFCW,
    ZC_ADAS_SOUND_FCW
};

enum {
    ZC_ADAS_FCW_LEVEL_NONE = 0,
    ZC_ADAS_FCW_LEVEL_PFCW,
    ZC_ADAS_FCW_LEVEL_FCW
};

enum {
    ZC_ADAS_HW_LEVEL_NONE = 0,
    ZC_ADAS_HW_LEVEL_NOWARN,
    ZC_ADAS_HW_LEVEL_WARN
};

enum ZC_DFM_ALARM_TYPE {
    ZC_DFM_ALARM_NONE = 0,
    ZC_DFM_FATIGUE_EYE,
    ZC_DFM_FATIGUE_YAWN,
    ZC_DFM_LOOK_DOWN = 4,
    ZC_DFM_LOOK_AROUND,
    ZC_DFM_ABSENCE,
    ZC_DFM_PHONE_CALL,
    ZC_DFM_SMOKE,
    ZC_DFM_EYE_OCCLUSION
};

enum ZhongChiAdas1CanOption : int32_t {
    ZC_ADAS1_SOUND_TYPE = 0,
    ZC_ADAS1_HEADWAY_MEASUREMENT,
    ZC_ADAS1_HEADWAY_VALID,
    ZC_ADAS1_LWD_SW,
    ZC_ADAS1_LEFT_LWD,
    ZC_ADAS1_RIGHT_LWD,
    ZC_ADAS1_VEHICLE_MONITORING,
    ZC_ADAS1_FCW_LEVEL,
    ZC_ADAS1_HW,
    ZC_ADAS1_FAULT_CODE,
};

typedef struct ZhongChiAdas1CanPkt_S {
    uint32_t frameId = 0x18FEF858;
    uint8_t  len = 8;

    uint8_t  soundType          : 4 = 0;
    uint8_t  r0                 : 4 = 0;

    uint8_t  r1                 : 1 = 0;
    uint8_t  headwayMeasurement : 7 = 0;

    uint8_t  headwayValid       : 2 = 0;
    uint8_t  ldwSw              : 2 = 0;
    uint8_t  leftLdw            : 2 = 0;
    uint8_t  rightLdw           : 2 = 0;

    uint8_t  r3                 : 6 = 0;
    uint8_t  vehicleMonitoring  : 2 = 0;

    uint8_t  fcwLevel           : 2 = 0;
    uint8_t  hw                 : 2 = 0;
    uint8_t  r4                 : 4 = 0;

    uint8_t r5_6[2] = {0};

    uint8_t faultCode = 0;
} __attribute__((packed)) ZhongChiAdas1CanPkt_S;

typedef struct ZhongChiAdas2CanPkt_S {
    uint32_t frameId = 0x18FEF958;
    uint8_t  len = 8;

    uint8_t  dfmAlarmType = 0;

    uint8_t  r1_7[7] = {0};
} __attribute__((packed)) ZhongChiAdas2CanPkt_S;

#define CAR_FLIC_CAN_ID 0x18A9175D

enum BsdFlicAudibleWarning {
    E_FLIC_AUDIBLE_WARNING_OFF = 0,
    E_FLIC_AUDIBLE_WARNING_ON,
};

enum BsdFlicAlarmLevel {
    E_FLIC_ALARM_LEVEL_NONE = 0,
    E_FLIC_ALARM_LEVEL_L1,
    E_FLIC_ALARM_LEVEL_L2,
    E_FLIC_ALARM_LEVEL_L3,
};

/*苏州金龙协议复用*/
struct CarFlicInfo {
    uint32_t frameId = CAR_FLIC_CAN_ID;
    uint8_t len = 8;

    uint8_t r0 = 0;

    uint8_t rightBsdWarning       : 2 = 0;
    uint8_t rightBsdLevel         : 2 = 0; /*仅启用前面四位，后面的都默认0*/
    uint8_t rightBsdAlarmCategory : 2 = 0;
    uint8_t r1                    : 2 = 0;

    uint8_t rightBsdAlarmType     : 2 = 0;
    uint8_t rightLcaAlarmArea     : 2 = 0;
    uint8_t r2                    : 4 = 0;

    uint8_t leftBsdWarning        : 2 = 0;
    uint8_t leftBsdLevel          : 2 = 0;
    uint8_t leftBsdAlarmCategory  : 2 = 0;
    uint8_t r3                    : 2 = 0;

    uint8_t leftBsdAlarmType      : 2 = 0;
    uint8_t leftLcaAlarmArea      : 2 = 0;
    uint8_t r4                    : 4 = 0;

    uint8_t r5_7[3] = {0};

} __attribute__((packed));

static ZC_ADAS_SOUND_TYPE adasCan2protSound(SOUND_TYPE_E s) {
    static const std::map<SOUND_TYPE_E, ZC_ADAS_SOUND_TYPE> subProtSoundEWMap = {
        {SOUND_TYPE_SILENT, ZC_ADAS_SOUND_NONE},
        {SOUND_TYPE_LDW_Left, ZC_ADAS_SOUND_LDW_LEFT},
        {SOUND_TYPE_LDW_Right, ZC_ADAS_SOUND_LDW_RIGHT},
        {SOUND_TYPE_HW, ZC_ADAS_SOUND_HW},
    };

    auto subProt = subProtSoundEWMap.find(s);

    if (subProt != subProtSoundEWMap.end()) {
        return subProt->second;
    }

    logd("not find sound %d, use ZC_ADAS_SOUND_NONE instead.", s);
    return ZC_ADAS_SOUND_NONE;
}

static ZC_DFM_ALARM_TYPE dmsEvt2protEvt(EVT_TYPE evt) {
    static const std::map<EVT_TYPE, ZC_DFM_ALARM_TYPE> subProtDmsEWMap = {
        {EVT_TYPE_DMS_FATIGUE_Eye, ZC_DFM_FATIGUE_EYE},
        {EVT_TYPE_DMS_FATIGUE_YAWN, ZC_DFM_FATIGUE_YAWN},
        {EVT_TYPE_DMS_LOOK_DOWN, ZC_DFM_LOOK_DOWN},
        {EVT_TYPE_DMS_LOOK_AROUND, ZC_DFM_LOOK_AROUND},
        {EVT_TYPE_DMS_ABSENCE, ZC_DFM_ABSENCE},
        {EVT_TYPE_DMS_PHONE_CALL, ZC_DFM_PHONE_CALL},
        {EVT_TYPE_DMS_SMOKE, ZC_DFM_SMOKE},
        {EVT_TYPE_DMS_EYE_OCCLUSION, ZC_DFM_EYE_OCCLUSION},
    };

    auto subProt = subProtDmsEWMap.find(evt);

    if (subProt != subProtDmsEWMap.end()) {
        return subProt->second;
    }

    logd("not find evt %d", evt);
    return ZC_DFM_ALARM_NONE;
}

enum WX_BSD_ALARM_LEVEL {
    WX_BSD_ALARM_LEVEL_NONE = 0,
    WX_BSD_ALARM_LEVEL_NOWARN,
    WX_BSD_ALARM_LEVEL_WARN_L1,
    WX_BSD_ALARM_LEVEL_WARN_L2
};

enum WX_BSD_WORK_STATE {
    WX_BSD_WORK_STATE_STOPPED = 0,
    WX_BSD_WORK_STATE_RUNNING,
    WX_BSD_WORK_STATE_FAULT,
    WX_BSD_WORK_STATE_SELF_TEST,
    WX_BSD_WORK_STATE_UNENABLED,
    WX_BSD_WORK_STATE_ERROR_SIGNAL = 6,
    WX_BSD_WORK_STATE_SIGNAL_INVALID
};

enum WX_BSD_FAULT_CODE {
    WX_BSD_FAULT_CODE_NONE = 0,
    WX_BSD_FAULT_CODE_MINOR_FAULT,
    WX_BSD_FAULT_CODE_GENERAL_FAULT,
    WX_BSD_FAULT_CODE_FATAL_FAULT
};

enum WX_BSD_TARGET_TYPE {
    WX_BSD_TARGET_NONE = 0,
    WX_BSD_TARGET_PEDESTRIAN,
    WX_BSD_TARGET_VEHICLE,
    WX_BSD_TARGET_OTHER
};

typedef struct WanXiangBsdCanPkt_S {
    uint32_t frameId = 0x18F00E8D;
    uint8_t  len = 8;

    uint8_t  alarmLevel     : 3 = 0;
    uint8_t  workState      : 3 = 0;
    uint8_t  sysFault       : 2 = 0;

    uint8_t  commFault      : 2 = 0;
    uint8_t  bsdTarget      : 3 = 0;
    uint8_t  cameraState    : 3 = 0;

    uint8_t  cameraFault    : 2 = 0;
    uint8_t  r2             : 6 = 0b111111;

    uint8_t  r3_7[5] = {0xFF, 0xFF, 0xFF, 0xFF, 0xFF};
} __attribute__((packed)) WanXiangBsdCanPkt_S;

// 伊格威尔定制：支持swayhigh告警显示器
enum SwayHighCanDisplayWarnings : uint8_t {
    E_SH_WARNING_NONE = 0x00,          // 不显示
    E_SH_WARNING_RED_SOLID = 0x01,     // 红色常亮
    E_SH_WARNING_YELLOW_SOLID = 0x02,  // 黄色常亮
    E_SH_WARNING_GREEN_SOLID = 0x03,   // 绿色常亮
    E_SH_WARNING_RED_BLINK = 0x04,     // 红色闪烁
    E_SH_WARNING_YELLOW_BLINK = 0x05,  // 黄色闪烁
    E_SH_WARNING_GREEN_BLINK = 0x06    // 绿色闪烁
};

enum SwayHighCanDisplayStatus : uint8_t {
    E_SH_DISPLAY_NONE = 0x00,  // 不显示
    E_SH_DISPLAY_WHITE_SOLID = 0x01,   // 白色常亮
    E_SH_DISPLAY_RED_SOLID = 0x02,     // 红色常亮
    E_SH_DISPLAY_YELLOW_SOLID = 0x03,  // 黄色常亮
    E_SH_DISPLAY_WHITE_BLINK = 0x04,   // 白色闪烁
    E_SH_DISPLAY_RED_BLINK = 0x05,     // 红色闪烁
    E_SH_DISPLAY_YELLOW_BLINK = 0x06   // 黄色闪烁
};

enum SwayHighCanDisplayErrors : uint8_t {
    E_SH_ERROR_NONE = 0x00,  // 不显示
    E_SH_ERROR_YELLOW_SOLID = 0x01,  // 黄色常亮
    E_SH_ERROR_RED_SOLID = 0x02,     // 红色常亮
    E_SH_ERROR_YELLOW_BLINK = 0x03,  // 黄色闪烁
    E_SH_ERROR_RED_BLINK = 0x04      // 红色闪烁
};

struct SwayHighCanDisplayWarningPkt {
    uint32_t frameId = 0x790;
    uint8_t len = sizeof(SwayHighCanDisplayWarningPkt) - 5;

    uint8_t type = 0x01;

    SwayHighCanDisplayWarnings frontPed;  // 车前行人状态
    SwayHighCanDisplayWarnings leftPed;   // 左侧行人状态
    SwayHighCanDisplayWarnings rightPed;  // 右侧行人状态

    static_assert(sizeof(SwayHighCanDisplayWarnings) == 1, "size error");

    // uint8_t r[4] = {0};

} __attribute__((packed));

struct SwayHighCanDisplayStatusPkt {
    uint32_t frameId = 0x790;
    uint8_t len = sizeof(SwayHighCanDisplayStatusPkt) - 5;

    uint8_t type = 0x02;

    SwayHighCanDisplayStatus frontIndicator;  // 车前指示
    SwayHighCanDisplayStatus vehicleIcon;     // 车辆图标
    SwayHighCanDisplayStatus leftIndicator;   // 左侧指示
    SwayHighCanDisplayStatus rightIndicator;  // 右侧指示

    static_assert(sizeof(SwayHighCanDisplayStatus) == 1, "size error");

    // uint8_t r[3] = {0};

} __attribute__((packed));

struct SwayHighCanDisplayErrorPkt {
    uint32_t frameId = 0x790;
    uint8_t len = sizeof(SwayHighCanDisplayErrorPkt) - 5;

    uint8_t type = 0x03;

    SwayHighCanDisplayErrors systemError;  // 系统异常提示
    SwayHighCanDisplayErrors cameraFault;  // 摄像头故障提示
    SwayHighCanDisplayErrors maintenance;  // 设备维护提示

    static_assert(sizeof(SwayHighCanDisplayErrors) == 1, "size error");

    // uint8_t r[4] = {0};
} __attribute__((packed));

static CRingBuf *pRBuf = nullptr;

#define ENABLE_EXPIRE_PROTO 0

#define BSD_WORK_STATUS_PROP "init.svc.bsd"
#define OBJP_WORK_STATUS_PROP "init.svc.objp"

#define ALGO_OUTPUT_CAN_PROT_CMD_HELP       0
#define ALGO_OUTPUT_CAN_PROT_CMD_SHOW       1
#define ALGO_OUTPUT_CAN_PROT_CMD_TRIGGER    2
static const std::vector<CmdStrT> gAlgoOutputCanProtCmds = {
    {
        "help",
        ALGO_OUTPUT_CAN_PROT_CMD_HELP,
        "show this usage.\n"
    },
    {
        "show",
        ALGO_OUTPUT_CAN_PROT_CMD_SHOW,
        "show last  data.\n"
    },
    {
        "trigger",
        ALGO_OUTPUT_CAN_PROT_CMD_TRIGGER,
        "fake to trigger event.\n"
    },
};
AlgoOutputCanProt::AlgoOutputCanProt()
{
    AlgoManager & am = AlgoManager::getInstance();
    char tmp[256];
    snprintf(tmp, sizeof(tmp), "AlgoOutputCanProt%p", this);
    am.addObserver(tmp, this);

    char propValue[PROP_VALUE_MAX] = {0};
    if (__system_property_get("persist.algo.output.can.prot", propValue) > 0) {
        mCanProtName = propValue;
    }

    // if (mCanProtName == "WanXiang") {
    //     auto rbName = "raw_bsd_main";
    //     auto bufSize = CRingBuf::getRbSize(rbName);
    //     pRBuf = new CRingBuf("WanXiang", rbName, bufSize, CRB_PERSONALITY_READER);
    // }
}
AlgoOutputCanProt::~AlgoOutputCanProt()
{
    AlgoManager & am = AlgoManager::getInstance();
    am.delObserver("AlgoOutputCanProt");

    if(pRBuf != nullptr){
        delete pRBuf;
        pRBuf = nullptr;
    }
}

std::string AlgoOutputCanProt::setupCmdList(const char * cmdName)
{
    mCmdUsage = CmdStrT::setupCmdList(gAlgoOutputCanProtCmds, cmdName);
    return mCmdUsage;
}

bool AlgoOutputCanProt::runCmd(int argc, char **argv, string &ack)
{
    uint32_t cmd = CmdStrT::strToCmd(gAlgoOutputCanProtCmds, argv[0]);

    switch (cmd) {
        case ALGO_OUTPUT_CAN_PROT_CMD_HELP: {
                ack = mCmdUsage;
                return true;
            }

        case ALGO_OUTPUT_CAN_PROT_CMD_SHOW: {
                ack = "\n";

                return true;
            }
        case ALGO_OUTPUT_CAN_PROT_CMD_TRIGGER: {
                if (argc >= 3) {
                    DeviceHub &devHub = DeviceHub::getInstance();
                    AlgoManager & am = AlgoManager::getInstance();
                    am.triggerEvent(argv[1], argv[2], devHub.getCarSpeed());
                    return true;
                }
            }
        default: {
                ack = mCmdUsage;
            }
    }

    return false;
}


int32_t AlgoOutputCanProt::onServerConnected(void)
{
    if (!mbStart) {
        mbStart = !start();
    }
    return 0;
}
int32_t AlgoOutputCanProt::onServerDisconnected(void)
{
    if (mbStart) {
        mbStart = false;
        stop();
    }
    return 0;
}

int32_t AlgoOutputCanProt::onDataRecevied(const char *p, uint32_t len)
{
    logd("on cmd data %d", len);
    logd("recv %s", my::hex(my::constr(p, len)).c_str());

    onCmdData(mRcvArray, p, len);
    return 0;
}

int32_t AlgoOutputCanProt::onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len)
{

    return 0;
}

int32_t AlgoOutputCanProt::onDataParse(const char *p, uint32_t size)
{

    return 0;
}

void AlgoOutputCanProt::run() {
    if (mCanProtName == "EagleWill") {
        mSwayHighCanDisplayWarningPkt = std::make_shared<SwayHighCanDisplayWarningPkt>();
        mSwayHighCanDisplayStatusPkt = std::make_shared<SwayHighCanDisplayStatusPkt>();
        mSwayHighCanDisplayErrorPkt = std::make_shared<SwayHighCanDisplayErrorPkt>();
    }

    while (!exiting()) {
        DeviceHub &devHub = DeviceHub::getInstance();
        McuMsgMcuStatT s;
        devHub.getMcuMsgStat(s);

#if ENABLE_EXPIRE_PROTO
        if (!mCanProtName.length() || (mCanProtName == "WoTeJia")) {
            outputCanMsgWoTeJia(s);
        } else
#endif
        if (mCanProtName == "ZhongChi") {
            outputCanMsgZhongChi();
        } else if (mCanProtName == "WanXiang") {
            outputCanMsgWanXiang();
        } else if (mCanProtName == "SuZhouShuWei") {
            outputCanMsgSuZhouShuWei();
        } else if (mCanProtName == "XiaMenCiBei") {
            outputOilIOXiaMenCiBei();
        } else if (mCanProtName == "EagleWill") {
            outputCanMsgSwayHighCanDisplay();
        } else if (mCanProtName == "JinLongSingleBsd"){
            outputCanMsgJinLongSingleBsd();
        }

        msleep(10);
    }
}

bool AlgoOutputCanProt::onAlgoEvent(std::shared_ptr<Event> e)
{
    DeviceHub &devHub = DeviceHub::getInstance();
    switch (e->type()) {
        case EVT_TYPE_BSD_Front:
        case EVT_TYPE_BSD_Behind: {
            if (mCanProtName == "XiaMenCiBei") {
                onBsdEventXiaMenCiBei(e);
            } else if (mCanProtName == "WanXiang") {
                onBsdEventWanXiang(e);
            }
            break;
        }
        case EVT_TYPE_BSD_Left: {
            if (mCanProtName == "XiaMenCiBei") {
                onBsdEventXiaMenCiBei(e);
            } else if (mCanProtName == "SuZhouShuWei") {
                onBsdEventSuZhouShuWei(e);
            } else if (mCanProtName == "WanXiang") {
                onBsdEventWanXiang(e);
            } else if (mCanProtName == "EagleWill") {
                onBsdEventSwayHighCanDisplay(e);
            }
            break;
        }
        case EVT_TYPE_BSD_Right: {
#if ENABLE_EXPIRE_PROTO
            if (!mCanProtName.length() || (mCanProtName == "WoTeJia")) {
                onBsdEventWoTeJia(e);
            } else
#endif
            if (mCanProtName == "XiaMenCiBei") {
                onBsdEventXiaMenCiBei(e);
            } else if (mCanProtName == "SuZhouShuWei") {
                onBsdEventSuZhouShuWei(e);
            } else if (mCanProtName == "WanXiang") {
                onBsdEventWanXiang(e);
            } else if (mCanProtName == "EagleWill") {
                onBsdEventSwayHighCanDisplay(e);
            } else if (mCanProtName == "JinLongSingleBsd") {
                onBsdEventJinLongSingleBsd(e);
            }
            break;
        }

        case EVT_TYPE_ADAS_CAN700_MSG: {
            if (mCanProtName == "ZhongChi") {
                onAdasEventZhongChi(e);
            }
            break;
        }

        case EVT_TYPE_DMS_FATIGUE_Eye:
        case EVT_TYPE_DMS_FATIGUE_YAWN:
        case EVT_TYPE_DMS_LOOK_DOWN:
        case EVT_TYPE_DMS_LOOK_AROUND:
        case EVT_TYPE_DMS_EYE_OCCLUSION: {
            if (mCanProtName == "ZhongChi") {
                onDmsEventZhongChi(e);
            }
            break;
        }

        default: {
                break;
        }
    }
    return true;
}

bool AlgoOutputCanProt::checkBsdAlgoWork() {
    char propValue[PROP_VALUE_MAX] = {0};

    if (__system_property_get(BSD_WORK_STATUS_PROP, propValue) > 0 && strcmp(propValue, "running") == 0) {
        return true;
    } else if (__system_property_get(OBJP_WORK_STATUS_PROP, propValue) > 0 && strcmp(propValue, "running") == 0) {
        return true;
    }
    return false;
}

bool AlgoOutputCanProt::checkCamWork() {
    char propValue[PROP_VALUE_MAX] = {0};

    // 摄像头
    bool bsdWorking = false, hodWorking = false, objpWorking = false;
    int sec, cams;
    my::conf::ini ini;
    my::conf::ini::load(ini, "/data/minieye/idvr/etc/config.ini");
    if (__system_property_get("rw.minieye.cam_status", propValue) == 0) {
        return false;
    }
    stringstream ss(propValue);
    if (ss >> sec >> cams) {
        char name[32] = "";
        int i = 1;
        while (cams) {
            if (cams & 1) {
                snprintf(name, sizeof(name), "media.ch%d", i);
                auto str = ini.get(name, "ai.func", "ch");
                if (str == "bsd") {
                    bsdWorking = true;
                }
                if (str == "objp") {
                    objpWorking = true;
                }
            }
            cams >>= 1;
            i++;
        }
    }

    if (mCamRunSec >= sec || (!bsdWorking && !objpWorking)) {
        mCamRunSec = sec;
        return false;
    }
    mCamRunSec = sec;
    return true;
}

bool checkStringsInFile(const std::string &filePath, const std::set<std::string> &stringsToMatch) {
    std::ifstream file(filePath);
    if (!file.is_open()) {
        return false;
    }

    std::set<std::string> unmatchedStrings = stringsToMatch;

    std::string line;
    while (std::getline(file, line)) {
        std::istringstream lineStream(line);
        std::string word;
        while (lineStream >> word) {
            unmatchedStrings.erase(word);
        }
        if (unmatchedStrings.empty()) {
            file.close();
            return true;
        }
    }

    file.close();
    return unmatchedStrings.empty();
}

bool AlgoOutputCanProt::checkMaintenance() {
    std::set<std::string> st{"--bsd_image_ack=true", "--bsd_left_image_ack=true"};
    return checkStringsInFile("/sdcard/run/bsd_setup.flag", st);
}

#if ENABLE_EXPIRE_PROTO
typedef struct {
    uint32_t frameId    = 0x18ffbde8;
    uint8_t  len        = 8;

    uint8_t  warn       = 0;
    uint8_t  level      = 0;
    uint8_t reserved[6] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
} __attribute__ ((packed)) WoTeJiaBsdCanPkt_S;

typedef struct {
    uint32_t frameId    = 0x18fa2fd0;
    uint8_t  len        = 8;

    uint8_t  acc        = 0xff;
    uint8_t  drvMode    = 0xff;
    uint16_t speedX10   = 0;    /* 0.1kmph */

    uint8_t  stall : 4  = 0xf;
    uint8_t  r     : 4  = 0;

    uint8_t  reserved[3] = {0};
} __attribute__ ((packed)) WoTeJiaVehicleCanPkt_S;

typedef struct {
    uint32_t frameId    = 0x18fac917;
    uint8_t  len        = 8;

    uint32_t mileageX10 = 0;    /* 0.1km */

    uint8_t  leftLamp   : 2 = 0;
    uint8_t  rightLamp  : 2 = 0;
    uint8_t  r          : 4 = 0;

    uint8_t reserved[3] = {0};
} __attribute__ ((packed)) WoTeJiaIoSigCanPkt_S;

bool AlgoOutputCanProt::onBsdEventWoTeJia(std::shared_ptr<Event> e)
{
    bool bSnd = true;
    do {
        std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);
        auto it = mEvtRcdMap.find(e->type());
        if (it != mEvtRcdMap.end()) {
            bSnd = (it->second.first.elapsed() >= 500) || (it->second.second != e->c.level);
        }
        if (bSnd) {
            mEvtRcdMap[e->type()] = std::pair<my::timestamp, int32_t>(my::timestamp::now(), e->c.level);
        }
    } while (0);
    if (bSnd) {
        std::lock_guard<std::mutex> lock(mCanMsgTsMtx);
        WoTeJiaBsdCanPkt_S bsd;
        mCanMsgTs[bsd.frameId] = my::timestamp::now();
        bsd.level = e->c.level;
        bsd.warn  = 1;
        msgEnque((void*)&bsd, sizeof(bsd));
    }
    return true;
}
#endif

bool AlgoOutputCanProt::onBsdEventSuZhouShuWei(std::shared_ptr<Event> e) {
    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);
    mEvtRcdMap[e->type()] = std::pair<my::timestamp, int32_t>(my::timestamp::now(), e->c.level);
    return true;
}

bool AlgoOutputCanProt::onBsdEventWanXiang(std::shared_ptr<Event> e) {
    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);
    mLastBsdEvt = {e->type(), my::timestamp::now()};
    mEvtRcdMap[e->type()] = std::pair<my::timestamp, int32_t>(my::timestamp::now(), e->c.level);
    return true;
}

bool AlgoOutputCanProt::onBsdEventXiaMenCiBei(std::shared_ptr<Event> e) {
    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);
    mLastBsdEvt = {e->type(), my::timestamp::now()};
    return true;
}

bool AlgoOutputCanProt::onAdasEventZhongChi(std::shared_ptr<Event> e) {
    my::string data = e->mEvtData["data"];
    ADAS_CAN700 *can700 = (ADAS_CAN700 *)data.c_str();

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);
    mEvtRcdMap[e->type()] = {my::timestamp::now(), e->c.level};
    memcpy(&mAdasCan700Pkt, can700, sizeof(ADAS_CAN700));
    return true;
}

bool AlgoOutputCanProt::onDmsEventZhongChi(std::shared_ptr<Event> e) {
    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);
    mLastDmsEvt = {e->type(), my::timestamp::now()};
    return true;
}

#if ENABLE_EXPIRE_PROTO
bool AlgoOutputCanProt::outputCanMsgWoTeJia(const McuMsgMcuStatT & s)
{
    WoTeJiaBsdCanPkt_S bsd;
    do {
        std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);
        for (auto r : mEvtRcdMap) {
            if ((EVT_TYPE_BSD_Right == r.first) &&
                (r.second.first.elapsed() <= 300)) {
                bsd.level = r.second.second;
                bsd.warn  = 1;
                break;
            }
        }
    } while (0);
    do {
        std::lock_guard<std::mutex> lock(mCanMsgTsMtx);
        if ((mCanMsgTs.find(bsd.frameId) == mCanMsgTs.end()) ||
            (mCanMsgTs[bsd.frameId].elapsed() >= 500)) {
            mCanMsgTs[bsd.frameId] = my::timestamp::now();

            msgEnque((void*)&bsd, sizeof(bsd));
        }
    } while (0);

    WoTeJiaVehicleCanPkt_S v;
    if ((mCanMsgTs.find(v.frameId) == mCanMsgTs.end()) ||
        (mCanMsgTs[v.frameId].elapsed() >= 200)) {
        mCanMsgTs[v.frameId] = my::timestamp::now();
        v.acc = s.pwr_set.acc ? 1 : 2;
        //v.drvMode;
        v.speedX10 = s.speed_x10;
        //v.stall;
        msgEnque((void*)&v, sizeof(v));
    }

    WoTeJiaIoSigCanPkt_S sig;
    if ((mCanMsgTs.find(sig.frameId) == mCanMsgTs.end()) ||
        (mCanMsgTs[sig.frameId].elapsed() >= 1000)) {
        mCanMsgTs[sig.frameId] = my::timestamp::now();
        sig.mileageX10 = s.total_mileage * 10;
        sig.leftLamp = !!s.turnl;
        sig.rightLamp = !!s.turnr;
        msgEnque((void*)&sig, sizeof(sig));
    }
    return true;
}
#endif

bool AlgoOutputCanProt::outputCanMsgSuZhouShuWei() {
    static E_DisplayStatus_t lastLeftLineStatus = E_DISPLAY_HIDE;
    static E_DisplayStatus_t lastRightLineStatus = E_DISPLAY_HIDE;
    static my::timestamp lastCanDisplayTs;

    auto l = E_DISPLAY_HIDE, r = E_DISPLAY_HIDE;

    for (auto &[evt, detail] : mEvtRcdMap) {
        if (detail.first.elapsed() <= 1000) {
            switch (evt) {
                case EVT_TYPE_BSD_Left: {
                    l = E_DISPLAY_SHOW;
                    break;
                }
                case EVT_TYPE_BSD_Right: {
                    r = E_DISPLAY_SHOW;
                    break;
                }
                default:
                    break;
            }
        }
    }

    if (lastLeftLineStatus != l || lastRightLineStatus != r || lastCanDisplayTs.elapsed() > 1000) {
        auto pedestrian = ((l == E_DISPLAY_SHOW || r == E_DISPLAY_SHOW) ? E_DISPLAY_SHOW : E_DISPLAY_HIDE);
        drawLine(l, r);
        drawPedestrian(pedestrian);

        lastLeftLineStatus = l;
        lastRightLineStatus = r;
        lastCanDisplayTs = my::timestamp::now();
    }

    return true;
}

bool AlgoOutputCanProt::checkVehicleMonitoring() {
    constexpr auto filePath = "/system/algo/adas/detect.flag";

    while (true) {
        std::ifstream file(filePath);
        if (file.is_open()) {
            std::string line;
            while (std::getline(file, line)) {
                if (line == "--enable_fcw_warning=true" || line == "--enable_hmw_warning=true") {
                    file.close();
                    return true;
                }
            }
            file.close();
        }
    }
    return false;
}

bool AlgoOutputCanProt::outputCanMsgZhongChi() {
    static ZhongChiAdas1CanPkt_S adas1Pkt;
    static ZhongChiAdas2CanPkt_S adas2Pkt;
    static my::timestamp ldwUpdateTs;

    std::lock_guard<std::mutex> lock(mCanMsgTsMtx);

    auto adas1It = mCanMsgTs.find(adas1Pkt.frameId);
    if (adas1It == mCanMsgTs.end() || adas1It->second.elapsed() >= 100) {
        if (adas1It->second.elapsed() >= 5000) {  // 消息过时
            adas1Pkt.soundType = ZC_ADAS_SOUND_NONE;
            adas1Pkt.headwayValid = false;
            adas1Pkt.headwayMeasurement = 0;
            adas1Pkt.ldwSw = false;
            adas1Pkt.fcwLevel = ZC_ADAS_FCW_LEVEL_NONE;
            adas1Pkt.hw = ZC_ADAS_HW_LEVEL_NONE;
            adas1Pkt.faultCode = 0;
            adas1Pkt.leftLdw = false;
            adas1Pkt.rightLdw = false;
        } else {
            adas1Pkt.soundType =
                mAdasCan700Pkt.fcw_on ? ZC_ADAS_SOUND_FCW : adasCan2protSound((SOUND_TYPE_E)mAdasCan700Pkt.sound_type);
            adas1Pkt.headwayMeasurement = mAdasCan700Pkt.headway_measurement * 10;  // 分辨率0.1s
            adas1Pkt.headwayValid = mAdasCan700Pkt.headway_valid;
            adas1Pkt.ldwSw = !mAdasCan700Pkt.ldw_off;
            adas1Pkt.fcwLevel = (mAdasCan700Pkt.fcw_on ? ZC_ADAS_FCW_LEVEL_FCW : ZC_ADAS_FCW_LEVEL_NONE);
            adas1Pkt.hw = mAdasCan700Pkt.headway_warning_level;
            adas1Pkt.faultCode = (mAdasCan700Pkt.no_error ? 0 : mAdasCan700Pkt.error_code);
            if (ldwUpdateTs.elapsed() > 500 || mAdasCan700Pkt.left_ldw || mAdasCan700Pkt.right_ldw) {
                adas1Pkt.leftLdw = mAdasCan700Pkt.left_ldw;
                adas1Pkt.rightLdw = mAdasCan700Pkt.right_ldw;
                ldwUpdateTs = my::timestamp::now();
            }
        }

        adas1Pkt.vehicleMonitoring = checkVehicleMonitoring();

        // my::string str((char *)&mZCAdas1CanPkt, sizeof(ZhongChiAdas1CanPkt_S));
        // my::hexdump(str, false);

        msgEnque((void *)&adas1Pkt, sizeof(ZhongChiAdas1CanPkt_S));
        mCanMsgTs[adas1Pkt.frameId] = my::timestamp::now();
    }

    auto adas2It = mCanMsgTs.find(adas2Pkt.frameId);
    if (adas2It == mCanMsgTs.end() || mLastDmsEvt.second.elapsed() <= 1000 && adas2It->second.elapsed() >= 1000) {
        adas2Pkt.dfmAlarmType = dmsEvt2protEvt(mLastDmsEvt.first);

        msgEnque((void *)&adas2Pkt, sizeof(ZhongChiAdas2CanPkt_S));
        mCanMsgTs[adas2Pkt.frameId] = my::timestamp::now();
    }
    return true;
}

bool AlgoOutputCanProt::onBsdEventJinLongSingleBsd(std::shared_ptr<Event> evt) {
    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);
    auto level = (evt->c.level == 3) ? 1 : (evt->c.level == 1) ? 3 : evt->c.level;
    mEvtRcdMap[evt->type()] = std::pair<my::timestamp, int32_t>(my::timestamp::now(), level);
    return true;
}

bool AlgoOutputCanProt::outputCanMsgJinLongSingleBsd() {
    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    auto elapsed = mEvtRcdMap[EVT_TYPE_BSD_Right].first.elapsed();
    auto bsdLevel = mEvtRcdMap[EVT_TYPE_BSD_Right].second;
    if (elapsed <= 2000 && bsdLevel != 0) {
        mCarFlicAlarmExitCnt = 5;  // 退出报警，包文最后发5次
        // logd("car flic alarm exit");
    }

    if (mCarFlicAlarmExitCnt > 0 && mCanMsgTs[CAR_FLIC_CAN_ID].elapsed() >= 200) {
        mCarFlicAlarmExitCnt = std::max(mCarFlicAlarmExitCnt - 1, 0);

        CarFlicInfo carFlicInfo;
        carFlicInfo.rightBsdLevel = (elapsed <= 2000) ? E_FLIC_ALARM_LEVEL_NONE + bsdLevel : E_FLIC_ALARM_LEVEL_NONE;
        carFlicInfo.rightBsdWarning = (carFlicInfo.rightBsdLevel == E_FLIC_ALARM_LEVEL_NONE)
                                          ? E_FLIC_AUDIBLE_WARNING_OFF
                                          : E_FLIC_AUDIBLE_WARNING_ON;

        msgEnque((void *)&carFlicInfo, sizeof(CarFlicInfo));
        mCanMsgTs[CAR_FLIC_CAN_ID] = my::timestamp::now();
    }
    return true;
}

bool AlgoOutputCanProt::outputOilIOXiaMenCiBei() {
    static bool bOilIOHigh = false;
    static my::timestamp lastOilIOAlarmTs;

    if (mLastBsdEvt.second.elapsed() > 1000) {
        if (bOilIOHigh) {
            bOilIOHigh = false;
            lastOilIOAlarmTs = my::timestamp::now();
            return LogCallProxyCmd::sendReq("prot", "cmd oilIO low");
        }
    } else {
        if (!bOilIOHigh) {
            bOilIOHigh = true;
            lastOilIOAlarmTs = my::timestamp::now();
            return LogCallProxyCmd::sendReq("prot", "cmd oilIO high");
        }
    }

    if (lastOilIOAlarmTs.elapsed() > 1000) {
        lastOilIOAlarmTs = my::timestamp::now();
        if (bOilIOHigh) {
            return LogCallProxyCmd::sendReq("prot", "cmd oilIO high");
        } else {
            return LogCallProxyCmd::sendReq("prot", "cmd oilIO low");
        }
    }

    return true;
}

static bool checkFrameValidity() {
    if (pRBuf == nullptr) {
        logd("pRBuf is null");
        return false;
    }

    uint32_t len = 0;
    RBFrame *pFrame = (RBFrame *)pRBuf->RequestReadFrame(&len);
    if (CRB_VALID_ADDRESS(pFrame) && len > 0) {
        if (IS_FRAME(pFrame)) {
            auto ts = my::timestamp::now();
            if (std::abs(pFrame->time - (int64_t)ts.milliseconds_from_19700101()) > 5000) {
                logd("expired frame");
                return false;
            }
            return true;
        }
        pRBuf->CommitRead();
    } else {
        logd("No more frame");
    }
    return false;
}

bool AlgoOutputCanProt::outputCanMsgWanXiang() {
    std::lock_guard<std::mutex> lock(mCanMsgTsMtx);

    WanXiangBsdCanPkt_S wxPkt;
    auto it = mCanMsgTs.find(wxPkt.frameId);
    if (it == mCanMsgTs.end() || it->second.elapsed() >= 100) {
        std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

        auto evt = mEvtRcdMap.find(mLastBsdEvt.first);
        if (evt != mEvtRcdMap.end() && evt->second.first.elapsed() <= 2000) {
            auto level = evt->second.second;
            wxPkt.alarmLevel = level == 1   ? WX_BSD_ALARM_LEVEL_WARN_L1
                               : level == 2 ? WX_BSD_ALARM_LEVEL_WARN_L2
                                            : WX_BSD_ALARM_LEVEL_NOWARN;
            wxPkt.bsdTarget = WX_BSD_TARGET_PEDESTRIAN;
        } else {
            wxPkt.alarmLevel = WX_BSD_ALARM_LEVEL_NONE;
            wxPkt.bsdTarget = WX_BSD_TARGET_NONE;
        }

        char propValue[PROP_VALUE_MAX] = {0};
        if (__system_property_get(BSD_WORK_STATUS_PROP, propValue) > 0 && strcmp(propValue, "running") == 0) {
            wxPkt.workState = WX_BSD_WORK_STATE_RUNNING;
            wxPkt.sysFault = WX_BSD_FAULT_CODE_NONE;
        } else if (__system_property_get(OBJP_WORK_STATUS_PROP, propValue) > 0 && strcmp(propValue, "running") == 0) {
            wxPkt.workState = WX_BSD_WORK_STATE_RUNNING;
            wxPkt.sysFault = WX_BSD_FAULT_CODE_NONE;
        } else {
            wxPkt.workState = WX_BSD_WORK_STATE_STOPPED;
            wxPkt.sysFault = WX_BSD_FAULT_CODE_FATAL_FAULT;
        }

        auto camWork = false;
        if(__system_property_get("rw.minieye.cam_status", propValue) > 0) {
            int sec, cams;
            std::stringstream ss(propValue);
            if (ss >> sec >> cams) {
                camWork = ((cams & 4) != 0);
            }
        }
        // wxPkt.cameraFault = checkFrameValidity() ? WX_BSD_FAULT_CODE_NONE : WX_BSD_FAULT_CODE_FATAL_FAULT;
        wxPkt.cameraState = camWork ? WX_BSD_WORK_STATE_RUNNING : WX_BSD_WORK_STATE_FAULT;
        wxPkt.cameraFault = wxPkt.cameraState == WX_BSD_WORK_STATE_RUNNING ? WX_BSD_FAULT_CODE_NONE
                                                                 : WX_BSD_FAULT_CODE_FATAL_FAULT;

        // my::string str((char *)&wxPkt, sizeof(WanXiangBsdCanPkt_S));
        // my::hexdump(str, false);

        msgEnque((void *)&wxPkt, sizeof(WanXiangBsdCanPkt_S));
        mCanMsgTs[wxPkt.frameId] = my::timestamp::now();
    }

    return true;
}

bool AlgoOutputCanProt::onBsdEventSwayHighCanDisplay(std::shared_ptr<Event> evt) {
    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);
    mEvtRcdMap[evt->type()] = std::pair<my::timestamp, int32_t>(my::timestamp::now(), evt->c.level);
    return true;
}

bool AlgoOutputCanProt::outputCanMsgSwayHighCanDisplay() {
    auto lBsdLevel = 0;
    auto rBsdLevel = 0;
    auto errorPktUpdate = false;
    auto displayUpdate = false;
    do {
        std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

        if (mLastSysDetectTs.elapsed() > 2000) {
            mLastSysDetectTs = my::timestamp::now();

            auto error = (checkBsdAlgoWork() ? E_SH_ERROR_NONE : E_SH_ERROR_YELLOW_SOLID);
            errorPktUpdate |= (error != mSwayHighCanDisplayErrorPkt->systemError);
            mSwayHighCanDisplayErrorPkt->systemError = error;

            error = (checkCamWork() ? E_SH_ERROR_NONE : E_SH_ERROR_YELLOW_SOLID);
            errorPktUpdate |= (error != mSwayHighCanDisplayErrorPkt->cameraFault);
            mSwayHighCanDisplayErrorPkt->cameraFault = error;
            // mSwayHighCanDisplayErrorPkt->maintenance = (checkMaintenance() ? E_SH_ERROR_NONE : E_SH_ERROR_YELLOW_SOLID);
        }

        auto it = mEvtRcdMap.find(EVT_TYPE_BSD_Left);
        if (it != mEvtRcdMap.end() && it->second.first.elapsed() <= 3000) {
            lBsdLevel = it->second.second;
        }
        it = mEvtRcdMap.find(EVT_TYPE_BSD_Right);
        if (it != mEvtRcdMap.end() && it->second.first.elapsed() <= 3000) {
            rBsdLevel = it->second.second;
        }
        if (lBsdLevel != mLastLBsdEventLevel || rBsdLevel != mLastRBsdEventLevel) {
            mLastLBsdEventLevel = lBsdLevel;
            mLastRBsdEventLevel = rBsdLevel;
            displayUpdate = true;

            mSwayHighCanDisplayStatusPkt->leftIndicator = (lBsdLevel != 0 ? E_SH_DISPLAY_WHITE_SOLID : E_SH_DISPLAY_NONE);
            mSwayHighCanDisplayStatusPkt->rightIndicator = (rBsdLevel != 0 ? E_SH_DISPLAY_WHITE_SOLID : E_SH_DISPLAY_NONE);
            mSwayHighCanDisplayStatusPkt->vehicleIcon =
                ((lBsdLevel != 0 || rBsdLevel != 0) ? E_SH_DISPLAY_WHITE_SOLID : E_SH_DISPLAY_NONE);

            mSwayHighCanDisplayWarningPkt->leftPed = (lBsdLevel == 1   ? E_SH_WARNING_RED_SOLID
                                                    : lBsdLevel == 2 ? E_SH_WARNING_YELLOW_SOLID
                                                    : lBsdLevel == 3 ? E_SH_WARNING_GREEN_SOLID
                                                                    : E_SH_WARNING_NONE);

            mSwayHighCanDisplayWarningPkt->rightPed = (rBsdLevel == 1   ? E_SH_WARNING_RED_SOLID
                                                    : rBsdLevel == 2 ? E_SH_WARNING_YELLOW_SOLID
                                                    : rBsdLevel == 3 ? E_SH_WARNING_GREEN_SOLID
                                                                        : E_SH_WARNING_NONE);
        }
    } while (0);

    if (mLastSwayHighRefreshTs.elapsed() > 3000 || displayUpdate || errorPktUpdate) {
        msgEnque((void *)mSwayHighCanDisplayStatusPkt.get(), sizeof(SwayHighCanDisplayStatusPkt));
        msleep(20);
        msgEnque((void *)mSwayHighCanDisplayWarningPkt.get(), sizeof(SwayHighCanDisplayWarningPkt));
        msleep(20);
        msgEnque((void *)mSwayHighCanDisplayErrorPkt.get(), sizeof(SwayHighCanDisplayErrorPkt));
        mLastSwayHighRefreshTs = my::timestamp::now();
    }
    return true;
}

}  // namespace minieye
