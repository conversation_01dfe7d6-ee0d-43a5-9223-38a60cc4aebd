#pragma once

#include "cmdline.h"
#include "FileLog.h"
#include "expand.message.h"
#include "algo.h"
#include "canDisplay.h"
#include "adas_alert.h"

// #include "ipcAgent.h"

namespace minieye {

class AlgoCanDisplay : public CanDisplay,
                       my::thread,
                       public IALGO_OBSERVER {
    
 public:
    enum AlarmDevice {
        E_DEVICE_LEFT,
        E_DEVICE_RIGHT,
        E_DEVICE_MAX,
    };

    explicit AlgoCanDisplay();
    virtual ~AlgoCanDisplay() noexcept;
    AlgoCanDisplay &operator=(const AlgoCanDisplay &other) = delete;
    AlgoCanDisplay(const AlgoCanDisplay &other) = delete;

 protected:
    virtual bool onAlgoEvent(std::shared_ptr<Event> evt);
    virtual int32_t onServerConnected(void);
    virtual int32_t onServerDisconnected(void);
    virtual int32_t onDataRecevied(const char *p, uint32_t len);
    virtual std::string setupCmdList(const char *cmdName);
    virtual bool runCmd(int argc, char **argv, string &ack);
    virtual void run();

 private:
    //  std::shared_ptr<IpcClient> mIpcClient;
    std::string mCmdUsage;

    std::mutex mUpdateLock;
    std::condition_variable mUpdateCond;

    /* adas msg */
    bool mEnableAdas = false;
    ADAS_CAN700 mLastAdasCan700 = {};
    ADAS_CAN700 mCurAdasCan700 = {};
    int mLastSpeed = 0;
    int mCurSpeed = 0;

    /* bsd alarm */
    bool mEnableBsd = false;
    my::timestamp mLastBsdAlarmTs[E_DEVICE_MAX];
    int mLastBsdLevel[E_DEVICE_MAX] = {0};

    /* display status */
    DispStatePack mAdasCanDisplay;     // adas需要显示的
    DispStatePack mBsdCanDisplay;      // bsd需要显示的
    DispStatePack mStationaryDisplay;  // 静止/行驶需要显示的
    DispStatePack mCanDisplay;         // 最终显示的

    my::timestamp mLastUpdateTs = 0;
};

}  // namespace minieye