
#ifndef __MINIEYE_DFH2_TPMS_HAOYUE_H__
#define __MINIEYE_DFH2_TPMS_HAOYUE_H__

#include "cmdline.h"
#include "expand.message.h"
namespace minieye
{

typedef struct {
    uint8_t  sensorIdx;
    uint32_t sensorID; /*MSB*/
    uint16_t tyrePressure;    /*压力值, 转换 Kpa 公式为：(TyrePressure-1)*2.75 */
    uint8_t  tyreTemperature; /*温度,转换℃公式为：TyreTemperature-55 */
} __attribute__((packed)) TyreSensorDataT;

typedef struct {
    uint8_t  magicL;
    uint8_t  magicH;
    uint8_t  devIdH;
    uint8_t  devIdM;
    uint8_t  devIdL;
    uint8_t  tyreNum;
    uint8_t  tempHiThrs;    /*温度高报警阈值,转换℃公式为：TyreTemperature-55 */
    uint16_t pressHiThrs;   /*MSB, 压力高报警阈值, 转换 Kpa 公式为：(TyrePressure-1)*2.75 */
    uint16_t pressLoThrs;   /*MSB, 压力低报警阈值, 转换 Kpa 公式为：(TyrePressure-1)*2.75 */
    TyreSensorDataT  tData[0];
    /*
        uint8_t  xorVal;
    */
} __attribute__((packed)) TyreSensorDataHeadT;

#define HY_DATA_LEN(ptr) (sizeof(TyreSensorDataHeadT) + ptr->tyreNum * sizeof(TyreSensorDataT) + 1)
typedef struct {
    TyreSensorDataHeadT sensorDataHead;
    std::vector<TyreSensorDataT> tyreDataTbl;

    bool checkXor(const uint8_t * data, int len)
    {
        uint32_t _xor = 0;

        for (uint32_t i = 0; i < len - 1; i++) {
            _xor = (_xor ^ data[i]) & 0xFF;
        }

        return (_xor & 0xFF) == data[len - 1];
    }

    int decode(const uint8_t * data, int len)
    {
        if (len < sizeof(TyreSensorDataHeadT)) {
            logw("not enough data %d!", len);
            return 0;
        }

        TyreSensorDataHeadT * head = (TyreSensorDataHeadT*)data;

        if (head->magicL != 0x55 || head->magicH != 0xAA) {
            loge("magic error 0x%02x%02x", head->magicL, head->magicH);
            return head->magicL != 0x55 ? 1 : 2;
        }

        int frameSize = HY_DATA_LEN(head);

        /*check data len*/
        if (frameSize > len) {
            logw("not enough data %d < %d!", len, frameSize);
            return 0;
        }

        if (!checkXor(data, frameSize)) {
            loge("invalid data %d!", len);
            return frameSize;
        }

        sensorDataHead = *head;
        sensorDataHead.pressHiThrs = ntohs(head->pressHiThrs);
        sensorDataHead.pressLoThrs = ntohs(head->pressLoThrs);

        for (int i = 0; i < sensorDataHead.tyreNum; i++) {
            TyreSensorDataT tData;
            tData = head->tData[i];
            tData.sensorID = ntohl(head->tData[i].sensorID);
            tData.tyrePressure = ntohs(head->tData[i].tyrePressure);
            tyreDataTbl.push_back(tData);
        }

        return frameSize;
    }

} TPMSHaoyueMsgT;


class TpmsHaoyue: public Protocol
{
    public:
        TpmsHaoyue() {};
        ~TpmsHaoyue() {};

        int32_t onServerConnected(void);
        int32_t onServerDisconnected(void);
        int32_t onDataRecevied(const char *p, uint32_t len);
        std::string setupCmdList(const char * cmdName);
        bool runCmd(int argc, char **argv, string &ack);

    private:
        int32_t onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len);
        int32_t onDataParse(const char *p, uint32_t size);
        bool onMessage(TPMSHaoyueMsgT * msg);


    private:
        string mCmdUsage;
        vector<uint8_t>     mRcvArray;

        expand::TpmsSensorMessage mLastMsg;
        /*for test*/
        int   mTempHiThrs  = 0;     /*温度高报警阈值, ℃*/
        float mPressHiThrs = 0.0;   /*压力高报警阈值, Kpa */
        float mPressLoThrs = 0.0;   /*压力低报警阈值, Kpa */
};

}


#endif
