
#ifndef __MINIEYE_PROTO__MANAGER_H__
#define __MINIEYE_PROTO__MANAGER_H__

#include "protocol.h"

#include "speedled.h"
#include "tahsensor.h"
#include "testProt.h"
#include "alertor.h"
#include "weightSensor.h"
#include "tpms.haoyue.h"
#include "displed.h"
#include "f9kGPS.h"
#include "zfRadar.h"
#include "f9kDisp.h"
#include "Display.xunjian.h"
#include "FuelMeter.ChangRun.h"
#include "FuelMeter.FuTai.h"
#include "LiquidMeter.FuTai.h"
#include "WeightSensor.FuTai.h"
#include "AnKaiCanProt.h"
#include "passenger.flow.meter.h"
#include "algoOutputCanProt.h"
#include "bsdAlert.XiaMenCiBei.h"
#include "SuZhouJinLong.h"
#include "AlinkAlertor.RongSheng.h"
#include "LongAnAlertor.RongSheng.h"
#include "BsdCanInfo.RongSheng.h"
#include "AlgoCanDisplay.h"
#include "NormalCan1Out.h"
#include "XiaMenShuoQi.CanProt.h"

namespace minieye {

namespace protmanager {

static Protocol* findProto(string& name) {
    std::unordered_map<std::string, ProtConstructorFn> protTbl = {
        {"algoOutput", protConstructor<AlgoOutputCanProt>},
        {"alertor", protConstructor<Alertor>},
        {"displed", protConstructor<dispLed>},
        {"speedled", protConstructor<SpeedLed>},
        {"testprot", protConstructor<TestProt>},
        {"tpms_haoyue", protConstructor<TpmsHaoyue>},
        {"tahsensor", protConstructor<TahSensor>},
        {"weightsensor_SAHX_120B", protConstructor<weightSensor_SAHX_120B>},
        {"weightsensor_ZZH_201", protConstructor<weightSensor_ZZH_201>},
        {"weightSensor_ZHF03", protConstructor<weightSensor_ZHF03>},
        {"gpsensor_F9K", protConstructor<F9KGPS>},
        {"ZfRadarProt", protConstructor<ZfRadarProt>},
        {"F9KDisPlay", protConstructor<F9KDisPlay>},
        {"Display_XUNJIAN", protConstructor<DisplayXunjian>},
        {"weightsensor_futai", protConstructor<WeightSensorFuTai>},
        {"fuel_futai", protConstructor<FuelMeterFuTai>},
        {"liquid_futai", protConstructor<LiquidMeterFutai>},
        {"fuel_changrun", protConstructor<FuelMeterChangRun>},
        {"ankaiCanProt", protConstructor<AnKaiCanProt>},
        {"passengerFlowMeter", protConstructor<PassengerFlowMeter>},
        {"bsdAlert_XiaMenCiBei", protConstructor<BsdAlertXiaMenCiBei>},
        {"suzhouJinLongCan1", protConstructor<SuZhouJinLongCan1>},
        {"suzhouJinLongCan2", protConstructor<SuZhouJinLongCan2>},
        {"alinkAlertor_rongsheng", protConstructor<AlinkAlertor>},
        {"longanAlertor_rongsheng", protConstructor<LongAnAlertor>},
        {"bsdCanInfo_rongsheng", protConstructor<BsdCanInfo>},
        {"algoCanDisplay", protConstructor<AlgoCanDisplay>},
        {"normalCan1Out", protConstructor<NormalCan1Out>},
        {"xiaMenShuoQi_CanProt", protConstructor<XiaMenShuoQiCanProt>},
    };

    auto algoProtNameList = {"ankaiCanProt",
                         "algoOutput",
                         "bsdAlert_XiaMenCiBei",
                         "suzhouJinLongCan1",
                         "alinkAlertor_rongsheng",
                         "longanAlertor_rongsheng",
                         "bsdCanInfo_rongsheng",
                         "algoCanDisplay",
                         "normalCan1Out"};

    auto pc = protTbl.find(name);
    if (pc != protTbl.end()) {
        for (auto algoProtName : algoProtNameList) {
            if (algoProtName == name) {
                AlgoManager& am = AlgoManager::getInstance();
                am.start();
            }
        }
        return pc->second();
    }
    return nullptr;
}
}  // namespace protmanager

}  // namespace minieye

#endif
