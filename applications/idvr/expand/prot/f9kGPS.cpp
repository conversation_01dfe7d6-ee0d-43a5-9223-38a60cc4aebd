#include "f9kGPS.h"
#include <fstream>
#include <iomanip>
#include "system_properties.h"
#include "idvrProperty.h"
#include "devicehub.h"
#include "json.hpp"

using json = nlohmann::json;

#define kBufferSize                     4096
#define NTRIP_SERVER_URL                "rtk.ntrip.qxwz.com"
#define NTRIP_SERVER_PROT               8002
#define NTRIP_SERVER_USER               "beidou23"
#define NTRIP_SERVER_PASSWD             "d80f27e"
#define NTRIP_MOUNT_POINT               "AUTO"

#define UBX_CFG_CLASS_ID                0x06

/**
 * @brief 
 * ubx_uart1_cfg_item
 */
#define CFG_UART1_BAUDRATE              0x40520001
#define CFG_UART1_STOPBITS              0x40520002
#define CFG_UART1_DATABITS              0x40520003
#define CFG_UART1_PARITY                0x40520004
#define CFG_UART1_ENABLED               0x40520005
#define CFG_UART1_REMAP                 0x40520006

#define HALF                            0           // 0.5 stopbits
#define ONE                             1           // 1.0 stopbits
#define ONEHALF                         2           // 1.5 stopbits
#define TWO                             3           // 2.0 stopbits

/**
 * @brief 
 * Constants for CFG-UART1-DATABITS
 */
#define EIGHT                           0           // 8 databits
#define SEVEN                           1           // 7 databits

/**
 * @brief 
 * Constants for CFG-UART1-PARITY
 */
#define NONE                            0           // No parity bit
#define ODD                             1           // Add an odd parity bit
#define EVEN                            2           // Add an even parity bit

/**
 * @brief 
 * Protocol in
 */
#define CFG_UART1_INPROT_UBX            0x10730001
#define CFG_UART1_INPROT_NMEA           0x10730002
#define CFG_UART1_INPROT_RTCM3X         0x10730004

/**
 * @brief 
 * Protocol out
 */
#define CFG_UART1OUTPROT_UBX            0x10740001
#define CFG_UART1OUTPROT_NMEA           0x10740002

/**
 * @brief 
 * UBX协议报文数据结构
 */
struct UbxCfg 
{
    uint8_t         head[2];        // 帧头: 由两个字节组成，即0xB5 0x62
    uint8_t         classID;        // CLASS ID
    uint8_t         msgID;          // MESSAGE ID
    uint16_t        length;         // 消息长度，表示后面发送的 PAYLOAD 消息长度，字节序为低字节序
    uint8_t         playLoad[0];    // 输出的 GPS 信息
    uint8_t         ckA;            // CK_A = CK_A + Buffer[I] (校验)
    uint8_t         ckB;            // CK_B = CK_B + CK_A (校验)
};

constexpr char kClientAgent[] = "NTRIP u-blox"; 

static std::vector<uint8_t> _baudRate115200 = {
    0xB5, 0x62, 0x06, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, \
    0xD0, 0x08, 0x00, 0x00, 0x00, 0xC2, 0x01, 0x00, 0x23, 0x00, \
    0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0xDC, 0x5E
};

static std::vector<uint8_t> _baudRate38400 = {
    0xB5, 0x62, 0x06, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, \
    0xD0, 0x08, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0x23, 0x00, \
    0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAF, 0x70
};

static std::vector<uint8_t> _workFreq5HZ = {
    0xB5, 0x62, 0x06, 0x08, 0x06, 0x00, 0xC8, 0x00, 0x01, 0x00, \
    0x01, 0x00, 0xDE, 0x6A
};

static std::vector<uint8_t> _workFreq10HZ = {
    0xB5, 0x62, 0x06, 0x08, 0x06, 0x00, 0x64, 0x00, 0x01, 0x00, \
    0x01, 0x00, 0x7A, 0x12 
};

static std::vector<uint8_t> _saveCfg = {
    0xB5, 0x62, 0x06, 0x09, 0x0D, 0x00, 0x00, 0x00, 0x00, 0x00, \
    0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x1D, \
    0xAB
};

namespace minieye {

    // 结构体和json的转换
    inline void toJson(json &j, const LocationInfo &i)
    {
        j = {
            {"status",       i.status},
            {"mode",         i.mode},
            {"direction",    i.direction},
            {"hdop",         i.hdop},
            {"latitude",     i.latitude},
            {"longitude",    i.longitude},
            {"altitude",     i.altitude},
            {"speed",        i.speed}
        };
    }

    static void nmeaTraceLog(const char *str, int32_t str_size)
    {
        loge("nmeaTraceLog %s", str);
    }

    F9KGPS::F9KGPS()
    {
        logd("F9KGPS +");
        mLastPktTs = my::timestamp::now();
        mGpsUpdateTime = my::timestamp::now();

        nmea_zero_INFO(&mNmeaInfo);
        nmea_parser_init(&mNmeaParser);
        nmea_property()->trace_func  = nmeaTraceLog;
        nmea_property()->error_func = nmeaTraceLog;
        nmea_property()->info_func  = nmeaTraceLog;

        getLastGpsLocatoin();
        
        mGpsLogger = new FileLog("/data/minieye/idvr/mlog/f9kgps_log/", 5 * 1024 * 1024, 10);
        mGpsLogger->prepare();

        // mNmeaLogger = new FileLog("/data/minieye/idvr/mlog/f9knmea_log", 50 * 1024 * 1024, 10);
        // mNmeaLogger->prepare();

        hostent *ent = gethostbyname(NTRIP_SERVER_URL);
        if (ent) {
            sockaddr_in addr;
            memcpy(&addr.sin_addr, ent->h_addr, ent->h_length);
            std::string ipAddr = inet_ntoa(addr.sin_addr);
            logd("Ntrip Server ip %s", ipAddr.c_str());
            mNtripClient = new NtripClient(
                ipAddr, 
                NTRIP_SERVER_PROT, 
                NTRIP_SERVER_USER, 
                NTRIP_SERVER_PASSWD, 
                NTRIP_MOUNT_POINT, this
            );
            if (mNtripClient) {
                mNtripClient->start();
            }
        } else {
            loge("get ip addr error %s", strerror(errno));
        }

        mLocatMonitor = my::timestamp::now();

        memset(&mBestposData, 0, sizeof(mBestposData));
        memset(&mInspvaxData, 0, sizeof(mInspvaxData));
        my::thread::start();
    }

    F9KGPS::~F9KGPS()
    {
        logd("F9KGPS ~");
        nmea_parser_destroy(&mNmeaParser);
        my::thread::stop();
        mNtripClient->stop();
        delete mNtripClient;
        mNtripClient = nullptr;
    }

    int32_t F9KGPS::onServerConnected(void) 
    {
        logd("f9kGps connected Config F9K");
        configF9k();
        return 0;
    }

    int32_t F9KGPS::onServerDisconnected(void) 
    {
        logd("f9kGps disconnected");
        return 0;
    }

    bool F9KGPS::getNetWorkStatus()
    {
        std::lock_guard<std::mutex> lock(mMutex);
        char propValue[PROP_VALUE_MAX] = { 0 };

        int32_t len = __system_property_get("rw.minieye.connected", propValue);
        if (len > 0) {
            if (strncmp(propValue, "0", strlen("0")) == 0) {
                return false;
            }
        }

        return true;
    }

    int32_t F9KGPS::onDataRecevied(const char *p, uint32_t len)
    {
        //logd("on cmd data %d", len);
        //logd("recv %s", my::hex(my::constr(p, len)).c_str());
        return onDataParse(p, len);
    }

    int32_t F9KGPS::onDataParse(const char * p, uint32_t size) 
    {
        if (mExpandExit && (mLastPktTs.elapsed() >= 2000)) {
            logd("Exit Expand For Config");
            mExpandExit = false;
            mLastPktTs = my::timestamp::now();
            exit(1);
        }
        int32_t offset = 0;
        while ((offset) < size) {
            switch(p[offset]) {
                case '#':
                case '$': {
                    mWaitMsgTyp = p[offset];
                    break;
                }
                case '\r': {
                    if ((mWaitMsgTyp != '$') &&
                        (mWaitMsgTyp != '#')) {
                        mWaitMsgTyp = 0;
                    }
                    break;
                }
                case '\n': {
                    if ((mWaitMsgTyp != '$') &&
                        (mWaitMsgTyp != '#')) {
                        mWaitMsgTyp = 0;
                    } else {
                        mWaitMsgTyp -= 2;
                    }
                    break;
                }
                default: {
                    break;
                }
            }

            switch(mWaitMsgTyp) {
                case '$' - 2:
                    mWaitMsgTyp = 0;
                case '$': {
                    mNmeaBuf << p[offset];
                    break;
                }
                case '#' - 2:
                    mWaitMsgTyp = 0;
                case '#': {
                    mNovAtelBuf << p[offset];
                    break;
                }
            }

            offset++;
        }

        if ((mWaitMsgTyp != '#') &&
            ((mWaitMsgTyp + 2) != '#') &&
            (mNovAtelBuf.length() > 16)) {
            mNovAtelBuf << 0;
            parseNovatel(mNovAtelBuf.c_str(), mNovAtelBuf.length(), &mBestposData, &mInspvaxData);
            mNovAtelBuf.clear();
        }

        if ((mWaitMsgTyp == '$') ||
            ((mWaitMsgTyp + 2) == '$') ||
            (mNmeaBuf.length() < 16)) {
            return -2;
        }
        const char *data = mNmeaBuf.c_str();
        int32_t len = mNmeaBuf.length();

        // mNmeaLogger->mlog_raw(data, len);
        // logd("dump NmeaBuf:");
        // my::hexdump(my::constr(mNmeaBuf.c_str(), mNmeaBuf.length()), true);

        int32_t nread = nmea_parse(&mNmeaParser, (const char *) data, len, &mNmeaInfo);
        mNmeaBuf.clear();
        if (nread <= 0) {
            loge("nmea_parse return error %d\n", nread);
            return -1;
        }

        // if (mNmeaInfo.txt != NULL) {
        //     //logd("External GPS txt info %.20s ", mInfo.txt);
        //     if(strstr(mNmeaInfo.txt, "OPEN")) // 天线状态
        //         mLbs.antenna = ANTENNA_OPEN;
        //     else if(strstr(mNmeaInfo.txt, "OK"))
        //         mLbs.antenna = ANTENNA_OK;
        //     else if(strstr(mNmeaInfo.txt, "SHORT"))
        //         mLbs.antenna = ANTENNA_SHORT;
        // }

        mLbs.status = mNmeaInfo.fix != 1;               // 1未定位, 2 2D定位 3 3D定位
        if (mNmeaInfo.sig > 0) {
            mLbs.antenna = ANTENNA_OK;
            mLbs.sat = mBestposData.sat_num;
            mLbs.status = 1;
        } else {
            mLbs.antenna = ANTENNA_UNKNOW;
            mLbs.sat = 0;
        }
        getGpsSignalLevel();
        getGpsTime();
        
        mGpsUpdateTime = my::timestamp::now();
        mDegLat = nmea_ndeg2degree(mNmeaInfo.lat);
        mDegLon = nmea_ndeg2degree(mNmeaInfo.lon);

        /* 更新GPS经纬度, 不定位时，保留上次经纬度 */
        if (mLbs.status) {
            mLbs.lat_x1kw = mDegLat * 10000000;
            mLbs.lng_x1kw = mDegLon * 10000000;
            mLbs.alt_x10 = mNmeaInfo.elv * 10;          // 高度
            updataLastGpsLocation();
        }
        mLbs.dir_x100 = mNmeaInfo.direction * 100;      // 方向
        mLbs.speed_x10 = mNmeaInfo.speed * 10;          // 速度 km/h
        mLbs.rtkData.sig = mNmeaInfo.sig;               // 定位模式
        mLbs.HDOP = (unsigned short) (mNmeaInfo.HDOP * 10);
        mLbs.rtkData.lat = mDegLat;
        mLbs.rtkData.lng = mDegLon;                     // 增加两个字段，保证精度
        mLbs.rtkData.enable = true;

        if (! (mNmeaInfo.sig == 4 || mNmeaInfo.sig == 5)) {
            if (mLocatMonitor.elapsed() >= 10000) {
                mGpsLogger->mlog("time: %s, sig %d lat %0.8f lon %0.8f alt %f net %d", my::timestamp::YYYYMMDD_HHMMSS_MS().c_str(), 
                    mNmeaInfo.sig, mDegLat, mDegLon, mNmeaInfo.elv, getNetWorkStatus());
                mLocatMonitor = my::timestamp::now();
            }
        }

        // logd("time: %s, sig %d lat %0.8f lon %0.8f alt %f", my::timestamp::YYYYMMDD_HHMMSS_MS().c_str(), 
        //     mNmeaInfo.sig, mDegLat, mDegLon, mNmeaInfo.elv);

        /* 生成$GPGGA , 只有NTRIP Client能连上server才需要生成 */
        char ggaBuff[kBufferSize];
        int32_t genOk = nmea_generate(ggaBuff, kBufferSize, &mNmeaInfo, GPGGA);
        if(genOk <= 0) {
            loge("generate GGA Data fail %d", genOk);
        } else if(mNtripClient != nullptr) {
            std::string ggaData(ggaBuff, strlen(ggaBuff));
            mNtripClient->setGgaBuffer(ggaData);
            //logd("ggaData: %s", ggaData.c_str());
        }

        /* 更新hostio gps */
        notiyLibs();

        /* 传定位数据给算法 */
        LocationInfo location;
        location.status = mLbs.status;
        location.mode = mLbs.rtkData.sig;
        location.direction = mNmeaInfo.direction;
        location.hdop = mNmeaInfo.HDOP;
        location.speed = mNmeaInfo.speed;
        location.altitude = mNmeaInfo.elv;
        location.latitude = mDegLat;
        location.longitude = mDegLon;
        //msgpack::sbuffer sbuf;
        //msgpack::pack(sbuf, location);

        /* 算法要求json格式 */
        try {
            json j; 
            toJson(j, location);
            std::string str_to_send = j.dump();
            logd("data: %s", str_to_send.c_str());
            ExpandSet::getInstance().sendLibFlow(RTK_LOCTION_LIBFLOW_TOPIIC, str_to_send.c_str(), str_to_send.size());
        } catch(json::exception e) {
            loge("Json:: %s", e.what());
        }

        return 0;
    }

    void F9KGPS::getGpsSignalLevel(void)
    {
        time_t                  now = time(NULL);
        static time_t           last = now;
        list<uint32_t>          v1;
        uint32_t                satNum = 0;

        if (mDebugLevel >= 1) {
            logd("Visible satellites %d", mNmeaInfo.satinfo.inview + mNmeaInfo.BDsatinfo.inview);
        }
        for (int32_t i = 0; i < mNmeaInfo.satinfo.inview; i++) {
            if (mDebugLevel >= 1) {
                logd("Satellite #%03d type %-8s SNR %03d(db-HZ)",
                        mNmeaInfo.satinfo.sat[i].id, "GPS", (int32_t)mNmeaInfo.satinfo.sat[i].sig);
            }

            if (mNmeaInfo.satinfo.sat[i].in_use) {
                v1.push_back((uint32_t)mNmeaInfo.satinfo.sat[i].sig);
                if (mNmeaInfo.satinfo.sat[i].sig > 0) {
                    satNum ++;
                }
            }
        }
        for (int32_t i = 0; i < mNmeaInfo.BDsatinfo.inview; i++) {
            if (mDebugLevel >= 1) {
                logd("Satellite #%03d type %-8s SNR %03d(db-HZ)",
                        mNmeaInfo.BDsatinfo.sat[i].id, "BEIDOU", (int32_t)mNmeaInfo.BDsatinfo.sat[i].sig);
            }

            if (mNmeaInfo.BDsatinfo.sat[i].in_use) {
                v1.push_back((uint32_t)mNmeaInfo.BDsatinfo.sat[i].sig);
                if (mNmeaInfo.BDsatinfo.sat[i].sig > 0) {
                    satNum++;
                }
            }
        }
        if (mDebugLevel >= 1) {
            logd("Satellite num %d", satNum);
        }

        if(now < last + 2)  {
            return;
        }

        last = now;
        mLbs.sat = satNum;         //信号强度大于0的卫星数量

        v1.sort();
        uint32_t sat_inuse = v1.size();
        if (!sat_inuse) {
            mLbs.sig_level = 0;
            return;
        }
        uint32_t max_sat_num_cal = sat_inuse > 5 ? 5 : sat_inuse;
        uint32_t sig_sum = 0;
        for (uint32_t i = 0; i < max_sat_num_cal; i++) {
            sig_sum += v1.back();
            v1.pop_back();
        }

        int32_t avg = sig_sum / max_sat_num_cal;
        if (avg < 20) {
            mLbs.sig_level = 0;
        } else if (avg < 30) {
            mLbs.sig_level = 1;
        } else if (avg < 40) {
            mLbs.sig_level = 2;
        } else {
            mLbs.sig_level = 3;
        }

        //logd("gps avg %d level %d\n", avg, mLbs.sig_level);
    }

    bool F9KGPS::setDebugLevel(int32_t level)
    {
        mDebugLevel = level;
        return true;
    }

    void F9KGPS::getGpsTime(void)
    {
        struct tm stm;
        stm.tm_year             = mNmeaInfo.utc.year;
        stm.tm_mon              = mNmeaInfo.utc.mon - 1;
        stm.tm_mday             = mNmeaInfo.utc.day;
        stm.tm_hour             = mNmeaInfo.utc.hour;
        stm.tm_min              = mNmeaInfo.utc.min;
        stm.tm_sec              = mNmeaInfo.utc.sec;
        stm.tm_isdst            = -1;

        time_t                  now = time(NULL);
        struct tm               tm_local;
        struct tm               tm_utc;
        unsigned long           time_local, time_utc;
        gmtime_r(&now, &tm_utc);
        localtime_r(&now, &tm_local);

        tm_local.tm_isdst       = -1;
        tm_utc.tm_isdst         = -1;
        time_local              = mktime(&tm_local);
        time_utc                = mktime(&tm_utc);
        long utc_diff           = time_utc - time_local;
        mLbs.time               = (long long) (mktime(&stm) - utc_diff);

        /* 如果GPS定位，判断是否需要校时, 时间大于2011年1月1日才认为有效 */
        if (mLbs.status &&  mLbs.time > 1293811200) {
            auto use_gps_time = [] (unsigned int gps_time, unsigned int diff) {
                unsigned int now = my::timestamp::utc_seconds();
                return (abs((int64_t)gps_time - (int64_t)now) > diff);
            };
            /* GPS时间和系统时间相差10s就会触发校时 */
            if (use_gps_time(mLbs.time, 10)) {
                mGpsLogger->mlog("gps_time %u sys_time %u, set rtc !!!", mLbs.time, my::timestamp::utc_seconds());
                struct timeval stime = {0, 0};
                stime.tv_sec = mLbs.time;
                settimeofday(&stime, NULL);
                /* 设置硬件时钟 */
                system("hwclock -wu &");
            }
        }

    }

    void F9KGPS::getLastGpsLocatoin(void)
    {
        double lat_x1kw, lng_x1kw;
        double lat_x1kw_df = 22.536779;
        double lng_x1kw_df = 113.95273;
        char prop[PROP_VALUE_MAX] = { 0 };

        if (__system_property_get(PROP_RW_MINIEYE_GPSLASTLOCATION, prop)) {
            sscanf(prop, "Location %lf, %lf", &lat_x1kw, &lng_x1kw);
            mLbs.lat_x1kw = (int32_t) (lat_x1kw * 10000000);
            mLbs.lng_x1kw = (int32_t) (lng_x1kw * 10000000);
            logd("get last gps location %lf %lf, int32_t %d %d\n", lat_x1kw, lng_x1kw, mLbs.lat_x1kw, mLbs.lng_x1kw);
        } else {    // never location, use default gps location
            snprintf(prop, sizeof(prop), "Location %f,%f", lat_x1kw_df, lng_x1kw_df);
            __system_property_set(PROP_RW_MINIEYE_GPSLASTLOCATION, prop);
            mLbs.lat_x1kw = lat_x1kw_df * 10000000;
            mLbs.lng_x1kw = lng_x1kw_df * 10000000;
        }
    }

    void F9KGPS::updataLastGpsLocation(void)
    {
        double lat_x1kw, lng_x1kw;
        char prop[PROP_VALUE_MAX] = { 0 };

        lat_x1kw = mLbs.lat_x1kw / 10000000.0; 
        lng_x1kw = mLbs.lng_x1kw / 10000000.0;

        snprintf(prop, sizeof(prop), "Location %f,%f", lat_x1kw, lng_x1kw);
        __system_property_set(PROP_RW_MINIEYE_GPSLASTLOCATION, prop);
    }

    void F9KGPS::dumpGpsInfo(void)
    {
        if (mDebugLevel >= 2) {
            logd("gps read: mask 0x%x sig %d fix %d PDOP %.1f HDOP %.1f VDOP %.1f lat %.4f lon %.4f elv %.1f "\
                    "sog %.1f speed %.1f dir %.2f, dec %.2f mode %c sat %d use %d BDsat %d BDuse %d\n"\
                    , mNmeaInfo.smask
                    , mNmeaInfo.sig         /**< GPS quality indicator (0 = Invalid; 1 = Fix; 2 = Differential, 3 = Sensitive) */
                    , mNmeaInfo.fix         /**< Operating mode, used for navigation (1 = Fix not available; 2 = 2D; 3 = 3D) */

                    , mNmeaInfo.PDOP        /**< Position Dilution Of Precision */
                    , mNmeaInfo.HDOP        /**< Horizontal Dilution Of Precision */
                    , mNmeaInfo.VDOP        /**< Vertical Dilution Of Precision */

                    , mNmeaInfo.lat         /**< Latitude in NDEG - +/-[degree][min].[sec/60] */
                    , mNmeaInfo.lon         /**< Longitude in NDEG - +/-[degree][min].[sec/60] */
                    , mNmeaInfo.elv         /**< Antenna altitude above/below mean sea level (geoid) in meters */
                    , mNmeaInfo.sog         /**< ÊýÖµ ¶ÔµØËÙ¶È£¬µ¥Î»Îª½Ú */
                    , mNmeaInfo.speed       /**< Speed over the ground in kilometers/hour */
                    , mNmeaInfo.direction   /**< Track angle in degrees True */
                    , mNmeaInfo.declination /**< Magnetic variation degrees (Easterly var. subtracts from true course) */
                    , mNmeaInfo.mode
                    , mNmeaInfo.satinfo.inview          // 卫星数量
                    , mNmeaInfo.satinfo.inuse           // 卫星数量
                    , mNmeaInfo.BDsatinfo.inview        // 卫星数量
                    , mNmeaInfo.BDsatinfo.inuse         // 卫星数量
            );
        }
    }

    void F9KGPS::run() 
    {
        while (!exiting()) {
            /* gps 超时清零 */
            if (mGpsUpdateTime.elapsed() > 60 * 1000 ) {
                mLbs.status = 0;
                mLbs.sig_level = 0;
                mLbs.sat = 0;
                mLbs.rtkData.sig = 0;
                mLbs.speed_x10 = 0;
                notiyLibs();
                mGpsLogger->mlog("long time no nmea data, gps timeout clear!!!");
                mGpsUpdateTime = my::timestamp::now();
            }

            /* Ntrip Client reconnect, 网络不正常需要重启NTRIP CLient */
            if (mNtripClient == nullptr) {
                hostent * ent = gethostbyname(NTRIP_SERVER_URL);
                if (ent) {
                    sockaddr_in addr;
                    memcpy(&addr.sin_addr, ent->h_addr, ent->h_length);
                    std::string ipAddr = inet_ntoa(addr.sin_addr);
                    mGpsLogger->mlog("Ntrip Server ip %s", ipAddr.c_str());
                    mNtripClient = new NtripClient(
                        ipAddr, 
                        NTRIP_SERVER_PROT, 
                        NTRIP_SERVER_USER, 
                        NTRIP_SERVER_PASSWD, 
                        NTRIP_MOUNT_POINT,
                        this
                    );
                    if (mNtripClient) {
                        mNtripClient->start();
                    }
                }
            } else if (!mNtripClient->isServiceRunning) {
                mGpsLogger->mlog("disconnect from cors platform, reconnect NtripCaster!");
                mNtripClient->start();
                usleep(20 * 1000);
            }

            usleep(500 * 1000);
        }
    }

    void F9KGPS::notiyLibs(void)
    {
        DeviceHub::getInstance().setGpsLbs(mLbs);
    }

    void F9KGPS::configF9k(void)
    {
        logd("<=============== F9K Config ==============>");

        /**
         * 配置路径："/data/minieye/idvr/etc/expand.json"
         * F9K默认波特率是38400
         * 获取当前的波特率，如果不等于115200就配置它
         * 配置完必须重启expand进程同步更新RS232波特率
         */
//         std::ifstream expand("/data/minieye/idvr/etc/expand.json", std::fstream::in);
// #define BAUDRATE 38400
//         try {
//             json j;
//             expand >> j;
//             expand.close();
//             int32_t curBaudRate = 0;
//             curBaudRate = j["RS232_1"].at("baudrate").get<int32_t>();
//             logd("current RS232_1 BaudRate is %d", curBaudRate);
//             if (curBaudRate != 115200) {
//                 j["RS232_1"]["baudrate"] = BAUDRATE;
//                 logd("#### Update RS232 BaudRate %d", BAUDRATE);
//                 std::ofstream o("/data/minieye/idvr/etc/expand.json");
//                 o << std::setw(4) << j << std::endl;
//                 o.close();
//                 msgEnque((void*)_baudRate115200.data(), _baudRate115200.size());
//                 mExpandExit = true;
//             }
//         } catch(json::exception e) {
//             loge("Json:: %s", e.what());
//             return;
//         }

        // 只有等波特率更新完成之后才去更新上报数据频率和配置保存
        msgEnque((void *) _workFreq10HZ.data(), _workFreq10HZ.size());
        msgEnque((void *) _saveCfg.data(), _saveCfg.size());

        logd("<================ F9K config end ===============>");
    }

    F9KGPS::NtripClient::NtripClient(std::string const& ip, int32_t port, std::string const& user_, std::string const& passwd_,
                    std::string const& mountpoint_, F9KGPS* gps) 
        : serverIp(ip)
        , serverPort(port)
        // , user(user_)
        // , passwd(passwd_)
        , mountpoint(mountpoint_)
        , mF9kGps(gps)
    { 
        ggaBuffer = "$GNGGA,020546.00,2232.44459,N,11356.84092,E,2,12,0.60,100.3,M,-2.7,M,,0740*56\r\n";
        getCustomConf();

        mNtripLogger = new FileLog("/data/minieye/idvr/mlog/f9kntrip_log", 50 * 1024 * 1024, 10);
        mNtripLogger->prepare();
    }

    void F9KGPS::NtripClient::init(std::string const& ip, int32_t port, std::string const& user_, std::string const& passwd_,
                std::string const& mountpoint_, F9KGPS* gps)
    {
        serverIp = ip;
        serverPort = port;
        user = user_;
        passwd = passwd_;
        mountpoint = mountpoint_;
        mF9kGps = gps;
    }

    void F9KGPS::NtripClient::getCustomConf(std::string path) 
    {
        std::ifstream cfgFile;
        cfgFile.open(path);
        if (!cfgFile.is_open()) {
            logd("cfg %s open error!! use default config!", path.c_str());
            return ;
        }

        std::string item;
        while (getline(cfgFile, item)) {
            if (!strncasecmp(item.c_str(), "user", strlen("user"))) {
                std::string usr = item.substr(item.find('=') + 1);
                user = usr;
            } else if (!strncasecmp(item.c_str(), "passwd", strlen("passwd"))) {
                std::string passWD = item.substr(item.find('=') + 1);
                passwd = passWD;
            } else if (!strncasecmp(item.c_str(), "mountpoint", strlen("mountpoint"))) {
                std::string mount = item.substr(item.find('=') + 1);
                mountpoint = mount;
            } else if (!strncasecmp(item.c_str(), "serverUrl", strlen("serverUrl"))) {
                std::string url = item.substr(item.find('=') + 1);
                hostent* ent = gethostbyname(NTRIP_SERVER_URL);
                if (ent) {
                    sockaddr_in addr;
                    memcpy(&addr.sin_addr, ent->h_addr, ent->h_length);
                    serverIp = inet_ntoa(addr.sin_addr);
                }
            } else if (!strncasecmp(item.c_str(), "serverPort", strlen("serverPort"))) {
                std::string port = item.substr(item.find('=') + 1);
                serverPort = atoi(port.c_str());
            }
        }

        logd("NTRIP user %s passwd %s", user.c_str(), passwd.c_str());

        cfgFile.close();

        return ;
    }

    void F9KGPS::NtripClient::setGgaBuffer(std::string const& gga)
    {
        std::lock_guard<std::mutex> _l(mutex_);
        ggaBuffer.clear();
        ggaBuffer = gga;
        isGgaUpdate = true;
    }

    bool F9KGPS::NtripClient::start(void) {
        
        if (isServiceRunning) {
            return true;
        }

        stop();

        if (socketFd_ > 0) {
            close(socketFd_);
            socketFd_ = -1;
        }

        /* 与NtripCaster建立连接 */
        struct sockaddr_in serverAddr;
        memset(&serverAddr, 0, sizeof(serverAddr));
        serverAddr.sin_family = AF_INET;
        serverAddr.sin_port = htons(serverPort);
        serverAddr.sin_addr.s_addr = inet_addr(serverIp.c_str());

        int32_t socketFd = socket(AF_INET, SOCK_STREAM, 0);
        if (socketFd == -1) {
            logd("Create socket failed, errno = -%d", errno);
            return false;
        }
        if (connect(
                socketFd, 
                reinterpret_cast<struct sockaddr *>(&serverAddr), 
                sizeof(serverAddr)
            ) < 0
        ) {
            mNtripLogger->mlog("Connect to NtripCaster[%s:%d] failed, errno = -%d\n",
                serverIp.c_str(), serverPort, errno);
            close(socketFd);
            return false;
        }
        
        /* Set non-blocking. */
        int32_t flags = fcntl(socketFd, F_GETFL);
        fcntl(socketFd, F_SETFL, flags | O_NONBLOCK);

        /* Ntrip connection authentication. */
        int32_t ret = -1;
        std::string userPasswd = user + ":" + passwd;
        std::string userPasswdBase64;
        std::unique_ptr<char[]> buffer(new char[kBufferSize], std::default_delete<char[]>());

        /* Generate base64 encoding of username and password. */
        userPasswdBase64 = my::base64e(userPasswd.c_str());
        logd("Ntrip userPassWd %s", userPasswd.c_str());

        /* Generate request data format of ntrip. */
        ret = snprintf(
            buffer.get(),
            kBufferSize - 1,
            "GET /%s HTTP/1.0\r\n"
            "User-Agent: %s\r\n"
            "Authorization: Basic %s\r\n"
            "\r\n",
            mountpoint.c_str(), kClientAgent, userPasswdBase64.c_str()
        );

        if (send(socketFd, buffer.get(), ret, 0) < 0) {
            mNtripLogger->mlog("failed to send request to get NtripCaster differential data!!!");
            close(socketFd);
            return false;
        }

        logd("NtripClient: %s", buffer.get());
        
        /* Waitting for request to connect caster success. */
        int32_t timeout = 3;
        while (timeout --) {
            ret = ::recv(socketFd, buffer.get(), kBufferSize, 0);
            if (ret > 0) {
                std::string result(buffer.get());
                if ((result.find("HTTP/1.1 200 OK") != std::string::npos) ||
                    (result.find("ICY 200 OK") != std::string::npos)) {
                        /* 请求成功之后，必须要给NTRIP server 发送一个GGA数据 */
                        ret = send(socketFd, ggaBuffer.c_str(), ggaBuffer.size(), 0);
                        if (ret < 0) {
                            // loge("Send gpgga data fail\n");
                            mNtripLogger->mlog("Send gpgga data fail");
                            close(socketFd);
                            return false;
                        }
                        logd("Send gpgga data ok\n");
                        break;
                } else {
                    mNtripLogger->mlog("Request error, result: %s\n", result.c_str());
                }
            } else if (ret == 0) {
                mNtripLogger->mlog("Remote socket close!");
                close(socketFd);
                return false;
            }
            msleep(1000);
        }
        
        if (timeout <= 0) {
            mNtripLogger->mlog("NtripServer[%s:%d %s %s %s] access failed!!!\n", serverIp.c_str(), serverPort,
                    user.c_str(), passwd.c_str(), mountpoint.c_str());
            close(socketFd);
            return false;
        }
        
        my::thread::start();
        socketFd_ = socketFd;
        return true;
    }

    void F9KGPS::NtripClient::stop(void)
    {
        isServiceRunning = false;
        if (socketFd_ > 0) {
            close(socketFd_);
            socketFd_ = -1;
        }
        my::thread::stop();
    }

    void F9KGPS::NtripClient::run() 
    {
        logd("NtripClient service running..");
        isServiceRunning = true;
        int32_t ret = -1;
        std::unique_ptr<char[]> buffer(
            new char[kBufferSize], std::default_delete<char[]>());

        auto tpBeg = std::chrono::steady_clock::now();
        auto tpEnd = tpBeg;
        int32_t intvMs = reportInterval_ * 1000;

        while (isServiceRunning) {
            ret = ::recv(socketFd_, buffer.get(), kBufferSize, 0);
            if (ret == 0) {
                mNtripLogger->mlog("Remote socket close!!!");
                break;
            } else if (ret < 0) {
                if ((errno != EAGAIN) && (errno != EWOULDBLOCK) && (errno != EINTR)) {
                    mNtripLogger->mlog("Remote socket error!!!");
                    break;
                }
            } else {
                /* 给模块发送RTK数据 */
                if (mF9kGps->getStatus()) {
                    mF9kGps->msgEnque((void*)buffer.get(), ret);
                }
                if (ret == kBufferSize) continue;
            }

            std::string ggaData;
            {
                std::lock_guard<std::mutex> _l(mutex_);
                ggaData = ggaBuffer;
            }
            /* 发送GGA数据 给NTRIP Server */
            tpEnd = std::chrono::steady_clock::now();
            if (std::chrono::duration_cast<std::chrono::milliseconds>(tpEnd - tpBeg).count() >= intvMs) {
                tpBeg = std::chrono::steady_clock::now();
                int32_t r = send(socketFd_, ggaData.c_str(), ggaData.size(), 0);
                logd("#### send gga %s", ggaData.c_str());
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(20));
        }

        // loge("NtripClient service done.\n");
        mNtripLogger->mlog("NtripClient service done.");
        isServiceRunning = false;
    }
}

