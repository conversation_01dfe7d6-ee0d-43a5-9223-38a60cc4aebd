
#ifndef __MINIEYE_DFH2_SPEEDLED_H__
#define __MINIEYE_DFH2_SPEEDLED_H__

#include <climits>

#include "cmdline.h"

namespace minieye
{

#define SLCMD_NONE                 INVALID_PROT_CMD
#define SLCMD_SPEED_SET            0xC0
#define SPEEDLED_CMD_MAGIC                0XAA55



class SpeedLed: public Protocol, public my::thread 
{
    public:
        SpeedLed(){};
        ~SpeedLed(){};

        int32_t onServerConnected(void);
        int32_t onServerDisconnected(void);
        int32_t onDataRecevied(const char *p, uint32_t len);
		uint32_t getTimeOutVal(void) 
		{
			return INT_MAX;
		}

        std::string setupCmdList(const char * cmdName);
        bool runCmd(int argc, char **argv, string &ack);
		const char * name() {
			return "SpeedLed";
		}

    private:
        int32_t sendCmd(uint8_t cmd, uint8_t *payload=NULL, int32_t len=0);
        uint8_t calSum(const char *ch, uint16_t len);
        uint64_t systime(void);

        uint64_t            mUartSendNsec = 0;
        string              mInfo;

    protected:
        void run();

};

}


#endif
