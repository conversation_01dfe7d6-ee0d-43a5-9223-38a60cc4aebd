#include <assert.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/time.h>

#include "expand.h"
#include "WeightSensor.FuTai.h"
#define WEIGHT_SENSOR_FUTAI_CMD_HELP       0
#define WEIGHT_SENSOR_FUTAI_CMD_SHOW       1

static const std::vector<CmdStrT> gWSFuTaiCmds = {
    {
        "help",
        WEIGHT_SENSOR_FUTAI_CMD_HELP,
        "show this usage.\n"
    },
    {
        "show",
        WEIGHT_SENSOR_FUTAI_CMD_SHOW,
        "show last WEIGHT SENSOR data.\n"
    },
};

std::string WeightSensorFuTai::setupCmdList(const char * cmdName)
{
    mCmdUsage = CmdStrT::setupCmdList(gWSFuTaiCmds, cmdName);
    return mCmdUsage;
}


bool WeightSensorFuTai::runCmd(int argc, char **argv, string &ack)
{
    uint32_t cmd = CmdStrT::strToCmd(gWSFuTaiCmds, argv[0]);

    switch (cmd) {
        case WEIGHT_SENSOR_FUTAI_CMD_HELP: {
                ack = mCmdUsage;
                return true;
            }

        case WEIGHT_SENSOR_FUTAI_CMD_SHOW: {
                ack = "\n";
                APPEND_STR_MSG(ack, "Msg UTC", 16, "%ld", mLastData.mUtcTime);

                return true;
            }

        default: {
                ack = mCmdUsage;
            }
    }

    return false;
}

WeightSensorFuTai::WeightSensorFuTai(void)
{
}
WeightSensorFuTai::~WeightSensorFuTai()
{
}
int32_t WeightSensorFuTai::onServerConnected(void)
{
    start();
    return 0;
}

int32_t WeightSensorFuTai::onServerDisconnected(void)
{
    stop();
    return 0;
}

int32_t WeightSensorFuTai::onDataRecevied(const char *p, uint32_t len)
{
    logd("len %d, recv  %s", len, my::hex(my::constr(p, len)).c_str());

    onCmdData(mRcvArray, p, len);
    return 0;
}
int32_t WeightSensorFuTai::onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len)
{
    recvArray.insert(recvArray.end(), p, p + len);

    int32_t parsed = 0;

    do {
        parsed = onDataParse(recvArray.data(), recvArray.size());

        //assert(parsed <= recvArray.size());
        if (recvArray.size() >= (1 << 20)) {
            parsed = recvArray.size();
            logd("frame too long!!\n");
        }

        if (parsed < 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + recvArray.size());
            parsed = 0;
            //logd("frame no header, erase all!!\n");

        } else if (parsed > 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + parsed);
            //logd("erase size %d\n", parsed);

        } else {
            logd("alertor frame not complete!!\n");
        }

        if (recvArray.size() <= 0 || parsed <= 0) {
            //logd("break");
            break;
        }

    } while (1);

    return parsed;

}


int32_t WeightSensorFuTai::onDataParse(const uint8_t *p, uint32_t size)
{
    int32_t start = -1;
    int32_t end = -1;
    uint32_t offset = 0;

    if (size < 27) {
        return 0;
    }

    int length = 0;

    for (int i = 0; i < static_cast<int32_t>(size); ++i) {
        if (FUTAI_WEIGHT_SENSOR_MSG_MAGIC == *((uint32_t*)(p + i))) {
            start = i;
            length = *((uint16_t*)(p + i + 4));

            if ((i + 6) <= size) {
                end = i + 6 + length - 1;
                break;
            }
        }
    }

    if (start >= 0) {
        if (end < 0) {
            return start;
        }

        const char * tmp = (const char*)(p + start);
        int len = end - start + 1;

        WeightSensorDataT wsd;
        mCurMsg = my::constr(tmp, len);
        int32_t eaten = mCurMsg.length();

        if (!wsd.decode(mCurMsg)) {
            loge("WeightSensorDataT decode fail!");
            return -1;

        } else {
            logd("WeightSensorDataT eaten size = %d\n", eaten);
        }

        if (eaten > 0) {
            logd("enter onMessage");
            onMessage(&wsd);

        } else {
            logd("Invalid data");
            return -1;
        }

    } else {
        //logd("******start =  %d, end = %d\n", start, end);
        return -1;
    }

    return end + 1;
}


bool WeightSensorFuTai::onMessage(WeightSensorDataT * wsd)
{
    std::shared_ptr<WeightSensorDataT> sp = make_shared<WeightSensorDataT>(wsd);

    return !my::BufferQueue<std::shared_ptr<WeightSensorDataT>>::push(sp, 100);
}

// (数字)字符串转BCD, 如果字符串的长度不足n, 在前面补0
void numstr2bcd(char* bcd, const my::string & sim, int n)
{
	my::constr str(sim);

	if (n & 0x1) n++; // 必须是偶数

	int m = str.length();
	if (m > n) m = n; // 如果是长字符串, m=n, 必定是偶数; 如果是短字符串, 结果需要n个, 也是偶数

	int d = n - m;
	if (d > 0) // 字符串长度不够, 需要补0 
	{
		int hd = d >> 1;
		for (int i = 0; i < hd; i++)
		{
			*bcd++ = 0x00; // 补0
		}

		if (d & 1)
		{
			*bcd++ = (*str - '0');
			str += 1; m--;
		}
	}

	m /= 2;
	for (int i = 0; i < m; i++)
	{
		my::uchar v0 = (*str - '0'); str += 1;
		my::uchar v1 = (*str - '0'); str += 1;
		*bcd++ = (my::uchar)(v0 << 4 | v1);
	}
}

void WeightSensorFuTai::run()
{
    uint32_t noAckCount = 0;
    bool ret = false;

    while (!exiting()) {
        std::shared_ptr<WeightSensorDataT> sp ;
        bool ret = wait(sp, std::chrono::microseconds(1000000));

        if (ret) {
            WeightSensorDataT * wsd = sp.get();
            mLastData.mUtcTime = my::timestamp::utc_milliseconds();
            mLastData.raw = std::string((const char*)wsd->raw, wsd->raw.length());
            mLastData.bcdTime = std::string((char*)wsd->timeBcd, 6);
            switch (wsd->cmd) {
                case WS_FUTAI_CMD_STAT_RPT: {//状态上报指令
                        wsd->ctx >> mLastData.hwVer >> mLastData.fwVer >> mLastData.BATmV;
                        logd("hwVer %d, fwVer %d, bat %d mV", mLastData.hwVer, mLastData.fwVer, mLastData.BATmV);
                        break;
                    }
                case WS_FUTAI_CMD_TM_SYNC_REQ: {//时间同步指令
#if 0
                    WeightSensorDataT rsp;
                    my::string timestr = (my::timestamp::YYMMDD_HHMMSS());
                    numstr2bcd((char*)rsp.timeBcd, timestr, sizeof(rsp.timeBcd) * 2);
                    if (time(NULL) < 1640966400) {
                        loge("timestr %s, %s", (const char*)timestr, (char*)my::timestamp::YYMMDD_HHMMSS());
                        loge("%02x%02x%02x_%02x%02x%02x", rsp.timeBcd[0], rsp.timeBcd[1], rsp.timeBcd[2], rsp.timeBcd[3], rsp.timeBcd[4], rsp.timeBcd[5]);
                    } else {
                        rsp.cmd = 0x0A;
                        int res = 0;
                        my::string data;
                        data << res;
                        for (int i = 0; i < sizeof (rsp.timeBcd); i++) {
                            data << rsp.timeBcd[i];
                        }
                        my::string msg = rsp.encode(data);
                        msgEnque((void*)msg.c_str(), msg.length());
                    }
#endif
                    break;
                }

                case WS_FUTAI_CMD_WEIGHT_DATA: {//称重数据上传指令
                        wsd->ctx >> mLastData.seq >> mLastData.isValidGps >> mLastData.latSNFlag;
                        wsd->ctx >> mLastData.lngEWFlag >> mLastData.lat >> mLastData.lng;
                        wsd->ctx >> mLastData.netWeightAcc >> mLastData.countAcc >> mLastData.resultCount;

                        my::string card1;
                        wsd->ctx >> mLastData.grossWeight1 >> mLastData.tareWeight1 >> mLastData.netWeight1 >> mLastData.padWeight1 >> card1(1);
                        mLastData.cardDataLen1 = card1.length();
                        mLastData.cardData1 = string(card1.c_str(), card1.length());

                        my::string card2;
                        wsd->ctx >> mLastData.grossWeight2 >> mLastData.tareWeight2 >> mLastData.netWeight2 >> mLastData.padWeight2 >> card2(1);
                        mLastData.cardDataLen2 = card2.length();
                        mLastData.cardData2 = string(card2.c_str(), card2.length());

                        logd("seq %d, isValidGps %d, latSNFlag %d", mLastData.seq, mLastData.isValidGps, mLastData.latSNFlag);
                        logd("lngEWFlag %d, lat %f, lng %f", mLastData.lngEWFlag, mLastData.lat, mLastData.lng);
                        logd("netWeightAcc %f, countAcc %d, resultCount %d", mLastData.netWeightAcc, mLastData.countAcc, mLastData.resultCount);
                        logd("grossWeight1 %f, tareWeight1 %f, netWeight1 %f, padWeight1 %f", mLastData.grossWeight1, mLastData.tareWeight1, mLastData.netWeight1, mLastData.padWeight1);
                        logd("card1 %d, %s", mLastData.cardDataLen1, mLastData.cardData1.c_str());

                        logd("grossWeight2 %f, tareWeight2 %f, netWeight2 %f, padWeight2 %f", mLastData.grossWeight2, mLastData.tareWeight2, mLastData.netWeight2, mLastData.padWeight2);
                        logd("card2 %d, %s", mLastData.cardDataLen2, mLastData.cardData2.c_str());

                        break;
                    }

                case WS_FUTAI_CMD_FAULT_CODE: {/*故障上传*/
                        wsd->ctx >> mLastData.faultCode;
                        logd("faultCode 0x%x", mLastData.faultCode);
                        switch(mLastData.faultCode) {
                            
                        }
                        break;
                    }

                default: {
                        break;
                    }
            }

            msgpack::sbuffer sbuf;
            msgpack::pack(sbuf, mLastData);
            ExpandSet & es = ExpandSet::getInstance();
            es.sendLibFlow(WEIGHTSENSOR_FUTAI_LIBFLOW_TOPIIC, sbuf.data(), sbuf.size());
        }
    }

}
bool WeightSensorFuTai::onDataValidate(const uint8_t *p, uint32_t len)
{
    return true;
}



