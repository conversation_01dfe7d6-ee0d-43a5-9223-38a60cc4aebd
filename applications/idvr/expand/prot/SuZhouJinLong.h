#pragma once

#include <atomic>

#include "canDisplay.h"
#include "cmdline.h"
#include "algo.h"
#include "libflow.h"

namespace minieye {

class SuZhouJinLong : public CanDisplay, my::thread, public IALGO_OBSERVER {
 public:
    struct RadarCarInfo;
    struct CarFlicInfo;

    enum AlarmEvt {
        E_ALARM_NONE = 0,
        E_RADAR_FLCA,
        E_RADAR_RLCA,
        E_RADAR_FLCA_AND_RLCA,
        E_RADAR_BSD,
        E_ALGO_BSD,
    };

    enum DeviceLocation {
        E_DEVICE_LEFT = 0,
        E_DEVICE_RIGHT,
        E_DEVICE_MAX
    };

    explicit SuZhouJinLong(bool isCan1Chn);
    ~SuZhouJinLong() noexcept;

    SuZhouJinLong &operator=(const Su<PERSON>houJinLong &other) = delete;
    SuZhouJinLong(const SuZhouJinLong &other) = delete;

 protected:
    virtual bool onAlgoEvent(std::shared_ptr<Event> evt);
    virtual int32_t onServerConnected(void);
    virtual int32_t onServerDisconnected(void);
    virtual int32_t onDataRecevied(const char *p, uint32_t len);
    virtual int32_t onMessage(const char *p, uint32_t len);
    virtual std::string setupCmdList(const char *cmdName);
    virtual bool runCmd(int argc, char **argv, string &ack);
    virtual void run();

 private:
    bool onMessage(uint8_t *p, uint32_t len);
    bool devCheck();

    std::string mCmdUsage;
    time_t mLastDataUtcTime = 0;

    bool mIsCan1Chn;

    static std::atomic<bool> mIsInit;

    static std::condition_variable mInfoUpdateCond;
    static std::mutex mInfoUpdateMtx;
    static std::map<uint32_t, my::timestamp> mCanMsgTs;
    static std::shared_ptr<RadarCarInfo> mRadarCarInfo;
    static std::shared_ptr<CarFlicInfo> mCarFlicInfo;
    static int32_t mCarFlicAlarmExitCnt;

    static bool mRadarRecv;
    static uint8_t mErrorCode;

    static my::timestamp mAudioTs;

    static std::map<std::string, my::timestamp> mRadarEvtTsMap;
    static std::pair<AlarmEvt, std::pair<my::timestamp, uint8_t /*level*/>> mRadarEvt[E_DEVICE_MAX];
    static std::pair<my::timestamp, uint8_t /*level 3>2>1*/> mAlgoEvt[E_DEVICE_MAX];

    static std::shared_ptr<LibflowServer> mLibflowServer;
};

class SuZhouJinLongCan1 : public SuZhouJinLong {
 public:
    SuZhouJinLongCan1();
};

class SuZhouJinLongCan2 : public SuZhouJinLong {
 public:
    SuZhouJinLongCan2();
};

}  // namespace minieye