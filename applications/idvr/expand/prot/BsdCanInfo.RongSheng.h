#pragma once

#include "cmdline.h"
#include "FileLog.h"
#include "expand.message.h"
#include "algo.h"
#include "canDisplay.h"
// #include "ipcAgent.h"

namespace minieye {

class BsdCanInfo : public Protocol, my::thread, public IALGO_OBSERVER {
 public:
    struct CanInfo;

    explicit BsdCanInfo();
    virtual ~BsdCanInfo() noexcept;
    BsdCanInfo &operator=(const BsdCanInfo &other) = delete;
    BsdCanInfo(const BsdCanInfo &other) = delete;

 protected:
    virtual bool onAlgoEvent(std::shared_ptr<Event> evt);
    virtual int32_t onServerConnected(void);
    virtual int32_t onServerDisconnected(void);
    virtual int32_t onDataRecevied(const char *p, uint32_t len);
    virtual std::string setupCmdList(const char *cmdName);
    virtual bool runCmd(int argc, char **argv, string &ack);
    virtual void run();

 private:
   //  std::shared_ptr<IpcClient> mIpcClient;
    std::string mCmdUsage;

    my::timestamp mLastAlertUpdateTs;
    my::timestamp mLastBsdAlarmTs;
    int mLastBsdLevel = 0;
};

}  // namespace minieye