#ifndef __LIQUID_METER_FUTAI_H__
#define __LIQUID_METER_FUTAI_H__

#include "cmdline.h"
#include "expand.message.h"
namespace minieye
{
#define LIQUID_METER_BGN_MAGIC '@'
#define LIQUID_METER_END_MAGIC '#'

#define LIQUID_METER_CMD_SET_BAUD       'A'
#define LIQUID_METER_CMD_CALIB_ZERO     'B'
#define LIQUID_METER_CMD_CALIB_FULL     'C'
#define LIQUID_METER_CMD_SET_VOL        'D'
#define LIQUID_METER_CMD_GET_LEVEL      'E'
#define LIQUID_METER_CMD_QSG_ID         'G' /*设置、查询及探测 ID 号, 0 : 查询; 1 : 设置; 2 : 探测*/
#define LIQUID_METER_CMD_GS_DAMPING     'H' /*查询/设置阻尼*/
#define LIQUID_METER_CMD_RESET_ALL      'T'

#define LIQUID_METER_PROP_BAUD  "persist.futai.liquid.meter.baud"

template<class _type_>
_type_ bcd2value(_type_ rawMSB)
{
    char tmp[128] = {0};
    memcpy(tmp, &rawMSB, sizeof(rawMSB));
    return (_type_)strtoul(tmp, NULL, 16);
}
typedef struct {
    uint8_t  bgn; /*LIQUID_METER_BGN_MAGIC*/
    uint16_t id;
    uint8_t  cmd;
    uint16_t ctxLen;
    my::string ctx;
    uint16_t  check_sum;/*id ~ data*/
    uint8_t  end;

    my::string raw;

    my::string encode(uint16_t curId, uint8_t curCmd, my::string & data)
    {
        my::constr tmp;
        my::string dataStr;
        bgn = LIQUID_METER_BGN_MAGIC;
        dataStr.assignf("%02X", curId);
        tmp = dataStr;
        tmp >> my::ntoh >> id;

        cmd = curCmd;
        dataStr.assignf("%02X", data.length());
        tmp = dataStr;
        tmp >> my::ntoh >> ctxLen;
        ctx = data;
        uint32_t chk = (id & 0xff);
        chk += ((id >> 8) & 0xff);
        chk += cmd;
        chk += ctxLen & 0xff;
        chk += ((ctxLen >> 8) & 0xff);

        for (int i = 0; i < data.length(); i++) {
            chk += (uint8_t)ctx.c_str()[i];
        }

        check_sum = (chk & 0xff);
        logd("chksum 0x%x", check_sum);
        dataStr.assignf("%02X", check_sum);
        tmp = dataStr;
        tmp >> my::hton >> check_sum;
        end = LIQUID_METER_END_MAGIC;

        dataStr.clear();
        dataStr << my::hton << bgn << id << cmd << ctxLen;
        dataStr << ctx << check_sum << end;

        return dataStr;
    }
    int decode(const uint8_t * p, int len)
    {
        raw = my::string((const char*)p, len);
        my::constr data((const char*)p, len);
        my::hexdump(data, true, "expand");
        uint16_t tmp = 0;
        data >> bgn >> id >> cmd >> tmp;
        id = bcd2value<uint16_t>(id);
        ctxLen = bcd2value<uint16_t>(tmp);
        logd("cmd %c, id 0x%x, ctxLen = 0x%x", cmd, id, ctxLen);
        ctx.assign((const char*)data, ctxLen);
        data += ctxLen;
        data >> check_sum >> end;

        if ((bgn != LIQUID_METER_BGN_MAGIC) ||
            (end != LIQUID_METER_END_MAGIC)) {
            loge("magic is invalid !");
            return -1;
        }

        logd("bgn %c id 0x%04x cmd 0x%02x ctxLen %d chk 0x%04x", bgn, id, cmd, ctx.length(), check_sum);
        uint8_t * ptr = (uint8_t *)raw.c_str();
        uint32_t  chk = 0;

        for (int i = 1; i < ctx.length() + 5; i++) {
            chk += ptr[i];
        }

        uint16_t r = chk & 0xff;
        my::string c;
        c.assignf("%02X", r);
        my::constr t = c;
        t >> my::hton >> r;

        if (r != check_sum) {
            loge("check sum is invalid !chk 0x%02x, r 0x%04x, chksum %04x", chk, r, check_sum);
            return (raw.length() - data.length());
        }

        return (raw.length() - data.length());
    }
} LiquidMeterSensorDataT;


class LiquidMeterFutai : public Protocol
{
    public:
        LiquidMeterFutai();
        ~LiquidMeterFutai() {};

        int32_t onServerConnected(void);
        int32_t onServerDisconnected(void);
        int32_t onDataRecevied(const char *p, uint32_t len);
        std::string setupCmdList(const char * cmdName);
        bool runCmd(int argc, char **argv, string &ack);

        /*空校准*/
        bool calibZero(uint16_t id);

        /*满校准*/
        bool calibFull(uint16_t id);

        /*设置容积*/
        bool setVolume(uint16_t id, int val);/*max val = 9999*/

        /*获取容积*/
        bool getVolume(uint16_t id);/*max val = 9999*/

        /*获取当前水位*/
        bool getCurLvl(uint16_t id);

        /*？？*/
        bool devidDetect();

        /*阻尼*/
        bool getDamping(uint16_t id);

    private:
        int32_t onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len);
        int32_t onDataParse(const char *p, uint32_t size);
        bool onMessage(LiquidMeterSensorDataT * msg);
        bool setBaud(uint16_t id, int val);


    private:
        string mCmdUsage;
        vector<uint8_t>     mRcvArray;
        time_t mLastDataUtcTime = 0;

        my::constr           mCurMsg;
        std::map<uint16_t, expand::LiquidMeterSensorMessage> mDevTbl;
};

}


#endif

