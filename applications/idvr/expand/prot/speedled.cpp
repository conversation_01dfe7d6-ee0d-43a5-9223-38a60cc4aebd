#include "speedled.h"
#include <assert.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <sys/time.h>
#include "devicehub.h"



namespace minieye {

static const std::vector<CmdStrT> gSpeedLedCmds = {
    {
        "set_speed", SLCMD_SPEED_SET, "set_speed [value]"
    }
};

typedef struct {
    uint16_t    magic;
    uint8_t     cmd;
    uint8_t     len;
    uint8_t     payload[0];
    //uint8_t     checksum;
} __attribute__((packed)) UartProtHeaderT;

int32_t SpeedLed::onServerConnected(void)
{
    start();
    return 0;
}
int32_t SpeedLed::onServerDisconnected(void)
{
    stop();
    return 0;
}

int32_t SpeedLed::onDataRecevied(const char *p, uint32_t len)
{
    logd("on cmd data %d", len);
	logd("recv %s", my::hex(my::constr(p, len)).c_str());
    
    return 0;
}

uint64_t SpeedLed::systime(void)
{
    struct timespec times = {0, 0};
    uint64_t time;

    clock_gettime(CLOCK_MONOTONIC, &times);
    //printf("CLOCK_MONOTONIC: %" FMT_LLU ", %" FMT_LLU "\n", times.tv_sec, times.tv_nsec);
    time = times.tv_sec * 1000 + times.tv_nsec / 1000000;
    //printf("time = %" FMT_LLD "\n", time);
    return time;
}

int32_t SpeedLed::sendCmd(uint8_t cmd, uint8_t *payload, int32_t len)
{
    uint8_t buf[1024];
    UartProtHeaderT *p = (UartProtHeaderT *)&buf[0];

    uint64_t now = systime();
    if (cmd == (SLCMD_SPEED_SET)) {
        /* limited send speed */
        if(now < mUartSendNsec + 100) {
            logd("trigger-cmd limited send speed");
            return -1;
        }
    }
    mUartSendNsec = now;
    p->magic = SPEEDLED_CMD_MAGIC; // 55 AA
    p->cmd = cmd;
    p->len = len;
    if(payload && len > 0){
        memcpy(p->payload, payload, len);
    }
    p->payload[len] = calSum((const char *)buf, sizeof(UartProtHeaderT) + len);
    msgEnque(buf, sizeof(UartProtHeaderT) + len + 1);

    return 0;
}

std::string SpeedLed::setupCmdList(const char * cmdName)
{
    return CmdStrT::setupCmdList(gSpeedLedCmds, cmdName);
}

bool SpeedLed::runCmd(int argc, char **argv, string &ack)
{
    bool wait = false;
    mInfo = "";
    bool ret = true;
    
    if (argc < 1) {
        ack = mInfo;
        return false;
    }
    uint32_t cmd = CmdStrT::strToCmd(gSpeedLedCmds, argv[0]);

    switch(cmd) {
        case SLCMD_SPEED_SET:
            {
                if(argc < 2 ) {
                    mInfo += "\ninput arg";
                } else {
                    uint8_t buf[2];
                    int speed = atoi(argv[1]);
                    if (speed >= 0 && speed <= 255) {
                        buf[0] = (uint8_t)speed;
                        sendCmd(cmd, buf, 1);
                        mInfo += argv[0];
                        mInfo += " ";
                        mInfo += argv[1];
                    } else {
                        mInfo += "\nerror speed arg";
                        ret = false;
                    }
                }
            }
            break;
        default:
            {
                mInfo += "\nsupport below:\n";
                for(auto c : gSpeedLedCmds) {
                    mInfo += c.mName;
                    mInfo += " ";
                }
                ret = false;
            }
            break;
    }

    ack = mInfo;
    return ret;
}


uint8_t SpeedLed::calSum(const char *ch, uint16_t len)
{
    uint32_t i;
    uint32_t sum=0;

    for (i = 0; i < len; i++) {
        sum += ch[i];
    }  
    return (sum & 0xFF);
}

// 速度自动显示功能
void SpeedLed::run()
{
    static int32_t lastSpeed = 0;
    static int32_t countTimeout = 0;
    while (!exiting()) {
        uint8_t buf[1];
        int32_t speed = DeviceHub::getInstance().getCarSpeed();
        if (speed != lastSpeed) {
            if (speed >= 0 && speed <= 255) {
                buf[0] = (uint8_t)speed;
                sendCmd(SLCMD_SPEED_SET, buf, 1);
                lastSpeed = speed;
                countTimeout = 0;
            } else {
                loge("error speed info %d", speed);
            }
        } else {
            // 长时间没有更新
            countTimeout++;
            if (countTimeout == 50) {
                buf[0] = (uint8_t)speed;
                sendCmd(SLCMD_SPEED_SET, buf, 1);
                countTimeout = 0;
            }
            
        }

        usleep(100 * 1000);
    
    }
}

} //namespace minieye

