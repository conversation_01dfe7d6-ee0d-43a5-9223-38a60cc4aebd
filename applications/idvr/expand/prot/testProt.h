
#ifndef __MINIEYE_TEST_PROTO_H__
#define __MINIEYE_TEST_PROTO_H__

#include "cmdline.h"
namespace minieye
{

class TestProt: public Protocol
{
    public:
        TestProt(){};
        ~TestProt(){};

        std::string setupCmdList(const char * cmdName)
        {
            return "";
        }
        bool runCmd(int argc, char **argv, string &ack)
        {
            uint8_t buf[5] = {1, 2, 3, 4, 5};
            msgEnque(buf, 5);

            ack = "test";
            return true;
        }


        int32_t onServerConnected(void){ return 0; };
        int32_t onServerDisconnected(void){ return 0; };
        int32_t onDataRecevied(const char *p, uint32_t len){ 
            logd("on cmd data %d\n", len);
        	logd("recv %s\n", my::hex(my::constr(p, len)).c_str());            
            return 0; 

        };

};

}


#endif
