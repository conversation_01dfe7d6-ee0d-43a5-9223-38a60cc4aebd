#include "alertor.h"
#include <assert.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <sys/time.h>
#include "mystd.h"


namespace minieye {

#define alogd(fmt, ...)\
		logd("[alertor]" fmt, ##__VA_ARGS__)
#if 0
#define ALERTOR_UPGRADE "upgrade"
#define ALERTOR_VERSION "version"
#define ALERTOR_SET_VOL "set_vol"
#define ALERTOR_GET_VOL "get_vol"
#define ALERTOR_SET_LUMI "set_lumi"
#define ALERTOR_SET_FRE "set_fre"
#define ALERTOR_WRITE_MUSIC "write_music"
#define ALERTOR_CMD_IDX(cmd) E_ALERTOR_CMD_##cmd
#endif
static const std::vector<CmdStrT> gAlertorCmds = {
    {
        "help",
        ATCMD_HELP
    },
    {
         "upgrade",
        ATCMD_MCU_UPGRADE
    },
    {
         "version",
        ATCMD_GET_VERSION
    },
    {
         "trigger",
        ATCMD_AUDIO_LED_START
    },
    {
         "set_vol",
        ATCMD_VOL_SET 
    },
    {
         "get_vol",
        ATCMD_VOL_GET
    },
    {
         "set_lumi",
        ATCMD_LED_LUMI
    },
    {
         "set_fre",
        ATCMD_LED_FRE
    },
    {
         "write_music",
        ATCMD_WRITE_MUSIC
    },
};

Alertor::Alertor(void)
{
    pthread_cond_init(&mCond, NULL);
    pthread_mutex_init(&mLock, NULL);
}

Alertor::~Alertor()
{

}

int32_t Alertor::onServerConnected(void)
{
    return 0;
}
int32_t Alertor::onServerDisconnected(void)
{
    return 0;
}

int32_t Alertor::onDataRecevied(const char *p, uint32_t len)
{
    alogd("on cmd data %d\n", len);
	alogd("recv %s\n", my::hex(my::constr(p, len)).c_str());
    
    onCmdData(mRcvArray, p, len);
    return 0;
}

uint64_t Alertor::systime(void)
{
    struct timespec times = {0, 0};
    uint64_t time;

    clock_gettime(CLOCK_MONOTONIC, &times);
    time = times.tv_sec * 1000 + times.tv_nsec / 1000000;
    return time;
}

int32_t Alertor::sendCmd(uint8_t cmd, uint8_t *payload, int32_t len)
{
    uint8_t buf[1024];
    UartProtHeaderT *p = (UartProtHeaderT *)&buf[0];

    uint64_t now = systime();
    if(cmd == (ATCMD_AUDIO_LED_START)){
        /* limited send speed */
        if(now < mUartSendNsec + 1000) {
            alogd("trigger-cmd limited send speed");
            return -1;
        }
    }
    mUartSendNsec = now;
    p->magic = UART_MAGIC2;
    p->cmd = cmd;
    p->len = len;
    if(payload && len > 0){
        memcpy(p->payload, payload, len);
    }
    p->payload[len] = calSum((const char *)buf, sizeof(UartProtHeaderT)+len);
    msgEnque(buf, sizeof(UartProtHeaderT)+len+1);

    return 0;
}

/******uart braud******
*  0-1200
*  1-2400
*  2-4800
*  3-9600
*  4-19200
*  5-38400
*  6-57600
*  7-115200
*  8-230400
*  9-460800
*  10-921600
******uart braud******/

int32_t Alertor::onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len)
{
    recvArray.insert(recvArray.end(), p, p + len);

    int32_t parsed = 0;
    do {
        parsed = onUartParse((const char *)recvArray.data(), recvArray.size());
        assert(parsed <= recvArray.size());
        if (recvArray.size() >= (1<<20)) {
            parsed = recvArray.size();
            alogd("frame too long!!\n");
        }
        if (parsed < 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + recvArray.size());
            parsed = 0;
            alogd("frame no header, erase all!!\n");
        } else if (parsed > 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + parsed);
            //alogd("erase size %d\n", parsed);
        } else {
            //alogd("alertor frame not complete!!\n");
        }

        if(recvArray.size() <=0 || parsed <= 0) {
            break;
        }

    } while (1);

    return parsed;
}

int32_t Alertor::onUartParse(const char *p, uint32_t size)
{
#define NEED_MORE_DATA      (0)
#define DATA_INVALID        (-1)
    uint32_t offset = 0;;
    uint32_t frameLen = 0;
    UartProtHeaderT *pHeader;
    uint32_t i=0;

    alogd("on uart parse size = %d\n", size);
	alogd("%s\n", my::hex(my::constr(p, size)).c_str());
    while (size - offset >= sizeof(UartProtHeaderT) + 1) { // 1 for checksum
        pHeader = (UartProtHeaderT *)&p[offset];
        //sync header
        if (pHeader->magic == UART_MAGIC1 || pHeader->magic == UART_MAGIC2) {
            frameLen = pHeader->len + sizeof(UartProtHeaderT) + 1; // 1 for checksum
            if (size - offset >= frameLen) {
                if (onUartValidate((const char *)&pHeader->magic, frameLen)) {
                    onMessage(pHeader, frameLen);
                }else{
                    alogd("frame err: magic: %x cmd: %x\n", pHeader->magic, pHeader->cmd);
                }
                return offset + frameLen; /* return one frame */
            } else { /* not complete */
                alogd("frame recv not complete, len %d\n", frameLen);
                break;
            }
        } else {/* header err*/
            offset++;
            alogd("magic err: 0x%x\n", pHeader->magic);
        }
    }

    if (offset) {
        return offset;
    } else {
        return NEED_MORE_DATA;
    }
}

bool Alertor::onUartValidate(const char *p, uint32_t len)
{
    UartProtHeaderT *pHeader = (UartProtHeaderT *)p;
    uint8_t cc = calSum(p, len - 1);
    if (cc != (uint8_t)p[len - 1]) {
        alogd("data calSum error:[0x%02x]-[0x%02x]\n", cc, p[len-1]);
	    alogd("%s\n", my::hex(my::constr(p, len)).c_str());
        return false;
    }
    return true;
}

void Alertor::wait_ack(void)
{
    struct timeval now;
    struct timespec timeout;
    int retcode=0;

    pthread_mutex_lock(&mLock);
    gettimeofday(&now, NULL);
    timeout.tv_sec = now.tv_sec + 1;
    timeout.tv_nsec = now.tv_usec * 1000;
    retcode = pthread_cond_timedwait(&mCond, &mLock, &timeout);
    if (retcode == ETIMEDOUT) {
        /* timeout occurred */
    } else {
        /* operate on x and y */
    }
    pthread_mutex_unlock(&mLock);
}

std::string Alertor::setupCmdList(const char * cmdName)
{
    mCmdUsage = CmdStrT::setupCmdList(gAlertorCmds, cmdName);
    return mCmdUsage;
}

bool Alertor::runCmd(int argc, char **argv, string &ack)
{
    bool wait = false;
    mInfo = "";
    bool ret = true;

    if (argc < 1) {
        ack = mInfo;
        return false;
    }
    uint32_t cmd = CmdStrT::strToCmd(gAlertorCmds, argv[0]);

    switch(cmd) {
        case ATCMD_HELP: {
            ack = mCmdUsage;
            return true;
        }
        case ATCMD_GET_VERSION:
        case ATCMD_VOL_GET:
            {
                sendCmd(cmd);
                wait = true;
            }
            break;
        case ATCMD_AUDIO_LED_START:
            {
                sendCmd(cmd);
            }
            break;
        case ATCMD_VOL_SET:
        case ATCMD_LED_LUMI:
        case ATCMD_LED_FRE:
            {
                if(argc < 2 ) {
                    mInfo += "\ninput arg";
                    ret = false;
                } else {
                    uint8_t buf[2];
                    int vol = atoi(argv[1]);
                    buf[0] = vol;
                    sendCmd(cmd, buf, 1);
                    mInfo += argv[0];
                    mInfo += " ";
                    mInfo += argv[1];
                }
            }
            break;
        default:
            {
                mInfo += "\nsupport below:\n";
                for(auto c : gAlertorCmds) {
                    mInfo += c.mName;
                    mInfo += " ";
                }
                ret = false;
            }
            break;
    }

    if (wait) {
        wait_ack();
    }

    ack = mInfo;
    return ret;
}

bool Alertor::upgradeInit(void)
{
    struct stat st;
    if(access(ALERTOR_UPGRD_FILENAME, F_OK)){
        return false;
    }

    if(!stat(ALERTOR_UPGRD_FILENAME, &st)) {
        mPacketIdx = 0;
        mPacketTotal = (st.st_size + ALERTOR_UPGRD_FRAME_SIZE - 1) / ALERTOR_UPGRD_FRAME_SIZE;
        mUpgradeEnable = true; 
        return true;
    }
    return false;
}

bool Alertor::sendUpgradeFrame(void)
{
    FILE *fp;
    uint8_t buf[1024];
    UpgradeHeaderT *p = (UpgradeHeaderT *)&buf[0];

    memset(buf, 0, sizeof(buf));
    p->crc = 0;
    p->packet_idx = mPacketIdx;
    p->packet_total = mPacketTotal;

    if(mPacketIdx == mPacketTotal) {
        mUpgradeEnable = false; 
        alogd("send over\n");
        return true;
    }
    
    fp = fopen(ALERTOR_UPGRD_FILENAME, "r");
    if(!fp) {
        return false;
    }
    fseek(fp, mPacketIdx*ALERTOR_UPGRD_FRAME_SIZE, 0);
    fread(p->payload, 1, 128, fp);
    sendCmd(ATCMD_MCU_UPGRADE, buf, sizeof(UpgradeHeaderT)+ALERTOR_UPGRD_FRAME_SIZE);
    fclose(fp);
    mPacketIdx++;

    return true;
}

bool Alertor::sendMusicFileInfo(void)
{
    uint8_t buf[1024];
    AudioFrameSeqT *p = (AudioFrameSeqT*)&buf[0];
    UpgradeFrameSeqT *info = (UpgradeFrameSeqT *)p->payload;
    struct stat st;
    if(access(ALERTOR_MUSIC_FILENAME, F_OK)){
        alogd("can't find %s\n", ALERTOR_MUSIC_FILENAME);
        return false;
    }

    if(!stat(ALERTOR_MUSIC_FILENAME, &st)) {
        mFileIdx = 0;
        mFileIdxMax = (st.st_size + MUSIC_FRAME_SIZE - 1) / MUSIC_FRAME_SIZE;
        p->idx_max = 1 + mFileIdxMax;
        p->idx = 0;
        info->file_seq = 1;
        info->file_crc = 0xaabb;
        info->file_size = st.st_size;
        sendCmd(ATCMD_WRITE_MUSIC, buf, sizeof(AudioFrameSeqT) + MUSIC_FRAME_SIZE);
        return true;
    }
    return false;
}

bool Alertor::sendMusicFrame(void)
{
    FILE *fp;
    uint8_t buf[1024];
    AudioFrameSeqT *p = (AudioFrameSeqT *)&buf[0];

    memset(buf, 0, sizeof(buf));
    p->idx_max = 1 + mFileIdxMax; 
    p->idx = mFileIdx + 1;

    if(mFileIdx == mFileIdxMax) {
        alogd("send music over\n");
        return true;
    }
    
    fp = fopen(ALERTOR_MUSIC_FILENAME, "r");
    if(!fp) {
        return false;
    }
    fseek(fp, mFileIdx*MUSIC_FRAME_SIZE, 0);
    fread(p->payload, 1, MUSIC_FRAME_SIZE, fp);
    
    alogd("update_music %d/%d\n", p->idx, p->idx_max);
    sendCmd(ATCMD_WRITE_MUSIC, buf, sizeof(UpgradeHeaderT)+MUSIC_FRAME_SIZE);
    fclose(fp);
    mFileIdx++;
    return true;
}

bool Alertor::onMessage(UartProtHeaderT *p, uint32_t len)
{
    if(len != sizeof(UartProtHeaderT) + p->len + 1){
        alogd("message len error %d %zu\n", len, sizeof(UartProtHeaderT) + p->len + 1);
        return false;
    }
    alogd("on cmd 0x%02x\n", p->cmd);
    switch(p->cmd){
        case ATCMD_GET_VERSION:
            {
                mUpgradeAck = false;
                alogd("version %d.%d\n", (p->payload[0]>>4 & 0x0F), p->payload[0] & 0x0F);
                char data[16];
                snprintf(data, sizeof(data), "%d.%d", (p->payload[0]>>4 & 0x0F), p->payload[0] & 0x0F);
                mInfo += data;
                pthread_mutex_lock(&mLock);
                pthread_cond_signal(&mCond);
                pthread_mutex_unlock(&mLock);
            }
            break;
        case ATCMD_VOL_GET:
            {
                #define VOLUME_FRAME_ACK_LEN 6
                #define VOLUME_pos 5
                char data[16];
                if(len >= sizeof(UartProtHeaderT) + VOLUME_FRAME_ACK_LEN) {
                    snprintf(data, sizeof(data), "%d", p->payload[VOLUME_pos-1]);
                    alogd("%s", data);
                    mInfo += data;
                    pthread_mutex_lock(&mLock);
                    pthread_cond_signal(&mCond);
                    pthread_mutex_unlock(&mLock);
                }
            }
            break;
        case ATCMD_WRITE_MUSIC:
            {
                AudioFrameSeqT *ack = (AudioFrameSeqT *)p->payload;
                alogd("update_music ack %d/%d\n", ack->idx, ack->idx_max);
                sendMusicFrame();
            }
            break;
        case ATCMD_WRITE_MUSIC_RESULT:
            {
                uint16_t *res = (uint16_t *)p->payload;
                alogd("erase_success %d\n erase_fail %d\n erase_skip %d\n write_success %d\n write_fail_cnt %d\n",\
                        res[0], res[1], res[2], res[3], res[4]);
            }
            break;
        case ATCMD_MCU_RESET:
            alogd("recv reset ack\n");
            break;
        case ATCMD_MCU_BLD_MODE:
            alogd("mcu bld mode\n");
            if(mUpgradeEnable) {
                sendUpgradeFrame();
                alogd("start send frame");
            }
            break;
        case ATCMD_MCU_APP_MODE:
            alogd("mcu app mode\n");
            break;
        case ATCMD_MCU_ALONE_MODE:
            alogd("mcu alone mode\n");
            break;
        case ATCMD_MCU_UPGRADE:
            mUpgradeAck = true;
            alogd("recv upgrade ack\n");
            if(mUpgradeEnable) {
                sendUpgradeFrame();
                alogd("send frame");
            }
            break;
        default:
            alogd("unknow cmd.\n");
            break;
    }
    
    return true;
}

uint8_t Alertor::calSum(const char *ch, uint16_t len)
{
    uint32_t i;
    uint32_t sum=0;

    for(i=0; i<len; i++){
        sum += ch[i];
    }  
    return (sum&0xFF);
}

uint16_t Alertor::UpdateCRC16(uint16_t crcIn, uint8_t byte)
{
    uint32_t crc = crcIn;
    uint32_t in = byte|0x100;
    do
    {
        crc <<= 1;
        in <<= 1;
        if (in&0x100)
            ++crc;
        if (crc&0x10000)
            crc ^= 0x1021;
    }
    while (!(in&0x10000));
    return crc&0xffffu;
}

uint16_t Alertor::CalCRC16(const uint8_t* data, uint32_t size)
{
    uint32_t crc = 0;
    const uint8_t* dataEnd = data+size;
    while (data<dataEnd)
        crc = UpdateCRC16(crc,*data++);

    crc = UpdateCRC16(crc,0);
    crc = UpdateCRC16(crc,0);
    return crc&0xffffu;
}

} //namespace minieye

