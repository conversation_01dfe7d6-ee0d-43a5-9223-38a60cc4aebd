1.速度显示器RS232通信协议:

串口配置： 9600_8N1

| 协议格式  |             |           |           |                                |
| --------- | ----------- | --------- | --------- | ------------------------------ |
| 2字节帧头 | 1字节命令字 | 1字节长度 | N字节数据 | 1字节校验和                    |
| 0x55 0xAA |             |           | N>=0      | 校验范围是校验和之前的所有数据 |
|           |             |           |           |                                |

2.触发指令字: 0xC0

3.发送速度为32公里/h的指令报文： 55 AA C0 01 20  E0

4.校验和代码


```c
uint8_t cal_sum(uint8_t *ch, uint16_t len)
 {
     uint32_t i;
     uint32_t sum=0; 
     for(i=0; i<len; i++){
         sum += ch[i];
     }   
     return (sum&0xFF);
 }
```

