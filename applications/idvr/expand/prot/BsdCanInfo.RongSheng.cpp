
#include "BsdCanInfo.RongSheng.h"

#include <assert.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <numeric>

#include "devicehub.h"
#include "expand.h"
#include "system_properties.h"

#define ALINK_ALERTOR_VOLUME_PROT "rw.minieye.alink_alertor.vol"

namespace minieye {

enum BsdCanInfoBsdAlarmLevel {
    E_BSD_ALERT_LEVEL_NONE = 0,
    E_BSD_ALERT_LEVEL_NOWARN,
    E_BSD_ALERT_LEVEL_WARN_L1,
    E_BSD_ALERT_LEVEL_WARN_L2
};

enum BsdCanInfoBsdWorkState {
    E_BSD_WORK_STATE_STOPPED = 0,
    E_BSD_WORK_STATE_RUNNING,
    E_BSD_WORK_STATE_FAULT,
    E_BSD_WORK_STATE_SELF_TEST,
    E_BSD_WORK_STATE_UNENABLED,
    E_BSD_WORK_STATE_ERROR_SIGNAL = 6,
    E_BSD_WORK_STATE_SIGNAL_INVALID
};

enum BsdCanInfoFaultCode {
    E_BSD_FAULT_CODE_NONE = 0,
    E_BSD_FAULT_CODE_MINOR_FAULT,
    E_BSD_FAULT_CODE_GENERAL_FAULT,
    E_BSD_FAULT_CODE_FATAL_FAULT
};

struct BsdCanInfo::CanInfo {
    uint32_t frameId = 0x18F00E8D;
    uint8_t len = 8;

    uint8_t alarmLevel : 3 = 0;
    uint8_t workState  : 3 = 0;
    uint8_t sysFault   : 2 = 0;

    uint8_t commFault  : 2 = 0;
    uint8_t r1         : 6 = 0b111111;

    uint8_t r2_7[6] = {0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF};
} __attribute__((packed));

BsdCanInfo::BsdCanInfo() {
    AlgoManager &am = AlgoManager::getInstance();
    char tmp[256];
    snprintf(tmp, sizeof(tmp), "BsdCanInfo%p", this);
    am.addObserver(tmp, this);

    start();
}
BsdCanInfo::~BsdCanInfo() noexcept {
    AlgoManager &am = AlgoManager::getInstance();
    char tmp[256];
    snprintf(tmp, sizeof(tmp), "BsdCanInfo%p", this);
    am.delObserver(tmp);
}
int32_t BsdCanInfo::onServerConnected(void) {
    return 0;
}
int32_t BsdCanInfo::onServerDisconnected(void) {
    return 0;
}

int32_t BsdCanInfo::onDataRecevied(const char *p, uint32_t len) {
    // logd("on cmd data %d", len);
    // logd("recv %s", my::hex(my::constr(p, len)).c_str());

    return 0;
}

bool BsdCanInfo::onAlgoEvent(std::shared_ptr<Event> e) {
    switch (e->type()) {
        case EVT_TYPE_BSD_Left:
        case EVT_TYPE_BSD_Right: {
            mLastBsdAlarmTs = my::timestamp::now();
            mLastBsdLevel = e->c.level;
            break;
        }
        default: {
            break;
        }
    }
    return true;
}

#define BSD_ALERT_CMD_HELP             0
#define BSD_ALERT_CMD_SHOW             1
#define BSD_ALERT_CMD_SET_ALERTOR_MODE 2

static const std::vector<CmdStrT> gBsdAlertCmds = {{"help", BSD_ALERT_CMD_HELP, "show this usage.\n"}};

std::string BsdCanInfo::setupCmdList(const char *cmdName) {
    mCmdUsage = CmdStrT::setupCmdList(gBsdAlertCmds, cmdName);
    return mCmdUsage;
}
bool BsdCanInfo::runCmd(int argc, char **argv, string &ack) {
    uint32_t cmd = CmdStrT::strToCmd(gBsdAlertCmds, argv[0]);

    switch (cmd) {
        case BSD_ALERT_CMD_HELP: {
            ack = mCmdUsage;
            return true;
        }
        default: {
            ack = mCmdUsage;
        }
    }

    return false;
}

#define BSD_WORK_STATUS_PROP  "init.svc.bsd"
#define OBJP_WORK_STATUS_PROP "init.svc.objp"

void BsdCanInfo::run() {
    prctl(PR_SET_NAME, "BsdCanInfo");
    // bool updateMcuMsgWorking = true;

    while (!exiting()) {
        if (mLastBsdAlarmTs.elapsed() >= 3000) {
            mLastBsdLevel = 0;
        }

        if (mLastAlertUpdateTs.elapsed() >= 100) {
            CanInfo canInfo;
            canInfo.alarmLevel = (mLastBsdLevel == 1   ? E_BSD_ALERT_LEVEL_WARN_L1
                                  : mLastBsdLevel == 2 ? E_BSD_ALERT_LEVEL_WARN_L2
                                  : mLastBsdLevel == 3 ? E_BSD_ALERT_LEVEL_NOWARN
                                                       : E_BSD_ALERT_LEVEL_NONE);

            char propValue[PROP_VALUE_MAX] = {0};
            if (__system_property_get(BSD_WORK_STATUS_PROP, propValue) > 0 && strcmp(propValue, "running") == 0) {
                canInfo.workState = E_BSD_WORK_STATE_RUNNING;
                canInfo.sysFault = E_BSD_FAULT_CODE_NONE;
            } else if (__system_property_get(OBJP_WORK_STATUS_PROP, propValue) > 0 &&
                       strcmp(propValue, "running") == 0) {
                canInfo.workState = E_BSD_WORK_STATE_RUNNING;
                canInfo.sysFault = E_BSD_FAULT_CODE_NONE;
            } else {
                canInfo.workState = E_BSD_WORK_STATE_STOPPED;
                canInfo.sysFault = E_BSD_FAULT_CODE_FATAL_FAULT;
            }

            msgEnque((void *)&canInfo, sizeof(canInfo));
            mLastAlertUpdateTs = my::timestamp::now();
            // logi("output can1 work state %d, alarm level %d", canInfo.workState, canInfo.alarmLevel);
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
    // updateMcuMsgWorking = false;
    // updateMcuMsgThread.join();
}

}  // namespace minieye
