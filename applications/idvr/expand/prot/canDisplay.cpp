#include "devicehub.h"
#include "canDisplay.h"

namespace minieye {


CanDisplay::CanDisplay()
{
    mStatusPack.leftLine = E_DISPLAY_HIDE;
    mStatusPack.rightLine = E_DISPLAY_HIDE;
    mStatusPack.pedestrian = E_DISPLAY_HIDE;

    fillVehicleStruct(mStatusPack.vehicle, E_DISPLAY_RED,E_DISPLAY_HIDE);
    fillSegNumStruct(mStatusPack.segNum, 0, false, E_DISPLAY_RED, E_DISPLAY_HIDE);

    mRefreshStamp  = my::timestamp::now();
}

CanDisplay::~CanDisplay()
{
}

void CanDisplay::sentCanData(uint8_t *data)
{
    canFrame_t canData;

    canData.frameId = DISPLAY_CAN_ID;
    canData.len = 8;

    memcpy(canData.data, data, sizeof(canData.data));
    msgEnque((void*)&canData, sizeof(canData));
}

void CanDisplay::drawPedestrian(E_DisplayStatus_t status)
{
    uint8_t data[8] = { 0 };

    data[0] = CMD_SHOW_PED;
    data[1] = status;
    sentCanData(data);
    usleep(10 * 1000); //每个can报文之间需要有10ms的延迟
}

void CanDisplay::drawLine(E_DisplayStatus_t leftLineStatus, E_DisplayStatus_t rightLineStatus)
{
    if (leftLineStatus == E_DISPLAY_FICK || rightLineStatus == E_DISPLAY_FICK) {
        logd("drawLine not support FICK !!!");
        return;
    }

    uint8_t data[8] = { 0 };

    data[0] = CMD_SHOW_LINE;
    data[1] = leftLineStatus;
    data[2] = rightLineStatus;
    sentCanData(data);
    usleep(10 * 1000); //每个can报文之间需要有10ms的延迟
}

void CanDisplay::drawCarSeg(E_Vehicle_t vehicle, E_SegNum_t seg)
{
    uint8_t data[8] = { 0 };

    data[0] = CMD_SHOW_CAR;
    data[1] = vehicle.color;
    data[2] = vehicle.state;
    data[3] = (seg.num / 16) % 16;
    data[4] = seg.num % 16;
    data[5] = seg.isDot;
    data[6] = seg.state;
    data[7] = seg.color;

    sentCanData(data);
    usleep(10 * 1000); //每个can报文之间需要有10ms的延迟
}

void CanDisplay::setLeftLineStatus(E_DisplayStatus_t status)
{
    mStatusPack.leftLine = status;
}

void CanDisplay::setRightLineStatus(E_DisplayStatus_t status)
{
    mStatusPack.rightLine = status;
}

void CanDisplay::setPedestrianStatus(E_DisplayStatus_t status)
{
    mStatusPack.pedestrian = status;
}

void CanDisplay::setVehicleStatus(E_Vehicle_t vehicle)
{
    mStatusPack.vehicle = vehicle;
}

void CanDisplay::setSegNumStatus(E_SegNum_t segNum)
{
    mStatusPack.segNum = segNum;
}


void CanDisplay::fillSegNumStruct(E_SegNum_t& segNum, uint8_t num, bool isDot, E_ColorStatus color, E_DisplayStatus_t state)
{
    segNum.num = num;
    segNum.isDot = isDot;
    segNum.color = color;
    segNum.state = state;
}

void CanDisplay::fillVehicleStruct(E_Vehicle_t& vehicle, E_ColorStatus color, E_DisplayStatus_t state)
{
    vehicle.color = color;
    vehicle.state = state;
}

bool CanDisplay::isEeqSegNumStruct(E_SegNum_t& segNum1, E_SegNum_t& segNum2)
{
    return ((segNum1.num == segNum2.num) &&  (segNum1.isDot == segNum2.isDot) &&
     (segNum1.color == segNum2.color) && (segNum1.state == segNum2.state));
}

bool CanDisplay::isEeqVehicleStruct(E_Vehicle_t& vehicle1, E_Vehicle_t& vehicle2)
{
    return ((vehicle1.color == vehicle2.color) && (vehicle1.state == vehicle2.state));
}

void CanDisplay::refreshDispStatePack()
{
    bool bRefresh = false;

    if (mRefreshStamp.elapsed() >= 1000) {
        mRefreshStamp  = my::timestamp::now();
        bRefresh = true;                        //一秒钟强制刷新，防止拔下显示器后，再插上时无法恢复到上次状态
    }
    if ((mLastStatusPack.pedestrian != mStatusPack.pedestrian) || bRefresh) {   //只有不相等的时候才需要刷新
        drawPedestrian(mStatusPack.pedestrian);
    }
    if ((mLastStatusPack.leftLine != mStatusPack.leftLine) || (mLastStatusPack.rightLine != mStatusPack.rightLine) || bRefresh) {
        drawLine(mStatusPack.leftLine,mStatusPack.rightLine);
    }
    if ((isEeqSegNumStruct(mStatusPack.segNum, mLastStatusPack.segNum) == 0)
    || (isEeqVehicleStruct(mStatusPack.vehicle, mLastStatusPack.vehicle) == 0) || bRefresh) {
        drawCarSeg(mStatusPack.vehicle, mStatusPack.segNum);
    }

    mLastStatusPack = mStatusPack;
}

}