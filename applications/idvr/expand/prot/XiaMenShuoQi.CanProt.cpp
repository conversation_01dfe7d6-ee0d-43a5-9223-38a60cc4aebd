#include <assert.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <sys/time.h>
#include <fstream>
#include <set>
#include <queue>
#include <cstdlib>

#include "expand.h"
#include "devicehub.h"
#include "XiaMenShuoQi.CanProt.h"
#include "system_properties.h"
#include "CRingBuf.h"
#include "RingBufFrame.h"
#include "mystd.h"

namespace minieye {

XiaMenShuoQiCanProt::XiaMenShuoQiCanProt() {
    char tmp[256];
    snprintf(tmp, sizeof(tmp), "XiaMenShuoQiCanProt%p", this);
}

XiaMenShuoQiCanProt::~XiaMenShuoQiCanProt() {
}

int32_t XiaMenShuoQiCanProt::onServerConnected(void) {
    logd("XiaMenShuoQiCanProt server connected");
    return 0;
}

int32_t XiaMenShuoQiCanProt::onServerDisconnected(void) {
    logd("XiaMenShuoQiCanProt server disconnected");
    return 0;
}

std::string XiaMenShuoQiCanProt::setupCmdList(const char *cmdName) {
    return "XiaMenShuoQiCanProt";
}

bool XiaMenShuoQiCanProt::runCmd(int argc, char **argv, std::string &ack) {
    ack = "XiaMenShuoQiCanProt command executed";
    return true;
}

int32_t XiaMenShuoQiCanProt::onDataRecevied(const char *p, uint32_t len) {
    logd("received data %d bytes", len);

    if (len >= sizeof(canFrame_t)) {
        canFrame_t *canFrame = (canFrame_t *)p;
        onCanMessageReceived(canFrame);
    }

    return 0;
}

// CAN消息接收处理
bool XiaMenShuoQiCanProt::onCanMessageReceived(canFrame_t *canFrame) {
    if (!canFrame) {
        return false;
    }

    switch (canFrame->frameId) {
        case 0x0CF02FA0:  // AEBS1
            parseAEBS1Message(canFrame);
            break;
        case 0x0C0BA027:  // AEBS2_27
            parseAEBS2_27Message(canFrame);
            break;
        case 0x10F007E8:  // FLI1
            parseFLI1Message(canFrame);
            break;
        case 0x18FE5BE8:  // FLI2
            parseFLI2Message(canFrame);
            break;
        case 0x0CF0EEA0:  // WEDR
            parseWEDRMessage(canFrame);
            break;
        case 0x18F0010B:  // EBC1
            parseEBC1Message(canFrame);
            break;
        case 0x18FEBF0B:  // EBC2
            parseEBC2Message(canFrame);
            break;
        case 0x18F0090B:  // VDC2
            parseVDC2Message(canFrame);
            break;
        case 0xCF00300:  // EEC2
            parseEEC2Message(canFrame);
            break;
        case 0x18F00503:  // ETC2
            parseETC2Message(canFrame);
            break;
        case 0x0CFDCC27:  // OEL_27
            parseOEL_27Message(canFrame);
            break;
        case 0x0CF901B2:  // UDAS
            parseUDASMessage(canFrame);
            break;
        default:
            logd("Unknown CAN ID: 0x%X", canFrame->frameId);
            break;
    }

    return true;
}

bool XiaMenShuoQiCanProt::parseAEBS1Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mAEBS1Pkt, canFrame, sizeof(canFrame_t));

    logd("AEBS1: state=%d, warning_level=%d, object_detected=%d, ttc=%d",
         mAEBS1Pkt.advanced_emergency_braking_system_state,
         mAEBS1Pkt.collision_warning_level,
         mAEBS1Pkt.relevant_object_detected,
         mAEBS1Pkt.time_to_collision);

    switch ((CollisionWarningLevel)mAEBS1Pkt.collision_warning_level) {
        case CollisionWarningLevel::WARNING_LEVEL_1:
        case CollisionWarningLevel::WARNING_LEVEL_3: {
            mAlarmEventData.target_obstacle_type = (uint8_t)TargetObstacleType::VEHICLE;
            break;
        }
        case CollisionWarningLevel::WARNING_LEVEL_2:
        case CollisionWarningLevel::WARNING_LEVEL_4: {
            mAlarmEventData.target_obstacle_type = (uint8_t)TargetObstacleType::PEDESTRIAN;
            break;
        }
        default: {
            mAlarmEventData.target_obstacle_type = (uint8_t)TargetObstacleType::INVALID;
            break;
        }
    }

    if (mAEBS1Pkt.time_to_collision <= 250) {
        mAlarmEventData.ttc_collision_time = mAEBS1Pkt.time_to_collision / 2;
    }

    switch ((AEBSSystemState)mAEBS1Pkt.advanced_emergency_braking_system_state) {
        case AEBSSystemState::EMERGENCY_BRAKING_ACTIVE: {
            mAlarmEventData.alarm_event_type = (uint8_t)AlarmEventType::AEB_BRAKING;
            mAlarmEventData.alarm_level = (uint8_t)AlarmLevel::LEVEL_1;
            break;
        }
        case AEBSSystemState::COLLISION_WARNING_ACTIVE: {
            mAlarmEventData.alarm_event_type = (uint8_t)AlarmEventType::AEB_WARNING;
            mAlarmEventData.alarm_level = (uint8_t)AlarmLevel::LEVEL_1;
            break;
        }
        case AEBSSystemState::COLLISION_WARNING_WITH_BRAKING: {
            mAlarmEventData.alarm_event_type = (uint8_t)AlarmEventType::AEB_WARNING;
            mAlarmEventData.alarm_level = (uint8_t)AlarmLevel::LEVEL_2;
            break;
        }
        default: {
            return true;
        }
    }

#warning trigger

    return true;
}

bool XiaMenShuoQiCanProt::parseAEBS2_27Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mAEBS2_27Pkt, canFrame, sizeof(canFrame_t));

    logd("AEBS2_27: driver_activation_demand=%d", mAEBS2_27Pkt.driver_activation_demand);

    if (mAEBS2_27Pkt.driver_activation_demand == (uint8_t)AEBSDriverActivationDemand::DRIVER_DEACTIVATION) {
        mAlarmEventData.aeb_brake_switch_status = 0;
    } else if (mAEBS2_27Pkt.driver_activation_demand == (uint8_t)AEBSDriverActivationDemand::DRIVER_ACTIVATION) {
        mAlarmEventData.aeb_brake_switch_status = 1;
    } else {
        mAlarmEventData.aeb_brake_switch_status = 0xFF;
    }

    return true;
}

bool XiaMenShuoQiCanProt::parseFLI1Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mFLI1Pkt, canFrame, sizeof(canFrame_t));

    logd("FLI1: right_ldw=%d, left_ldw=%d",
         mFLI1Pkt.lane_departure_imminent_right_side,
         mFLI1Pkt.lane_departure_imminent_left_side);

    if (mFLI1Pkt.lane_departure_imminent_left_side == (uint8_t)LaneDepartureStatus::IMMINENT) {
        mAlarmEventData.alarm_event_type = (uint8_t)AlarmEventType::LANE_DEPARTURE_WARNING;
        mAlarmEventData.lane_departure_warning = (uint8_t)LaneDepartureWarning::LEFT_BOUNDARY_WARNING;
    } else if (mFLI1Pkt.lane_departure_imminent_right_side == (uint8_t)LaneDepartureStatus::IMMINENT) {
        mAlarmEventData.alarm_event_type = (uint8_t)AlarmEventType::LANE_DEPARTURE_WARNING;
        mAlarmEventData.lane_departure_warning = (uint8_t)LaneDepartureWarning::RIGHT_BOUNDARY_WARNING;
    } else {
        mAlarmEventData.lane_departure_warning = (uint8_t)LaneDepartureWarning::NO_WARNING;
    }

#warning trigger

    return true;
}

bool XiaMenShuoQiCanProt::parseFLI2Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mFLI2Pkt, canFrame, sizeof(canFrame_t));

    logd("FLI2: right_tracking=%d, left_tracking=%d, ldws_enable=%d",
         mFLI2Pkt.lane_tracking_status_right_side,
         mFLI2Pkt.lane_tracking_status_left_side,
         mFLI2Pkt.lane_departure_indication_enable_status);

    return true;
}

bool XiaMenShuoQiCanProt::parseWEDRMessage(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mWEDRPkt, canFrame, sizeof(canFrame_t));

    logd("WEDR: aeb_state=%d, distance=%d, velocity=%d, ldw_lamp=%d, fcw_lamp=%d",
         mWEDRPkt.advanced_emergency_braking_system_state,
         mWEDRPkt.distance_to_main_target_ahead,
         mWEDRPkt.relative_velocity_to_main_target_ahead,
         mWEDRPkt.amber_warning_lamp_status_ldw,
         mWEDRPkt.amber_warning_lamp_status_fcw);

    mAlarmEventData.longitudinal_relative_distance = mWEDRPkt.distance_to_main_target_ahead;
    mAlarmEventData.longitudinal_relative_velocity = (int32_t)mWEDRPkt.relative_velocity_to_main_target_ahead - 125;

    return true;
}

bool XiaMenShuoQiCanProt::parseEBC1Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mEBC1Pkt, canFrame, sizeof(canFrame_t));

    logd("EBC1: brake_switch=%d, brake_pedal=%d, abs_operational=%d",
         mEBC1Pkt.ebs_brake_switch,
         mEBC1Pkt.ebs_brake_pedal_position,
         mEBC1Pkt.abs_fully_operational);

    mAlarmEventData.brake_pedal_opening = std::min(mEBC1Pkt.ebs_brake_pedal_position * 0.4, 100.0);

    return true;
}

bool XiaMenShuoQiCanProt::parseEBC2Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mEBC2Pkt, canFrame, sizeof(canFrame_t));

    if (mEBC2Pkt.mean_front_axle_speed < 0xFB00) {
        mAlarmEventData.vehicle_speed = std::floor(mEBC2Pkt.mean_front_axle_speed / 256.0);
    } else {
        mAlarmEventData.vehicle_speed = 0xFF;
        return true;
    }

    logd("EBC2: front_axle_speed=%d", mEBC2Pkt.mean_front_axle_speed);

    return true;
}

bool XiaMenShuoQiCanProt::parseVDC2Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mVDC2Pkt, canFrame, sizeof(canFrame_t));

    logd("VDC2: steer_angle=%d, yaw_rate=%d, lat_accel=%d, lon_accel=%d",
         mVDC2Pkt.steer_wheel_angle,
         mVDC2Pkt.yaw_rate,
         mVDC2Pkt.lateral_acceleration,
         mVDC2Pkt.longitudinal_acceleration);

    mAlarmEventData.steering_wheel_angle = std::floor(-31.374 + mVDC2Pkt.steer_wheel_angle * 0.0009766);

    return true;
}

bool XiaMenShuoQiCanProt::parseEEC2Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mEEC2Pkt, canFrame, sizeof(canFrame_t));

    logd("EEC2: accel_pedal_position=%d", mEEC2Pkt.accel_pedal_position1);

    mAlarmEventData.accelerator_pedal_opening = std::min(mEEC2Pkt.accel_pedal_position1 * 0.4, 100.0);

    return true;
}

bool XiaMenShuoQiCanProt::parseETC2Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mETC2Pkt, canFrame, sizeof(canFrame_t));

    logd("ETC2: current_gear=%d", mETC2Pkt.current_gear);

    if (mETC2Pkt.current_gear == 0x7d) {
        mAlarmEventData.gear_status = (uint8_t)GearStatus::NEUTRAL;
    } else if (mETC2Pkt.current_gear < 0x7d) {
        mAlarmEventData.gear_status = (uint8_t)GearStatus::REVERSE;
    } else if (mETC2Pkt.current_gear > 0x7d) {
        mAlarmEventData.gear_status = (uint8_t)GearStatus::DRIVE;
    }

    return true;
}

bool XiaMenShuoQiCanProt::parseOEL_27Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mOEL_27Pkt, canFrame, sizeof(canFrame_t));

    logd(
        "OEL_27: turn_signal=%d, fcw_lamp=%d", mOEL_27Pkt.turn_signal_switch, mOEL_27Pkt.amber_warning_lamp_status_fcw);

    if (mOEL_27Pkt.amber_warning_lamp_status_fcw == (uint8_t)AmberWarningLampStatus::LAMP_ON) {
        mAlarmEventData.device_status_fault_code = 0b11;  // 雷达和前视摄像头异常
    }

    return true;
}

bool XiaMenShuoQiCanProt::parseUDASMessage(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mUDASPkt, canFrame, sizeof(canFrame_t));

    logd("UDAS: RA=%d, RB=%d, RC=%d, RD=%d, FA=%d, FB=%d, FC=%d, FD=%d",
         mUDASPkt.ra,
         mUDASPkt.rb,
         mUDASPkt.rc,
         mUDASPkt.rd,
         mUDASPkt.fa,
         mUDASPkt.fb,
         mUDASPkt.fc,
         mUDASPkt.fd);

    return true;
}

void XiaMenShuoQiCanProt::run() {
    while (!exiting()) {
        LBS lbs;
        DeviceHub::getInstance().getGpsLbs(lbs);

        {
            std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);
            updateGpsData(lbs);
            addRealTimeData(lbs);
        }
        std::this_thread::sleep_for(76.9230ms);
    }
}

void XiaMenShuoQiCanProt::updateGpsData(LBS lbs){
    // 以度为单位的纬度值乘以 10 的 6 次方，精确到百万分之一度
    mAlarmEventData.latitude = lbs.lat_x1kw / 10;
    mAlarmEventData.longitude = lbs.lng_x1kw / 10;
    mAlarmEventData.altitude = lbs.alt_x10 / 10;
}

void XiaMenShuoQiCanProt::addRealTimeData(LBS lbs) {
    uint64_t timestamp_ms = my::getTimestampMs();

    // AEB功能状态(1打开,0关闭)
    uint8_t aeb_status = (mAlarmEventData.alarm_event_type == (uint8_t)AlarmEventType::AEB_WARNING) ? 1 : 0;

    // 实时报警状态(0 正常行驶, 1 FCW一级预警, 2 FCW二级预警, 3 AEB制动)
    uint8_t alarm_status = 0;
    if (mAlarmEventData.forward_collision_warning_level == (uint8_t)ForwardCollisionWarningLevel::LEVEL_1_WARNING) {
        alarm_status = 1;
    } else if (mAlarmEventData.forward_collision_warning_level == (uint8_t)ForwardCollisionWarningLevel::LEVEL_2_WARNING) {
        alarm_status = 2;
    } else if (mAlarmEventData.aeb_brake_status != (uint8_t)AEBBrakingStatus::NO_BRAKING &&
               mAlarmEventData.aeb_brake_status != (uint8_t)AEBBrakingStatus::INVALID) {
        alarm_status = 3;
    }

    // GPS定位状态(0未定位,1定位)
    uint8_t gps_status = lbs.status;

    // 南北纬标志(0 北纬, 1 南纬)
    uint8_t ns_flag = (lbs.lat_x1kw < 0) ? 1 : 0;

    // 东西经标志(0东经, 1 西经)
    uint8_t ew_flag = (lbs.lng_x1kw < 0) ? 1 : 0;

    // 纬度(度)
    double latitude = lbs.lat_x1kw / 10000000.0;

    // 经度(度)
    double longitude = lbs.lng_x1kw / 10000000.0;

    // 高度(米)
    double altitude = lbs.alt_x10 / 10.0;

    // 速度(km/h)
    double speed = mAlarmEventData.vehicle_speed;

    // 方向(0-359度)
    double direction = lbs.dir_x100 / 100.0;

    // 纵向车速(Km/h 前进为正)
    double longitudinal_speed = mAlarmEventData.vehicle_speed;

    // 横向车速(km/h, 向左为正)
    double lateral_speed = 0.0;

    // 纵向加速度(m/s2)
    double longitudinal_accel = 0.0;
    if (mVDC2Pkt.longitudinal_acceleration != 0xFF) {
        longitudinal_accel = mVDC2Pkt.longitudinal_acceleration * 0.1 - 12.5;
    }

    // 横向加速度(m/s2)
    double lateral_accel = 0.0;
    if (mVDC2Pkt.lateral_acceleration != 0xFFFF) {
        lateral_accel = mVDC2Pkt.lateral_acceleration * 0.0004883 - 15.687;
    }

    // 横摆角速度(rad/s)
    double yaw_rate = 0.0;
    if (mVDC2Pkt.yaw_rate != 0xFFFF) {
        yaw_rate = mVDC2Pkt.yaw_rate * 0.0001221 - 3.92;
    }

    // 档位(N 0, R 1, D 2)
    uint8_t gear = 0;
    if (mAlarmEventData.gear_status == (uint8_t)GearStatus::NEUTRAL) {
        gear = 0;
    } else if (mAlarmEventData.gear_status == (uint8_t)GearStatus::REVERSE) {
        gear = 1;
    } else if (mAlarmEventData.gear_status == (uint8_t)GearStatus::DRIVE) {
        gear = 2;
    }

    // 油门踏板开度(0~100%)
    double throttle_opening =
        (mAlarmEventData.accelerator_pedal_opening == 0xFF) ? 0.0 : mAlarmEventData.accelerator_pedal_opening;

    // 刹车踏板开度(0~100%)
    double brake_opening = (mAlarmEventData.brake_pedal_opening == 0xFF) ? 0.0 : mAlarmEventData.brake_pedal_opening;

    // 转向灯信号(0未开启, 1左转, 2右转)
    uint8_t turn_signal = 0;
    if (mOEL_27Pkt.turn_signal_switch == (uint8_t)TurnSignalSwitchStatus::LEFT_TURN_FLASHING) {
        turn_signal = 1;
    } else if (mOEL_27Pkt.turn_signal_switch == (uint8_t)TurnSignalSwitchStatus::RIGHT_TURN_FLASHING) {
        turn_signal = 2;
    }

    // 目标纵向相对距离(m)
    double target_longitudinal_distance = mAlarmEventData.longitudinal_relative_distance * 0.1;

    // 目标横向相对距离(m)
    double target_lateral_distance = mAlarmEventData.lateral_relative_distance * 0.1;

    // TTC碰撞时间(s)
    double ttc = (mAlarmEventData.ttc_collision_time == 0) ? 999.000000 : mAlarmEventData.ttc_collision_time * 0.1;

    // 格式化数据为CSV字符串
    char csv_line[512];
    snprintf(csv_line,
             sizeof(csv_line),
             "%llu, %d, %d, %d, %d, %d, %.6f, %.6f, %.6f, %.6f, %.6f, %.6f, %.6f, %.6f, %.6f, %.6f, %d, %.0f, %.0f, "
             "%d, %.6f, %.6f, %.6f",
             timestamp_ms,                  // 时间戳(ms)
             aeb_status,                    // AEB功能状态
             alarm_status,                  // 实时报警状态
             gps_status,                    // GPS定位状态
             ns_flag,                       // 南北纬标志
             ew_flag,                       // 东西经标志
             latitude,                      // 纬度(度)
             longitude,                     // 经度(度)
             altitude,                      // 高度(米)
             speed,                         // 速度(km/h)
             direction,                     // 方向(0-359度)
             longitudinal_speed,            // 纵向车速(Km/h)
             lateral_speed,                 // 横向车速(km/h)
             longitudinal_accel,            // 纵向加速度
             lateral_accel,                 // 横向加速度
             yaw_rate,                      // 横摆角速度(rad/s)
             gear,                          // 档位
             throttle_opening,              // 油门踏板开度(0~100%)
             brake_opening,                 // 刹车踏板开度(0~100%)
             turn_signal,                   // 转向灯信号
             target_longitudinal_distance,  // 目标纵向相对距离(m)
             target_lateral_distance,       // 目标横向相对距离(m)
             ttc                            // TTC碰撞时间(s)
    );

    list.push_back(std::string(csv_line));

    const size_t MAX_FRAMES = 130;
    while (list.size() > MAX_FRAMES) {
        list.pop_front();
    }
}

void XiaMenShuoQiCanProt::report(){
    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);
    mAlarmEventData.alarm_id = mAlarmid++;
    my::time2bcd(my::timestamp::now().seconds_from_19700101(), (char *)mAlarmEventData.date_time);

    // 使用 msgpack 序列化 AlarmEventData_S 结构体
    try {
        msgpack::sbuffer sbuf;
        msgpack::pack(sbuf, mAlarmEventData);

        logd("AlarmEventData serialized to msgpack, size: %zu bytes", sbuf.size());

        // 可以将序列化后的数据发送到其他模块
        // 例如：发送到 idvr.ccu 模块
        // sendToIdvrCcu(sbuf.data(), sbuf.size());

        // 示例：反序列化验证
        msgpack::object_handle unpack = msgpack::unpack(sbuf.data(), sbuf.size());
        msgpack::object obj = unpack.get();
        AlarmEventData_S deserialized = obj.as<AlarmEventData_S>();

        logd("Deserialized alarm_id: %u, alarm_event_type: %u",
             deserialized.alarm_id, deserialized.alarm_event_type);

    } catch (const std::exception& e) {
        loge("msgpack serialization failed: %s", e.what());
    }
}

}  // namespace minieye