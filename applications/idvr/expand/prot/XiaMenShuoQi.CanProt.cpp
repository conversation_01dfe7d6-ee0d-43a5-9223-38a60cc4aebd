#include <assert.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <sys/time.h>
#include <fstream>
#include <set>
#include <queue>

#include "expand.h"
#include "devicehub.h"
#include "XiaMenShuoQi.CanProt.h"
#include "system_properties.h"
#include "CRingBuf.h"
#include "RingBufFrame.h"

namespace minieye {

XiaMenShuoQiCanProt::XiaMenShuoQiCanProt() {
    char tmp[256];
    snprintf(tmp, sizeof(tmp), "XiaMenShuoQiCanProt%p", this);
}

XiaMenShuoQiCanProt::~XiaMenShuoQiCanProt() {
}

int32_t XiaMenShuoQiCanProt::onDataRecevied(const char *p, uint32_t len) {
    logd("received data %d bytes", len);

    if (len >= sizeof(canFrame_t)) {
        canFrame_t *canFrame = (canFrame_t *)p;
        onCanMessageReceived(canFrame);
    }

    return 0;
}

// CAN消息接收处理
bool XiaMenShuoQiCanProt::onCanMessageReceived(canFrame_t *canFrame) {
    if (!canFrame) {
        return false;
    }

    switch (canFrame->frameId) {
        case 0x0CF02FA0:  // AEBS1
            parseAEBS1Message(canFrame);
            break;
        case 0x0C0BA027:  // AEBS2_27
            parseAEBS2_27Message(canFrame);
            break;
        case 0x10F007E8:  // FLI1
            parseFLI1Message(canFrame);
            break;
        case 0x18FE5BE8:  // FLI2
            parseFLI2Message(canFrame);
            break;
        case 0x0CF0EEA0:  // WEDR
            parseWEDRMessage(canFrame);
            break;
        case 0x18F0010B:  // EBC1
            parseEBC1Message(canFrame);
            break;
        case 0x18FEBF0B:  // EBC2
            parseEBC2Message(canFrame);
            break;
        case 0x18F0090B:  // VDC2
            parseVDC2Message(canFrame);
            break;
        case 0xCF00300:  // EEC2
            parseEEC2Message(canFrame);
            break;
        case 0x18F00503:  // ETC2
            parseETC2Message(canFrame);
            break;
        case 0x0CFDCC27:  // OEL_27
            parseOEL_27Message(canFrame);
            break;
        case 0x0CF901B2:  // UDAS
            parseUDASMessage(canFrame);
            break;
        default:
            logd("Unknown CAN ID: 0x%X", canFrame->frameId);
            break;
    }

    return true;
}

bool XiaMenShuoQiCanProt::parseAEBS1Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mAEBS1Pkt, canFrame, sizeof(canFrame_t));

    logd("AEBS1: state=%d, warning_level=%d, object_detected=%d, ttc=%d",
         mAEBS1Pkt.advanced_emergency_braking_system_state,
         mAEBS1Pkt.collision_warning_level,
         mAEBS1Pkt.relevant_object_detected,
         mAEBS1Pkt.time_to_collision);

    switch ((CollisionWarningLevel)mAEBS1Pkt.collision_warning_level) {
        case CollisionWarningLevel::WARNING_LEVEL_1:
        case CollisionWarningLevel::WARNING_LEVEL_3: {
            mAlarmEventData.target_obstacle_type = (uint8_t)TargetObstacleType::VEHICLE;
            break;
        }
        case CollisionWarningLevel::WARNING_LEVEL_2:
        case CollisionWarningLevel::WARNING_LEVEL_4: {
            mAlarmEventData.target_obstacle_type = (uint8_t)TargetObstacleType::PEDESTRIAN;
            break;
        }
        default: {
            mAlarmEventData.target_obstacle_type = (uint8_t)TargetObstacleType::INVALID;
            break;
        }
    }

    if (mAEBS1Pkt.time_to_collision <= 250) {
        mAlarmEventData.ttc_collision_time = mAEBS1Pkt.time_to_collision / 2;
    }

    switch ((AEBSSystemState)mAEBS1Pkt.advanced_emergency_braking_system_state) {
        case AEBSSystemState::EMERGENCY_BRAKING_ACTIVE: {
            mAlarmEventData.alarm_event_type = (uint8_t)AlarmEventType::AEB_BRAKING;
            mAlarmEventData.alarm_level = (uint8_t)AlarmLevel::LEVEL_1;
            break;
        }
        case AEBSSystemState::COLLISION_WARNING_ACTIVE: {
            mAlarmEventData.alarm_event_type = (uint8_t)AlarmEventType::AEB_WARNING;
            mAlarmEventData.alarm_level = (uint8_t)AlarmLevel::LEVEL_1;
            break;
        }
        case AEBSSystemState::COLLISION_WARNING_WITH_BRAKING: {
            mAlarmEventData.alarm_event_type = (uint8_t)AlarmEventType::AEB_WARNING;
            mAlarmEventData.alarm_level = (uint8_t)AlarmLevel::LEVEL_2;
            break;
        }
        default: {
            return true;
        }
    }

#warning trigger

    return true;
}

bool XiaMenShuoQiCanProt::parseAEBS2_27Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mAEBS2_27Pkt, canFrame, sizeof(canFrame_t));

    logd("AEBS2_27: driver_activation_demand=%d", mAEBS2_27Pkt.driver_activation_demand);

    if (mAEBS2_27Pkt.driver_activation_demand == (uint8_t)AEBSDriverActivationDemand::DRIVER_DEACTIVATION) {
        mAlarmEventData.aeb_brake_switch_status = 0;
    } else if (mAEBS2_27Pkt.driver_activation_demand == (uint8_t)AEBSDriverActivationDemand::DRIVER_ACTIVATION) {
        mAlarmEventData.aeb_brake_switch_status = 1;
    } else {
        mAlarmEventData.aeb_brake_switch_status = 0xFF;
    }

    return true;
}

bool XiaMenShuoQiCanProt::parseFLI1Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mFLI1Pkt, canFrame, sizeof(canFrame_t));

    logd("FLI1: right_ldw=%d, left_ldw=%d",
         mFLI1Pkt.lane_departure_imminent_right_side,
         mFLI1Pkt.lane_departure_imminent_left_side);

    if (mFLI1Pkt.lane_departure_imminent_left_side == (uint8_t)LaneDepartureStatus::IMMINENT) {
        mAlarmEventData.alarm_event_type = (uint8_t)AlarmEventType::LANE_DEPARTURE_WARNING;
        mAlarmEventData.lane_departure_warning = (uint8_t)LaneDepartureWarning::LEFT_BOUNDARY_WARNING;
    } else if (mFLI1Pkt.lane_departure_imminent_right_side == (uint8_t)LaneDepartureStatus::IMMINENT) {
        mAlarmEventData.alarm_event_type = (uint8_t)AlarmEventType::LANE_DEPARTURE_WARNING;
        mAlarmEventData.lane_departure_warning = (uint8_t)LaneDepartureWarning::RIGHT_BOUNDARY_WARNING;
    } else {
        mAlarmEventData.lane_departure_warning = (uint8_t)LaneDepartureWarning::NO_WARNING;
    }

#warning trigger

    return true;
}

bool XiaMenShuoQiCanProt::parseFLI2Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mFLI2Pkt, canFrame, sizeof(canFrame_t));

    logd("FLI2: right_tracking=%d, left_tracking=%d, ldws_enable=%d",
         mFLI2Pkt.lane_tracking_status_right_side,
         mFLI2Pkt.lane_tracking_status_left_side,
         mFLI2Pkt.lane_departure_indication_enable_status);

    return true;
}

bool XiaMenShuoQiCanProt::parseWEDRMessage(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mWEDRPkt, canFrame, sizeof(canFrame_t));

    logd("WEDR: aeb_state=%d, distance=%d, velocity=%d, ldw_lamp=%d, fcw_lamp=%d",
         mWEDRPkt.advanced_emergency_braking_system_state,
         mWEDRPkt.distance_to_main_target_ahead,
         mWEDRPkt.relative_velocity_to_main_target_ahead,
         mWEDRPkt.amber_warning_lamp_status_ldw,
         mWEDRPkt.amber_warning_lamp_status_fcw);

    mAlarmEventData.longitudinal_relative_distance = mWEDRPkt.distance_to_main_target_ahead;
    mAlarmEventData.longitudinal_relative_velocity = -125 + mWEDRPkt.relative_velocity_to_main_target_ahead;

    return true;
}

bool XiaMenShuoQiCanProt::parseEBC1Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mEBC1Pkt, canFrame, sizeof(canFrame_t));

    logd("EBC1: brake_switch=%d, brake_pedal=%d, abs_operational=%d",
         mEBC1Pkt.ebs_brake_switch,
         mEBC1Pkt.ebs_brake_pedal_position,
         mEBC1Pkt.abs_fully_operational);

    mAlarmEventData.brake_pedal_opening = std::min(mEBC1Pkt.ebs_brake_pedal_position * 0.4, 100.0);

    return true;
}

bool XiaMenShuoQiCanProt::parseEBC2Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mEBC2Pkt, canFrame, sizeof(canFrame_t));

    if (mEBC2Pkt.mean_front_axle_speed < 0xFB00) {
        mAlarmEventData.vehicle_speed = std::floor(mEBC2Pkt.mean_front_axle_speed / 256.0);
    } else {
        mAlarmEventData.vehicle_speed = 0xFF;
        return true;
    }

    logd("EBC2: front_axle_speed=%d", mEBC2Pkt.mean_front_axle_speed);

    return true;
}

bool XiaMenShuoQiCanProt::parseVDC2Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mVDC2Pkt, canFrame, sizeof(canFrame_t));

    logd("VDC2: steer_angle=%d, yaw_rate=%d, lat_accel=%d, lon_accel=%d",
         mVDC2Pkt.steer_wheel_angle,
         mVDC2Pkt.yaw_rate,
         mVDC2Pkt.lateral_acceleration,
         mVDC2Pkt.longitudinal_acceleration);

    mAlarmEventData.steering_wheel_angle = -31.374 + mVDC2Pkt.steer_wheel_angle * 0.0009766;

    return true;
}

bool XiaMenShuoQiCanProt::parseEEC2Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mEEC2Pkt, canFrame, sizeof(canFrame_t));

    logd("EEC2: accel_pedal_position=%d", mEEC2Pkt.accel_pedal_position1);

    mAlarmEventData.accelerator_pedal_opening = std::min(mEEC2Pkt.accel_pedal_position1 * 0.4, 100.0);

    return true;
}

bool XiaMenShuoQiCanProt::parseETC2Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mETC2Pkt, canFrame, sizeof(canFrame_t));

    logd("ETC2: current_gear=%d", mETC2Pkt.current_gear);

    mETC2Pkt.current_gear;

    return true;
}

bool XiaMenShuoQiCanProt::parseOEL_27Message(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mOEL_27Pkt, canFrame, sizeof(canFrame_t));

    logd("OEL_27: turn_signal=%d, fcw_lamp=%d", mOEL_27Pkt.turn_signal_switch, mOEL_27Pkt.amber_warning_lamp_status_fcw);

    if(mOEL_27Pkt.amber_warning_lamp_status_fcw == (uint8_t)AmberWarningLampStatus::LAMP_ON){
        mAlarmEventData.device_status_fault_code = 0b11;  // 雷达和前视摄像头异常
    }

    return true;
}

bool XiaMenShuoQiCanProt::parseUDASMessage(canFrame_t *canFrame) {
    if (!canFrame || canFrame->len < 8) {
        return false;
    }

    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);

    memcpy((uint8_t *)&mUDASPkt, canFrame, sizeof(canFrame_t));

    logd("UDAS: RA=%d, RB=%d, RC=%d, RD=%d, FA=%d, FB=%d, FC=%d, FD=%d",
         mUDASPkt.ra,
         mUDASPkt.rb,
         mUDASPkt.rc,
         mUDASPkt.rd,
         mUDASPkt.fa,
         mUDASPkt.fb,
         mUDASPkt.fc,
         mUDASPkt.fd);

    return true;
}

void XiaMenShuoQiCanProt::run() {
    
}

void XiaMenShuoQiCanProt::addRealTimeData() {
    std::lock_guard<std::mutex> lock(mEvtRcdMapMtx);
    q.push({});
}

}  // namespace minieye