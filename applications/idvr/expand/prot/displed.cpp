#include "displed.h"
#include <string>
#include <stdio.h>
#include "devicehub.h"
#include "properties.h"
#include "idvrProperty.h"
namespace minieye {
int32_t dispLed::onServerConnected(void)
{
    sendCmd(DISP_OPEN_SCREEN, "1");
    start();
    return 0;
}
int32_t dispLed::onServerDisconnected(void)
{
    sendCmd(DISP_OPEN_SCREEN, "0");
    stop();
    return 0;
}

int32_t dispLed::onDataRecevied(const char *p, uint32_t len)
{
	logd("len %d, recv  %s", len, my::hex(my::constr(p, len)).c_str());

    onCmdData(mRcvArray, p, len);
    return 0;
}

int32_t dispLed::onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len)
{
    recvArray.insert(recvArray.end(), p, p + len);

    int32_t parsed = 0;
    do {
        parsed = onDataParse((const char *)recvArray.data(), recvArray.size());
        //assert(parsed <= recvArray.size());
        if (recvArray.size() >= (1<<20)) {
            parsed = recvArray.size();
            logd("frame too long!!\n");
        }
        
        if (parsed < 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + recvArray.size());
            parsed = 0;
            logd("frame no header, erase all!!\n");
        } else if (parsed > 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + parsed);
            logd("erase size %d\n", parsed);
        } else {
            logd("alertor frame not complete!!\n");
        }

        if(recvArray.size() <=0 || parsed <= 0) {
            logd("break");
            break;
        }

    } while(1);
    return parsed;

}

int32_t dispLed::onDataParse(const char *p, uint32_t size)
{
    int32_t start = -1;
    int32_t end = -1;
    uint32_t offset = 0;
    for (int i = 0; i < static_cast<int32_t>(size); ++i) {
        if (i + 1 >= size) {
            break;
        }
        if (p[i] == DISP_BEGIN && p[i + 1] == DISP_BEGIN) {
            start = i + 2;
        }
        if (p[i] == DISP_END && p[i + 1] == DISP_END) {
            end = i - 1;
        }
    }
    if (start > 0 && end > 0)
    {
        uint8_t* tmp = (uint8_t*)(p + start);
        uint32_t len = end - start + 1;
        
        if (onDataValidate((const uint8_t *)tmp, len)) {
            logd("enter onDataValidate");
            onMessage(tmp, len);
        } else {
            logd("Invalid data");
            return -1;
        }
    } else {
        //logd("******start =  %d, end = %d\n", start, end);
        return -1;
    }
    
    return end + 1;
}

bool dispLed::onMessage(const uint8_t *p, uint32_t len)
{
    if ('1' == p[0] && '1' == p[1]) {
        logd("open screen success");
		sendCmd(DISP_CLEAR_SCREEN, "0");
    } else if ('1' == p[0] && '2' == p[1]) {
        logd("clear screen success");
		sendCmd(DISP_SET_BRIGHT, "8");
    } else if ('2' == p[0] && '7' == p[1]) {
    	logd("set brightness success");
		mFlag = true;
    } else if ('1' == p[0] && '0' == p[1]) {
		logd("versoin");
    }
    return true;
}

 bool dispLed::onDataValidate(const uint8_t *p, uint32_t len)
{
    return true;
}

int32_t dispLed::send(const uint8_t* data, uint32_t len)
{
    vector<uint8_t> send;
    send.insert(send.end(),2, DISP_BEGIN);
    
    for(uint8_t i = 0; i < len; i++) {
        send.push_back(data[i]);
    }
    
    send.insert(send.end(), 2, DISP_END);
    
    msgEnque(send.data(), send.size());

    return 0;
}

bool dispLed::sendCmd(std::string cmd, std::string param)
{
    std::string buf;
    buf += cmd;
    buf += "000001";
    buf += param;
    send((const uint8_t *)buf.c_str(), buf.length());
    return true;
}

bool dispLed::sendData(std::string color, std::string data)
{
    std::string buf;
    buf += "00";    // 指令
    buf += "000002";//流水号
    buf += color;   //颜色00红色 01绿色 02黄色
    buf += "00";    //字体 00宋体
    buf += "100";   //信箱号 0-200
    buf += "1";     //即时广告
    buf += "00";    //00静止 01闪烁
    buf += "0";     //播放规则 0:秒 1:次
    buf += "001";   //播放参数 01-999 秒/次
    buf += "90";    //01-99屏幕移动速度
    buf += "01";    //01-99每页停顿时间
    buf += "000000";//开始显示时间
    buf += "000000";//结束显示时间
    buf += data;   //显示内容
    buf += "AA";
    send((const uint8_t *)buf.c_str(), buf.length());
    return true;
}

bool dispLed::runCmd(int argc, char **argv, string &ack)
{
    sendCmd(DISP_OPEN_SCREEN, "1");
    sleep(1);
    //清除屏幕
    sendCmd(DISP_CLEAR_SCREEN, "0");
    sleep(1);
    //发送文本
    sendData(DISP_COLOR_RED, "123km/h");
    return true;
}

bool dispLed::utf8_2_GB2312(const char* in, int32_t intLen, char* out, int32_t outLen)
{
// TODO

    my::utf8ToGbk((char*)in, out);
    return true;
}

void dispLed::dispSpeed(int32_t speed)
{   
    char buf[25] = {0};
    char send[25] = {0};
    
    sprintf(buf, "%03dkm/h", speed);
    bool ret = utf8_2_GB2312(buf, strlen(buf), send, sizeof(send));
    if (!ret) {
        return;
    }

    if (speed <= 60) { //速度低于60显示绿色
        sendData(DISP_COLOR_GREEN, send);
    } else {
        sendData(DISP_COLOR_RED, send);
    }
}

void dispLed::dispCert(bool isVaild)
{
    char buf[25] = {0};
    char send[25] = {0};
    if (isVaild) {
        sprintf(buf, "%s", "证件有效");
    } else {
        sprintf(buf, "%s", "证件无效");
    }

    bool ret = utf8_2_GB2312(buf, strlen(buf), send, sizeof(send));
    if (!ret) {
        return;
    }
    if (isVaild) {
        sendData(DISP_COLOR_GREEN, send);//证件有效绿色
    } else {
        sendData(DISP_COLOR_RED, send);
    }
}

void dispLed::run()
{   
    my::timestamp dispTS = my::timestamp::now();
    while(!exiting()) {
        dispTS = my::timestamp::now();
        while(1) {
            if ((dispTS.elapsed() >= 10000)) {
                break;
            }
            int32_t speed = DeviceHub::getInstance().getCarSpeed();
            dispSpeed(speed);
            msleep(500);
        }
        dispTS = my::timestamp::now();

        while(1) {
            if (dispTS.elapsed() >= 10000) {
                break;
            }
            bool isVaild = false;
            char propValue[PROP_VALUE_MAX] = {0};
            if (__system_property_get(PROP_RW_MINIEYE_CERT_VAILD, propValue)) {
                isVaild = !strcmp(propValue, "true");
            }
            dispCert(isVaild);
            msleep(500);
        }
        msleep(500);
    }
    return;
}
}

