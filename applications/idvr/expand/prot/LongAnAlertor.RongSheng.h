/*
 * Copyright [2025] MINIEYE
 * Descripttion : 文件描述
 * Author       : x<PERSON><PERSON><PERSON>n
 * Date         : 2025-04-17
 */
#pragma once

#include "cmdline.h"
#include "FileLog.h"
#include "expand.message.h"
#include "algo.h"
#include "canDisplay.h"
// #include "ipcAgent.h"

namespace minieye {

class LongAnAlertor : public Protocol, my::thread, public IALGO_OBSERVER {
 public:
    enum LongAnAlertorPlayContent {
        E_CONTENT_NONE = 0,
        E_BSD_ALERT = 1,
        E_TURN = 2, // deprecated
        E_REVERSE = 3,
        E_TURN_LEFT = 4,
        E_TURN_RIGHT = 5
    };

    struct LongAnAlertorMsg;

    explicit LongAnAlertor();
    virtual ~LongAnAlertor() noexcept;
    LongAnAlertor &operator=(const LongAnAlertor &other) = delete;
    LongAnAlertor(const LongAnAlertor &other) = delete;

 protected:
    virtual bool onAlgoEvent(std::shared_ptr<Event> evt);
    virtual int32_t onServerConnected(void);
    virtual int32_t onServerDisconnected(void);
    virtual int32_t onDataRecevied(const char *p, uint32_t len);
    virtual std::string setupCmdList(const char *cmdName);
    virtual bool runCmd(int argc, char **argv, string &ack);
    virtual void run();

 private:
    // 支持音量0~音量10
    my::string getVolumeCmd(uint8_t volume);
    //  std::shared_ptr<IpcClient> mIpcClient;
    std::string mCmdUsage;

    int mSetVolumeCnt = 0;  // 设置一次可能无效，这里设置三次
    my::timestamp mLastChkVolumeTs;
    int mCurVolume = -1;

    my::timestamp mLastAlertUpdateTs;
    my::timestamp mLastBsdAlarmTs;
    bool mIsReverseOn = false;
    bool mIsTurnOn = false;
};

}  // namespace minieye