
#include <assert.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <sys/time.h>

#include "tahsensor.h"
#include "expand.message.h"
#include "expand.h"
#include "mystd.h"

namespace minieye {



int32_t TahSensor::onServerConnected(void)
{
    return 0;
}
int32_t TahSensor::onServerDisconnected(void)
{
    return 0;
}

int32_t TahSensor::onDataRecevied(const char *p, uint32_t len)
{
    logd("on cmd data %d", len);
	logd("recv %s", my::hex(my::constr(p, len)).c_str());

    onCmdData(mRcvArray, p, len);
    return 0;
}

int32_t TahSensor::onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len)
{
    recvArray.insert(recvArray.end(), p, p + len);

    int32_t parsed = 0;
    do {
        parsed = onDataParse((const char *)recvArray.data(), recvArray.size());
        assert(parsed <= recvArray.size());
        if (recvArray.size() >= (1 << 20)) {
            parsed = recvArray.size();
            logd("frame too long!!\n");
        }
        if (parsed < 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + recvArray.size());
            parsed = 0;
            logd("frame no header, erase all!!\n");
        } else if (parsed > 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + parsed);
            //logd("erase size %d\n", parsed);
        } else {
            //logd("alertor frame not complete!!\n");
        }

        if(recvArray.size() <=0 || parsed <= 0) {
            break;
        }

    } while (1);

    return parsed;
}

int32_t TahSensor::onDataParse(const char *p, uint32_t size)
{
#define NEED_MORE_DATA      (0)
#define DATA_INVALID        (-1)
    uint32_t offset = 0;;
    uint32_t frameLen = 0;
    TAHProtHeaderT *pHeader;
    uint32_t i=0;

    logd("on uart parse size = %d\n", size);
	logd("%s\n", my::hex(my::constr(p, size)).c_str());
    while (size - offset >= TAH_PACKAGE_SIZE_MIN) {
        pHeader = (TAHProtHeaderT *)&p[offset];
        //sync header
        if (pHeader->magic == TAH_PACKAGE_MAGIC) {
            frameLen = pHeader->len + 3; //  frameLen = pHeader->len + 数据头 + 主信令 + 长度
            if (size - offset >= frameLen) {
                if (onDataValidate((const char *)&pHeader->magic, frameLen)) {
                    onMessage(pHeader, frameLen);
                    return offset + frameLen; /* return one frame */
                } else {
                    offset++;
                    logd("frame err: magic: %x cmd: %x\n", pHeader->magic, pHeader->cmd);
                }

            } else { /* not complete */
                logd("frame recv not complete, len %d\n", frameLen);
                break;
            }
        } else {/* header err*/
            offset++;
            logd("magic err: 0x%x\n", pHeader->magic);
        }
    }

    if (offset) {
        return offset;
    } else {
        return NEED_MORE_DATA;
    }
}

bool TahSensor::onDataValidate(const char *p, uint32_t len)
{
    TAHProtHeaderT *pHeader = (TAHProtHeaderT *)p;
    if ((uint8_t)p[len - 1] != TAH_PACKAGE_END) {
        return false;
    }
    //TODO
#if 0
    uint8_t bcc = calBcc(p, len - 2); // 校验码在倒数第二个字节
    if (bcc != (uint8_t)p[len - 2]) {
        logd("data calBcc error:[0x%02x]-[0x%02x]\n", bcc, (uint8_t)p[len - 2]);
	    logd("%s\n", my::hex(my::constr(p, len)).c_str());
        return false;
    }
#endif
    return true;
}


bool TahSensor::onMessage(TAHProtHeaderT *p, uint32_t len)
{
    if(len != p->len + 3) { //len = pHeader->len + 数据头 + 主信令 + 长度
        logd("message len error %d %" FMT_LLD "\n", len, sizeof(TAHProtHeaderT) + p->len + 1);
        return false;
    }
    logd("on cmd 0x%02x\n", p->cmd);
    switch(p->cmd){
        case TAH_CMD_SENSOR_INFO:
            {

                expand::TAHSensorMessage message;
                message.mUtcTime = my::timestamp::utc_milliseconds();
                double temp, tmp1, tmp2 = 0.0;

                tmp1 = (double)p->payload[0];
                tmp2 = (double)p->payload[1] / 10.0;
                if (tmp1 > (double)0x80 && tmp1 != TAH_SENSOR_NOEXIST_VALUE) {
                    temp = (double)0x80 - tmp1 - tmp2;
                } else {
                    temp = tmp1 + tmp2;
                }
                message.mTemps.push_back(temp);
                message.mHumis.push_back(p->payload[2] + p->payload[3] / 10.0);

                tmp1 = (double)p->payload[5];
                tmp2 = (double)p->payload[6] / 10.0;
                if (tmp1 > (double)0x80 && tmp1 != TAH_SENSOR_NOEXIST_VALUE) {
                    temp = (double)0x80 - tmp1 - tmp2;
                } else {
                    temp = tmp1 + tmp2;
                }
                message.mTemps.push_back(temp);
                message.mHumis.push_back(p->payload[7] + p->payload[8] / 10.0);

                tmp1 = (double)p->payload[10];
                tmp2 = (double)p->payload[11] / 10.0;
                if (tmp1 > (double)0x80 && tmp1 != TAH_SENSOR_NOEXIST_VALUE) {
                    temp = (double)0x80 - tmp1 - tmp2;
                } else {
                    temp = tmp1 + tmp2;
                }
                message.mTemps.push_back(temp);
                message.mHumis.push_back(p->payload[12] + p->payload[13] / 10.0);

                tmp1 = (double)p->payload[15];
                tmp2 = (double)p->payload[16] / 10.0;
                if (tmp1 > (double)0x80 && tmp1 != TAH_SENSOR_NOEXIST_VALUE) {
                    temp = (double)0x80 - tmp1 - tmp2;
                } else {
                    temp = tmp1 + tmp2;
                }
                message.mTemps.push_back(temp);
                message.mHumis.push_back(p->payload[17] + p->payload[18] / 10.0);

                message.mRawData.ptr = (const char*)p;
                message.mRawData.size = len;
                logi("%s: (%f %f), %(%f %f), (%f %f), (%f %f)",
                    message.mType.c_str(), message.mTemps[0], message.mHumis[0],
                        message.mTemps[1], message.mHumis[1],
                        message.mTemps[2], message.mHumis[2],
                        message.mTemps[3], message.mHumis[3]);


                msgpack::sbuffer  sbuf;
                msgpack::pack(sbuf, message);
                ExpandSet::getInstance().sendLibFlow(TAHSENSOR_LIBFLOW_TOPIC, sbuf.data(), sbuf.size());
            }
            break;
        default:
            logd("unknow cmd.\n");
            break;
    }
    
    return true;
}

int32_t TahSensor::sendCmd(uint8_t cmd, uint8_t *payload, int32_t len)
{

    return 0;
}


bool TahSensor::runCmd(int argc, char **argv, string &ack)
{

#if 0
    /* test code */
    uint8_t p[25] = { 0x24, 0xf2, 0x16, 
                      0x20, 0x06, 0x4c, 0x07, 0x00, 
                      0x92, 0x01, 0x35, 0x04, 0x00,
                      0xff, 0x00, 0xff, 0x00, 0x00,
                      0xff, 0x00, 0xff, 0x00, 0x00,
                      0x0f,
                      0xff};
    onDataRecevied((const char *)p, 25);
#endif
    ack = "";
    return false;
}

uint8_t TahSensor::calBcc(const char *ch, uint16_t len)
{
    uint32_t i;
    uint32_t chk = 0;

    for (i = 0; i < len; i++) {
        chk = (chk ^ ch[i]) & 0xFF;
    }  
    return (chk & 0xFF);
}


} //namespace minieye

