#include "f9kDisp.h"
#include "devicehub.h"
#include "idvrProperty.h"
#include "properties.h"


namespace minieye {


F9KDisPlay::F9KDisPlay()
    :
    mLastLineShow(0),
    mSpdThres(5)
{
   
}

F9KDisPlay::~F9KDisPlay()
{
    my::thread::stop();
}

int32_t F9KDisPlay::onServerConnected(void)
{
    logd("F9KDisPlay is Connected");
    my::thread::start();
    return 0;
}

int32_t F9KDisPlay::onServerDisconnected(void)
{
    logd("F9KDisPlay is Disconnect");
    return 0;
}

int32_t F9KDisPlay::onDataRecevied(const char *p, uint32_t len)
{
    return onDataParse(p, len);
}

bool F9KDisPlay::runCmd(int argc, char **argv, string &ack)
{
    return true;
}

void F9KDisPlay::run()
{
    
    canFrame_t canData;
    canData.frameId = DISPLAY_CAN_ID;
    canData.len = 8;
    while (!exiting()) {
        LBS lbs;
        uint8_t data[8];
        memset(&lbs, 0, sizeof(lbs));

        // 车辆图标 是否开启巡检功能 todo
        // memset(data, 0, sizeof(data));
        
        
        // 定位状态
        memset(data, 0, sizeof(data));
        DeviceHub::getInstance().getGpsLbs(lbs);
        // 左侧灯带长亮
        if (lbs.rtkData.sig == 4 || lbs.rtkData.sig == 5) {
            data[0] = CMD_SHOW_LINE;
            data[1] = SHOW;
        } else if (lbs.rtkData.sig == 1 || lbs.rtkData.sig == 2) {
            uint8_t show = mLastLineShow ? HIDE : SHOW;
            data[0] = CMD_SHOW_LINE;
            data[1] = FICK;
        } else {
            data[0] = CMD_SHOW_LINE;
            data[1] = HIDE;
        }
        memcpy(canData.data, data, sizeof(canData.data));
        msgEnque((void*)&canData, sizeof(canData));
        usleep(500*1000);
        
        // 超速 闪烁显示行人图标
        memset(data, 0, sizeof(data));
        DeviceHub& devHub = DeviceHub::getInstance();
        double speed = devHub.getCarSpeed();
        if(speed > mSpdThres) {
             data[0] = CMD_SHOW_PED;
             data[1] = FICK;
        } else {
            data[0] = CMD_SHOW_PED;
            data[1] = HIDE;
        }
        memcpy(canData.data, data, sizeof(canData.data));
        msgEnque((void*)&canData, sizeof(canData));
        usleep(500*1000);

        // 检查巡检平台链接 显示右侧灯带
        char tmp[128];
        std::string prot = "";
        FILE* fp = popen("ndc prot cmd prot_stat 0 0 | grep 2", "r");
        if (fp != NULL) {
            while(fgets(tmp, sizeof(tmp), fp) != NULL) {
                tmp[strlen(tmp) - 1] = 0;
                prot = std::string(tmp, strlen(tmp));
                logd("%s", prot.c_str());
            }
        }
        
        memset(data, 0, sizeof(data));
        if (prot == "200 0 OK") {
            data[0] = CMD_SHOW_LINE;
            data[1] = HIDE;
            data[2] = SHOW;
        } else {
            data[0] = CMD_SHOW_LINE;
            data[1] = HIDE;
            data[2] = HIDE;
        }
        memcpy(canData.data, data, sizeof(canData.data));
        msgEnque((void*)&canData, sizeof(canData));
        usleep(500*1000);
        
        // 数码管 
        // 1.E1 Acc 断开
        memset(data, 0, sizeof(data));
        IOStatus io_status;
        DeviceHub::getInstance().getIOStatus(io_status);
        if(!io_status.acc) {
            logd("acc off");
            data[0] = CMD_SHOW_CAR;
            data[2] = SHOW_CAR;
            data[3] = SHOW_E;
            data[4] = SHOW;
            data[5] = SHOW;
            data[6] = SHOW;
        } else {
            data[0] = CMD_SHOW_CAR;
            data[6] = HIDE;
        }
        memcpy(canData.data, data, sizeof(canData.data));
        msgEnque((void*)&canData, sizeof(canData));
        usleep(500*1000);
        
        // 2.E2 电压过低
        memset(data, 0, sizeof(data));
        SysStatus_t sys;
        DeviceHub::getInstance().getSysStatus(sys);
        if(sys.power_low) {
            logd("pwd_low");
            data[0] = CMD_SHOW_CAR;
            data[2] = SHOW_CAR;
            data[3] = SHOW_E;
            data[4] = 0x2;
            data[5] = SHOW;
            data[6] = SHOW;
        } else {
            data[0] = CMD_SHOW_CAR;
            data[6] = HIDE;
        }
        memcpy(canData.data, data, sizeof(canData.data));
        msgEnque((void*)&canData, sizeof(canData));
        usleep(500*1000);
        
        // 3 E.3 RTK定位异常
        memset(data, 0, sizeof(data));
        if (lbs.rtkData.sig == 0 || lbs.rtkData.sig == 6) {
            logd("RTK sig %d", lbs.rtkData.sig);
            data[0] = CMD_SHOW_CAR;
            data[2] = SHOW_CAR;
            data[3] = SHOW_E;
            data[4] = 0x3;
            data[5] = SHOW;
            data[6] = SHOW;
        } else {
            data[0] = CMD_SHOW_CAR;
            data[6] = HIDE;
        }
        memcpy(canData.data, data, sizeof(canData.data));
        msgEnque((void*)&canData, sizeof(canData));
        usleep(500*1000);
        
        // 4 E.4 摄像头异常
        memset(data, 0, sizeof(data));
        char prop[PROP_VALUE_MAX] = {0};
        int updateTm = 0;
        int camStatus = 0;
        if (__system_property_get(PROP_RW_MINIEYE_CAM_STATUS, prop)) {
            sscanf(prop, "%d %d", &updateTm, &camStatus);
            if(camStatus != 3) {
                logd("camStatus %d", camStatus);
                data[0] = CMD_SHOW_CAR;
                data[2] = SHOW_CAR;
                data[3] = SHOW_E;
                data[4] = 0x4;
                data[5] = SHOW;
                data[6] = SHOW;
            } else {
                data[0] = CMD_SHOW_CAR;
                data[6] = HIDE;
            }
        }
        memcpy(canData.data, data, sizeof(canData.data));
        msgEnque((void*)&canData, sizeof(canData));
        usleep(500*1000);
        
        // 5.E.5 存储卡异常
        // 6.E.6 录像异常
        int32_t record = 0;
        memset(data, 0, sizeof(data));
        devHub.getRecordStatus(record);
        if ((record & 0xFF)) {
            logd("Record %#x", record);
            data[0] = CMD_SHOW_CAR;
            data[2] = SHOW_CAR;
            data[3] = SHOW_E;
            data[4] = 0x5;
            data[5] = SHOW;
            data[6] = SHOW;
        } else {
            data[0] = CMD_SHOW_CAR;
            data[6] = HIDE;
        }
        memcpy(canData.data, data, sizeof(canData.data));
        msgEnque((void*)&canData, sizeof(canData));
        usleep(500*1000);
        
        memset(data, 0, sizeof(data));
        if((record >> 16) & 0xFF) {
            logd("Disk %#x", record);
            data[0] = CMD_SHOW_CAR;
            data[2] = SHOW_CAR;
            data[3] = SHOW_E;
            data[4] = 0x6;
            data[5] = SHOW;
            data[6] = SHOW;
        } else {
            data[0] = CMD_SHOW_CAR;
            data[6] = HIDE;
        }
        memcpy(canData.data, data, sizeof(canData.data));
        msgEnque((void*)&canData, sizeof(canData));
        // usleep(100*100);
    }
}

int32_t F9KDisPlay::onDataParse(const char *p, uint32_t size)
{
    logd("recv %s", my::hex(my::constr(p, size)).c_str());
    return 0;
}


}

