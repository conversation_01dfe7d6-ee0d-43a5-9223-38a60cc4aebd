
#include <stdio.h>
#include <string.h>
#include <stdint.h>
#include "mystd.h"
#include "f9kGpsNovatelMsg.h"

int parse_bestposa(const char* data, BestposData* out) {
    if (strncmp(data, "#BESTPOSA", 9) != 0){
        return 0;
    }

    sscanf(data,
           "#BESTPOSA,%*[^,],%hu,%lf,%*[^,],%lf,%lf,%f,%*[^,],%hhu",
           &out->week,
           &out->seconds,
           &out->latitude,
           &out->longitude,
           &out->altitude,
           &out->sat_num);
    return 1;
}

int parse_inspvaxa(const char* data, InspvaxData* out) {
    if (strncmp(data, "#INSPVAXA", 9) != 0){
        return 0;
    }

    sscanf(data,
           "#INSPVAXA,%*[^,],%*[^,],%lf,%lf,%f,%f,%f,%f,%f,%f,%f",
           &out->latitude,
           &out->longitude,
           &out->altitude,
           &out->north_vel,
           &out->east_vel,
           &out->up_vel,
           &out->roll,
           &out->pitch,
           &out->azimuth);
    return 1;
}

void parseNovatel(const char* buffer, int32_t size, BestposData* pBestposData, InspvaxData* pInspvaxData) {
    char* line = strtok((char*)buffer, "\r\n");
    while (line) {
        BestposData bp_data;
        InspvaxData ip_data;

        if (parse_bestposa(line, &bp_data)) {
            *pBestposData = bp_data;
            logd("BESTPOSA: Lat=%.8f Lon=%.8f Alt=%.2fm\n", bp_data.latitude, bp_data.longitude, bp_data.altitude);
        } else if (parse_inspvaxa(line, &ip_data)) {
            *pInspvaxData = ip_data;
            logd("INSPVAXA: Roll=%.2f Pitch=%.2f Azimuth=%.2f\n", ip_data.roll, ip_data.pitch, ip_data.azimuth);
        }
        line = strtok(NULL, "\r\n");
    }
}
