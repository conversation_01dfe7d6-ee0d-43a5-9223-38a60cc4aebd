#include "devicehub.h"
#include "idvrProperty.h"
#include "properties.h"
#include "Display.xunjian.h"

namespace minieye {

DisplayXunjian::DisplayXunjian()
    :
    mSpdThres(5),
    mCtrlRightLineFilp(0),
    mCtrlLeftLineFilp(0),
    mErrIndex(1)

{
    memset(mStateSet, 0x00, sizeof(mStateSet));
    mLeftTimeStamp  = my::timestamp::now();
    mRightTimeStamp = mLeftTimeStamp;
    mErrMonitorTimeStamp = mLeftTimeStamp;
}

DisplayXunjian::~DisplayXunjian()
{
    my::thread::stop();
}

int32_t DisplayXunjian::onServerConnected(void)
{
    logd("DisplayXunjian is Connected");
    my::thread::start();
    return 0;
}

int32_t DisplayXunjian::onServerDisconnected(void)
{
    logd("DisplayXunjian is Disconnect");
    return 0;
}

int32_t DisplayXunjian::onDataRecevied(const char *p, uint32_t len)
{
    return onDataParse(p, len);
}

bool DisplayXunjian::runCmd(int argc, char **argv, string &ack)
{
    return true;
}

int32_t DisplayXunjian::onDataParse(const char *p, uint32_t size)
{
    logd("recv %s", my::hex(my::constr(p, size)).c_str());
    return 0;
}

/**
 * @brief 巡检功能的开启或停用
 *  1) 红色车辆图标代表停用巡检功能
 *  2) 白色车辆图标代表启用巡检功能
 */
void DisplayXunjian::monitorxunjian()
{
    mStateSet[STATE_XUNJIAN] = E_XUNJIAN_START;
    displayxunjian();
}

void DisplayXunjian::displayxunjian()
{
    if (mStateSet[STATE_XUNJIAN] == E_XUNJIAN_START) {
        E_Vehicle_t vehicle;
        fillVehicleStruct(vehicle, E_DISPLAY_WHITE, E_DISPLAY_SHOW);
        setVehicleStatus(vehicle);
    } else {
        E_Vehicle_t vehicle;
        fillVehicleStruct(vehicle, E_DISPLAY_RED, E_DISPLAY_SHOW);
        setVehicleStatus(vehicle);
    }
}

/**
 * @brief 定位状态指示---> 左侧白色灯带
 *  常量   : rtk状态为 4/5
 *  熄灭   : rtk状态为 0/6
 *  1hz频闪: rtk状态为 1/2
 */
void DisplayXunjian::monitorLocateState()
{
    LBS lbs;
    memset(&lbs, 0, sizeof(lbs));

    DeviceHub::getInstance().getGpsLbs(lbs);

    if (lbs.rtkData.sig == 4 || lbs.rtkData.sig == 5) {
        mStateSet[STATE_LOCATE] = E_RTK_STATE_4_5;
    } else if (lbs.rtkData.sig == 1 || lbs.rtkData.sig == 2) {
        mStateSet[STATE_LOCATE] = E_RTK_STATE_1_2;
    } else {
        mStateSet[STATE_LOCATE] = E_RTK_STATE_0_6;
    }

    displayLocateState();
}

void DisplayXunjian::displayLocateState()
{
    if (mStateSet[STATE_LOCATE] == E_RTK_STATE_4_5) {
        setLeftLineStatus(E_DISPLAY_SHOW);
    } else if (mStateSet[STATE_LOCATE] == E_RTK_STATE_0_6) {
        setLeftLineStatus(E_DISPLAY_HIDE);
    } else if (mStateSet[STATE_LOCATE] == E_RTK_STATE_1_2) {
        if (mLeftTimeStamp.elapsed() >= 500) {
            mLeftTimeStamp = my::timestamp::now();
            mCtrlLeftLineFilp = !mCtrlLeftLineFilp;
            setLeftLineStatus((E_DisplayStatus_t) mCtrlLeftLineFilp);
        }
    } else {
        logd("Illegal mStateSet[STATE_LOCATE] status");
    }
}

/**
 * @brief 网络状态指示---> 右侧白色灯带
 *  常量   : 巡检平台已连接(4G 在线)
 *  熄灭   : 4G 不在线
 *  1hz频闪: 巡检车平台未连接但4G在线
 */
void DisplayXunjian::monitorPlatformConnectState()
{
    char val[PROP_VALUE_MAX] = {0};
    uint8_t connected = 0;

    memset(val, 0, sizeof(val));
    __system_property_get("rw.minieye.net_reg", val);
    connected = atoi(val);

    if (connected) {
        char tmp[128];
        std::string prot = "";
        FILE* fp = popen("ndc prot cmd prot_stat 0 0 | grep 2", "r");
        if (fp != NULL) {
            while(fgets(tmp, sizeof(tmp), fp) != NULL) {
                tmp[strlen(tmp) - 1] = 0;
                prot = std::string(tmp, strlen(tmp));
                logd("%s", prot.c_str());
            }
        }
        if (prot == "200 0 OK") {
            mStateSet[STATE_NetCONNECT] = E_PLATFORM_CONNECTED;
        } else {
            mStateSet[STATE_NetCONNECT] = E_PLATFORM_NOT_CONNECTED_HAVE_4G;
        }

        fclose(fp);

    } else {
        mStateSet[STATE_NetCONNECT] = E_PLATFORM_NOT_CONNECTED_NOT_4G;
    }

    displayPlatformConnectState();
}

void DisplayXunjian::displayPlatformConnectState()
{
    if (mStateSet[STATE_NetCONNECT] == E_PLATFORM_NOT_CONNECTED_NOT_4G) {
        setRightLineStatus(E_DISPLAY_HIDE);
    } else if (mStateSet[STATE_NetCONNECT] == E_PLATFORM_CONNECTED) {
        setRightLineStatus(E_DISPLAY_SHOW);
    } else if (mStateSet[STATE_NetCONNECT] == E_PLATFORM_NOT_CONNECTED_HAVE_4G) {
        if (mRightTimeStamp.elapsed() >= 500) {
            mRightTimeStamp = my::timestamp::now();
            mCtrlRightLineFilp = (!mCtrlRightLineFilp);
            setRightLineStatus((E_DisplayStatus_t)mCtrlRightLineFilp);
        }
    } else {
        logd("Illegal mNetStatus status");
    }
}

/**
 * @brief 超速指示---> 行人图标
 *  熄灭   : 未超速
 *  1hz频闪: 超速
 */
void DisplayXunjian::monitorSpeedingState()
{
    DeviceHub& devHub = DeviceHub::getInstance();
    int speed = devHub.getCarSpeed();

    if (speed > mSpdThres) {
        mStateSet[STATE_SPEEDING] = E_OVERSPEED;
    } else {
        mStateSet[STATE_SPEEDING] = E_NO_OVERSPEED;
    }

    displaySpeedingState();
}

void DisplayXunjian::displaySpeedingState()
{
    if (mStateSet[STATE_SPEEDING] == E_OVERSPEED) {
        setPedestrianStatus(E_DISPLAY_FICK);
    } else {
        setPedestrianStatus(E_DISPLAY_HIDE);
    }
}

/**
 * @brief 异常状态---> 数码管
 *  E1 : 系统异常
 *  E2 : 电压过低
 *  E3 : rtk定位异常
 *  E4 : 摄像头异常
 *  E5 : 存储卡异常
 *  E6 : 录像异常
 */

void DisplayXunjian::monitorErrState()
{
    if (mErrMonitorTimeStamp.elapsed() >= 1000) {
        mErrMonitorTimeStamp = my::timestamp::now();
        mStateSet[STATE_SYSERROR] = E0_SYSTEM_NOT_ERROR;
        for (int i = E1_SYSTEM_ERROR; i <= E6_RECORD_ERROR; i++) {

            if (mErrIndex > E6_RECORD_ERROR) {
                mErrIndex = E1_SYSTEM_ERROR;
            }

            switch (mErrIndex)
            {
                case E1_SYSTEM_ERROR:
                {
                    if (scanSystemError()) {     //出现异常
                        mErrIndex++;
                        i = E6_RECORD_ERROR + 1; //这里旨意是退出for循环,不能用break
                        mStateSet[STATE_SYSERROR] = E1_SYSTEM_ERROR;
                        //break;
                    } else {   // 没有异常
                        mErrIndex++;
                        continue;
                    }
                }
                    break;
                case E2_VOL_TOO_LOW:
                {
                    if (scanVolError()) {     //出现异常
                        mErrIndex++;
                        i = E6_RECORD_ERROR + 1; //这里旨意是退出for循环,不能用break
                        mStateSet[STATE_SYSERROR] = E2_VOL_TOO_LOW;
                        //break;
                    } else {   // 没有异常
                        mErrIndex++;
                        continue;
                    }
                }
                    break;
                case E3_RTK_LOCATE_ERR:
                {
                    if (scanRtkLocateError()) {     //出现异常
                        mErrIndex++;
                        i = E6_RECORD_ERROR + 1; //这里旨意是退出for循环,不能用break
                        mStateSet[STATE_SYSERROR] = E3_RTK_LOCATE_ERR;
                        //break;
                    } else {   // 没有异常
                        mErrIndex++;
                        continue;
                    }
                }
                    break;
                case E4_CAMERA_ERROR:
                {
                    if (scanCameraError()) {     //出现异常
                        mErrIndex++;
                        i = E6_RECORD_ERROR + 1; //这里旨意是退出for循环,不能用break
                        mStateSet[STATE_SYSERROR] = E4_CAMERA_ERROR;
                        //break;
                    } else {   // 没有异常
                        mErrIndex++;
                        continue;
                    }
                }
                    break;
                case E5_MEM_CARD_ERROR:
                {
                    if (scanSDcardError()) {     //出现异常
                        mErrIndex++;
                        i = E6_RECORD_ERROR + 1; //这里旨意是退出for循环,不能用break
                        mStateSet[STATE_SYSERROR] = E5_MEM_CARD_ERROR;
                        //break;
                    } else {   // 没有异常
                        mErrIndex++;
                        continue;
                    }
                }
                    break;
                case E6_RECORD_ERROR:
                {
                    if (scanRecordError()) {     //出现异常
                        mErrIndex++;
                        i = E6_RECORD_ERROR + 1; //这里旨意是退出for循环,不能用break
                        mStateSet[STATE_SYSERROR] = E6_RECORD_ERROR;
                        //break;
                    } else {   // 没有异常
                        mErrIndex++;
                        continue;
                    }
                }
                    break;
                default:
                logd("not error");
                    break;
            }
        }
    }

    displayErrState();
}

void DisplayXunjian::displayErrState()
{
    uint8_t num = 0xE0 + (uint8_t)mStateSet[STATE_SYSERROR];
    E_SegNum_t SegNum;
    if (num == 0xE0) {
        fillSegNumStruct(SegNum, num, true, E_DISPLAY_WHITE, E_DISPLAY_SHOW);
    } else {
        fillSegNumStruct(SegNum, num, true, E_DISPLAY_RED, E_DISPLAY_SHOW);
    }
    setSegNumStatus(SegNum);
}


void DisplayXunjian::run()
{
    while (!exiting()) {
        monitorLocateState();
        monitorPlatformConnectState();
        monitorSpeedingState();
        monitorxunjian();
        monitorErrState();
        refreshDispStatePack();
    }
}


/**
 * @brief 下面是异常扫描检测
 *        出现异常返回true,正常返回false
 */
bool DisplayXunjian::scanSystemError()
{
    return false;
}

bool DisplayXunjian::scanVolError()
{
    SysStatus_t sys;
    DeviceHub::getInstance().getSysStatus(sys);

    if (sys.power_low) {
        return true;
    } else {
        return false;
    }
}

bool DisplayXunjian::scanRtkLocateError()
{
    LBS lbs;
    memset(&lbs, 0, sizeof(lbs));

    DeviceHub::getInstance().getGpsLbs(lbs);

    if (lbs.rtkData.sig == 0 || lbs.rtkData.sig == 6) {
        return true;
    } else {
        return false;
    }

}

bool DisplayXunjian::scanCameraError()
{
    char prop[PROP_VALUE_MAX] = {0};
    int updateTm = 0;
    int camStatus = 0;
    if (__system_property_get(PROP_RW_MINIEYE_CAM_STATUS, prop)) {
        sscanf(prop, "%d %d", &updateTm, &camStatus);
        if (camStatus != 3) {
            return true;

        } else {
            return false;
        }
    }

    return true;
}

bool DisplayXunjian::scanSDcardError()
{
    int32_t sdcard = 0;
    DeviceHub& devHub = DeviceHub::getInstance();
    devHub.getRecordStatus(sdcard);
    if (((sdcard >> 16) & 0xFF)) {
        logd("SDcard %#x", sdcard);
        return false;
    } else {
        logd("<not SDcard %#x", sdcard);
        return true;
    }
}

bool DisplayXunjian::scanRecordError()
{
    int32_t record = 0;
    DeviceHub& devHub = DeviceHub::getInstance();
    devHub.getRecordStatus(record);

    if ((record & 0xFF)) {
        logd("Record %#x", record);
        return false;
    } else {
        logd("not Record %#x", record);
        return true;
    }
}

}

