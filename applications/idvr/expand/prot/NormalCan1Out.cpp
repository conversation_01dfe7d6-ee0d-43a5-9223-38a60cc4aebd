#include "NormalCan1Out.h"

#include <assert.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <numeric>
#include <fstream>

#include "devicehub.h"
#include "expand.h"
#include "properties.h"

#define CAN_ID_PROP "persist.can1out.id"

#define BSD_CAM_ID_PROT  "rw.objp2bsd.right"
#define BSD_CAM_ID_VALUE "2"

#define RUN_DIR   "/sdcard/run/"
#define ADAS_DIR  "/system/algo/adas/"
#define DMS_DIR   "/system/algo/dms/"
#define ADAS_FLAG "detect.flag"
#define DMS_FLAG  "dms.flag"

namespace minieye {

struct NormalCanMsg {
    uint32_t frameId = 0x18100070;
    uint8_t len = 8;

    uint8_t sysFault    : 3 = 0;
    uint8_t r1_1        : 1 = 0;
    uint8_t sysStatus   : 3 = 0;
    uint8_t r1_2        : 1 = 0;

    uint8_t adasStatus  : 6 = 0;
    uint8_t r2          : 2 = 0;

    uint8_t rBsdWarning : 3 = 0;
    uint8_t r3_1        : 1 = 0;
    uint8_t lBsdWarning : 3 = 0;
    uint8_t r3_2        : 1 = 0;

    uint8_t fcwWarning  : 1 = 0;
    uint8_t ufcwWarning : 1 = 0;
    uint8_t hmwWarning  : 1 = 0;
    uint8_t pcwWarning  : 1 = 0;
    uint8_t ldwWarning  : 2 = 0;
    uint8_t r4          : 2 = 0;

    uint8_t sliWarning  : 7 = 0;
    uint8_t r5          : 1 = 0;

    uint8_t dmsStatus = 0;

    uint8_t dmsFuncStatus : 6 = 0;
    uint8_t r7            : 2 = 0;

    uint8_t dmsWarning = 0;

} __attribute__((packed));

NormalCan1Out::NormalCan1Out() {
    AlgoManager &am = AlgoManager::getInstance();
    char tmp[256];
    snprintf(tmp, sizeof(tmp), "NormalCan1Out%p", this);
    am.addObserver(tmp, this);
    __system_property_set(BSD_CAM_ID_PROT, BSD_CAM_ID_VALUE);

    if (!checkFlag()) {
        loge("failed to check flag");
    }

    start();
}
int32_t NormalCan1Out::onServerConnected(void) {
    return 0;
}
int32_t NormalCan1Out::onServerDisconnected(void) {
    return 0;
}

int32_t NormalCan1Out::onDataRecevied(const char *p, uint32_t len) {
    // logd("on cmd data %d", len);
    // logd("recv %s", my::hex(my::constr(p, len)).c_str());

    onCmdData(mRcvArray, p, len);
    return 0;
}

int32_t NormalCan1Out::onCmdData(std::vector<uint8_t> &recvArray, const char *p, uint32_t len) {
#if 0
    recvArray.insert(recvArray.end(), p, p + len);
    my::hexdump(my::constr((const char *)recvArray.data(), recvArray.size()), true, "expand.onCmdData");
    int32_t parsed = 0;

    do {
        parsed = onDataParseAMsg((const char *)recvArray.data(), recvArray.size());
        //assert(parsed <= recvArray.size());

        if (recvArray.size() >= (1 << 20)) {
            parsed = recvArray.size();
            logd("frame too long!!\n");
        }

        if (parsed < 0) {
            logd("frame no header, erase all!! %d\n", parsed);
            recvArray.erase(recvArray.begin(), recvArray.begin() + recvArray.size());
            parsed = 0;

        } else if (parsed > 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + parsed);
            //logd("erase size %d\n", parsed);

        } else {
            //logd("alertor frame not complete!!\n");
        }

        if (recvArray.size() <= 0 || parsed <= 0) {
            break;
        }

    } while (1);
    return parsed;
#else
    return 0;
#endif
}

bool NormalCan1Out::checkFlag() {
    return checkAdasFlag() && checkDmsFlag();
}

bool NormalCan1Out::checkAdasFlag() {
    if (access(RUN_DIR ADAS_FLAG, F_OK) == 0) {
        return true;
    }
    std::ifstream src(ADAS_DIR ADAS_FLAG, std::ios::binary);
    std::ofstream dest(RUN_DIR ADAS_FLAG, std::ios::binary);

    dest << src.rdbuf();
    return src && dest;
}

bool NormalCan1Out::checkDmsFlag() {
    if (access(RUN_DIR DMS_FLAG, F_OK) == 0) {
        return true;
    }
    std::ifstream src(DMS_DIR DMS_FLAG, std::ios::binary);
    std::ofstream dest(RUN_DIR DMS_FLAG, std::ios::binary);

    dest << src.rdbuf();
    return src && dest;
}

bool NormalCan1Out::onAlgoEvent(std::shared_ptr<Event> e) {
    switch (e->type()) {
        /* ADAS events */
        //TODO: implement UFCW
        case EVT_TYPE_ADAS_FCW:
        case EVT_TYPE_ADAS_HW:
        case EVT_TYPE_ADAS_PCW:
        case EVT_TYPE_ADAS_LeftLDW:
        case EVT_TYPE_ADAS_RightLDW:
        case EVT_TYPE_ADAS_CamOcclusion:

        /* DMS events */
        case EVT_TYPE_DMS_FATIGUE_Eye:
        case EVT_TYPE_DMS_FATIGUE_Eye2:
        case EVT_TYPE_DMS_LOOK_AROUND:
        case EVT_TYPE_DMS_FATIGUE_YAWN:
        case EVT_TYPE_DMS_PHONE_CALL:
        case EVT_TYPE_DMS_SMOKE:
        case EVT_TYPE_DMS_ABSENCE:
        case EVT_TYPE_DMS_LOOK_DOWN:
        case EVT_TYPE_DMS_CAM_OCCLUSION:
        case EVT_TYPE_DMS_EYE_OCCLUSION:
        
        /* OCCLUSION events */
        case EVT_TYPE_CAM_OCCLUSION_INFO: {
            std::lock_guard<std::mutex> _l_(mUpdateMutex);
            mEventMap[e->type()] = {my::timestamp::now(), e};
            break;
        }

        /* BSD events */
        case EVT_TYPE_BSD_Front:
        case EVT_TYPE_BSD_Behind: {
            std::lock_guard<std::mutex> _l_(mUpdateMutex);
            auto evt = e->type() == EVT_TYPE_BSD_Front ? EVT_TYPE_BSD_Left : EVT_TYPE_BSD_Right;
            mEventMap[evt] = {my::timestamp::now(), e};
            break;
        }
        case EVT_TYPE_BSD_Left:
        case EVT_TYPE_BSD_Right: {
            std::lock_guard<std::mutex> _l_(mUpdateMutex);
            mEventMap[e->type()] = {my::timestamp::now(), e};
            break;
        }

        default:
            break;
    }
    return true;
}

#define UDAS_CAN_CMD_HELP 0

static const std::vector<CmdStrT> gBsdAlertCmds = {{"help", 1, "show this usage.\n"}};

std::string NormalCan1Out::setupCmdList(const char *cmdName) {
    mCmdUsage = CmdStrT::setupCmdList(gBsdAlertCmds, cmdName);
    return mCmdUsage;
}
bool NormalCan1Out::runCmd(int argc, char **argv, string &ack) {
    uint32_t cmd = CmdStrT::strToCmd(gBsdAlertCmds, argv[0]);

    switch (cmd) {
        case UDAS_CAN_CMD_HELP: {
            ack = mCmdUsage;
            return true;
        }
        default: {
            ack = mCmdUsage;
        }
    }

    return false;
}

void NormalCan1Out::run() {
    auto handleSysFault = [this](NormalCanMsg &msg) {  // dms遮挡也在这处理了
        uint8_t sysFault = E_SYSTEM_NO_ERROR;
        uint8_t dmsFuncStatus = E_DMS_DISABLED;

        // 算法自带遮挡检测
        auto evt = mEventMap.find(EVT_TYPE_ADAS_CamOcclusion);
        if (evt != mEventMap.end() && evt->second.first.elapsed() < 2100) {
            sysFault |= E_SYSTEM_ADAS_CAMERA_BLOCK;
        }
        evt = mEventMap.find(EVT_TYPE_DMS_CAM_OCCLUSION);
        if (evt != mEventMap.end() && evt->second.first.elapsed() < 2100) {
            sysFault |= E_SYSTEM_DMS_CAMERA_ERROR;
            dmsFuncStatus |= E_DMS_OCCLUSION_WARNING;
        }
        evt = mEventMap.find(EVT_TYPE_DMS_EYE_OCCLUSION);
        if (evt != mEventMap.end() && evt->second.first.elapsed() < 2100) {
            sysFault |= E_SYSTEM_DMS_CAMERA_ERROR;
            dmsFuncStatus |= E_DMS_EYE_OCCLUSION_WARNING;
        }

        // 专用遮挡算法检测
        my::conf::ini ini;
        my::conf::ini::load(ini, "/data/minieye/idvr/etc/config.ini");
        auto occlusion = mEventMap.find(EVT_TYPE_CAM_OCCLUSION_INFO);
        if (occlusion != mEventMap.end() && occlusion->second.first.elapsed() < 2100) {
            auto occlusionInfo = occlusion->second.second->mEvtDataInt["chBitTbl"];
            char name[32] = "";
            int i = 0;
            while (occlusionInfo > 0) {
                if (occlusionInfo & 1) {
                    snprintf(name, sizeof(name), "media.ch%d", i);
                    auto str = ini.get(name, "cam.phy", "");
                    if (str == "ADAS" || str == "adas") {
                        sysFault |= E_SYSTEM_ADAS_CAMERA_BLOCK;
                    } else if (str == "BSD" || str == "bsd") {
                        sysFault |= E_SYSTEM_BSD_CAMERA_BLOCK;
                    } else if (str == "DMS" || str == "dms") {
                        sysFault |= E_SYSTEM_DMS_CAMERA_ERROR;
                        dmsFuncStatus |= E_DMS_OCCLUSION_WARNING;
                    }
                }
                occlusionInfo >>= 1;
                i++;
            }
        }

        // 摄像头状态
        int sec, cams;
        char propValue[PROPERTY_VALUE_MAX] = {0};
        property_get("rw.minieye.cam_status", propValue, "");
        stringstream ss(propValue);
        if (ss >> sec >> cams) {
            char name[32] = "";
            int i = 1;
            while (cams) {
                if (cams & 1) {
                    snprintf(name, sizeof(name), "media.ch%d", i);
                    auto str = ini.get(name, "cam.phy", "");
                    if (str == "ADAS" || str == "adas") {
                        sysFault |= E_SYSTEM_ADAS_CAMERA_BLOCK;
                    } else if (str == "BSD" || str == "bsd") {
                        sysFault |= E_SYSTEM_BSD_CAMERA_BLOCK;
                    } else if (str == "DMS" || str == "dms") {
                        sysFault |= E_SYSTEM_DMS_CAMERA_ERROR;
                    }
                }
                cams >>= 1;
                i++;
            }
        }

        msg.sysFault = sysFault;
        msg.dmsFuncStatus = dmsFuncStatus;
    };

    auto handleSysStatus = [this](NormalCanMsg &msg)  {
        uint8_t sysStatus = E_SYSTEM_OFF;

        char propValue[PROPERTY_VALUE_MAX] = {0};

        __system_property_get("init.svc.bsd", propValue);
        if (strcmp(propValue, "running") == 0) {
            sysStatus |= E_RIGHT_BSD_ACTIVE;
        } else {
            __system_property_get("init.svc.objp", propValue);
            if (strcmp(propValue, "running") == 0) {
                sysStatus |= E_RIGHT_BSD_ACTIVE;
            }
        }

        __system_property_get("init.svc.adas", propValue);
        if (strcmp(propValue, "running") == 0) {
            sysStatus |= E_ADAS_ACTIVE;
        }

        __system_property_get("init.svc.dms", propValue);
        if (strcmp(propValue, "running") == 0) {
            sysStatus |= E_ADAS_ACTIVE;
        }

        if (sysStatus != 0) {
            sysStatus |= E_SYSTEM_ON;
        }
        msg.sysStatus = sysStatus;
    };

    auto handleAdasSystemStatus = [this](NormalCanMsg &msg)  {
        static const std::unordered_map<std::string, AdasSystemStatus> kAdasSystemStatusMap = {
            {"--enable_fcw_warning=true"s, E_FCW_ENABLE},
            // {"--enable_ufcw_warning=true"s, E_UFCW_ENABLE},
            {"--enable_hmw_warning=true"s, E_HMW_ENABLE},
            {"--enable_ped=true"s, E_PCW_ENABLE},
            {"--enable_lane=true"s, E_LDW_ENABLE},
            {"--enable_tsr_warning=true"s, E_SLI_ENABLE},
        };

        uint8_t adasSystemStatus = E_ADAS_DISABLED;
        std::ifstream src(RUN_DIR ADAS_FLAG);
        while (!src.eof()) {
            std::string line;
            while (std::getline(src, line)) {
                while (line.back() == ' ') {
                    line.pop_back();
                }
                auto it = kAdasSystemStatusMap.find(line);
                if (it != kAdasSystemStatusMap.end()) {
                    adasSystemStatus |= it->second;
                }
            }
        }
        msg.adasStatus = adasSystemStatus;
    };

    auto handleBsdWarning = [this](NormalCanMsg &msg){
        uint8_t rBsdWarning = E_BSD_NO_WARNING;
        uint8_t lBsdWarning = E_BSD_NO_WARNING;


        auto evt = mEventMap.find(EVT_TYPE_BSD_Right);
        if (evt != mEventMap.end() && evt->second.first.elapsed() < 3000) {
            auto level = evt->second.second->c.level;
            rBsdWarning = (level == 1 ? E_BSD_WARNING_L1 : level == 2 ? E_BSD_WARNING_L2 : E_BSD_WARNING_L3);
        }
        msg.rBsdWarning = rBsdWarning;

        evt = mEventMap.find(EVT_TYPE_BSD_Left);
        if (evt != mEventMap.end() && evt->second.first.elapsed() < 3000) {
            auto level = evt->second.second->c.level;
            lBsdWarning = (level == 1 ? E_BSD_WARNING_L1 : level == 2 ? E_BSD_WARNING_L2 : E_BSD_WARNING_L3);
        }
        msg.lBsdWarning = lBsdWarning;
    };

    auto handleAdasWarning = [this](NormalCanMsg &msg) {
        auto evt = mEventMap.find(EVT_TYPE_ADAS_FCW);
        if (evt != mEventMap.end() && evt->second.first.elapsed() < 2100) {
            msg.fcwWarning = E_FCW_WARNING;
        } else {
            msg.fcwWarning = E_FCW_NO_WARNING;
        }

        // evt = mEventMap.find(EVT_TYPE_ADAS_UFCW);

        evt = mEventMap.find(EVT_TYPE_ADAS_HW);
        if (evt != mEventMap.end() && evt->second.first.elapsed() < 2100) {
            msg.hmwWarning = E_HMW_WARNING;
        } else {
            msg.hmwWarning = E_HMW_NO_WARNING;
        }

        evt = mEventMap.find(EVT_TYPE_ADAS_PCW);
        if (evt != mEventMap.end() && evt->second.first.elapsed() < 2100) {
            msg.pcwWarning = E_PCW_WARNING;
        } else {
            msg.pcwWarning = E_PCW_NO_WARNING;
        }

        evt = mEventMap.find(EVT_TYPE_ADAS_LeftLDW);
        if (evt != mEventMap.end() && evt->second.first.elapsed() < 2100) {
            msg.ldwWarning = E_LEFT_LDW_WARNING;
        } else {
            evt = mEventMap.find(EVT_TYPE_ADAS_RightLDW);
            if (evt != mEventMap.end() && evt->second.first.elapsed() < 2100) {
                msg.ldwWarning = E_RIGHT_LDW_WARNING;
            } else {
                msg.ldwWarning = E_LDW_NO_WARNING;
            }
        }

        constexpr int speedLimitValue[] = {0, 5, 10, 15, 20, 25, 30 /*, 35*/};
        constexpr SliWarningStatus speedLimitLevel[] = {E_SLI_WARNING_L1,
                                                        E_SLI_WARNING_L2,
                                                        E_SLI_WARNING_L3,
                                                        E_SLI_WARNING_L4,
                                                        E_SLI_WARNING_L5,
                                                        E_SLI_WARNING_L6,
                                                        E_SLI_WARNING_L7,
                                                        /*E_SLI_WARNING_L7*/};
        msg.sliWarning = E_SLI_NO_WARNING;
        char propValue[PROPERTY_VALUE_MAX] = {0};
        property_get("rw.algo.speed_limit", propValue, "0");
        auto speedLimit = std::stoi(propValue);
        if (speedLimit > 0) {
            DeviceHub &dh = DeviceHub::getInstance();
            auto curSpeed = dh.getCarSpeed();
            for (auto i = 7; i >= 0; i--) {
                if (curSpeed > speedLimit + speedLimitValue[i]) {
                    msg.sliWarning = speedLimitLevel[i];
                    break;
                }
            }
        }
    };

    auto handleDmsSystemStatus = [this](NormalCanMsg &msg) {
        static const std::unordered_map<std::string, DmsSystemStatus> kDmsSystemStatusMap = {
            {"--alert_item_eyeclose1_enable=true"s, E_DMS_EYE_CLOSED1_ENABLE},
            {"--alert_item_eyeclose2_enable=true"s, E_DMS_EYE_CLOSED2_ENABLE},
            {"--enable_look_around_alert=true"s, E_DMS_LOOK_AROUND_ENABLE},
            {"--enable_yawn_alert=true"s, E_DMS_YAWN_ENABLE},
            {"--enable_phone_alert=true"s, E_DMS_PHONE_ENABLE},
            {"--enable_smoking_alert=true"s, E_DMS_SMOKING_ENABLE},
            {"--enable_absence_alert=true"s, E_DMS_ABSENCE_ENABLE},
            {"--enable_look_down_alert=true"s, E_DMS_LOOKDOWN_ENABLE},
        };

        uint8_t dmsSystemStatus = E_DMS_DISABLED;
        std::ifstream src(RUN_DIR DMS_FLAG);
        while (!src.eof()) {
            std::string line;
            while (std::getline(src, line)) {
                while (line.back() == ' ') {
                    line.pop_back();
                }
                auto it = kDmsSystemStatusMap.find(line);
                if (it != kDmsSystemStatusMap.end()) {
                    dmsSystemStatus |= it->second;
                }
            }
        }
        msg.dmsStatus = dmsSystemStatus;
    };

    auto handleDmsFuncStatus = [this](NormalCanMsg &msg) {
        static const std::unordered_map<std::string, DmsFuncStatus> kDmsFuncStatusMap = {
            {"--enable_occlusion_alert=true"s, E_DMS_OCCLUSION_ENABLE},
            {"--enable_eye_occlusion_alert=true"s, E_DMS_EYE_OCCLUSION_ENABLE},
        };
        uint8_t dmsFuncStatus = msg.dmsFuncStatus; // 遮挡在sysFault中处理了
        std::ifstream src(RUN_DIR DMS_FLAG);
        while (!src.eof()) {
            std::string line;
            while (std::getline(src, line)) {
                while (line.back() == ' ') {
                    line.pop_back();
                }
                auto it = kDmsFuncStatusMap.find(line);
                if (it != kDmsFuncStatusMap.end()) {
                    dmsFuncStatus |= it->second;
                }
            }
        }
        msg.dmsFuncStatus = dmsFuncStatus;
    };

    auto handleDmsWarning = [this](NormalCanMsg &msg) {
        uint8_t dmsWarning = E_DMS_NO_WARNING;
        auto evt = mEventMap.find(EVT_TYPE_DMS_FATIGUE_Eye);
        if (evt != mEventMap.end() && evt->second.first.elapsed() < 2100) {
            dmsWarning |= E_DMS_EYE_CLOSED1_WARNING;
        }

        evt = mEventMap.find(EVT_TYPE_DMS_FATIGUE_Eye2);
        if (evt != mEventMap.end() && evt->second.first.elapsed() < 2100) {
            dmsWarning |= E_DMS_EYE_CLOSED2_WARNING;
        }

        evt = mEventMap.find(EVT_TYPE_DMS_LOOK_AROUND);
        if (evt != mEventMap.end() && evt->second.first.elapsed() < 2100) {
            dmsWarning |= E_DMS_LOOK_AROUND_WARNING;
        }

        evt = mEventMap.find(EVT_TYPE_DMS_FATIGUE_YAWN);
        if (evt != mEventMap.end() && evt->second.first.elapsed() < 2100) {
            dmsWarning |= E_DMS_YAWN_WARNING;
        }

        evt = mEventMap.find(EVT_TYPE_DMS_PHONE_CALL);
        if (evt != mEventMap.end() && evt->second.first.elapsed() < 2100) {
            dmsWarning |= E_DMS_PHONE_WARNING;
        }

        evt = mEventMap.find(EVT_TYPE_DMS_SMOKE);
        if (evt != mEventMap.end() && evt->second.first.elapsed() < 2100) {
            dmsWarning |= E_DMS_SMOKING_WARNING;
        }

        evt = mEventMap.find(EVT_TYPE_DMS_ABSENCE);
        if (evt != mEventMap.end() && evt->second.first.elapsed() < 2100) {
            dmsWarning |= E_DMS_ABSENCE_WARNING;
        }

        evt = mEventMap.find(EVT_TYPE_DMS_LOOK_DOWN);
        if (evt != mEventMap.end() && evt->second.first.elapsed() < 2100) {
            dmsWarning |= E_DMS_LOOKDOWN_WARNING;
        }

        msg.dmsWarning = dmsWarning;
    };

    constexpr auto kSendInterval = std::chrono::milliseconds(200);
    prctl(PR_SET_NAME, "NormalCan1Out");
    auto canId = -1ll;
    while (!exiting()) {
        char propValue[PROP_VALUE_MAX] = {0};
        if (__system_property_get(CAN_ID_PROP, propValue) > 0) {
            try {
                std::string canIdStr(propValue);
                auto pos = canIdStr.find('x');
                if (pos != std::string::npos) {
                    canIdStr.erase(0, pos + 1);
                    canId = std::stoll(canIdStr, nullptr, 16);
                }
            } catch (...) {
                canId = -1ll;
            }
        }
        do {
            std::lock_guard<std::mutex> _l_(mUpdateMutex);
            NormalCanMsg msg;
            if (canId != -1ll) {
                msg.frameId = canId;
            }
            handleSysFault(msg);
            handleSysStatus(msg);
            handleAdasSystemStatus(msg);
            handleBsdWarning(msg);
            handleAdasWarning(msg);
            handleDmsSystemStatus(msg);
            handleDmsFuncStatus(msg);
            handleDmsWarning(msg);

            msgEnque((void *)&msg, sizeof(msg));

        } while (0);

        std::this_thread::sleep_for(kSendInterval);
    }
}

}  // namespace minieye
