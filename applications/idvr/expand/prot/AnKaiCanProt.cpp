#include <assert.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <sys/time.h>

#include "expand.h"
#include "devicehub.h"
#include "AnKaiCanProt.h"
#include "system_properties.h"

namespace minieye
{
#define PRO_PERSISI_HAW_HDW_HCW_ENABLE                              "persist.haw.enable"            /* 三急功能使能标志位*/

#define ANKAI_ALGO_CAN_ID 0x181a06f1
#define ANKAI_SPD_CAN_ID  0x18ff304b
#define ANKAI_TURN_CAN_ID 0x18f607bd

#define ANKAI_ALGO_SPD_THRES_FOR_LEVEL 50
enum {
    ANKAI_DMS_EYE_CLOSE = 1,
    ANKAI_DMS_YAWN,
    ANKAI_DMS_SMOKING,
    ANKAI_DMS_PHONE_CALL,
    ANKAI_DMS_DISTACT,
    ANKAI_DMS_CAM_OCCLUSION,
    ANKAI_DMS_NO_BELT,
};

enum {
    ANKAI_ADAS_FCW = 1,
    ANKAI_ADAS_HW,
    ANKAI_ADAS_LDW,
    ANKAI_ADAS_PCW,
};
typedef struct {
    uint32_t  frameId;
    uint8_t   len = 8;
    uint8_t dmsEvt      = 0;
    uint8_t reserved1   = 0;
    uint8_t adasEvt     = 0;
    uint8_t warnLvl     = 0;
    uint8_t reserved2   = 0;
    uint8_t aggressiveDetLvl    = 0;
    uint8_t aggressiveDet       = 0;
    uint8_t reserved3           = 0;
} __attribute__ ((packed)) ANKAI_ALGO_CAN_PKT_T;

#define ANKAI_CAN_PROT_CMD_HELP       0
#define ANKAI_CAN_PROT_CMD_SHOW       1
#define ANKAI_CAN_PROT_CMD_TRIGGER    2
static const std::vector<CmdStrT> gAnKaiCanProtCmds = {
    {
        "help",
        ANKAI_CAN_PROT_CMD_HELP,
        "show this usage.\n"
    },
    {
        "show",
        ANKAI_CAN_PROT_CMD_SHOW,
        "show last  data.\n"
    },
    {
        "trigger",
        ANKAI_CAN_PROT_CMD_TRIGGER,
        "fake to trigger event.\n"
    },
};
AnKaiCanProt::AnKaiCanProt()
{
    AlgoManager & am = AlgoManager::getInstance();
    am.addObserver("AnKaiCanProt", this);
    bool drivingbh = true;
    char propValue[PROP_VALUE_MAX] = {0};
    if (__system_property_get(PRO_PERSISI_HAW_HDW_HCW_ENABLE, propValue) > 0) {
        drivingbh = !!atoi(propValue);
    }
    if (drivingbh) {
        mspDrvBehavior = std::make_shared<DrivingBehavior>();
        mspDrvBehavior->start();
    }
}
AnKaiCanProt::~AnKaiCanProt()
{
    AlgoManager & am = AlgoManager::getInstance();
    am.delObserver("AnKaiCanProt");
}

std::string AnKaiCanProt::setupCmdList(const char * cmdName)
{
    mCmdUsage = CmdStrT::setupCmdList(gAnKaiCanProtCmds, cmdName);
    return mCmdUsage;
}

bool AnKaiCanProt::runCmd(int argc, char **argv, string &ack)
{
    uint32_t cmd = CmdStrT::strToCmd(gAnKaiCanProtCmds, argv[0]);

    switch (cmd) {
        case ANKAI_CAN_PROT_CMD_HELP: {
                ack = mCmdUsage;
                return true;
            }

        case ANKAI_CAN_PROT_CMD_SHOW: {
                ack = "\n";

                return true;
            }
        case ANKAI_CAN_PROT_CMD_TRIGGER: {
                if (argc >= 3) {
                    DeviceHub &devHub = DeviceHub::getInstance();
                    AlgoManager & am = AlgoManager::getInstance();
                    am.triggerEvent(argv[1], argv[2], devHub.getCarSpeed());
                    return true;
                }
            }
        default: {
                ack = mCmdUsage;
            }
    }

    return false;
}


int32_t AnKaiCanProt::onServerConnected(void)
{
    start();
    return 0;
}
int32_t AnKaiCanProt::onServerDisconnected(void)
{
    stop();
    return 0;
}

int32_t AnKaiCanProt::onDataRecevied(const char *p, uint32_t len)
{
    logd("on cmd data %d", len);
    logd("recv %s", my::hex(my::constr(p, len)).c_str());

    onCmdData(mRcvArray, p, len);
    return 0;
}

int32_t AnKaiCanProt::onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len)
{

    return 0;
}

int32_t AnKaiCanProt::onDataParse(const char *p, uint32_t size)
{

    return 0;
}


void AnKaiCanProt::run()
{
    while (!exiting()) {
        DeviceHub &devHub = DeviceHub::getInstance();
        double speed = devHub.getCarSpeed();
        LBS lbs;
        devHub.getGpsLbs(lbs);
        float dir = lbs.dir_x100;
        dir /= 100;
        mspDrvBehavior->feedParam(speed, dir);
        {
            std::lock_guard<std::mutex> lock(mAlgoMsgLock);
            if (mAlgoMsgTs.elapsed() >= 1000) {
                mAlgoMsgTs = my::timestamp::now();
                ANKAI_ALGO_CAN_PKT_T f;
                f.frameId = ANKAI_ALGO_CAN_ID;
                msgEnque((void*)&f, sizeof(f));
            }
        }
        if (mSpdMsgTs.elapsed() >= 200) {
            mSpdMsgTs = my::timestamp::now();
            canFrame_t f = {ANKAI_SPD_CAN_ID, 8, {0}};
            f.data[4] = speed;
            msgEnque((void*)&f, sizeof(f));
        }
        uint8_t r = devHub.getTurnRight();
        uint8_t l = devHub.getTurnLeft();
        if ((mTurnMsgTs.elapsed() >= 1000) || (mLastTurnR != r) || (mLastTurnL != l)) {
            mTurnMsgTs = my::timestamp::now();
            mLastTurnR = r;
            mLastTurnL = l;
            canFrame_t f = {ANKAI_TURN_CAN_ID, 8, {0}};
            f.data[0] = (r << 4) | (l << 5);
            msgEnque((void*)&f, sizeof(f));
        }
        msleep(1);
    }

}

bool AnKaiCanProt::onAlgoEvent(std::shared_ptr<Event> e)
{
    DeviceHub &devHub = DeviceHub::getInstance();
    switch (e->type()) {
        case EVT_TYPE_DMS_FATIGUE:
            break;
        case EVT_TYPE_DMS_FATIGUE_Eye: {
            {
                std::lock_guard<std::mutex> lock(mAlgoMsgLock);
                mAlgoMsgTs = my::timestamp::now();
            }
            ANKAI_ALGO_CAN_PKT_T f;
            f.frameId = ANKAI_ALGO_CAN_ID;
            f.dmsEvt = ANKAI_DMS_EYE_CLOSE;
            f.warnLvl = 1 + (devHub.getCarSpeed() >= ANKAI_ALGO_SPD_THRES_FOR_LEVEL);
            msgEnque((void*)&f, sizeof(f));
            break;
        }
        case EVT_TYPE_DMS_FATIGUE_YAWN:{
            {
                std::lock_guard<std::mutex> lock(mAlgoMsgLock);
                mAlgoMsgTs = my::timestamp::now();
            }
            ANKAI_ALGO_CAN_PKT_T f;
            f.frameId = ANKAI_ALGO_CAN_ID;
            f.dmsEvt = ANKAI_DMS_YAWN;
            f.warnLvl = 1 + (devHub.getCarSpeed() >= ANKAI_ALGO_SPD_THRES_FOR_LEVEL);
            msgEnque((void*)&f, sizeof(f));
            break;
        }
        case EVT_TYPE_DMS_LOOK_AROUND:
        case EVT_TYPE_DMS_LOOK_DOWN:
        case EVT_TYPE_DMS_LOOK_UP: {
            {
                std::lock_guard<std::mutex> lock(mAlgoMsgLock);
                mAlgoMsgTs = my::timestamp::now();
            }
            ANKAI_ALGO_CAN_PKT_T f;
            f.frameId = ANKAI_ALGO_CAN_ID;
            f.dmsEvt = ANKAI_DMS_DISTACT;
            f.warnLvl = 1 + (devHub.getCarSpeed() >= ANKAI_ALGO_SPD_THRES_FOR_LEVEL);
            msgEnque((void*)&f, sizeof(f));
            break;
        }
        case EVT_TYPE_DMS_PHONE_CALL: {
            {
                std::lock_guard<std::mutex> lock(mAlgoMsgLock);
                mAlgoMsgTs = my::timestamp::now();
            }
            ANKAI_ALGO_CAN_PKT_T f;
            f.frameId = ANKAI_ALGO_CAN_ID;
            f.dmsEvt = ANKAI_DMS_PHONE_CALL;
            f.warnLvl = 1 + (devHub.getCarSpeed() >= ANKAI_ALGO_SPD_THRES_FOR_LEVEL);
            msgEnque((void*)&f, sizeof(f));
            break;
        }
        case EVT_TYPE_DMS_CAM_OCCLUSION: {
            {
                std::lock_guard<std::mutex> lock(mAlgoMsgLock);
                mAlgoMsgTs = my::timestamp::now();
            }
            ANKAI_ALGO_CAN_PKT_T f;
            f.frameId = ANKAI_ALGO_CAN_ID;
            f.dmsEvt = ANKAI_DMS_CAM_OCCLUSION;
            f.warnLvl = 1 + (devHub.getCarSpeed() >= ANKAI_ALGO_SPD_THRES_FOR_LEVEL);
            msgEnque((void*)&f, sizeof(f));
            break;
        }
        case EVT_TYPE_DMS_SMOKE:
        case EVT_TYPE_DMS_ABSENCE:
        case EVT_TYPE_DMS_EYE_OCCLUSION:
            break;
        case EVT_TYPE_DMS_NOT_BELT: {
            {
                std::lock_guard<std::mutex> lock(mAlgoMsgLock);
                mAlgoMsgTs = my::timestamp::now();
            }
            ANKAI_ALGO_CAN_PKT_T f;
            f.frameId = ANKAI_ALGO_CAN_ID;
            f.dmsEvt = ANKAI_DMS_NO_BELT;
            f.warnLvl = 1 + (devHub.getCarSpeed() >= ANKAI_ALGO_SPD_THRES_FOR_LEVEL);
            msgEnque((void*)&f, sizeof(f));
            break;
        }

        case EVT_TYPE_DMS_HANDSOFF: {
                break;
            }
        case EVT_TYPE_ADAS_LeftLDW:
        case EVT_TYPE_ADAS_RightLDW: {
            {
                std::lock_guard<std::mutex> lock(mAlgoMsgLock);
                mAlgoMsgTs = my::timestamp::now();
            }
            ANKAI_ALGO_CAN_PKT_T f;
            f.frameId = ANKAI_ALGO_CAN_ID;
            f.adasEvt = ANKAI_ADAS_LDW;
            f.warnLvl = 1 + (devHub.getCarSpeed() >= ANKAI_ALGO_SPD_THRES_FOR_LEVEL);
            msgEnque((void*)&f, sizeof(f));
            break;
        }
        case EVT_TYPE_ADAS_FCW: {
            {
                std::lock_guard<std::mutex> lock(mAlgoMsgLock);
                mAlgoMsgTs = my::timestamp::now();
            }
            ANKAI_ALGO_CAN_PKT_T f;
            f.frameId = ANKAI_ALGO_CAN_ID;
            f.adasEvt = ANKAI_ADAS_FCW;
            f.warnLvl = 1 + (devHub.getCarSpeed() >= ANKAI_ALGO_SPD_THRES_FOR_LEVEL);
            msgEnque((void*)&f, sizeof(f));
            break;
        }
        case EVT_TYPE_ADAS_HW: {
            {
                std::lock_guard<std::mutex> lock(mAlgoMsgLock);
                mAlgoMsgTs = my::timestamp::now();
            }
            ANKAI_ALGO_CAN_PKT_T f;
            f.frameId = ANKAI_ALGO_CAN_ID;
            f.adasEvt = ANKAI_ADAS_HW;
            f.warnLvl = 1 + (devHub.getCarSpeed() >= ANKAI_ALGO_SPD_THRES_FOR_LEVEL);
            msgEnque((void*)&f, sizeof(f));
            break;
        }
        case EVT_TYPE_ADAS_PCW: {
            {
                std::lock_guard<std::mutex> lock(mAlgoMsgLock);
                mAlgoMsgTs = my::timestamp::now();
            }
            ANKAI_ALGO_CAN_PKT_T f;
            f.frameId = ANKAI_ALGO_CAN_ID;
            f.adasEvt = ANKAI_ADAS_PCW;
            f.warnLvl = 1 + (devHub.getCarSpeed() >= ANKAI_ALGO_SPD_THRES_FOR_LEVEL);
            msgEnque((void*)&f, sizeof(f));
            break;
        }
        case EVT_TYPE_ADAS_CamOcclusion: {

            break;
        }

        case EVT_TYPE_ADAS_HAW:  /*急加速*/ {
            IOStatus iostat;
            devHub.getIOStatus(iostat);
            if (!iostat.acc) {
                break;
            }
            {
                std::lock_guard<std::mutex> lock(mAlgoMsgLock);
                mAlgoMsgTs = my::timestamp::now();
            }
            ANKAI_ALGO_CAN_PKT_T f;
            f.frameId = ANKAI_ALGO_CAN_ID;
            f.aggressiveDet = 1;
            f.aggressiveDetLvl = 1 + (devHub.getCarSpeed() >= ANKAI_ALGO_SPD_THRES_FOR_LEVEL);
            msgEnque((void*)&f, sizeof(f));
            break;
        }
        case EVT_TYPE_ADAS_HCW:  /*急转弯*/ {
            IOStatus iostat;
            devHub.getIOStatus(iostat);
            if (!iostat.acc) {
                break;
            }
            {
                std::lock_guard<std::mutex> lock(mAlgoMsgLock);
                mAlgoMsgTs = my::timestamp::now();
            }
            ANKAI_ALGO_CAN_PKT_T f;
            f.frameId = ANKAI_ALGO_CAN_ID;
            f.aggressiveDet = 3;
            f.aggressiveDetLvl = 1 + (devHub.getCarSpeed() >= ANKAI_ALGO_SPD_THRES_FOR_LEVEL);
            msgEnque((void*)&f, sizeof(f));
            break;
        }
        case EVT_TYPE_ADAS_HDW: /*急减速*/ {
            IOStatus iostat;
            devHub.getIOStatus(iostat);
            if (!iostat.acc) {
                break;
            }
            {
                std::lock_guard<std::mutex> lock(mAlgoMsgLock);
                mAlgoMsgTs = my::timestamp::now();
            }
            ANKAI_ALGO_CAN_PKT_T f;
            f.frameId = ANKAI_ALGO_CAN_ID;
            f.aggressiveDet = 2;
            f.aggressiveDetLvl = 1 + (devHub.getCarSpeed() >= ANKAI_ALGO_SPD_THRES_FOR_LEVEL);
            msgEnque((void*)&f, sizeof(f));
            break;
        }

        default: {
                break;
            }
    }
    return true;
}


} //namespace minieye


