#ifndef __WEIGHT_SENSOR_FUTAI_H__
#define __WEIGHT_SENSOR_FUTAI_H__

#include "cmdline.h"
#include "expand.message.h"

#define FUTAI_WEIGHT_SENSOR_MSG_MAGIC 0x5AA555AC

enum : uint8_t {
    WS_FUTAI_CMD_STAT_RPT    = 0x07,
    WS_FUTAI_CMD_STAT_RSP    = 0x08,/*应答给称重传感器*/
    WS_FUTAI_CMD_TM_SYNC_REQ = 0x09,
    WS_FUTAI_CMD_TM_SYNC_RSP = 0x0A,
    WS_FUTAI_CMD_WEIGHT_DATA = 0x0B,
    WS_FUTAI_CMD_WEIGHT_RSP  = 0x0C,
    WS_FUTAI_CMD_FAULT_CODE  = 0x0D,
    WS_FUTAI_CMD_FAULT_RSP   = 0x0E,
    WS_FUTAI_CMD_REALTIME_WEIGHT = 0x1A,

    WS_FUTAI_CMD_MAX = 255
};
typedef struct WeightSensorDataT{
    uint32_t magic = 0;
    uint16_t length = 0;
    uint8_t  sn[12];
    uint8_t  timeBcd[6];
    uint8_t  cmd = 0;
    my::constr ctx;
    uint16_t crc = 0;

    my::constr raw;
    WeightSensorDataT()
    {
        magic = FUTAI_WEIGHT_SENSOR_MSG_MAGIC;
        memset(sn, 0, sizeof(sn));
        memset(timeBcd, 0, sizeof(timeBcd));
    }
    WeightSensorDataT(WeightSensorDataT * wsd)
    {
        magic = wsd->magic;
        length = wsd->length;
        memcpy(sn, wsd->sn, sizeof(sn));
        memcpy(timeBcd, wsd->timeBcd, sizeof(timeBcd));
        cmd = wsd->cmd;
        ctx = wsd->ctx;
        crc = wsd->crc;
        raw = wsd->raw;
    }
    bool decode(my::constr & data)
    {
        if (data.length() < 27) {
            loge("No enough data");
            return false;
        }

        raw = data;
        data >> magic >> length;

        if (magic != FUTAI_WEIGHT_SENSOR_MSG_MAGIC) {
            loge("Invalid magic 0x%x", magic);
            return false;
        }

        uint16_t crcCalc = crc16((uint8_t*)(const char*)raw, raw.length() - 2);

        for (int i = 0; i < sizeof(sn); i++) {
            data >> sn[i];
        }

        for (int i = 0; i < sizeof(timeBcd); i++) {
            data >> timeBcd[i];
        }

        data >> cmd;
        ctx.assign((const char *)data, data.length() - 2);
        data += ctx.length();
        data >> crc;
        if (crc == crcCalc){
            logd("cmd = 0x%02x", cmd);
        } else {
            loge("Invalid msg!");
        }
        return (crc == crcCalc);
    }
    my::string encode(my::string & data){
        ctx.assign(data);
        length = data.length() + 21;
        my::string msg;
        msg << magic << length;
        for (int i = 0; i < sizeof (sn); i++) {
            msg << sn[i];
        }
        for (int i = 0; i < sizeof (sn); i++) {
            msg << timeBcd[i];
        }
        msg << cmd << ctx;
        uint16_t crc = crc16((uint8_t*)(const char*)msg, msg.length());
        msg << crc;
        return msg;
    }
    uint16_t crc16(uint8_t* ptr, uint16_t len)/*查表法*/
    {
        static const uint16_t crc_ccitt_table[] = {
            0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50a5, 0x60c6, 0x70e7,
            0x8108, 0x9129, 0xa14a, 0xb16b, 0xc18c, 0xd1ad, 0xe1ce, 0xf1ef,
            0x1231, 0x0210, 0x3273, 0x2252, 0x52b5, 0x4294, 0x72f7, 0x62d6,
            0x9339, 0x8318, 0xb37b, 0xa35a, 0xd3bd, 0xc39c, 0xf3ff, 0xe3de,
            0x2462, 0x3443, 0x0420, 0x1401, 0x64e6, 0x74c7, 0x44a4, 0x5485,
            0xa56a, 0xb54b, 0x8528, 0x9509, 0xe5ee, 0xf5cf, 0xc5ac, 0xd58d,
            0x3653, 0x2672, 0x1611, 0x0630, 0x76d7, 0x66f6, 0x5695, 0x46b4,
            0xb75b, 0xa77a, 0x9719, 0x8738, 0xf7df, 0xe7fe, 0xd79d, 0xc7bc,
            0x48c4, 0x58e5, 0x6886, 0x78a7, 0x0840, 0x1861, 0x2802, 0x3823,
            0xc9cc, 0xd9ed, 0xe98e, 0xf9af, 0x8948, 0x9969, 0xa90a, 0xb92b,
            0x5af5, 0x4ad4, 0x7ab7, 0x6a96, 0x1a71, 0x0a50, 0x3a33, 0x2a12,
            0xdbfd, 0xcbdc, 0xfbbf, 0xeb9e, 0x9b79, 0x8b58, 0xbb3b, 0xab1a,
            0x6ca6, 0x7c87, 0x4ce4, 0x5cc5, 0x2c22, 0x3c03, 0x0c60, 0x1c41,
            0xedae, 0xfd8f, 0xcdec, 0xddcd, 0xad2a, 0xbd0b, 0x8d68, 0x9d49,
            0x7e97, 0x6eb6, 0x5ed5, 0x4ef4, 0x3e13, 0x2e32, 0x1e51, 0x0e70,
            0xff9f, 0xefbe, 0xdfdd, 0xcffc, 0xbf1b, 0xaf3a, 0x9f59, 0x8f78,
            0x9188, 0x81a9, 0xb1ca, 0xa1eb, 0xd10c, 0xc12d, 0xf14e, 0xe16f,
            0x1080, 0x00a1, 0x30c2, 0x20e3, 0x5004, 0x4025, 0x7046, 0x6067,
            0x83b9, 0x9398, 0xa3fb, 0xb3da, 0xc33d, 0xd31c, 0xe37f, 0xf35e,
            0x02b1, 0x1290, 0x22f3, 0x32d2, 0x4235, 0x5214, 0x6277, 0x7256,
            0xb5ea, 0xa5cb, 0x95a8, 0x8589, 0xf56e, 0xe54f, 0xd52c, 0xc50d,
            0x34e2, 0x24c3, 0x14a0, 0x0481, 0x7466, 0x6447, 0x5424, 0x4405,
            0xa7db, 0xb7fa, 0x8799, 0x97b8, 0xe75f, 0xf77e, 0xc71d, 0xd73c,
            0x26d3, 0x36f2, 0x0691, 0x16b0, 0x6657, 0x7676, 0x4615, 0x5634,
            0xd94c, 0xc96d, 0xf90e, 0xe92f, 0x99c8, 0x89e9, 0xb98a, 0xa9ab,
            0x5844, 0x4865, 0x7806, 0x6827, 0x18c0, 0x08e1, 0x3882, 0x28a3,
            0xcb7d, 0xdb5c, 0xeb3f, 0xfb1e, 0x8bf9, 0x9bd8, 0xabbb, 0xbb9a,
            0x4a75, 0x5a54, 0x6a37, 0x7a16, 0x0af1, 0x1ad0, 0x2ab3, 0x3a92,
            0xfd2e, 0xed0f, 0xdd6c, 0xcd4d, 0xbdaa, 0xad8b, 0x9de8, 0x8dc9,
            0x7c26, 0x6c07, 0x5c64, 0x4c45, 0x3ca2, 0x2c83, 0x1ce0, 0x0cc1,
            0xef1f, 0xff3e, 0xcf5d, 0xdf7c, 0xaf9b, 0xbfba, 0x8fd9, 0x9ff8,
            0x6e17, 0x7e36, 0x4e55, 0x5e74, 0x2e93, 0x3eb2, 0x0ed1, 0x1ef0
        };
        uint16_t cRc_16 = 0x0000; /*初始化   */

        while ((len--) > 0) {
            cRc_16 = (cRc_16 << 8) ^ crc_ccitt_table [((cRc_16 >> 8) ^ * (ptr++)) & 0xff];
        }

        return cRc_16;
    }
} WeightSensorDataT;


/*称重传感器*/
class  WeightSensorFuTai
    : public Protocol
    , public my::BufferQueue<std::shared_ptr<WeightSensorDataT>>
    , public my::thread
{

    public:
        WeightSensorFuTai();
        ~WeightSensorFuTai();

        int32_t onServerConnected(void);
        int32_t onServerDisconnected(void);
        int32_t onDataRecevied(const char *p, uint32_t len);

        std::string setupCmdList(const char * cmdName);
        bool runCmd(int argc, char **argv, string &ack);
        bool onDataValidate(const uint8_t *p, uint32_t len);

        const char * name()
        {
            return "WeightSensorFuTai";
        }
    private:
        void run();
        int32_t onDataParse(const uint8_t *p, uint32_t size);
        int32_t onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len);
        bool onMessage(WeightSensorDataT * wsd);
    private:
        string mCmdUsage;
        vector<uint8_t>     mRcvArray;
        my::constr mCurMsg;
        expand::WeightSensorFuTaiMessage mLastData;
};

#endif
