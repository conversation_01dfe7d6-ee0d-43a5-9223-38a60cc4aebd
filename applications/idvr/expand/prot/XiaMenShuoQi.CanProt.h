#ifndef __MINIEYE_XIAMEN_SHUOQI_CAN_PROT_H__
#define __MINIEYE_XIAMEN_SHUOQI_CAN_PROT_H__

/*
    厦门硕奇CAN协议定制
*/
#include "mystd.h"
#include "cmdline.h"
#include "algo.h"
#include "canDisplay.h"
#include "adas_alert.h"
#include "algoOutputCanProt.h"
#include "devicehub.h"
#include <msgpack.hpp>

namespace minieye {

enum class AEBSSystemState : uint8_t {
    SYSTEM_NOT_READY = 0x0,                  // 系统初始化
    SYSTEM_TEMPORARILY_NOT_AVAILABLE = 0x1,  // 系统起效车速未到
    SYSTEM_DEACTIVATED_BY_DRIVER = 0x2,      // 驾驶员关闭系统
    SYSTEM_READY = 0x3,                      // 系统准备好
    DRIVER_OVERRIDES_SYSTEM = 0x4,           // 驾驶员接管系统
    COLLISION_WARNING_ACTIVE = 0x5,          // 系统一级报警——AEB预警，等级一级
    COLLISION_WARNING_WITH_BRAKING = 0x6,    // 系统二级报警（点亮制动灯）——AEB预警，等级二级
    EMERGENCY_BRAKING_ACTIVE = 0x7,          // 系统紧急制动（点亮制动灯）——AEB制动，等级一级
    // 0x8~0xD: Not Available
    ERROR_INDICATION = 0xE,  // 系统故障
    NOT_AVAILABLE = 0xF      // 不可用/未安装
};

enum class CollisionWarningLevel : uint8_t {
    NO_WARNING = 0x0,       // 无报警
    WARNING_LEVEL_1 = 0x1,  // 车辆一级报警
    WARNING_LEVEL_2 = 0x2,  // 行人一级报警
    WARNING_LEVEL_3 = 0x3,  // 车辆二级报警
    WARNING_LEVEL_4 = 0x4,  // 行人二级报警
    // 0x5~0xD: Not Available
    ERROR_INDICATION = 0xE,  // 错误指示
    NOT_AVAILABLE = 0xF      // 不可用/未安装
};

enum class RelevantObjectDetected : uint8_t {
    NO_RELEVANT_OBJECT = 0x0,            // 无相关目标监控
    RELEVANT_OBJECT_MONITORED = 0x1,     // 相关目标正在监控
    OBJECT_NOT_DETECTED_RELIABLY = 0x2,  // 相关目标检测不可靠
    // 0x3~0x5: Not Available
    ERROR_INDICATION = 0x6,  // 错误指示
    NOT_AVAILABLE = 0x7      // 不可用/未安装
};

enum class AEBSDriverActivationDemand : uint8_t {
    DRIVER_DEACTIVATION = 0x0,  // 驾驶员不希望AEBS警告或干预（系统关闭）
    DRIVER_ACTIVATION = 0x1,    // 驾驶员希望AEBS在必要时警告或干预（系统开启）
    ERROR = 0x2,                // 错误
    DONT_CARE = 0x3             // 不关心/不采取行动
};

enum class LaneDepartureStatus : uint8_t {
    NOT_IMMINENT = 0x0,     // 未偏离
    IMMINENT = 0x1,         // 即将偏离
    ERROR_INDICATOR = 0x2,  // 错误指示
    NOT_AVAILABLE = 0x3     // 不可用
};

enum class LaneTrackingStatus : uint8_t {
    NOT_TRACKING = 0x0,  // 未跟踪车道线
    TRACKING = 0x1,      // 正在跟踪车道线
    RESERVED = 0x2,      // 保留
    DONT_CARE = 0x3      // 不关心/不采取行动
};

enum class LDWSEnableStatus : uint8_t {
    DISABLED = 0x0,  // 车道偏离指示功能关闭
    ENABLED = 0x1,   // 车道偏离指示功能开启
    RESERVED = 0x2,  // 保留
    NOT_USED = 0x3   // 未使用
};

enum class AmberWarningLampStatus : uint8_t {
    LAMP_OFF = 0x0,      // 灯灭
    LAMP_ON = 0x1,       // 灯亮
    RESERVED = 0x2,      // 保留
    NOT_AVAILABLE = 0x3  // 不可用
};

enum class AEBCollisionRiskTrigEDR : uint8_t {
    NO_TRIG_MODE = 0x0,  // AEB碰撞风险无触发模式
    TRIG_MODE = 0x1,     // AEB碰撞风险触发模式
    RESERVED = 0x2,      // 保留
    LDW_FCW_MODE = 0x3   // LDW & FCW模式
};

enum class ASREngCtrlStatus : uint8_t {
    PASSIVE_BUT_INSTALLED = 0x0,  // ASR发动机控制被动但已安装
    ACTIVE = 0x1,                 // ASR发动机控制激活
    RESERVED = 0x2,               // 保留
    NOT_AVAILABLE = 0x3           // 不可用
};

enum class ASRBrakeCtrlStatus : uint8_t {
    PASSIVE_BUT_INSTALLED = 0x0,  // ASR制动控制被动但已安装
    ACTIVE = 0x1,                 // ASR制动控制激活
    RESERVED = 0x2,               // 保留
    NOT_AVAILABLE = 0x3           // 不可用
};

enum class ABSActiveStatus : uint8_t {
    PASSIVE_BUT_INSTALLED = 0x0,  // ABS被动但已安装
    ACTIVE = 0x1,                 // ABS激活
    RESERVED = 0x2,               // 保留
    NOT_AVAILABLE = 0x3           // 不可用
};

enum class BrakeSwitchStatus : uint8_t {
    NOT_PRESSED = 0x0,   // 制动踏板未被踩下
    PRESSED = 0x1,       // 制动踏板被踩下
    ERROR = 0x2,         // 错误
    NOT_AVAILABLE = 0x3  // 不可用
};

enum class ABSOperationalStatus : uint8_t {
    NOT_FULLY_OPERATIONAL = 0x0,  // 非全功能
    FULLY_OPERATIONAL = 0x1,      // 全功能
    RESERVED = 0x2,               // 保留
    NOT_AVAILABLE = 0x3           // 不可用
};

enum class ABSEBSWarningSignal : uint8_t {
    OFF = 0x0,            // 关闭
    ON = 0x1,             // 开启
    RESERVED = 0x2,       // 保留
    TAKE_NO_ACTION = 0x3  // 不采取行动
};

enum class EBSRedWarningState : uint8_t {
    OFF = 0x0,            // 关闭
    ON = 0x1,             // 开启
    RESERVED = 0x2,       // 保留
    TAKE_NO_ACTION = 0x3  // 不采取行动
};

enum class TurnSignalSwitchStatus : uint8_t {
    NO_TURN_SIGNAL = 0x0,       // 无转向信号
    LEFT_TURN_FLASHING = 0x1,   // 左转闪烁
    RIGHT_TURN_FLASHING = 0x2,  // 右转闪烁
    // 0x3~0xD: Reserved
    ERROR = 0xE,         // 错误
    NOT_AVAILABLE = 0xF  // 不可用
};

enum class UltrasonicDistanceStatus : uint8_t {
    NO_DISTANCE = 0x0,  // 没有距离
    // 0x1~0xFA: 对应检测距离（距离 = 值 * 2cm）
    INVALID_1 = 0xFB,                 // 无效
    INVALID_2 = 0xFC,                 // 无效
    RADAR_INTERMITTENT_FAULT = 0xFD,  // 雷达偶发性故障
    RADAR_FAULT = 0xFE,               // 雷达故障
    INVALID_3 = 0xFF                  // 无效
};

// 报警/事件相关枚举定义

// 标志状态枚举
enum class AlarmFlagStatus : uint8_t {
    NOT_AVAILABLE = 0x00,  // 不可用
    START_FLAG = 0x01,     // 开始标志
    END_FLAG = 0x02        // 结束标志
};

// 报警/事件类型枚举
enum class AlarmEventType : uint8_t {
    NO_WARNING = 0x00,                         // 无报警（生成附件自用）
    AEB_WARNING = 0x01,                        // AEB预警
    AEB_BRAKING = 0x02,                        // AEB制动（前向主动制动）
    SHARP_STEERING_ALARM = 0x03,               // 急打方向盘报警
    SHARP_BRAKING_ALARM = 0x04,                // 急踩刹车报警
    SHARP_ACCELERATION_ALARM = 0x05,           // 急踩油门报警
    ACCELERATOR_MISPRESS_ALARM = 0x06,         // 油门防误踩报警
    LANE_DEPARTURE_WARNING = 0x07,             // 车道偏离预警（AEB）
    FOLLOWING_DISTANCE_TOO_CLOSE = 0x08,       // 跟车距离过近预警（AEB）
    PEDESTRIAN_COLLISION_WARNING = 0x09,       // 行人碰撞预警（AEB）
    VEHICLE_COLLISION_WARNING = 0x0A,          // 前车碰撞预警（AEB）
    FORWARD_BRAKING_FUNCTION_OFF = 0x0B,       // 前向制动功能关闭（AEB）
    DISTANCE_KEEPING = 0x0C,                   // 车距保持（AEB）
    PEDESTRIAN_COLLISION_BRAKING = 0x0D,       // 行人碰撞制动（AEB）（预留）
    VEHICLE_COLLISION_BRAKING = 0x0E,          // 前车碰撞制动（AEB）（预留）
    DRIVER_POSITION_DEPARTURE_BRAKING = 0x0F,  // 偏离驾驶位制动报警(AEB)
    LONG_TIME_EYES_CLOSED_BRAKING = 0x10,      // 长时间闭眼制动报警(AEB)
    HANDS_OFF_STEERING_BRAKING = 0x11,         // 双手脱离方向盘制动报警(AEB)
    LEFT_BLIND_SPOT_BRAKING = 0x31,            // 左侧盲区制动报警（预留）
    RIGHT_BLIND_SPOT_BRAKING = 0x32,           // 右侧盲区制动报警
    FRONT_BLIND_SPOT_BRAKING = 0x33,           // 前侧盲区制动报警（预留）
    REAR_BLIND_SPOT_BRAKING = 0x34             // 后侧盲区制动报警（预留）
};

// 报警级别/制动级别枚举
enum class AlarmLevel : uint8_t {
    LEVEL_0 = 0x00,  // 无报警，生成附件自用
    LEVEL_1 = 0x01,  // 一级报警/制动
    LEVEL_2 = 0x02   // 二级报警/制动
};

// 目标障碍物类型枚举
enum class TargetObstacleType : uint8_t {
    INVALID = 0,     // 无效值
    VEHICLE = 1,     // 车辆
    PEDESTRIAN = 2,  // 人
    OTHER_3 = 3,     // 其他
    OTHER_4 = 4,     // 其他
    OTHER_5 = 5,     // 其他
    OTHER_6 = 6,     // 其他
    OTHER_7 = 7,     // 其他
    OTHER_8 = 8,     // 其他
    OTHER_9 = 9,     // 其他
    OTHER_10 = 10,   // 其他
    OTHER_11 = 11    // 其他
};

// 前向碰撞预警等级枚举
enum class ForwardCollisionWarningLevel : uint8_t {
    NO_WARNING = 0,       // 否
    LEVEL_1_WARNING = 1,  // 一级预警
    LEVEL_2_WARNING = 2   // 二级预警
};

// 车道线偏离预警枚举
enum class LaneDepartureWarning : uint8_t {
    NO_WARNING = 0,             // 无预警
    LEFT_BOUNDARY_WARNING = 1,  // 左边界预警
    RIGHT_BOUNDARY_WARNING = 2  // 右边界预警
};

// 车道线类型枚举
enum class LaneLineType : uint8_t {
    NONE = 0,                // 无
    PREDICTED = 1,           // 预测
    DASHED_LINE = 2,         // 虚线
    SOLID_LINE = 3,          // 实线
    DOUBLE_DASHED_LINE = 4,  // 双虚线
    DOUBLE_SOLID_LINE = 5,   // 双实线
    TRIPLE_LINE = 6,         // 三线
    RESERVED_7 = 7,          // 保留
    RESERVED_8 = 8,          // 保留
    RESERVED_9 = 9,          // 保留
    RESERVED_10 = 10,        // 保留
    RESERVED_11 = 11,        // 保留
    RESERVED_12 = 12,        // 保留
    RESERVED_13 = 13,        // 保留
    RESERVED_14 = 14,        // 保留
    RESERVED_15 = 15         // 保留
};

// AEB制动状态枚举
enum class AEBBrakingStatus : uint8_t {
    NO_BRAKING = 0,                      // 无制动
    BINOCULAR_BRAKING = 1,               // 双目制动
    MILLIMETER_WAVE_BRAKING = 2,         // 毫米波制动
    ULTRASONIC_BRAKING = 3,              // 超声波制动
    MONOCULAR_RADAR_FUSION_BRAKING = 4,  // 单目雷达融合制动
    INVALID = 0xFF                       // 无效
};

// 方向盘状态枚举
enum class SteeringWheelStatus : uint8_t {
    TURN_LEFT = 0,      // 向左打
    TURN_RIGHT = 1,     // 向右打
    RETURN_CENTER = 2,  // 回正
    INVALID = 0xFF      // 无效
};

// 档位状态枚举
enum class GearStatus : uint8_t {
    INVALID = 0,  // 无效
    NEUTRAL = 1,  // N档
    DRIVE = 2,    // D档
    REVERSE = 3   // R档
};

// AEBS1 (0x0CF02FA0)
typedef struct AEBS1CanPkt_S {
    uint32_t frameId = 0x0CF02FA0;
    uint8_t len = 8;

    uint8_t advanced_emergency_braking_system_state : 4 = (uint8_t)AEBSSystemState::SYSTEM_NOT_READY;  // 系统当前状态
    uint8_t collision_warning_level                 : 4 = (uint8_t)CollisionWarningLevel::NO_WARNING;  // 碰撞报警等级

    uint8_t relevant_object_detected : 3 = (uint8_t)RelevantObjectDetected::NO_RELEVANT_OBJECT;  // AEBS监控相关目标
    uint8_t r1                       : 5 = 0;

    uint8_t time_to_collision = 0;  // 碰撞时间TTC，精度0.05

    uint8_t r3_7[5] = {0};
} __attribute__((packed)) AEBS1CanPkt_S;

// AEBS2_27 (0x0C0BA027)
typedef struct AEBS2_27CanPkt_S {
    uint32_t frameId = 0x0C0BA027;
    uint8_t len = 8;

    uint8_t driver_activation_demand : 2 = (uint8_t)AEBSDriverActivationDemand::DRIVER_ACTIVATION;  // AEBS开关使能
    uint8_t r0                       : 6 = 0;

    uint8_t r1_7[7] = {0};
} __attribute__((packed)) AEBS2_27CanPkt_S;

// FLI1 (0x10F007E8)
typedef struct FLI1CanPkt_S {
    uint32_t frameId = 0x10F007E8;
    uint8_t len = 8;

    uint8_t r0                                 : 4 = 0;
    uint8_t lane_departure_imminent_right_side : 2 = (uint8_t)LaneDepartureStatus::NOT_IMMINENT;  // 右边界预警
    uint8_t lane_departure_imminent_left_side  : 2 = (uint8_t)LaneDepartureStatus::NOT_IMMINENT;  // 左边界预警

    uint8_t r1_7[7] = {0};
} __attribute__((packed)) FLI1CanPkt_S;

// FLI2 (0x18FE5BE8)
typedef struct FLI2CanPkt_S {
    uint32_t frameId = 0x18FE5BE8;
    uint8_t len = 8;

    uint8_t r0                              : 2 = 0;
    uint8_t lane_tracking_status_right_side : 2 = (uint8_t)LaneTrackingStatus::DONT_CARE;      // 是否检测到右边车道线
    uint8_t lane_tracking_status_left_side  : 2 = (uint8_t)LaneTrackingStatus::DONT_CARE;      // 是否检测到左边车道线
    uint8_t lane_departure_indication_enable_status : 2 = (uint8_t)LDWSEnableStatus::ENABLED;  // LDWS功能开启状态

    uint8_t r1_7[7] = {0};
} __attribute__((packed)) FLI2CanPkt_S;

// WEDR (0x0CF0EEA0)
typedef struct WEDRCanPkt_S {
    uint32_t frameId = 0x0CF0EEA0;
    uint8_t len = 8;

    uint8_t advanced_emergency_braking_system_state : 4 = (uint8_t)AEBSSystemState::SYSTEM_NOT_READY;  // 系统当前状态
    uint8_t r0                                      : 4 = 0;

    uint8_t lane_departure_indication_enable_status : 2 = (uint8_t)LDWSEnableStatus::NOT_USED;    // LDWS功能开启状态
    uint8_t lane_departure_imminent_right_side : 2 = (uint8_t)LaneDepartureStatus::NOT_IMMINENT;  // 车辆已向右偏离车道
    uint8_t lane_departure_imminent_left_side  : 2 = (uint8_t)LaneDepartureStatus::NOT_IMMINENT;  // 车辆已向左偏离车道
    uint8_t r1                                 : 2 = 0;

    uint8_t distance_to_main_target_ahead = 0;           // 前方主要目标距离
    uint8_t relative_velocity_to_main_target_ahead = 0;  // 前方主要目标相对速度

    uint8_t amber_warning_lamp_status_ldw : 2 = (uint8_t)AmberWarningLampStatus::LAMP_OFF;  // LDW故障灯
    uint8_t r2                            : 2 = 0;
    uint8_t amber_warning_lamp_status_fcw : 2 = (uint8_t)AmberWarningLampStatus::LAMP_OFF;  // FCW故障灯
    uint8_t r3                            : 2 = 0;

    uint8_t r4 = 0;

    uint8_t aeb_collision_risk_trig_edr : 2 = (uint8_t)AEBCollisionRiskTrigEDR::LDW_FCW_MODE;  // AEB碰撞风险触发EDR
    uint8_t less_than_20_second_after_last_aeb_trig : 1 = 0;                                   // 距离上次AEB触发小于20s
    uint8_t r5                                      : 5 = 0;

    uint8_t r6 = 0;
} __attribute__((packed)) WEDRCanPkt_S;

// EBC1 (0x18F0010B)
typedef struct EBC1CanPkt_S {
    uint32_t frameId = 0x18F0010B;
    uint8_t len = 8;

    uint8_t asr_eng_ctrl_active      : 2 = (uint8_t)ASREngCtrlStatus::PASSIVE_BUT_INSTALLED;    // ASR发动机控制激活
    uint8_t asr_brake_ctrl_active    : 2 = (uint8_t)ASRBrakeCtrlStatus::PASSIVE_BUT_INSTALLED;  // ASR制动控制激活
    uint8_t anti_lock_braking_active : 2 = (uint8_t)ABSActiveStatus::PASSIVE_BUT_INSTALLED;     // ABS激活
    uint8_t ebs_brake_switch         : 2 = (uint8_t)BrakeSwitchStatus::NOT_PRESSED;             // 制动开关状态

    uint8_t ebs_brake_pedal_position = 0;  // 制动踏板位置

    uint8_t r2_4[3] = {0};

    uint8_t abs_fully_operational        : 2 = (uint8_t)ABSOperationalStatus::FULLY_OPERATIONAL;  // ABS全功能信号
    uint8_t ebs_red_warning_state        : 2 = (uint8_t)EBSRedWarningState::OFF;                  // EBS红灯状态
    uint8_t abs_ebs_amber_warning_signal : 2 = (uint8_t)ABSEBSWarningSignal::OFF;                 // ABS故障灯
    uint8_t r5                           : 2 = 0;

    uint8_t r6_7[2] = {0};
} __attribute__((packed)) EBC1CanPkt_S;

// EBC2 (0x18FEBF0B)
typedef struct EBC2CanPkt_S {
    uint32_t frameId = 0x18FEBF0B;
    uint8_t len = 8;

    uint16_t mean_front_axle_speed = 0;  // 前轴速

    uint8_t r2_7[6] = {0};
} __attribute__((packed)) EBC2CanPkt_S;

// VDC2 (0x18F0090B)
typedef struct VDC2CanPkt_S {
    uint32_t frameId = 0x18F0090B;
    uint8_t len = 8;

    uint16_t steer_wheel_angle = 0xFFFF;  // 方向盘转角
    uint8_t r2 = 0;
    uint16_t yaw_rate = 0xFFFF;                // 横摆角速度
    uint16_t lateral_acceleration = 0xFFFF;    // 横向加速度
    uint8_t longitudinal_acceleration = 0xFF;  // 纵向加速度
} __attribute__((packed)) VDC2CanPkt_S;

// EEC2 (0xCF00300)
typedef struct EEC2CanPkt_S {
    uint32_t frameId = 0xCF00300;
    uint8_t len = 8;

    uint8_t r0 = 0;
    uint8_t accel_pedal_position1 = 0;  // 加速踏板百分比

    uint8_t r2_7[6] = {0};
} __attribute__((packed)) EEC2CanPkt_S;

// ETC2 (0x18F00503)
typedef struct ETC2CanPkt_S {
    uint32_t frameId = 0x18F00503;
    uint8_t len = 8;

    uint8_t r0_2[3] = {0};
    uint8_t current_gear = 0xFF;  // 当前档位信号

    uint8_t r4_7[4] = {0};
} __attribute__((packed)) ETC2CanPkt_S;

// OEL_27 (0x0CFDCC27)
typedef struct OEL_27CanPkt_S {
    uint32_t frameId = 0x0CFDCC27;
    uint8_t len = 8;

    uint8_t r0 = 0;
    uint8_t turn_signal_switch : 4 = (uint8_t)TurnSignalSwitchStatus::NO_TURN_SIGNAL;  // 转向开关状态
    uint8_t r1                 : 4 = 0;

    uint8_t r2_3[2] = {0};

    uint8_t r4                            : 4 = 0;
    uint8_t amber_warning_lamp_status_fcw : 2 = (uint8_t)AmberWarningLampStatus::LAMP_OFF;  // FCW故障灯
    uint8_t r5                            : 2 = 0;

    uint8_t r6                            : 1 = 0;
    uint8_t dm1_2a_spn_conversion_method  : 1 = 0;  // 没有描述
    uint8_t r7                            : 6 = 0;

    uint8_t r8_9[2] = {0};
} __attribute__((packed)) OEL_27CanPkt_S;

// UDAS (0x0CF901B2)
typedef struct UDASCanPkt_S {
    uint32_t frameId = 0x0CF901B2;
    uint8_t len = 8;

    uint8_t ra = (uint8_t)UltrasonicDistanceStatus::NO_DISTANCE;  // 后右
    uint8_t rb = (uint8_t)UltrasonicDistanceStatus::NO_DISTANCE;  // 后右中
    uint8_t rc = (uint8_t)UltrasonicDistanceStatus::NO_DISTANCE;  // 后左中
    uint8_t rd = (uint8_t)UltrasonicDistanceStatus::NO_DISTANCE;  // 后左
    uint8_t fa = (uint8_t)UltrasonicDistanceStatus::NO_DISTANCE;  // 前左
    uint8_t fb = (uint8_t)UltrasonicDistanceStatus::NO_DISTANCE;  // 前左中
    uint8_t fc = (uint8_t)UltrasonicDistanceStatus::NO_DISTANCE;  // 前右中
    uint8_t fd = (uint8_t)UltrasonicDistanceStatus::NO_DISTANCE;  // 前右
} __attribute__((packed)) UDASCanPkt_S;

// 报警/事件数据结构体
typedef struct AlarmEventData_S {
    uint32_t alarm_id = 0;                                                // 报警ID
    uint8_t flag_status = (uint8_t)AlarmFlagStatus::NOT_AVAILABLE;        // 标志状态
    uint8_t alarm_event_type = (uint8_t)AlarmEventType::AEB_WARNING;      // 报警/事件类型
    uint8_t alarm_level = (uint8_t)AlarmLevel::LEVEL_1;                   // 报警级别/制动级别
    uint8_t target_speed = 0xFF;                                          // 目标车速 0xFF=不可用
    uint8_t target_obstacle_type = (uint8_t)TargetObstacleType::INVALID;  // 目标障碍物类型
    uint8_t ttc_collision_time = 0;                                       // TTC碰撞时间 单位100ms
    uint16_t longitudinal_relative_distance = 0;                          // 纵向相对距离 单位0.1m
    uint16_t lateral_relative_distance = 0;                               // 横向相对距离 单位0.1m
    uint8_t ultrasonic_distance = 0;                                      // 超声波距离 单位分米
    int8_t longitudinal_relative_velocity = 0;                            // 纵向相对速度 单位m/s
    uint8_t forward_collision_warning_level = (uint8_t)ForwardCollisionWarningLevel::NO_WARNING;  // 前向碰撞预警等级
    uint8_t lane_departure_warning = (uint8_t)LaneDepartureWarning::NO_WARNING;                   // 车道线偏离预警
    uint8_t left_lane_line_type = (uint8_t)LaneLineType::NONE;                                    // 左车道线类型
    uint8_t right_lane_line_type = (uint8_t)LaneLineType::NONE;                                   // 右车道线类型
    uint8_t device_status_fault_code = 0;                                                         // 设备状态故障码
    uint8_t aeb_brake_switch_status = 0xFF;                                 // AEB制动开关状态 0xFF=无效
    uint8_t aeb_brake_status = (uint8_t)AEBBrakingStatus::INVALID;          // AEB制动状态
    uint16_t steering_wheel_angle = 0xFFFF;                                 // 方向盘角度 0xFFFF=无效
    uint8_t steering_wheel_status = (uint8_t)SteeringWheelStatus::INVALID;  // 方向盘状态
    uint8_t gear_status = (uint8_t)GearStatus::INVALID;                     // 档位状态
    uint8_t accelerator_pedal_opening = 0xFF;                               // 油门踏板开度 0xFF=无效
    uint8_t brake_pedal_opening = 0xFF;                                     // 刹车踏板开度 0xFF=无效
    uint8_t vehicle_speed = 0;                                              // 车速 单位km/h
    uint16_t altitude = 0;                                                  // 高程 单位m
    uint32_t latitude = 0;                                                  // 纬度 度*10^6
    uint32_t longitude = 0;                                                 // 经度 度*10^6
    uint8_t date_time[6] = {0};                                             // 日期时间 (BCD[6]) YY-MM-DD-hh-mm-ss
    uint16_t vehicle_status = 0;                                            // 车辆状态
    uint8_t alarm_identification[16] = {0};                                 // 报警标识号

    // msgpack 序列化宏
    MSGPACK_DEFINE(alarm_id, flag_status, alarm_event_type, alarm_level,
                   target_speed, target_obstacle_type, ttc_collision_time,
                   longitudinal_relative_distance, lateral_relative_distance,
                   ultrasonic_distance, longitudinal_relative_velocity,
                   forward_collision_warning_level, lane_departure_warning,
                   left_lane_line_type, right_lane_line_type, device_status_fault_code,
                   aeb_brake_switch_status, aeb_brake_status, steering_wheel_angle,
                   steering_wheel_status, gear_status, accelerator_pedal_opening,
                   brake_pedal_opening, vehicle_speed, altitude, latitude, longitude,
                   date_time, vehicle_status, alarm_identification);
} AlarmEventData_S;

class XiaMenShuoQiCanProt : public Protocol, my::thread {
 public:
    XiaMenShuoQiCanProt();
    ~XiaMenShuoQiCanProt();

 protected:
    virtual int32_t onServerConnected(void) override;
    virtual int32_t onServerDisconnected(void) override;
    virtual int32_t onDataRecevied(const char *p, uint32_t len) override;
    virtual std::string setupCmdList(const char * cmdName) override;
    virtual bool runCmd(int argc, char **argv, std::string &ack) override;
    virtual void run() override;

 private:
    // CAN消息接收处理函数
    bool onCanMessageReceived(canFrame_t *canFrame);

    // CAN消息解析函数
    bool parseAEBS1Message(canFrame_t *canFrame);
    bool parseAEBS2_27Message(canFrame_t *canFrame);
    bool parseFLI1Message(canFrame_t *canFrame);
    bool parseFLI2Message(canFrame_t *canFrame);
    bool parseWEDRMessage(canFrame_t *canFrame);
    bool parseEBC1Message(canFrame_t *canFrame);
    bool parseEBC2Message(canFrame_t *canFrame);
    bool parseVDC2Message(canFrame_t *canFrame);
    bool parseEEC2Message(canFrame_t *canFrame);
    bool parseETC2Message(canFrame_t *canFrame);
    bool parseOEL_27Message(canFrame_t *canFrame);
    bool parseUDASMessage(canFrame_t *canFrame);

    void addRealTimeData(LBS lbs);
    void updateGpsData(LBS lbs);

 private:
    // CAN协议数据成员变量
    AEBS1CanPkt_S mAEBS1Pkt;
    AEBS2_27CanPkt_S mAEBS2_27Pkt;
    FLI1CanPkt_S mFLI1Pkt;
    FLI2CanPkt_S mFLI2Pkt;
    WEDRCanPkt_S mWEDRPkt;
    EBC1CanPkt_S mEBC1Pkt;
    EBC2CanPkt_S mEBC2Pkt;
    VDC2CanPkt_S mVDC2Pkt;
    EEC2CanPkt_S mEEC2Pkt;
    ETC2CanPkt_S mETC2Pkt;
    OEL_27CanPkt_S mOEL_27Pkt;
    UDASCanPkt_S mUDASPkt;

    // 报警/事件数据
    AlarmEventData_S mAlarmEventData;
    uint32_t mAlarmid = 0;
    std::list<std::string> list;

 private:
    std::mutex mEvtRcdMapMtx;

    void report();
};

};  // namespace minieye

#endif