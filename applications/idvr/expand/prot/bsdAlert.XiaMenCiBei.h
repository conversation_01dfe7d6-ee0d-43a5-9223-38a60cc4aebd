#ifndef _BSDALERT_XIAMENCIBEI_H_
#define _BSDALERT_XIAMENCIBEI_H_

#include "cmdline.h"
#include "FileLog.h"
#include "expand.message.h"
#include "algo.h"

namespace minieye {

class BsdAlertXiaMenCiBei : public Protocol, my::thread, public IALGO_OBSERVER {
 public:
    enum E_DEVICE_TYPE {
        E_DEVICE_ALERTOR = 0,
        E_DEVICE_PROMPTER = 1
    };

    enum E_TARGET_DEVICE {
        E_DEVICE_LEFT = 0,
        E_DEVICE_RIGHT = 1,
        E_DEVICE_MAX = 2
    };

    typedef struct AlertorDevInfo {
        uint8_t addr;
        uint8_t baudrate;
        uint8_t alertMode;
        my::timestamp lastAlertTs;

    } AlertorDevInfoT;

    typedef struct PrompterDevInfo {
        uint8_t addr;
        uint8_t baudrate;
        uint8_t alertMode;
        my::timestamp lastRefreshTs;

    } PrompterDevInfoT;

    BsdAlertXiaMenCiBei();
    ~BsdAlertXiaMenCiBei() = default;

 protected:
    virtual bool onAlgoEvent(std::shared_ptr<Event> evt);

 protected:
    virtual int32_t onServerConnected(void);
    virtual int32_t onServerDisconnected(void);
    virtual int32_t onDataRecevied(const char *p, uint32_t len);
    virtual std::string setupCmdList(const char *cmdName);
    virtual bool runCmd(int argc, char **argv, string &ack);
    virtual void run();

 private:
    int32_t onCmdData(vector<uint8_t> &recvArray, const char *p, uint32_t len);
    int32_t onDataParseAMsg(const char *p, uint32_t size);
    bool onMessage(uint8_t *p, uint32_t len);

 private:
    std::string mCmdUsage;
    std::vector<uint8_t> mRcvArray;
    time_t mLastDataUtcTime = 0;
    my::timestamp mHWAlarmTs = 0;

    std::mutex mSendCmdMtx;
    std::condition_variable mSendCmdCv;
    my::timestamp mLastAlertorCmdTs;
    my::timestamp mLastPrompterCmdTs;
    std::shared_ptr<Event> mLastBsdEvt[E_DEVICE_MAX];
    E_DEVICE_TYPE mNextTriggerDev[E_DEVICE_MAX];

    std::mutex mAlertorDevInfoMtx;
    AlertorDevInfoT mAlertorDevInfo[E_DEVICE_MAX] = {{0x01, 0x05, 0x00}, {0x02, 0x05, 0x00}};

    std::mutex mPrompterDevInfoMtx;
    PrompterDevInfoT mPrompterDevInfo[E_DEVICE_MAX] = {{0x11, 0x05, 0x00}, {0x11, 0x05, 0x00}};  // AVL终端地址

    FileLog *mpFileLog = nullptr;
};

}  // namespace minieye

#endif
