
#include "AlinkAlertor.RongSheng.h"

#include <assert.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <numeric>

#include "devicehub.h"
#include "expand.h"
#include "system_properties.h"

#define ALINK_ALERTOR_VOLUME_PROT "rw.minieye.alink_alertor.vol"
#define ALINK_ALERTOR_ALARM_MODE "persist.minieye.rs.alarm.mode" // BASIC（默认）:启动bsd； ADVANCE:启动转向，倒车，bsd
#define BSD_CAM_ID_PROT "rw.objp2bsd.right"
#define BSD_CAM_ID_VALUE "2"

namespace minieye {
// --- Alink Alertor protocol begin ---

#define ALERTOR_PROT_MAGIC (0x7E)

struct AlinkAlertor::AlinkMsg {
    uint8_t magicBgn = ALERTOR_PROT_MAGIC;
    uint16_t msgId = 0xF001;      // 报警id，固定
    uint8_t cmd = 0x1;            // 0x1:设置报警等级，0x2:解除报警
    uint16_t sequenceNumber = 0;  // 未使用
    uint8_t crc = 0;              // 未使用
    uint8_t volume = 0x8;         // 音量，默认8
    uint8_t playMode = 0x0;       // 播放次数，0为一直播放
    uint8_t playContent = 0x1;    // 播放内容
    uint8_t ledFreq = 0x2;
    uint8_t magicEnd = ALERTOR_PROT_MAGIC;

    int32_t encode(my::string &msg);
    // int32_t decode(uint8_t *msg, int32_t len);
    // static bool onValidate(uint8_t *p, int32_t len);
} AlinkMsg;

int32_t AlinkAlertor::AlinkMsg::encode(my::string &msg) {
    msg << my::hton;
    msg << magicBgn;
    msg << msgId << cmd << sequenceNumber << crc << volume << playMode << playContent << ledFreq;
    msg << magicEnd;
    return msg.length();
}

// --- Alink Alertor protocol end ---

AlinkAlertor::AlinkAlertor()
/* , mIpcClient(make_shared<IpcClient>("expand", "/mnt/obb/alink_alertor"))*/ {
    AlgoManager &am = AlgoManager::getInstance();
    char tmp[256];
    snprintf(tmp, sizeof(tmp), "AlinkAlertor%p", this);
    am.addObserver(tmp, this);

    char propValue[64];
    if (__system_property_get(ALINK_ALERTOR_VOLUME_PROT, propValue) <= 0) {
        __system_property_set(ALINK_ALERTOR_VOLUME_PROT, "8");
    }
    __system_property_set(BSD_CAM_ID_PROT, BSD_CAM_ID_VALUE);
    start();
}
AlinkAlertor::~AlinkAlertor() noexcept {
    AlgoManager &am = AlgoManager::getInstance();
    char tmp[256];
    snprintf(tmp, sizeof(tmp), "AlinkAlertor%p", this);
    am.delObserver(tmp);
}
int32_t AlinkAlertor::onServerConnected(void) { return 0; }
int32_t AlinkAlertor::onServerDisconnected(void) { return 0; }

int32_t AlinkAlertor::onDataRecevied(const char *p, uint32_t len) {
    // logd("on cmd data %d", len);
    // logd("recv %s", my::hex(my::constr(p, len)).c_str());

    return 0;
}

bool AlinkAlertor::onAlgoEvent(std::shared_ptr<Event> e) {
    switch (e->type()) {
        case EVT_TYPE_BSD_Left: 
        case EVT_TYPE_BSD_Right: {
            if (e->c.level != 3) {
                mLastBsdAlarmTs = my::timestamp::now();
            }
            break;
        }
        default: {
            break;
        }
    }
    return true;
}

#define BSD_ALERT_CMD_HELP 0
#define BSD_ALERT_CMD_SHOW 1
#define BSD_ALERT_CMD_SET_ALERTOR_MODE 2

static const std::vector<CmdStrT> gBsdAlertCmds = {{"help", BSD_ALERT_CMD_HELP, "show this usage.\n"},
                                                   {"alertor", BSD_ALERT_CMD_SET_ALERTOR_MODE, "set alertor mode.\n"}};

std::string AlinkAlertor::setupCmdList(const char *cmdName) {
    mCmdUsage = CmdStrT::setupCmdList(gBsdAlertCmds, cmdName);
    return mCmdUsage;
}
bool AlinkAlertor::runCmd(int argc, char **argv, string &ack) {
    uint32_t cmd = CmdStrT::strToCmd(gBsdAlertCmds, argv[0]);

    switch (cmd) {
        case BSD_ALERT_CMD_HELP: {
            ack = mCmdUsage;
            return true;
        }
        case BSD_ALERT_CMD_SET_ALERTOR_MODE: {
            ack = "\n";
            return true;
        }
        default: {
            ack = mCmdUsage;
        }
    }

    return false;
}

void AlinkAlertor::run() {
    prctl(PR_SET_NAME, "AlinkAlertor");
    auto lastPlayContent = E_CONTENT_NONE;
    // bool updateMcuMsgWorking = true;

    auto getVolume = []() {
        char propValue[64];
        if (__system_property_get(ALINK_ALERTOR_VOLUME_PROT, propValue) > 0) {
            return atoi(propValue);
        }
        return 8;
    };
    // auto updateMcuMsg = [this, &updateMcuMsgWorking]() {
    //     uint8_t buf[MCU_MSG_MAX_SIZE];
    //     McuMessage *pMsg = (McuMessage *)&buf[0];

    //     while (updateMcuMsgWorking) {
    //         if (mIpcClient->recv(&(pMsg->ipcMsg))) {
    //             switch (pMsg->ipcMsg.type) {
    //                 case MCU_MSG_TYPE_CAR_INFO: {
    //                     if (MCU_MSG_SIZE_CAR_INFO == pMsg->ipcMsg.len) {
    //                         McuMsgCarInfoT &carInfo = pMsg->u.carInfo[0];
    //                         mIsReverseOn = carInfo.canTurn;
    //                         mIsTurnOn = carInfo.canReverse;
    //                     }
    //                 } break;
    //                 default: {
    //                     // loge("unknown mcu msg type %d, len %d", msg->type, msg->len);
    //                     break;
    //                 }
    //             }
    //         } else {
    //             loge("mcu client read fail!\n");
    //         }
    //     }
    // };
    // auto updateMcuMsgThread = std::thread(updateMcuMsg);

    while (!exiting()) {
        auto playContent = E_CONTENT_NONE;
        if (mLastBsdAlarmTs.elapsed() <= 2000) {
            playContent = E_BSD_ALERT;
        } else {
            char propValue[PROP_VALUE_MAX] = {0};
            if (__system_property_get(ALINK_ALERTOR_ALARM_MODE, propValue) > 0) {
                if (strcmp(propValue, "advance") == 0) {
                    auto &dh = DeviceHub::getInstance();
                    if (dh.getReverse() != 0) {
                        playContent = E_REVERSE;
                    } else if (dh.getTurnLeft() != 0) {
                        playContent = E_TURN_LEFT;
                    } else if (dh.getTurnRight() != 0) {
                        playContent = E_TURN_RIGHT;
                    } else {
                        playContent = E_CONTENT_NONE;
                    }
                }
            }
        }

        if (mLastAlertUpdateTs.elapsed() >= 2000 || playContent != lastPlayContent) {
            AlinkMsg msg;
            msg.cmd = (playContent == E_CONTENT_NONE ? 0x02 : 0x01);
            msg.playContent = playContent;
            msg.volume = getVolume();

            my::string payload;
            msg.encode(payload);
            msgEnque((void *)payload.c_str(), payload.length());

            mLastAlertUpdateTs = my::timestamp::now();
            lastPlayContent = playContent;
            logi("set alink alertor cmd %d, content %d, volume %d", msg.cmd, playContent, msg.volume);
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
    // updateMcuMsgWorking = false;
    // updateMcuMsgThread.join();
}

}  // namespace minieye
