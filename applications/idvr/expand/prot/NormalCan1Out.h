#pragma once

#include "cmdline.h"
#include "FileLog.h"
#include "expand.message.h"
#include "algo.h"

namespace minieye {

class NormalCan1Out : public Protocol, my::thread, public IALGO_OBSERVER {
 public:
    enum SystemFault : uint8_t {
        E_SYSTEM_NO_ERROR = 0x0,
        E_SYSTEM_ADAS_CAMERA_BLOCK = 0x1,
        E_SYSTEM_BSD_CAMERA_BLOCK = 0x2,
        E_SYSTEM_DMS_CAMERA_ERROR = 0x4,
    };

    enum SystemStatus : uint8_t {
        E_SYSTEM_OFF = 0x00,
        E_SYSTEM_ON = 0x01,
        E_RIGHT_BSD_ACTIVE = 0x02,
        E_ADAS_ACTIVE = 0x04,
        E_DMS_ACTIVE = 0x08,
    };

    enum AdasSystemStatus : uint8_t {
        E_ADAS_DISABLED = 0x00,
        E_FCW_ENABLE = 0x01,
        E_UFCW_ENABLE = 0x02,
        E_HMW_ENABLE = 0x04,
        E_PCW_ENABLE = 0x08,
        E_LDW_ENABLE = 0x10,
        E_SLI_ENABLE = 0x20,
    };

    enum BsdWarningStatus : uint8_t {
        E_BSD_NO_WARNING = 0x00,
        E_BSD_WARNING_L1 = 0x01,
        E_BSD_WARNING_L2 = 0x02,
        E_BSD_WARNING_L3 = 0x04,
    };

    enum FcwWarningStatus : uint8_t {
        E_FCW_NO_WARNING = 0x00,
        E_FCW_WARNING = 0x01,
    };

    enum UfcwWarningStatus : uint8_t {
        E_UFCW_NO_WARNING = 0x00,
        E_UFCW_WARNING = 0x01,
    };

    enum HhwWarningStatus : uint8_t {
        E_HMW_NO_WARNING = 0x00,
        E_HMW_WARNING = 0x01,
    };

    enum PcwWarningStatus : uint8_t {
        E_PCW_NO_WARNING = 0x00,
        E_PCW_WARNING = 0x01,
    };

    enum LdwWarningStatus : uint8_t {
        E_LDW_NO_WARNING = 0x00,
        E_LEFT_LDW_WARNING = 0x01,
        E_RIGHT_LDW_WARNING = 0x02,
    };

    enum SliWarningStatus : uint8_t {
        E_SLI_NO_WARNING = 0x00,
        E_SLI_WARNING_L1 = 0x01,
        E_SLI_WARNING_L2 = 0x02,
        E_SLI_WARNING_L3 = 0x04,
        E_SLI_WARNING_L4 = 0x08,
        E_SLI_WARNING_L5 = 0x10,
        E_SLI_WARNING_L6 = 0x20,
        E_SLI_WARNING_L7 = 0x40,
    };

    enum DmsSystemStatus : uint8_t {
        E_DMS_DISABLED = 0x00,
        E_DMS_EYE_CLOSED1_ENABLE = 0x01,
        E_DMS_EYE_CLOSED2_ENABLE = 0x02,
        E_DMS_LOOK_AROUND_ENABLE = 0x04,
        E_DMS_YAWN_ENABLE = 0x08,
        E_DMS_PHONE_ENABLE = 0x10,
        E_DMS_SMOKING_ENABLE = 0x20,
        E_DMS_ABSENCE_ENABLE = 0x40,
        E_DMS_LOOKDOWN_ENABLE = 0x80,
    };

    enum DmsFuncStatus : uint8_t {
        // E_DMS_DISABLED = 0x00,
        E_DMS_OCCLUSION_ENABLE = 0x01,
        E_DMS_EYE_OCCLUSION_ENABLE = 0x02,
        E_DMS_OCCLUSION_WARNING = 0x10,
        E_DMS_EYE_OCCLUSION_WARNING = 0x20,
    };

    enum DmsWarningStatus : uint8_t {
        E_DMS_NO_WARNING = 0x00,
        E_DMS_EYE_CLOSED1_WARNING = 0x01,
        E_DMS_EYE_CLOSED2_WARNING = 0x02,
        E_DMS_LOOK_AROUND_WARNING = 0x04,
        E_DMS_YAWN_WARNING = 0x08,
        E_DMS_PHONE_WARNING = 0x10,
        E_DMS_SMOKING_WARNING = 0x20,
        E_DMS_ABSENCE_WARNING = 0x40,
        E_DMS_LOOKDOWN_WARNING = 0x80,
    };
    NormalCan1Out();
    ~NormalCan1Out() = default;

 protected:
    virtual bool onAlgoEvent(std::shared_ptr<Event> evt);

    virtual int32_t onServerConnected(void);
    virtual int32_t onServerDisconnected(void);
    virtual int32_t onDataRecevied(const char *p, uint32_t len);
    virtual std::string setupCmdList(const char *cmdName);
    virtual bool runCmd(int argc, char **argv, string &ack);
    virtual void run();

 private:
    int32_t onCmdData(std::vector<uint8_t> &recvArray, const char *p, uint32_t len);
    bool checkFlag();
    bool checkAdasFlag();
    bool checkDmsFlag();

    std::string mCmdUsage;
    std::vector<uint8_t> mRcvArray;

    std::mutex mUpdateMutex;
    std::map<EVT_TYPE, std::pair<my::timestamp, std::shared_ptr<Event>>> mEventMap;
};

}  // namespace minieye

