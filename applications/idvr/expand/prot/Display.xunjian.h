#ifndef __DISP_XUNJIAN_H__
#define __DISP_XUNJIAN_H__

#include "protocol.h"
#include "nmea/nmea.h"
#include "McuMessage.h"
#include "FileLog.h"
#include "canDisplay.h"

namespace minieye {


enum SysState
{
    STATE_XUNJIAN,
    STATE_SPEEDING,
    STATE_LOCATE,
    STATE_NetCONNECT,
    STATE_SYSERROR,
    STATE_MAX
};

enum XinJianStateType
{
    E_XUNJIAN_STOP = 0,
    E_XUNJIAN_START
};

enum LocateStateType
{
    E_RTK_STATE_4_5 = 0,
    E_RTK_STATE_0_6,
    E_RTK_STATE_1_2,
};

enum NetConnectStateType
{
    E_PLATFORM_NOT_CONNECTED_NOT_4G = 0,
    E_PLATFORM_CONNECTED,
    E_PLATFORM_NOT_CONNECTED_HAVE_4G,
};

enum SpeedingType
{
    E_NO_OVERSPEED,
    E_OVERSPEED
};

enum ErrStateType
{
    E0_SYSTEM_NOT_ERROR,
    E1_SYSTEM_ERROR,
    E2_VOL_TOO_LOW,
    E3_RTK_LOCATE_ERR,
    E4_CAMERA_ERROR,
    E5_MEM_CARD_ERROR,
    E6_RECORD_ERROR
};

class DisplayXunjian: public CanDisplay, public my::thread
{
public:
    DisplayXunjian();
    ~DisplayXunjian();

    int32_t onServerConnected(void);
    int32_t onServerDisconnected(void);
    int32_t onDataRecevied(const char *p, uint32_t len);
    std::string setupCmdList(const char *cmdName) { return "DisplayXunjian";}
    bool runCmd(int argc, char **argv, string &ack);
    void run();

    void monitorxunjian();
    void monitorLocateState();
    void monitorPlatformConnectState();
    void monitorSpeedingState();
    void monitorErrState();

    void displayErrState();
    void displaySpeedingState();
    void displayPlatformConnectState();
    void displayLocateState();
    void displayxunjian();

    bool scanSystemError();
    bool scanVolError();
    bool scanRtkLocateError();
    bool scanCameraError();
    bool scanSDcardError();
    bool scanRecordError();
private:
    int32_t onDataParse(const char *p, uint32_t size);
private:


    float               mSpdThres;
    // monitor status
    uint8_t             mStateSet[STATE_MAX];
    // 标志位
    bool                mCtrlRightLineFilp;
    bool                mCtrlLeftLineFilp;
    uint8_t             mErrIndex;
    my::timestamp       mLeftTimeStamp;
    my::timestamp       mRightTimeStamp;
    my::timestamp       mErrMonitorTimeStamp;
};
}
#endif
