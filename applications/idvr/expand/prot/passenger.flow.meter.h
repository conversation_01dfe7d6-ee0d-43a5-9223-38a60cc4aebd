#ifndef __PASSENGER_FLOW_METER_H__
#define __PASSENGER_FLOW_METER_H__

#include "cmdline.h"
#include "FileLog.h"
#include "expand.message.h"

namespace minieye
{
typedef struct ChipInfo
{
    char    chipId[11] = {0};/*10*/
    char    devId[7] = {0};/*6*/
    uint8_t slaveId = 0;
    uint32_t pcbVersion = 0;
    uint32_t sysVersion = 0;
    uint32_t appVersion = 0;
    uint32_t iniVersion = 0;
    uint32_t algoVersion = 0;
    uint32_t modVersion = 0;
    uint32_t qttVersion = 0;
    char     platId[9] = {0}; /*8*/
    char     undefine[28] = {0};/*27*/

    bool decode(my::constr & msg)
    {
        try {
            for (int i = 0; i < sizeof(chipId) - 1; i++) {
                msg >> chipId[i];
            }
            for (int i = 0; i < sizeof(devId) - 1; i++) {
                msg >> devId[i];
            }
            msg >> slaveId >> pcbVersion >> sysVersion >> appVersion;
            msg >> iniVersion >> algoVersion >> modVersion >> qttVersion;
            for (int i = 0; i < sizeof(platId) - 1; i++) {
                msg >> platId[i];
            }
            for (int i = 0; i < sizeof(undefine) - 1; i++) {
                msg >> undefine[i];
            }
        } catch (std::exception & e) {
            loge("decode ChipInfo fail!");
            return false;
        }
        return true;
    }
}ChipInfoT;
typedef struct XE100
{
    uint8_t chipNum;
    std::vector<ChipInfoT> chipInfo;
    bool decode(uint8_t * data, uint32_t len)
    {
        my::constr msg((const char *)data, len);
        msg >> my::ntoh >> chipNum;
        logd("chipNum %d, left %d", chipNum, msg.length());
        int32_t count = 0;
        while (msg.length() > 0) {
            ChipInfoT ci;
            if (ci.decode(msg)) {
                chipInfo.push_back(ci);
                count++;
            } else {
                loge("XE100 decode fail! left %d bytes", msg.length());
                break;
            }
            logd("count %d, left %d", count, msg.length());
        }
        return (count == chipNum);
    }
} XE100_T;

class PassengerFlowMeter
    : public Protocol
    , my::thread
{
    public:
        PassengerFlowMeter();
        ~PassengerFlowMeter() {};
        /*libflow recv*/
        virtual void recv(const char* source,  // '\0' terminated string
                    const char* topic,   // any binary data
                    const char* data,    // any binary data
                    size_t size);
    protected:
        virtual int32_t onServerConnected(void);
        virtual int32_t onServerDisconnected(void);
        virtual int32_t onDataRecevied(const char *p, uint32_t len);
        virtual std::string setupCmdList(const char * cmdName);
        virtual bool runCmd(int argc, char **argv, string &ack);
        virtual void run();
    private:
        bool onEscape(uint8_t *p, uint32_t len, std::vector<uint8_t> &v);
        bool onUnEscape(uint8_t *p, uint32_t len, std::vector<uint8_t> &v);
        bool onValidate(uint8_t *p, uint32_t len);

        int32_t onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len);
        int32_t onDataParse(const char *p, uint32_t size);
        bool onMessage(uint8_t *p, uint32_t len);

    private:
        std::string mCmdUsage;
        std::vector<uint8_t> mEscape = {0x7E, 0x7D, 0x02, 0x01};
        std::vector<uint8_t>     mRcvArray;
        time_t mLastDataUtcTime = 0;

        std::mutex mSndCmdBufMtx;
        std::map<int32_t/*cmd*/, std::pair<my::string, my::timestamp>> mSndCmdBuf;

        std::mutex  mDevinfoMtx;
        XE100_T     mDevInfo;

        FileLog * mpFileLog = nullptr;

        uint8_t mLastDoorBits = 0;
};

}


#endif

