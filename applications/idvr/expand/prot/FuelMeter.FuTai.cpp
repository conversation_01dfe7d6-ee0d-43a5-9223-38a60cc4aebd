
#include <assert.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <sys/time.h>

#include "FuelMeter.FuTai.h"
#include "expand.h"

namespace minieye
{
#define FUEL_METER_MAGIC_STR "VT"
#define FUEL_METER_MAGIC *((uint16_t*)FUEL_METER_MAGIC_STR)
typedef struct {
    float fuelHeightCm;
    float tempC;
    int   statCount;
    float lastFuelHeightCm;
    float echoCount;/*超声波回波个数*/
} UltrasonicStatT;
typedef struct {
    float fuelPercent;/*百分比？？？？？*/
    int   B;
    int   C;
    float fuelPercent2;
} AnologStatT;
typedef struct FuelMeterSensorData {
    uint16_t  magic = 0; /*FUEL_METER_MAGIC*/
    int32_t   type = -1; /*1是超声波，2是模拟量*/
    union {
        UltrasonicStatT u;
        AnologStatT     a;
    } u;
    my::constr raw;

    int decode(my::constr & data, int   aCoef)
    {
        raw = data;
        data >> magic;

        if (magic != FUEL_METER_MAGIC) {
            loge("magic is invalid !");
            return -1;
        }
        data += 1;/*skip the first ','*/
        std::vector<my::constr> tbl = data.split(',', false);
        type = atoi((const char *)tbl[4]);

        if (type) {
            u.u.fuelHeightCm = atof((const char *)tbl[0]) * 0.01;
            u.u.tempC = atof((const char *)tbl[1]);
            u.u.statCount = atoi((const char *)tbl[2]);
            u.u.lastFuelHeightCm = atof((const char *)tbl[3]) * 0.01;
            u.u.echoCount = atoi((const char *)tbl[5]);
            logd("tempC %f, fuelHeightCm %f, lastFuelHeightCm %f", u.u.tempC, u.u.fuelHeightCm, u.u.lastFuelHeightCm);

        } else {
            u.a.fuelPercent = atof((const char *)tbl[0]) * aCoef / 3100;
            u.a.B = atoi((const char *)tbl[1]);
            u.a.C = atoi((const char *)tbl[2]);
            u.a.fuelPercent2 = atof((const char *)tbl[3]) * aCoef / 3100;
        }

        return 0;
    }
} FuelMeterSensorDataT;

#define FUEL_CMD_HELP       0
#define FUEL_CMD_SHOW       1
#define FUEL_CMD_QUERY   2
static const std::vector<CmdStrT> gFuelMeterCmds = {
    {
        "help",
        FUEL_CMD_HELP,
        "show this usage.\n"
    },
    {
        "show",
        FUEL_CMD_SHOW,
        "show last FUEL data.\n"
    },
    {
        "query",
        FUEL_CMD_QUERY,
        "query FUEL data.\n"
    },
};

std::string FuelMeterFuTai::setupCmdList(const char * cmdName)
{
    mCmdUsage = CmdStrT::setupCmdList(gFuelMeterCmds, cmdName);
    return mCmdUsage;
}

bool FuelMeterFuTai::runCmd(int argc, char **argv, string &ack)
{
    uint32_t cmd = CmdStrT::strToCmd(gFuelMeterCmds, argv[0]);

    switch (cmd) {
        case FUEL_CMD_HELP: {
                ack = mCmdUsage;
                return true;
            }

        case FUEL_CMD_SHOW: {
                ack = "\n";
                APPEND_STR_MSG(ack, "Msg UTC", 16, "%ld", mLastData.mUtcTime);
                APPEND_STR_MSG(ack, "type", 16, "type %d",
                               mLastData.type);

                return true;
            }
        case FUEL_CMD_QUERY: {
                std::string msg = "*VT";
                msgEnque((void *)msg.c_str(), msg.length());
                return true;
            }
        default: {
                ack = mCmdUsage;
            }
    }

    return false;
}


int32_t FuelMeterFuTai::onServerConnected(void)
{
    start();
    return 0;
}
int32_t FuelMeterFuTai::onServerDisconnected(void)
{
    stop();
    return 0;
}

int32_t FuelMeterFuTai::onDataRecevied(const char *p, uint32_t len)
{
    logd("on cmd data %d", len);
    logd("recv %s", my::hex(my::constr(p, len)).c_str());

    onCmdData(mRcvArray, p, len);
    return 0;
}

int32_t FuelMeterFuTai::onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len)
{
    recvArray.insert(recvArray.end(), p, p + len);

    int32_t parsed = 0;

    do {
        parsed = onDataParse((const char *)recvArray.data(), recvArray.size());
        //assert(parsed <= recvArray.size());

        if (recvArray.size() >= (1 << 20)) {
            parsed = recvArray.size();
            logd("frame too long!!\n");
        }

        if (parsed < 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + recvArray.size());
            parsed = 0;
            logd("frame no header, erase all!!\n");

        } else if (parsed > 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + parsed);
            //logd("erase size %d\n", parsed);

        } else {
            //logd("alertor frame not complete!!\n");
        }

        if (recvArray.size() <= 0 || parsed <= 0) {
            break;
        }

    } while (1);

    return parsed;
}

int32_t FuelMeterFuTai::onDataParse(const char *p, uint32_t size)
{
    int32_t start = -1;
    int32_t end = -1;
    uint32_t offset = 0;

    for (int i = 0; i < static_cast<int32_t>(size); ++i) {
        if (i + sizeof(FUEL_METER_MAGIC) > size) {
            //logd("no found %d", i);
            break;
        }

        if (start >= 0 && (FUEL_METER_MAGIC == *((uint16_t*)(p + i)))) {
            //logd("end %d", i);
            end = i - 1;
            break;
        }

        if (FUEL_METER_MAGIC == *((uint16_t*)(p + i))) {
            //logd("start %d", i);
            start = i;
        }
    }

    if (start >= 0) {
        if (end < 0) {
            return start;
        }

        const char * tmp = (const char*)(p + start);
        int len = end - start + 1;

        FuelMeterSensorDataT fmsd;
        mCurMsg = my::constr(tmp, len);
        int32_t eaten = mCurMsg.length();

        if (fmsd.decode(mCurMsg, mCoef) < 0) {
            loge("fuel meter decode fail!");
            return -1;

        } else {
            logd("fuelmeter data eaten size = %d\n", eaten);
        }

        if (eaten > 0) {
            logd("enter onDataValidate");
            onMessage(&fmsd);

        } else {
            logd("Invalid data");
            return -1;
        }

    } else {
        //logd("******start =  %d, end = %d\n", start, end);
        return -1;
    }

    return end + 1;
}

bool FuelMeterFuTai::onMessage(FuelMeterSensorData * msg)
{
    expand::FuelMeterSensorMessage message;
    message.mUtcTime = my::timestamp::utc_milliseconds();

    message.type = msg->type;
    message.fuelHeight = msg->u.u.fuelHeightCm;
    message.raw = std::string((const char *)msg->raw, msg->raw.length());

    msgpack::sbuffer  sbuf;
    msgpack::pack(sbuf, message);
    ExpandSet::getInstance().sendLibFlow(FUEL_LIBFLOW_RAW_TOPIC, sbuf.data(), sbuf.size());
    return true;
}

void FuelMeterFuTai::run()
{
    my::timestamp lastQueryTime;
    while (!exiting()) {
        if (lastQueryTime.elapsed() >= 5000) {
            std::string msg = "*VT";
            msgEnque((void *)msg.c_str(), msg.length());
            lastQueryTime = my::timestamp::now();
        }
        msleep(1000);
    }

}




} //namespace minieye

