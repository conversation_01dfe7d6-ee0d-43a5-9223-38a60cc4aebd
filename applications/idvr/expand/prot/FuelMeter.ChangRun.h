
#ifndef __MINIEYE_FUEL_METER_CHANGRUN_H__
#define __MINIEYE_FUEL_METER_CHANGRUN_H__
/*
    长润油耗设备协议（中至智控）
*/
#include "cmdline.h"
#include "expand.message.h"
namespace minieye
{
struct FuelMeterSensorData;

class FuelMeterChangRun
    : public Protocol
    , public my::thread
{
    public:
        FuelMeterChangRun() {};
        ~FuelMeterChangRun() {};

        int32_t onServerConnected(void);
        int32_t onServerDisconnected(void);
        int32_t onDataRecevied(const char *p, uint32_t len);
        std::string setupCmdList(const char * cmdName);
        bool runCmd(int argc, char **argv, string &ack);

    private:
        void run();
        int32_t onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len);
        int32_t onDataParse(const char *p, uint32_t size);
        bool onMessage(FuelMeterSensorData * msg);


    private:
        string mCmdUsage;
        vector<uint8_t>     mRcvArray;

        my::constr           mCurMsg;
        expand::FuelMeterSensorMessage mLastData;
        my::timestamp   mFuelHeightUpdateTs;
        int mDataStat = 0;

        std::mutex      mRFHMtx;
        my::timestamp   mRecentFuelHeightTs;
        float           mRecentFuelHeight = 0;
};

}


#endif
