
#ifndef __MINIEYE_FUEL_METER_FUTAI_H__
#define __MINIEYE_FUEL_METER_FUTAI_H__
/*
    油耗设备协议V2.2
*/
#include "cmdline.h"
#include "expand.message.h"
namespace minieye
{
struct FuelMeterSensorData;

class FuelMeterFuTai
    : public Protocol
    , public my::thread
{
    public:
        FuelMeterFuTai() {};
        ~FuelMeterFuTai() {};

        int32_t onServerConnected(void);
        int32_t onServerDisconnected(void);
        int32_t onDataRecevied(const char *p, uint32_t len);
        std::string setupCmdList(const char * cmdName);
        bool runCmd(int argc, char **argv, string &ack);

    private:
        void run();
        int32_t onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len);
        int32_t onDataParse(const char *p, uint32_t size);
        bool onMessage(FuelMeterSensorData * msg);


    private:
        string mCmdUsage;
        vector<uint8_t>     mRcvArray;

        my::constr           mCurMsg;
        expand::FuelMeterSensorMessage mLastData;
        int mCoef = 1.0;
        int mDataStat = 0;
};

}


#endif
