
#include <assert.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <sys/time.h>

#include "tpms.haoyue.h"
#include "expand.h"

namespace minieye
{



int32_t TpmsHaoyue::onServerConnected(void)
{
    return 0;
}
int32_t TpmsHaoyue::onServerDisconnected(void)
{
    return 0;
}

int32_t TpmsHaoyue::onDataRecevied(const char *p, uint32_t len)
{
    logd("on cmd data %d", len);
    logd("recv %s", my::hex(my::constr(p, len)).c_str());

    onCmdData(mRcvArray, p, len);
    return 0;
}

int32_t TpmsHaoyue::onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len)
{
    recvArray.insert(recvArray.end(), p, p + len);

    int32_t parsed = 0;

    do {
        parsed = onDataParse((const char *)recvArray.data(), recvArray.size());
        assert(parsed <= recvArray.size());

        if (recvArray.size() >= (1 << 20)) {
            parsed = recvArray.size();
            logd("frame too long!!\n");
        }

        if (parsed < 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + recvArray.size());
            parsed = 0;
            logd("frame no header, erase all!!\n");

        } else if (parsed > 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + parsed);
            //logd("erase size %d\n", parsed);

        } else {
            //logd("alertor frame not complete!!\n");
        }

        if (recvArray.size() <= 0 || parsed <= 0) {
            break;
        }

    } while (1);

    return parsed;
}

int32_t TpmsHaoyue::onDataParse(const char *p, uint32_t size)
{
#define NEED_MORE_DATA      (0)
#define DATA_INVALID        (-1)
    uint32_t offset = 0;

    do {
        TPMSHaoyueMsgT tpmsMsg;
        int32_t eaten = tpmsMsg.decode((uint8_t *)&p[offset], size);
        logd("tpms eaten size = %d\n", eaten);

        if (eaten > 0) {
            if (eaten > sizeof(TyreSensorDataHeadT)) {
                onMessage(&tpmsMsg);

            } else {
                logd("%s\n", my::hex(my::constr((char *)&p[offset], eaten)).c_str());
            }

            size -= eaten;
            offset += eaten;

        } else {
            break;
        }
    } while (size > 0);

    if (offset) {
        return offset;

    } else {
        return NEED_MORE_DATA;
    }
}

bool TpmsHaoyue::onMessage(TPMSHaoyueMsgT * msg)
{
    expand::TpmsSensorMessage message;
    double temp, tmp1, tmp2 = 0.0;
    message.tyreNum = msg->sensorDataHead.tyreNum;
    message.tempHiThrs = msg->sensorDataHead.tempHiThrs - 55;
    message.pressHiThrs = (float)(msg->sensorDataHead.pressHiThrs - 1) * 2.75;
    message.pressLoThrs = (float)(msg->sensorDataHead.pressLoThrs - 1) * 2.75;
#if 0
    logd("TPMS : tyreNum %d, threshold <tempHi %d, pressHi %f, pressLo %f>",
         msg->sensorDataHead.tyreNum, msg->sensorDataHead.tempHiThrs, msg->sensorDataHead.pressHiThrs, msg->sensorDataHead.pressLoThrs);

    for (auto it : msg->tyreDataTbl) {
        logd("\t | Tyre#%d : press %d", it.sensorIdx, it.tyrePressure);
    }

    for (auto it : msg->tyreDataTbl) {
        logd("\t | Tyre#%d : temp  %d", it.sensorIdx, it.tyreTemperature);
    }

#endif

    for (auto it : msg->tyreDataTbl) {
        message.tyrePressure.insert(std::pair<int, float>(it.sensorIdx - 1, (float)(it.tyrePressure - 1) * 2.75));
        message.tyreTemperature.insert(std::pair<int, float>(it.sensorIdx - 1, it.tyreTemperature - 55));
    }

#if 0
    logd("TPMS : UTC %" FMT_LLD "", message.mUtcTime);
    logd("TPMS : tyreNum %d, threshold <tempHi %d, pressHi %f, pressLo %f>",
         message.tyreNum, message.tempHiThrs, message.pressHiThrs, message.pressLoThrs);

    for (auto it : message.tyrePressure) {
        logd("\t | Tyre#%d : press %f", it.first, it.second);
    }

    for (auto it : message.tyreTemperature) {
        logd("\t | Tyre#%d : temp  %f", it.first, it.second);
    }

#endif
    message.mUtcTime = my::timestamp::utc_milliseconds();
    mLastMsg = message;

    if (mTempHiThrs) {
        message.tempHiThrs = mTempHiThrs;
    }

    if (mPressHiThrs) {
        message.pressHiThrs = mPressHiThrs;
    }

    if (mPressLoThrs) {
        message.pressLoThrs = mPressLoThrs;
    }

    msgpack::sbuffer  sbuf;
    msgpack::pack(sbuf, message);
    ExpandSet::getInstance().sendLibFlow(TPMS_LIBFLOW_TOPIC, sbuf.data(), sbuf.size());
    return true;
}
#define TPMS_CMD_HELP       0
#define TPMS_CMD_SHOW       1
#define TPMS_CMD_SET_THRS   2
#define TPMS_CMD_FAKE       3
static const std::vector<CmdStrT> gTpmsCmds = {
    {
        "help",
        TPMS_CMD_HELP,
        "show this usage.\n"
    },
    {
        "show",
        TPMS_CMD_SHOW,
        "show last TPMS data.\n"
    },
    {
        "set",
        TPMS_CMD_SET_THRS,
        "set fake param for test. [tempHi pressHi pressLo]\n"
    },
    {
        "fake",
        TPMS_CMD_FAKE,
        "set fake data for test, [idx] press\n"
    }
};

std::string TpmsHaoyue::setupCmdList(const char * cmdName)
{
    mCmdUsage = CmdStrT::setupCmdList(gTpmsCmds, cmdName);
    return mCmdUsage;
}

bool TpmsHaoyue::runCmd(int argc, char **argv, string &ack)
{
    uint32_t cmd = CmdStrT::strToCmd(gTpmsCmds, argv[0]);

    switch (cmd) {
        case TPMS_CMD_HELP: {
                ack = mCmdUsage;
                return true;
            }

        case TPMS_CMD_SHOW: {
                ack = "\n";
                APPEND_STR_MSG(ack, "Msg UTC", 16, "%" FMT_LLD "", mLastMsg.mUtcTime);
                APPEND_STR_MSG(ack, "TPMS", 16, "tyreNum %d, threshold <tempHi %d, pressHi %f, pressLo %f>",
                               mLastMsg.tyreNum, mLastMsg.tempHiThrs, mLastMsg.pressHiThrs, mLastMsg.pressLoThrs);
                APPEND_STR_MSG(ack, "mTempHiThrs", 16, "%d", mTempHiThrs);
                APPEND_STR_MSG(ack, "mPressHiThrs", 16, "%f", mPressHiThrs);
                APPEND_STR_MSG(ack, "mPressLoThrs", 16, "%f", mPressLoThrs);

                for (auto it : mLastMsg.tyrePressure) {
                    APPEND_STR_MSG(ack, "\t", 16, "Tyre#%d : press %f Kpa", it.first, it.second);
                }

                for (auto it : mLastMsg.tyreTemperature) {
                    APPEND_STR_MSG(ack, "\t", 16, "Tyre#%d : temp  %f ℃", it.first, it.second);
                }
                if (argc >= 2 && !strcmp(argv[1], "send")) {
                    mLastMsg.mUtcTime = my::timestamp::utc_milliseconds();
                    logd("send time %" FMT_LLD, mLastMsg.mUtcTime);
                    msgpack::sbuffer  sbuf;
                    msgpack::pack(sbuf, mLastMsg);
                    ExpandSet::getInstance().sendLibFlow(TPMS_LIBFLOW_TOPIC, sbuf.data(), sbuf.size());
                }
                return true;
            }

        case TPMS_CMD_SET_THRS: {
                ack = "\n";

                if (argc >= 4) {
                    mTempHiThrs  = atoi(argv[1]);
                    mPressHiThrs = atof(argv[2]);
                    mPressLoThrs = atof(argv[3]);
                    APPEND_STR_MSG(ack, "mTempHiThrs",  16, "%d", mTempHiThrs);
                    APPEND_STR_MSG(ack, "mPressHiThrs", 16, "%f", mPressHiThrs);
                    APPEND_STR_MSG(ack, "mPressLoThrs", 16, "%f", mPressLoThrs);
                    if (mTempHiThrs) {
                        mLastMsg.tempHiThrs = mTempHiThrs;
                    }
                    
                    if (mPressHiThrs) {
                        mLastMsg.pressHiThrs = mPressHiThrs;
                    }
                    
                    if (mPressLoThrs) {
                        mLastMsg.pressLoThrs = mPressLoThrs;
                    }

                    return true;

                } else {
                    ack += "not enough param!\n";
                }

                break;
            }
        case TPMS_CMD_FAKE: {
            ack = "\n";
            if (argc >=3) {
                int idx = atoi(argv[1]);
                float press = atof(argv[2]);
                mLastMsg.tyreNum = 6;
                if (idx < 0) {
                    for (int i = 0; i < mLastMsg.tyreNum; i++) {
                        mLastMsg.tyrePressure[i] = press;
                        mLastMsg.tyreTemperature[i] = 46.0 + 0.2 * ((i % 2) ? -1 : 1);
                    }
                } else if (idx < mLastMsg.tyreNum) {
                    mLastMsg.tyrePressure[idx] = press;
                }
                return true;
            } else {
                ack += "not enough param!\n";
            }
            break;
        }
        default: {
                ack = mCmdUsage;
            }
    }

    return false;
}


} //namespace minieye

