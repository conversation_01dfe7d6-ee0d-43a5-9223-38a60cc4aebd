#ifndef __MINIEYE_ANKAI_CAN_PROT_H__
#define __MINIEYE_ANKAI_CAN_PROT_H__
/*
    安凯CAN协议定制
*/
#include "mystd.h"
#include "cmdline.h"
#include "algo.h"
#include "drivingBeHavior.h"

namespace minieye
{

class AnKaiCanProt
    : public Protocol
    , public IALGO_OBSERVER
    , public my::thread
{
    public:
        AnKaiCanProt();
        ~AnKaiCanProt();

        int32_t onServerConnected(void);
        int32_t onServerDisconnected(void);
        int32_t onDataRecevied(const char *p, uint32_t len);
        std::string setupCmdList(const char * cmdName);
        bool runCmd(int argc, char **argv, string &ack);
    protected:
        virtual bool onAlgoEvent(std::shared_ptr<Event> evt);
    private:
        void run();
        int32_t onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len);
        int32_t onDataParse(const char *p, uint32_t size);


    private:
        string mCmdUsage;
        vector<uint8_t>     mRcvArray;

        std::mutex    mAlgoMsgLock;
        my::timestamp mAlgoMsgTs;
        my::timestamp mSpdMsgTs;
        my::timestamp mTurnMsgTs;
        uint8_t mLastTurnR = 0;
        uint8_t mLastTurnL = 0;

        std::shared_ptr<DrivingBehavior> mspDrvBehavior;
};
};
#endif
