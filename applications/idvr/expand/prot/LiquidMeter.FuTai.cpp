
#include <assert.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <sys/time.h>

#include "LiquidMeter.FuTai.h"
#include "expand.h"

namespace minieye
{

LiquidMeterFutai::LiquidMeterFutai()
{
#if 0
    uint32_t val = 0;
    char value[PROP_VALUE_MAX];
    memset(value, 0, sizeof(value));
    int len = __system_property_get(LIQUID_METER_PROP_BAUD, value);

    if (len > 0) {
        val = atoi(value);
        setBaud(val);
    }

#endif
}
int32_t LiquidMeterFutai::onServerConnected(void)
{
    return 0;
}
int32_t LiquidMeterFutai::onServerDisconnected(void)
{
    return 0;
}

int32_t LiquidMeterFutai::onDataRecevied(const char *p, uint32_t len)
{
    logd("on cmd data %d", len);
    logd("recv %s", my::hex(my::constr(p, len)).c_str());

    onCmdData(mRcvArray, p, len);
    return 0;
}

int32_t LiquidMeterFutai::onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len)
{
    recvArray.insert(recvArray.end(), p, p + len);

    int32_t parsed = 0;

    do {
        parsed = onDataParse((const char *)recvArray.data(), recvArray.size());

        if (recvArray.size() >= (1 << 20)) {
            parsed = recvArray.size();
            logd("frame too long!!\n");
            //recvArray.clear();
        }

        if (parsed < 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + recvArray.size());
            parsed = 0;
            logd("frame no header, erase all!!\n");

        } else if (parsed > 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + parsed);
            //logd("erase size %d\n", parsed);

        } else {
            //logd("alertor frame not complete!!\n");
        }

        if (recvArray.size() <= 0 || parsed <= 0) {
            break;
        }

    } while (1);

    return parsed;
}

int32_t LiquidMeterFutai::onDataParse(const char *p, uint32_t size)
{
    int32_t start = -1;
    int32_t end = -1;
    uint32_t offset = 0;

    for (int i = 0; i < static_cast<int32_t>(size); ++i) {
        if (i + 1 > size) {
            break;
        }

        if (p[i] == LIQUID_METER_BGN_MAGIC) {
            start = i;
        }

        if (p[i] == LIQUID_METER_END_MAGIC) {
            end = i;
        }
    }

    if (start >= 0 && end > 0) {
        uint8_t* tmp = (uint8_t*)(p + start);
        uint32_t len = end - start + 1;
        LiquidMeterSensorDataT lmsd;

        if (lmsd.decode((const uint8_t *)tmp, len) >= 0) {
            logd("LiquidMeterSensorData");
            onMessage(&lmsd);

        } else {
            logd("Invalid data");
            return -1;
        }

    } else {
        logd("******start =  %d, end = %d\n", start, end);
        return -1;
    }

    return end + 1;
}


bool LiquidMeterFutai::onMessage(LiquidMeterSensorDataT * msg)
{
    expand::LiquidMeterSensorMessage & message = mDevTbl[msg->id];
    message.mUtcTime = my::timestamp::utc_milliseconds();
    mLastDataUtcTime = time(NULL);

    message.id = msg->id;
    message.raw = my::string((const char *)msg->raw, msg->raw.length());

    switch (msg->cmd) {
        case LIQUID_METER_CMD_SET_VOL: { /*查询返回*/
                message.maxVolume = atoi(msg->ctx.c_str());
                logd("%s, maxVolume %d\n", msg->ctx.c_str(), message.maxVolume);
                break;
            }

        case LIQUID_METER_CMD_GET_LEVEL: {
                char tmp[128] = {0};
                memcpy(tmp, msg->ctx.c_str(), 5);
                message._10KRatio = strtoul(tmp, nullptr, 10);/*当前水位万分比*/

                memset(tmp, 0, sizeof(tmp));
                memcpy(tmp, msg->ctx.c_str() + 5, 4);
                int lvl = strtoul(tmp, nullptr, 10);

                memset(tmp, 0, sizeof(tmp));
                memcpy(tmp, msg->ctx.c_str() + 9, 6);
                message.leftVolume = strtoul(tmp, nullptr, 10);
                message.leftVolume /= 100;/*剩余体积,升数L*/
                break;
            }

        default: {
                break;
            }
    }

    msgpack::sbuffer  sbuf;
    msgpack::pack(sbuf, message);
    ExpandSet::getInstance().sendLibFlow(LIQUID_LIBFLOW_RAW_TOPIC, sbuf.data(), sbuf.size());
    return true;
}

bool LiquidMeterFutai::setBaud(uint16_t id, int val)
{
    switch (val) {
        case 4800:
        case 9600:
        case 57600:
        case 115200: {
                LiquidMeterSensorDataT lmsd;
                my::string data;
                data.assignf("%1d%04d", 1/*set baud*/, val);
                my::string msg = lmsd.encode(id, LIQUID_METER_CMD_SET_BAUD, data);
                msgEnque((void *)msg.c_str(), msg.length());

                break;
            }

        default: {
                loge(LIQUID_METER_PROP_BAUD " set invalid value %d!", val);
                return false;
            }
    }

    return true;
}

bool LiquidMeterFutai::calibZero(uint16_t id)
{
    LiquidMeterSensorDataT lmsd;
    my::string data;
    data.assignf("%1d", 1/*set the zero value*/);
    my::string msg = lmsd.encode(id, LIQUID_METER_CMD_CALIB_ZERO, data);
    msgEnque((void *)msg.c_str(), msg.length());

    return true;
}
bool LiquidMeterFutai::calibFull(uint16_t id)
{
    LiquidMeterSensorDataT lmsd;
    my::string data;
    data.assignf("%1d", 1/*set the full value*/);
    my::string msg = lmsd.encode(id, LIQUID_METER_CMD_CALIB_FULL, data);
    msgEnque((void *)msg.c_str(), msg.length());

    return true;
}

bool LiquidMeterFutai::setVolume(uint16_t id, int val)
{
    LiquidMeterSensorDataT lmsd;
    my::string data;
    data.assignf("%1d%05d", 1, val);
    my::string msg = lmsd.encode(id, LIQUID_METER_CMD_SET_VOL, data);
    msgEnque((void *)msg.c_str(), msg.length());

    return true;
}

bool LiquidMeterFutai::getVolume(uint16_t id)
{
    LiquidMeterSensorDataT lmsd;
    my::string data;
    data.assignf("%1d", 0);
    my::string msg = lmsd.encode(id, LIQUID_METER_CMD_SET_VOL, data);
    msgEnque((void *)msg.c_str(), msg.length());

    return true;
}

bool LiquidMeterFutai::getCurLvl(uint16_t id)
{
    LiquidMeterSensorDataT lmsd;
    my::string data = "";
    my::string msg = lmsd.encode(id, LIQUID_METER_CMD_GET_LEVEL, data);
    msgEnque((void *)msg.c_str(), msg.length());

    return true;
}
bool LiquidMeterFutai::getDamping(uint16_t id)
{
    LiquidMeterSensorDataT lmsd;
    my::string data = "0";
    my::string msg = lmsd.encode(id, LIQUID_METER_CMD_GS_DAMPING, data);
    msgEnque((void *)msg.c_str(), msg.length());

    return true;
}

//????如何使用
bool LiquidMeterFutai::devidDetect()
{
    LiquidMeterSensorDataT lmsd;
    my::string data = "0";//查询
    data += "01";//id
    my::string msg = lmsd.encode(0, LIQUID_METER_CMD_QSG_ID, data);
    msgEnque((void *)msg.c_str(), msg.length());

    return true;
}


#define LIQUID_CMD_HELP       0
#define LIQUID_CMD_SHOW       1
#define LIQUID_CMD_GET_LVL    2
#define LIQUID_CMD_CALIB_ZERO 3
#define LIQUID_CMD_CALIB_FULL 4
#define LIQUID_CMD_SET_VOLUME 5

static const std::vector<CmdStrT> gFuelMeterCmds = {
    {
        "help",
        LIQUID_CMD_HELP,
        "show this usage.\n"
    },
    {
        "show",
        LIQUID_CMD_SHOW,
        "show last LIQUID data.\n"
    },
    {
        "level",
        LIQUID_CMD_GET_LVL,
        "show last LIQUID data.\n"
    },
    {
        "zero",
        LIQUID_CMD_CALIB_ZERO,
        "calib zero.\n"
    },
    {
        "full",
        LIQUID_CMD_CALIB_FULL,
        "calib full.\n"
    },
};

std::string LiquidMeterFutai::setupCmdList(const char * cmdName)
{
    mCmdUsage = CmdStrT::setupCmdList(gFuelMeterCmds, cmdName);
    return mCmdUsage;
}

bool LiquidMeterFutai::runCmd(int argc, char **argv, string &ack)
{
    uint32_t cmd = CmdStrT::strToCmd(gFuelMeterCmds, argv[0]);

    switch (cmd) {
        case LIQUID_CMD_HELP: {
                ack = mCmdUsage;
                return true;
            }

        case LIQUID_CMD_SHOW: {
                ack = "\n";
                APPEND_STR_MSG(ack, "Msg UTC", 16, "%ld", mLastDataUtcTime);

                return true;
            }

        case LIQUID_CMD_GET_LVL: {
                if (argc >= 2) {
                    getCurLvl(atoi(argv[1]));
                    return true;
                }
            }

        default: {
                ack = mCmdUsage;
            }
    }

    return false;
}


} //namespace minieye


