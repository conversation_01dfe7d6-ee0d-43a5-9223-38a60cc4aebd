#ifndef _ZF_RADAR_H_
#define _ZF_RADAR_H_

#include "protocol.h"
#include <unordered_map>

namespace minieye
{

struct calParam_t {
    int32_t angle;
    int32_t distance;
    my::string language;
    calParam_t()
    {
        angle = 4000;
        distance = 120;
        language = "zh";
    }
};

class  ZfRadarProt: public Protocol, public my::thread
{
    public:
        ZfRadarProt();
        ~ZfRadarProt();
        const char * name()
        {
            return "radar_canport";
        }
        int32_t onServerConnected(void);
        int32_t onServerDisconnected(void);
        int32_t onDataRecevied(const char *p, uint32_t len);
        bool runCmd(int argc, char **argv, string &ack);
        std::string setupCmdList(const char * cmdName)
        {
            return "";
        }
        virtual void setChannelId(int channelId)
        {
            mCanChId = channelId;
            logd("setChannelId!\n");
            loadConfig();
        }

        virtual  uint32_t getTimeOutVal(void)
        {
            uint32_t i = 3;
            return i;
        }
        void run();
    private:
        bool initCalibrationInfo();
        void clearFilter();
        void setFilter(uint32_t canId[], uint32_t canIdNum);
        void setCanMode(std::string mode);
        bool loadConfig();
        bool loadCalibConfig();
        void sendCarInfo();

        int shangfuCanAlarmDataDeal(const uint32_t canID, const uint8_t *canData);
        int chengtaiCanAlarmDataDeal(const uint32_t canID, const uint8_t *canData);

        int32_t onDataParse(const uint8_t *p, uint32_t size);
        int32_t onCmdData(vector<uint8_t> &recvArray, const char *p, uint32_t len);
        bool loadCalParam();
        void radarConnectCheck();
        void startAngleCal();
        void sendCalParam(int32_t angle, int32_t distance);
        void startCal(int32_t angle, int32_t distance);
        void calCheck();
        void calLoop();
        void calMsgDeal(uint8_t angleCalFlg, int32_t distance, int32_t angle);
        void calMsgDeal(int32_t distance, int32_t angle);
        bool setRadarCalProperty(int32_t status, int32_t distance, int32_t angle);
        void playCalResult(int32_t status);

        void ttsGBK(const my::string spch, int32_t tmGapLimit = 0);
        bool sendFlowMessage(void);
    private:
        my::timestamp   mMsgTm;
        vector<uint8_t> mRcvArray;
        int             mCanChId = 1;
        bool            mCanEnable = false;
        std::string     mCanBaudRate = "500k";
        std::string     mCanMode;
        my::timestamp   mLastCarInfSnd;
        my::timestamp   mLastRadarMsgRcvTm;
        my::timestamp   mLastOsdSnd;

        my::timestamp   mWarnLastTs;

    private:
        std::mutex      mLock;
        int32_t         mAngle;
        int32_t         mDistance;
        int32_t         mCalLoop;
        my::timestamp   mCalTm;
        my::timestamp   mCalDisTm;
        calParam_t      mCalReq;

        std::mutex      mPlayLock;
        std::unordered_map<std::string, uint32_t> mTtsMsgList;
        bool mbCanRcvFail = false;
};
}
#endif

