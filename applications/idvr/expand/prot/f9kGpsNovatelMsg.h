
#include <stdio.h>
#include <string.h>
#include <stdint.h>

typedef struct {
    double latitude;   // 纬度(度)
    double longitude;  // 经度(度)
    float altitude;    // 高度(m)
    uint16_t week;     // GPS周数
    double seconds;    // GPS秒数
    uint8_t sat_num;   // 卫星数
} BestposData;

typedef struct {
    double latitude;
    double longitude;
    float altitude;
    float north_vel;   // 北向速度(m/s)
    float east_vel;    // 东向速度(m/s)
    float up_vel;      // 垂直速度(m/s)
    float roll;        // 横滚角(度)
    float pitch;       // 俯仰角(度)
    float azimuth;     // 方位角(度)
} InspvaxData;

void parseNovatel(const char* buffer, int32_t size, BestposData * pBestposData, InspvaxData * pInspvaxData);
