
#include <assert.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <sys/time.h>

#include "system_properties.h"
#include "FuelMeter.ChangRun.h"
#include "idvrProperty.h"
#include "expand.h"

namespace minieye
{

#define FUEL_METER_MAGIC_STR "*CFV"
#define FUEL_METER_MAGIC *((uint32_t*)FUEL_METER_MAGIC_STR)

typedef struct FuelMeterSensorData {
    uint32_t  magic = 0; /*FUEL_METER_MAGIC*/
    uint16_t  idx = 0;
    float    height = 0;//mm
    uint16_t chkSum = 0;
    my::constr raw;

    int decode(my::constr & data)
    {
        if (data.length() < 16) {
            loge("data length = %d < 14!", data.length());
            return -1;
        }
        raw = data;
        data >> magic >> idx;

        if (magic != FUEL_METER_MAGIC) {
            loge("magic is invalid !");
            return -2;
        }
        my::uchar ad[7] = {0};
        for (int i = 0; i < sizeof(ad) - 1; i++) {
            data >> ad[i];
        }
        char * bgnptr = (char*)ad;
        char * endptr = nullptr;
        long val = strtol(bgnptr, &endptr, 16);
        if (endptr == bgnptr) {
            loge("strtol fail ! %s", (const char *)raw);
            return -3;
        }
        height = val * 1000;
        height /= 65535;
        logd("fuel height = %f", height);
        data >> chkSum;

        uint8_t c = 0;
        for (int i = 0; i < 12; i++) {
            c += raw[i];
        }

        char cc[128] = {0};
        snprintf(cc, sizeof(cc), "%02X", c); 
        if ((*(uint16_t*)cc) != chkSum) {
            loge("chksum fail! %02x != %02x", (*(uint16_t*)cc), chkSum);
        }
        return 0;
    }
} FuelMeterSensorDataT;

#define FUEL_CMD_HELP       0
#define FUEL_CMD_SHOW       1
#define FUEL_CMD_QUERY      2
static const std::vector<CmdStrT> gFuelMeterCmds = {
    {
        "help",
        FUEL_CMD_HELP,
        "show this usage.\n"
    },
    {
        "show",
        FUEL_CMD_SHOW,
        "show last FUEL data.\n"
    },
    {
        "query",
        FUEL_CMD_QUERY,
        "query FUEL data.\n"
    },
};

std::string FuelMeterChangRun::setupCmdList(const char * cmdName)
{
    mCmdUsage = CmdStrT::setupCmdList(gFuelMeterCmds, cmdName);
    return mCmdUsage;
}

bool FuelMeterChangRun::runCmd(int argc, char **argv, string &ack)
{
    uint32_t cmd = CmdStrT::strToCmd(gFuelMeterCmds, argv[0]);

    switch (cmd) {
        case FUEL_CMD_HELP: {
                ack = mCmdUsage;
                return true;
            }

        case FUEL_CMD_SHOW: {
                ack = "\n";
                APPEND_STR_MSG(ack, "type", 16, "%d",   mLastData.type);
                APPEND_STR_MSG(ack, "fuel", 16, "%fmm", mLastData.fuelHeight);
                APPEND_STR_MSG(ack, "Msg UTC", 16, "%" FMT_LLD, mLastData.mUtcTime);

                return true;
            }
        case FUEL_CMD_QUERY: {
                std::string msg = "$!RY0151";
                msgEnque((void *)msg.c_str(), msg.length());
                return true;
            }
        default: {
                ack = mCmdUsage;
            }
    }

    return false;
}


int32_t FuelMeterChangRun::onServerConnected(void)
{
    start();
    return 0;
}
int32_t FuelMeterChangRun::onServerDisconnected(void)
{
    stop();
    return 0;
}

int32_t FuelMeterChangRun::onDataRecevied(const char *p, uint32_t len)
{
    //logd("on cmd data %d", len);
    //logd("recv %s", my::hex(my::constr(p, len)).c_str());

    onCmdData(mRcvArray, p, len);
    return 0;
}

int32_t FuelMeterChangRun::onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len)
{
    recvArray.insert(recvArray.end(), p, p + len);

    int32_t parsed = 0;

    do {
        parsed = onDataParse((const char *)recvArray.data(), recvArray.size());
        //assert(parsed <= recvArray.size());

        if (recvArray.size() >= (1 << 20)) {
            parsed = recvArray.size();
            logd("frame too long!!\n");
        }

        if (parsed < 0) {
            logd("frame no header, erase all!! %d\n", parsed);
            recvArray.erase(recvArray.begin(), recvArray.begin() + recvArray.size());
            parsed = 0;

        } else if (parsed > 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + parsed);
            //logd("erase size %d\n", parsed);

        } else {
            //logd("alertor frame not complete!!\n");
        }

        if (recvArray.size() <= 0 || parsed <= 0) {
            break;
        }

    } while (1);

    return parsed;
}

int32_t FuelMeterChangRun::onDataParse(const char *p, uint32_t size)
{
    int32_t start = -1;
    int32_t end = -1;
    uint32_t offset = 0;
    if (size < 14) {
        return 0;
    }
    for (int i = 0; i < static_cast<int32_t>(size); ++i) {
        if (i + sizeof(FUEL_METER_MAGIC) > size) {
            //logd("no found %d", i);
            break;
        }

        if (start >= 0 && (FUEL_METER_MAGIC == *((uint32_t*)(p + i)))) {
            //logd("end %d", i);
            end = i - 1;
            break;
        }

        if (FUEL_METER_MAGIC == *((uint32_t*)(p + i))) {
            //logd("start %d", i);
            start = i;
        }
    }

    if (start >= 0) {
        if (end < 0) {
            return start;
        }

        const char * tmp = (const char*)(p + start);
        int len = end - start + 1;

        FuelMeterSensorData fmsd;
        mCurMsg = my::constr(tmp, len);
        int32_t eaten = mCurMsg.length();

        if (fmsd.decode(mCurMsg) < 0) {
            loge("fuel meter decode fail!");
            return -1;

        } else {
            logd("fuelmeter data eaten size = %d\n", eaten);
        }

        if (eaten > 0) {
            logd("enter onDataValidate");
            onMessage(&fmsd);

        } else {
            logd("Invalid data");
            return -1;
        }

    } else {
        //logd("******start =  %d, end = %d\n", start, end);
        return -1;
    }

    return end + 1;
}

bool FuelMeterChangRun::onMessage(FuelMeterSensorData * msg)
{
    expand::FuelMeterSensorMessage message;
    message.mUtcTime = my::timestamp::utc_milliseconds();

    message.type = 1;
    message.fuelHeight = msg->height;
    message.raw = std::string((const char *)msg->raw, msg->raw.length());
    mLastData = message;
    msgpack::sbuffer  sbuf;
    msgpack::pack(sbuf, message);
    ExpandSet::getInstance().sendLibFlow(FUEL_LIBFLOW_RAW_TOPIC, sbuf.data(), sbuf.size());
    {
        std::lock_guard<std::mutex> lock(mRFHMtx);
        mRecentFuelHeight = msg->height;
        mFuelHeightUpdateTs = my::timestamp::now();
    }
    return true;
}

void FuelMeterChangRun::run()
{
    my::timestamp lastQueryTime;
    while (!exiting()) {
        if (lastQueryTime.elapsed() >= 2000) {
            std::string msg = "$!RY0151\r\n";
            //logd("send %s", msg.c_str());
            msgEnque((void *)msg.c_str(), msg.length());
            lastQueryTime = my::timestamp::now();
        }
        {
            std::lock_guard<std::mutex> lock(mRFHMtx);
            if ((mFuelHeightUpdateTs.elapsed() <= (60 * 1000)) && /*60s内更新过*/
                (mRecentFuelHeightTs.elapsed() >= (30 * 1000))) {
                mFuelHeightUpdateTs = 0;
                mRecentFuelHeightTs = my::timestamp::now();
                char propValue[PROP_VALUE_MAX] = {0};
                snprintf(propValue, sizeof(propValue), "%f", mRecentFuelHeight);
                __system_property_set(PROP_PERSIST_FUEL_HEIGHT, propValue);
                logd("PROP_PERSIST_FUEL_HEIGHT %f", mRecentFuelHeight);
            }
        }
        msleep(1000);
    }

}




} //namespace minieye

