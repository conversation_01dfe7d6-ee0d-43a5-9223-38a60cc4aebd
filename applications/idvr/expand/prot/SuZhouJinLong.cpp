#include "SuZhouJinLong.h"

#include <sstream>
#include <fstream>

#include "properties.h"
#include "ttsPlay.h"
#include "devicehub.h"
#include "config/stdConfIni.h"

namespace minieye {

#define DEFAULT_RADAR_ALARM_HOLD_TIME_MS 3000
#define DEBUG 0
#if DEBUG
#warning "DEBUG is enabled"
#endif

// --

#define CAR_TCO1_CAN_ID         0x0CFE6CEE
#define CAR_OEL_CAN_ID          0x18FDCC17
#define CAR_GEAR_INFO_CAN_ID    0x184317D0
#define CAR_ANGLE_SENSOR_CAN_ID 0x18F01DE4

enum CarOelTurnSigStatus {
    E_CAR_OEL_TURN_SIG_OFF = 0,
    E_CAR_OEL_TURN_SIG_LEFT,
    E_CAR_OEL_TURN_SIG_RIGHT,
};

enum CarOelHazardLampsStatus {
    E_CAR_OEL_HAZARD_LAMPS_OFF = 0,
    E_CAR_OEL_HAZARD_LAMPS_FLASHING,
    E_CAR_OEL_HAZARD_LAMPS_ERROR,
    E_CAR_OEL_HAZARD_LAMPS_NOT_AVAILABLE,
};

enum CarGearMode {
    E_CAR_GEAR_MODE_N = 0,
    E_CAR_GEAR_MODE_D,
    E_CAR_GEAR_MODE_R,
    E_CAR_GEAR_MODE_L,
    E_CAR_GEAR_MODE_M,
};

enum CarAngleSensorActiveMode {
    E_CAR_AS_PROGRAMING_MODE = 0,
    E_CAR_AS_NORMAL_MODE,
    E_CAR_AS_RESERVERD,
    E_CAR_AS_NOT_AVAILABLE,
};

enum CarAngleSensorLongitudinalAcceleration {
    E_CAR_LA_SAS_NOT_CALIBRATED = 0,
    E_CAR_LA_SAS_CALIBRATED,
    E_CAR_LA_SAS_RESERVED,
    E_CAR_LA_SAS_NOT_AVAILABLE,
};

// struct CarTco1Info {
//     uint32_t frameId = CAR_TCO1_CAN_ID;
//     uint8_t len = 8;

//     uint8_t r0_5[6];

//     uint16_t vehicleSpeed;

// } __attribute__((packed));

// struct CarOelInfo {
//     uint32_t frameId = CAR_OEL_CAN_ID;
//     uint8_t len = 8;

//     uint8_t r0;

//     uint8_t turnSigStatus     : 4;
//     uint8_t hazardLampsStatus : 4;

//     uint8_t r2_7[6];

// } __attribute__((packed));

struct CarGearInfo {
    uint32_t frameId = CAR_GEAR_INFO_CAN_ID;
    uint8_t len = 8;

    uint8_t r0_1[2];

    uint8_t gearMode : 4 = 0;
    uint8_t r3       : 4 = 0;

    uint8_t r3_7[5];

} __attribute__((packed));

struct CarAngleSensorInfo {
    uint32_t frameId = CAR_ANGLE_SENSOR_CAN_ID;
    uint8_t len = 8;

    uint16_t steeringWheelAngle = 0;

    uint8_t r2_3[2];

    uint16_t steeringWheelAngleRange = 0;

    uint8_t steeringAngleSensorActiveMode  : 2 = 0;
    uint8_t longitudinalAccelerationStatus : 2 = 0;
    uint8_t r6                             : 4 = 0;

    uint8_t messageCounter                 : 4 = 0;
    uint8_t messageChecksum                : 4 = 0;

} __attribute__((packed));

// --

#define RADAR_CAR_INFO_CAN_ID    0x3F0
#define RADAR_ALARM_LEFT_CAN_ID  0x7B4
#define RADAR_ALARM_RIGHT_CAN_ID 0x7B5

enum RadarGearSig {
    E_RADAR_GEAR_SIG_INVALID = 0,
    E_RADAR_GEAR_SIG_N = 1,
    E_RADAR_GEAR_SIG_D = 2,
    E_RADAR_GEAR_SIG_R = 3,
};

enum RadarExpandSig {
    E_RADAR_EXPAND_SIG_DISABLE = 0,
    E_RADAR_EXPAND_SIG_LEFT = 0,
    E_RADAR_EXPAND_SIG_RIGHT = 1,
};

enum RadarFlag {
    E_RADAR_FLAG_NONE = 0,
    E_RADAR_FLAG_RADAR_WARNING,
};

enum RadarAlarmLevel {
    E_RADAR_ALARM_L1 = 0,
    E_RADAR_ALARM_L2,
    E_RADAR_ALARM_L3,
};

enum RadarAlarmType {
    E_RADAR_ALARM_TYPE_BSD = 0,
    E_RADAR_ALARM_TYPE_FLCA,
    E_RADAR_ALARM_TYPE_RLCA,
    E_RADAR_ALARM_TYPE_FLCA_AND_RLCA
};

struct SuZhouJinLong::RadarCarInfo {
    uint32_t frameId = RADAR_CAR_INFO_CAN_ID;
    uint8_t len = 8;

    uint8_t r0                : 2 = 0;
    uint8_t expandValid       : 1 = 0;
    uint8_t yawRateValid      : 1 = 0;
    uint8_t gearSigValid      : 1 = 0;
    uint8_t turnSigValid      : 1 = 0;
    uint8_t doorSigValid      : 1 = 0;
    uint8_t hostVelocityValid : 1 = 0;

    uint8_t hostVelocityM     : 7 = 0;
    uint8_t r1                : 1 = 0;
    uint8_t hostVelocityL     : 8 = 0;

    uint8_t gearSig           : 2 = 0;  // 0:无效 1:N 2:D 3:R
    uint8_t turnSigR          : 1 = 0;
    uint8_t turnSigL          : 1 = 0;
    uint8_t doorSigRR         : 1 = 0;
    uint8_t doorSigLR         : 1 = 0;
    uint8_t doorSigRF         : 1 = 0;
    uint8_t doorSigLF         : 1 = 0;

    int16_t yawRate = 0;

    uint8_t expandSig : 2 = 0;
    uint16_t r7_8     : 14 = 0;
} __attribute__((packed));

struct RadarAlarmInfo {
    uint32_t frameId = RADAR_ALARM_LEFT_CAN_ID;
    uint8_t len = 8;

    uint8_t AlarmEvt   : 4 = 0;
    uint8_t alarmLevel : 3 = 0;
    uint8_t flag       : 1 = 0;

    uint8_t r1_7[7] = {0};

} __attribute__((packed));

// --

#define CAR_FLIC_CAN_ID 0x18A9175D

enum BsdFlicAudibleWarning {
    E_FLIC_AUDIBLE_WARNING_OFF = 0,
    E_FLIC_AUDIBLE_WARNING_ON,
} BsdFlicAudibleWarning;

enum BsdFlicAlarmLevel {
    E_FLIC_ALARM_LEVEL_NONE = 0,
    E_FLIC_ALARM_LEVEL_L1,
    E_FLIC_ALARM_LEVEL_L2,
    E_FLIC_ALARM_LEVEL_L3,
} BsdFlicAlarmLevel;

enum BsdFlicAlarmCategory {
    E_FLIC_ALARM_CATEGORY_NO_ALARM = 0,
    E_FLIC_ALARM_CATEGORY_BSD,
    E_FLIC_ALARM_CATEGORY_LCA,
} BsdFlicAlarmCategory;

enum BsdFlicAlarmType {
    E_FLIC_ALARM_TYPE_NONE = 0,
    E_FLIC_ALARM_TYPE_CAMERA,
    E_FLIC_ALARM_TYPE_RADAR,
} BsdFlicAlarmType;

enum BsdFlicAlarmArea {
    E_FLIC_ALARM_AREA_NONE = 0,
    E_FLIC_ALARM_AREA_FRONT,
    E_FLIC_ALARM_AREA_REAR,
    E_FLIC_ALARM_AREA_FRONT_AND_REAR,
} BsdFlicAlarmArea;

struct SuZhouJinLong::CarFlicInfo {
    uint32_t frameId = CAR_FLIC_CAN_ID;
    uint8_t len = 8;

    uint8_t r0 = 0;

    uint8_t rightBsdWarning       : 2 = 0;
    uint8_t rightBsdLevel         : 2 = 0;
    uint8_t rightBsdAlarmCategory : 2 = 0;
    uint8_t r1                    : 2 = 0;

    uint8_t rightBsdAlarmType     : 2 = 0;
    uint8_t rightLcaAlarmArea     : 2 = 0;
    uint8_t r2                    : 4 = 0;

    uint8_t leftBsdWarning        : 2 = 0;
    uint8_t leftBsdLevel          : 2 = 0;
    uint8_t leftBsdAlarmCategory  : 2 = 0;
    uint8_t r3                    : 2 = 0;

    uint8_t leftBsdAlarmType      : 2 = 0;
    uint8_t leftLcaAlarmArea      : 2 = 0;
    uint8_t r4                    : 4 = 0;

    uint8_t r5_7[3] = {0};

} __attribute__((packed));

int32_t SuZhouJinLong::onServerConnected(void) {
    auto &devicehub = DeviceHub::getInstance();
    if (mIsCan1Chn) {
        uint32_t can1List[] = {/*CAR_TCO1_CAN_ID, CAR_OEL_CAN_ID, */ CAR_GEAR_INFO_CAN_ID, CAR_ANGLE_SENSOR_CAN_ID};
        if (!devicehub.setCanFilter(DeviceHub::DEVICE_TYPE_CAN0, can1List, sizeof(can1List) / sizeof(can1List[0]))) {
            loge("set can filter failed");
        }
        // devicehub.setCanFilter(DeviceHub::DEVICE_TYPE_CAN0, CAR_TCO1_CAN_ID);
        // devicehub.setCanFilter(DeviceHub::DEVICE_TYPE_CAN0, CAR_OEL_CAN_ID);

    } else {  // Can2Chn
        uint32_t can2List[] = {RADAR_ALARM_LEFT_CAN_ID, RADAR_ALARM_RIGHT_CAN_ID};
        if (!devicehub.setCanFilter(DeviceHub::DEVICE_TYPE_CAN1, can2List, sizeof(can2List) / sizeof(can2List[0]))) {
            loge("set can filter failed");
        }
        static bool devCheck = false;
        if (!devCheck) {
            devCheck = true;
            auto devCheckThread = std::thread([this] { this->devCheck(); });
            devCheckThread.detach();
        }
    }
    return 0;
}
int32_t SuZhouJinLong::onServerDisconnected(void) {
    return 0;
}

int getRadarAlarmConfig(const std::string &config, std::string path = "/sdcard/run/bsd.flag") {
    ifstream cfgFile(path.c_str(), ios::in);
    if (!cfgFile.is_open()) {
        return DEFAULT_RADAR_ALARM_HOLD_TIME_MS;
    }
    char buf[128] = {0};
    int ret = DEFAULT_RADAR_ALARM_HOLD_TIME_MS;
    while (cfgFile.getline(buf, sizeof(buf))) {
        if (!::strncasecmp(config.c_str(), buf, strlen(config.c_str()))) {
            string tm = buf;
            string time = tm.substr(tm.find('=') + 1);
            ret = stoi(time.c_str());
            return ret;
        } else {
            memset(buf, 0, sizeof(buf));
            continue;
        }
    }
    return ret;
}

int32_t SuZhouJinLong::onMessage(const char *p, uint32_t len) {
    mLastDataUtcTime = time(NULL);

    if (mRadarCarInfo == nullptr) {
        loge("error mRadarCarInfo == nullptr");
        return -1;
    }

    auto frameId = *(uint32_t *)p;
    switch (frameId) {
            //             case CAR_TCO1_CAN_ID: {
            //                 std::lock_guard<std::mutex> lg(mInfoUpdateMtx);
            //                 CarTco1Info *info = (CarTco1Info *)p;
            //                 mRadarCarInfo->hostVelocity =
            //                     (uint16_t)(((info->vehicleSpeed << 8) | ((info->vehicleSpeed >> 8) & 0xFF)) /
            //                     25600.0);
            //                 mRadarCarInfo->hostVelocityValid = true;
            // #if DEBUG
            //                 logd("speed %d", mRadarCarInfo->hostVelocity);
            // #endif
            //                 break;
            //             }
            //             case CAR_OEL_CAN_ID: {
            //                 std::lock_guard<std::mutex> lg(mInfoUpdateMtx);
            //                 CarOelInfo *info = (CarOelInfo *)p;
            //                 mRadarCarInfo->turnSigL = (info->turnSigStatus == E_CAR_OEL_TURN_SIG_LEFT);
            //                 mRadarCarInfo->turnSigR = (info->turnSigStatus == E_CAR_OEL_TURN_SIG_RIGHT);
            //                 mRadarCarInfo->turnSigValid = true;
            // #if DEBUG
            //                 logd("turn sig L:%d R:%d", mRadarCarInfo->turnSigL, mRadarCarInfo->turnSigR);
            // #endif
            //                 break;
            //             }
        case CAR_GEAR_INFO_CAN_ID: {
            std::lock_guard<std::mutex> lg(mInfoUpdateMtx);
            CarGearInfo *info = (CarGearInfo *)p;
            switch (info->gearMode) {
                case E_CAR_GEAR_MODE_N: {
                    mRadarCarInfo->gearSig = E_RADAR_GEAR_SIG_N;
                    mRadarCarInfo->gearSigValid = true;
                    break;
                }
                case E_CAR_GEAR_MODE_D: {
                    mRadarCarInfo->gearSig = E_RADAR_GEAR_SIG_D;
                    mRadarCarInfo->gearSigValid = true;
                    break;
                }
                case E_CAR_GEAR_MODE_R: {
                    mRadarCarInfo->gearSig = E_RADAR_GEAR_SIG_R;
                    mRadarCarInfo->gearSigValid = true;
                    break;
                }
                default: {
                    mRadarCarInfo->gearSig = 0;
                    mRadarCarInfo->gearSigValid = false;
                    break;
                }
            }
#if DEBUG
            logd("gear sig %d", mRadarCarInfo->gearSig);
#endif
            break;
        }
        case CAR_ANGLE_SENSOR_CAN_ID: {
            std::lock_guard<std::mutex> lg(mInfoUpdateMtx);
            CarAngleSensorInfo *info = (CarAngleSensorInfo *)p;
            auto u16angle = (info->steeringWheelAngle << 8) | ((info->steeringWheelAngle >> 8) & 0xFF);
            auto angle = -((float)(((float)u16angle / 1024) - 31.374));
            // 车辆给出来的方向盘弧度值
            // 顺时针是负，逆时针是正
            int16_t yawRate = (int16_t)(angle * 57.2957);  // 弧度转换为角度值
            mRadarCarInfo->yawRate = htons(yawRate);
            mRadarCarInfo->yawRateValid = true;

            if (mLibflowServer != nullptr) {
                std::string rate = "{";
                rate += "\"yawRate\":";
                rate += std::to_string(mRadarCarInfo->yawRate);
                rate += "}";

                mLibflowServer->send(rate.c_str(), rate.size());
                logd("angle %f rate: %s", angle, rate.c_str());
            }
#if DEBUG
            logd("angle %f rate %d", angle, mRadarCarInfo->yawRate);
#endif
            break;
        }
        case RADAR_ALARM_LEFT_CAN_ID:
        case RADAR_ALARM_RIGHT_CAN_ID: {
            mRadarRecv = true;
            RadarAlarmInfo *info = (RadarAlarmInfo *)p;
            AlarmEvt radarEvt = E_ALARM_NONE;
            if (info->flag) {
                std::string type;
                std::string location = (frameId == RADAR_ALARM_LEFT_CAN_ID ? "l" : "r");
                std::string level = to_string(info->alarmLevel);
#if DEBUG
                logi("%s radar alarm: %s", location.c_str(), level.c_str());
#endif

                switch (info->AlarmEvt) {
                    case E_RADAR_ALARM_TYPE_BSD: {
                        type = "bsd_";
                        radarEvt = E_RADAR_BSD;
                        logd("rlca alarm bsd, level %d", info->alarmLevel);
                        break;
                    }
                    case E_RADAR_ALARM_TYPE_FLCA: {
                        type = "lca_";
                        location += 'f';
                        radarEvt = E_RADAR_FLCA;
                        logd("rlca alarm llca, level %d", info->alarmLevel);
                        break;
                    }
                    case E_RADAR_ALARM_TYPE_RLCA: {
                        type = "lca_";
                        location += 'l';
                        radarEvt = E_RADAR_RLCA;
                        logd("rlca alarm rlca, level %d", info->alarmLevel);
                        break;
                    }
                    case E_RADAR_ALARM_TYPE_FLCA_AND_RLCA: {
                        radarEvt = E_RADAR_FLCA_AND_RLCA;
                        break;
                    }
                    default: {
                        logd("unknown radar alarm type %d", info->AlarmEvt);
                        break;
                    }
                }
                std::string config = "--radar_" + type + location + level + "warning_block_time";

                if ((radarEvt == E_RADAR_FLCA_AND_RLCA) ||
                    (mRadarEvtTsMap[config].elapsed() >= getRadarAlarmConfig(config))) {
                    std::lock_guard<std::mutex> lg(mInfoUpdateMtx);
                    mRadarEvt[frameId == RADAR_ALARM_RIGHT_CAN_ID] = {
                        radarEvt, {my::timestamp::now(), (uint8_t)(info->alarmLevel + 1)}};
                    mInfoUpdateCond.notify_one();
                }
            } else {
                std::lock_guard<std::mutex> lg(mInfoUpdateMtx);
                if (mRadarEvt[frameId == RADAR_ALARM_RIGHT_CAN_ID].second.first.elapsed() >= 1000) {
                    mRadarEvt[frameId == RADAR_ALARM_RIGHT_CAN_ID] = {radarEvt, {my::timestamp::now(), 0}};
                }
            }
            break;
        }
        default: {
            return -1;
        }
    }
    return 0;
}

int32_t SuZhouJinLong::onDataRecevied(const char *p, uint32_t len) {
    logd("on cmd data %d", len);
    logd("recv %s", my::hex(my::constr(p, len)).c_str());

    char *data = const_cast<char *>(p);
    uint32_t dataLength = len;
    constexpr uint32_t canPktLen = 4 + 1 + 8;

    while (dataLength > 0) {
        if (dataLength < canPktLen) {
            dataLength = 0;
            loge("data frame too short, len %d", len);
        } else {
            if (dataLength >= canPktLen) {
                if (onMessage(data, canPktLen) != 0) {
                    auto frameId = *(uint32_t *)data;
                    if (frameId != CAR_OEL_CAN_ID && frameId != CAR_TCO1_CAN_ID) {
                        loge("unknown frameId %x", *(uint32_t *)data);
                    }
                }
                data += canPktLen;
                dataLength -= canPktLen;
            }
        }
    }

    return 0;
}

#define SZJL_CAN_PROT_CMD_HELP    0
#define SZJL_CAN_PROT_CMD_SHOW    1
#define SZJL_CAN_PROT_CMD_TRIGGER 2

static const std::vector<CmdStrT> gSZJLCanProtCmds = {
    {"help", SZJL_CAN_PROT_CMD_HELP, "show this usage.\n"},
    {"show", SZJL_CAN_PROT_CMD_SHOW, "show last  data.\n"},
    {"trigger", SZJL_CAN_PROT_CMD_TRIGGER, "fake to trigger event.\n"},
};

std::string SuZhouJinLong::setupCmdList(const char *cmdName) {
    if (mIsCan1Chn) {
        mCmdUsage = CmdStrT::setupCmdList(gSZJLCanProtCmds, cmdName);
    }
    return mCmdUsage;
}

bool SuZhouJinLong::runCmd(int argc, char **argv, string &ack) {
    uint32_t cmd = CmdStrT::strToCmd(gSZJLCanProtCmds, argv[0]);

    switch (cmd) {
        case SZJL_CAN_PROT_CMD_HELP: {
            ack = mCmdUsage;
            return true;
        }

        case SZJL_CAN_PROT_CMD_SHOW: {
            ack = "\n";

            return true;
        }
        case SZJL_CAN_PROT_CMD_TRIGGER: {
            if (argc >= 3) {
                DeviceHub &devHub = DeviceHub::getInstance();
                AlgoManager &am = AlgoManager::getInstance();
                am.triggerEvent(argv[1], argv[2], devHub.getCarSpeed());
                return true;
            }
        }
        default: {
            ack = mCmdUsage;
        }
    }

    return false;
}

bool SuZhouJinLong::onAlgoEvent(std::shared_ptr<Event> e) {
    switch (e->type()) {
        case EVT_TYPE_BSD_Left:
        case EVT_TYPE_BSD_Right: {
            do {
                std::lock_guard<std::mutex> lg(mInfoUpdateMtx);
                auto level = (e->c.level == 2 ? 2 : ((e->c.level + 2) % 4));  // 调换一级和三级报警
                mAlgoEvt[e->type() == EVT_TYPE_BSD_Right] = {my::timestamp::now(), level};
#if DEBUG
                logd("onAlgoEvent %d level %d", e->type(), level);
#endif
            } while (0);
            mInfoUpdateCond.notify_one();
            break;
        }
        default: {
            loge("unknown event type %d", e->type());
            break;
        }
    }
    return true;
}

SuZhouJinLong::SuZhouJinLong(bool isCan1Chn)
    : mIsCan1Chn(isCan1Chn) {
    if (mIsCan1Chn) {
        mLibflowServer = std::make_shared<LibflowServer>("127.0.0.1", "24077", "vehicle.yawrate.info");
        if (mLibflowServer != nullptr) {
            mLibflowServer->start();
        }

        AlgoManager &am = AlgoManager::getInstance();
        char tmp[256];
        snprintf(tmp, sizeof(tmp), "SuZhouJinLong%p", this);
        am.addObserver(tmp, this);
        mIsInit.store(true);
    } else {
        while (!mIsInit.load()) {
            std::this_thread::yield();
        }
    }

    start();
}
SuZhouJinLong::~SuZhouJinLong() noexcept {
    if (mIsCan1Chn) {
        if (mLibflowServer != nullptr) {
            mLibflowServer->stop();
        }

        AlgoManager &am = AlgoManager::getInstance();
        char tmp[256];
        snprintf(tmp, sizeof(tmp), "SuZhouJinLong%p", this);
        am.delObserver(tmp);
    }
}

int playAudio(const std::string &path, uint8_t priority) {
    char cmd[256];
    if (!access(path.c_str(), R_OK)) {
        snprintf(cmd, sizeof(cmd), "cmd play %s %d", path.c_str(), priority);
        // logd("cmd = %s", cmd);
        LogCallProxyCmd::sendReq("media", cmd);
        return 0;
    }
    return -1;
}

void SuZhouJinLong::run() {
    auto handleRadarCarInfo = [this] {
        if (mRadarCarInfo == nullptr) {
            loge("error mRadarCarInfo == nullptr");
            return;
        }

        if (mCanMsgTs[RADAR_CAR_INFO_CAN_ID].elapsed() >= 50) {
            bool enExPandSig =
                (mRadarCarInfo->yawRate * 0.01) >= getRadarAlarmConfig("--radar_bsd_expandsig_enable_angle");

            auto& dh = DeviceHub::getInstance();
            uint16_t speed = dh.getCarSpeed() * 100;
            mRadarCarInfo->hostVelocityM = ((speed >> 8) & 0x7F);
            mRadarCarInfo->hostVelocityL = (speed & 0xFF);
            mRadarCarInfo->hostVelocityValid = true;
            mRadarCarInfo->turnSigL = dh.getTurnLeft();
            mRadarCarInfo->turnSigR = dh.getTurnRight();

            // 是否开启扩展BSD功能
            if (enExPandSig || mRadarCarInfo->turnSigL || mRadarCarInfo->turnSigR) {
                if (mRadarCarInfo->turnSigL) {
                    mRadarCarInfo->expandSig = 0x1;  // 左侧扩展区域
                } else if (mRadarCarInfo->turnSigR) {
                    mRadarCarInfo->expandSig = 0x2;  // 右侧扩展区域
                } else {
                    // todo 开启那边的区域？？？？
                }
                mRadarCarInfo->expandValid = true;
            } else {
                mRadarCarInfo->expandSig = 0;
            }

            msgEnque((void *)mRadarCarInfo.get(), sizeof(RadarCarInfo));
            mCanMsgTs[RADAR_CAR_INFO_CAN_ID] = my::timestamp::now();
        }
    };

    auto handleCarFlicInfo = [this](AlarmEvt *alarmEvt, uint8_t *level) {
        if (mCarFlicInfo == nullptr) {
            loge("error mCarFlicInfo == nullptr");
            return;
        }

        if (level[E_DEVICE_LEFT] != 0 || level[E_DEVICE_RIGHT] != 0) {
            mCarFlicAlarmExitCnt = 5;  // 退出报警，包文最后发5次
            // logd("car flic alarm exit");
        }

        if (mCarFlicAlarmExitCnt > 0 && mCanMsgTs[CAR_FLIC_CAN_ID].elapsed() >= 200) {
            if (level[E_DEVICE_LEFT] == 0 && level[E_DEVICE_RIGHT] == 0) {
                mCarFlicAlarmExitCnt = std::max(mCarFlicAlarmExitCnt - 1, 0);
            }
            mCarFlicInfo->leftBsdLevel = E_FLIC_ALARM_LEVEL_NONE + level[E_DEVICE_LEFT];
            mCarFlicInfo->rightBsdLevel = E_FLIC_ALARM_LEVEL_NONE + level[E_DEVICE_RIGHT];

            switch (alarmEvt[E_DEVICE_RIGHT]) {
                case E_ALARM_NONE: {
                    mCarFlicInfo->rightBsdWarning = E_FLIC_AUDIBLE_WARNING_OFF;
                    mCarFlicInfo->rightBsdAlarmCategory = E_FLIC_ALARM_CATEGORY_NO_ALARM;
                    mCarFlicInfo->rightBsdAlarmType = E_FLIC_ALARM_TYPE_NONE;
                    mCarFlicInfo->rightLcaAlarmArea = E_FLIC_ALARM_AREA_NONE;
                    break;
                }
                case E_RADAR_FLCA: {
                    mCarFlicInfo->rightBsdWarning = E_FLIC_AUDIBLE_WARNING_ON;
                    mCarFlicInfo->rightBsdAlarmCategory = E_FLIC_ALARM_CATEGORY_LCA;
                    mCarFlicInfo->rightBsdAlarmType = E_FLIC_ALARM_TYPE_RADAR;
                    mCarFlicInfo->rightLcaAlarmArea = E_FLIC_ALARM_AREA_FRONT;
                    break;
                }
                case E_RADAR_RLCA: {
                    mCarFlicInfo->rightBsdWarning = E_FLIC_AUDIBLE_WARNING_ON;
                    mCarFlicInfo->rightBsdAlarmCategory = E_FLIC_ALARM_CATEGORY_LCA;
                    mCarFlicInfo->rightBsdAlarmType = E_FLIC_ALARM_TYPE_RADAR;
                    mCarFlicInfo->rightLcaAlarmArea = E_FLIC_ALARM_AREA_REAR;
                    break;
                }
                case E_RADAR_FLCA_AND_RLCA: {
                    mCarFlicInfo->rightBsdWarning = E_FLIC_AUDIBLE_WARNING_ON;
                    mCarFlicInfo->rightBsdAlarmCategory = E_FLIC_ALARM_CATEGORY_LCA;
                    mCarFlicInfo->rightBsdAlarmType = E_FLIC_ALARM_TYPE_RADAR;
                    mCarFlicInfo->rightLcaAlarmArea = E_FLIC_ALARM_AREA_FRONT_AND_REAR;
                    break;
                }
                case E_RADAR_BSD: {
                    mCarFlicInfo->rightBsdWarning = E_FLIC_AUDIBLE_WARNING_ON;
                    mCarFlicInfo->rightBsdAlarmCategory = E_FLIC_ALARM_CATEGORY_BSD;
                    mCarFlicInfo->rightBsdAlarmType = E_FLIC_ALARM_TYPE_RADAR;
                    break;
                }
                case E_ALGO_BSD: {
                    mCarFlicInfo->rightBsdWarning = E_FLIC_AUDIBLE_WARNING_ON;
                    mCarFlicInfo->rightBsdAlarmCategory = E_FLIC_ALARM_CATEGORY_BSD;
                    mCarFlicInfo->rightBsdAlarmType = E_FLIC_ALARM_TYPE_CAMERA;
                    break;
                }
                default: {
                    loge("error alarmEvt");
                    break;
                }
            }

            switch (alarmEvt[E_DEVICE_LEFT]) {
                case E_ALARM_NONE: {
                    mCarFlicInfo->leftBsdWarning = E_FLIC_AUDIBLE_WARNING_OFF;
                    mCarFlicInfo->leftBsdLevel = E_FLIC_ALARM_LEVEL_NONE;
                    mCarFlicInfo->leftBsdAlarmCategory = E_FLIC_ALARM_CATEGORY_NO_ALARM;
                    mCarFlicInfo->leftBsdAlarmType = E_FLIC_ALARM_TYPE_NONE;
                    mCarFlicInfo->leftLcaAlarmArea = E_FLIC_ALARM_AREA_NONE;
                    break;
                }
                case E_RADAR_FLCA: {
                    mCarFlicInfo->leftBsdWarning = E_FLIC_AUDIBLE_WARNING_ON;
                    mCarFlicInfo->leftBsdAlarmCategory = E_FLIC_ALARM_CATEGORY_LCA;
                    mCarFlicInfo->leftBsdAlarmType = E_FLIC_ALARM_TYPE_RADAR;
                    mCarFlicInfo->leftLcaAlarmArea = E_FLIC_ALARM_AREA_FRONT;
                    break;
                }
                case E_RADAR_RLCA: {
                    mCarFlicInfo->leftBsdWarning = E_FLIC_AUDIBLE_WARNING_ON;
                    mCarFlicInfo->leftBsdAlarmCategory = E_FLIC_ALARM_CATEGORY_LCA;
                    mCarFlicInfo->leftBsdAlarmType = E_FLIC_ALARM_TYPE_RADAR;
                    mCarFlicInfo->leftLcaAlarmArea = E_FLIC_ALARM_AREA_REAR;
                    break;
                }
                case E_RADAR_FLCA_AND_RLCA: {
                    mCarFlicInfo->leftBsdWarning = E_FLIC_AUDIBLE_WARNING_ON;
                    mCarFlicInfo->leftBsdAlarmCategory = E_FLIC_ALARM_CATEGORY_LCA;
                    mCarFlicInfo->leftBsdAlarmType = E_FLIC_ALARM_TYPE_RADAR;
                    mCarFlicInfo->leftLcaAlarmArea = E_FLIC_ALARM_AREA_FRONT_AND_REAR;
                    break;
                }
                case E_RADAR_BSD: {
                    mCarFlicInfo->leftBsdWarning = E_FLIC_AUDIBLE_WARNING_ON;
                    mCarFlicInfo->leftBsdAlarmCategory = E_FLIC_ALARM_CATEGORY_BSD;
                    mCarFlicInfo->leftBsdAlarmType = E_FLIC_ALARM_TYPE_RADAR;
                    break;
                }
                case E_ALGO_BSD: {
                    mCarFlicInfo->leftBsdWarning = E_FLIC_AUDIBLE_WARNING_ON;
                    mCarFlicInfo->leftBsdAlarmCategory = E_FLIC_ALARM_CATEGORY_BSD;
                    mCarFlicInfo->leftBsdAlarmType = E_FLIC_ALARM_TYPE_CAMERA;
                    break;
                }
                default: {
                    loge("error alarmEvt");
                    break;
                }
            }
#if DEBUG
            logd("car flic alarm left %d right %d", mCarFlicInfo->leftBsdLevel, mCarFlicInfo->rightBsdLevel);
#endif
            msgEnque((void *)mCarFlicInfo.get(), sizeof(CarFlicInfo));
            mCanMsgTs[CAR_FLIC_CAN_ID] = my::timestamp::now();
        }
    };

    auto handleCanDisplay = [this](AlarmEvt *alarmEvt, uint8_t *level) {
        if (mRadarCarInfo == nullptr) {
            loge("error mRadarCarInfo == nullptr");
            return;
        }

        auto leftLineStatus = E_DISPLAY_HIDE;
        auto rightLineStatus = E_DISPLAY_HIDE;
        auto pedestrianStatus = E_DISPLAY_HIDE;
        auto segNumStatus = SegNum{0, true, E_DISPLAY_WHITE, E_DISPLAY_HIDE};
        auto vehicleStatus = Vehicle{E_DISPLAY_WHITE, E_DISPLAY_HIDE};

        auto &dh = DeviceHub::getInstance();
        int speed = dh.getCarSpeed();
        if (speed == 0) {
            if (mErrorCode != 0) {
                segNumStatus = SegNum{mErrorCode, true, E_DISPLAY_RED, E_DISPLAY_SHOW};
            } else {
                segNumStatus = SegNum{0, true, E_DISPLAY_WHITE, E_DISPLAY_SHOW};
            }
        }
        if (alarmEvt[E_DEVICE_LEFT] != E_ALARM_NONE) {
            leftLineStatus = E_DISPLAY_SHOW;
            if (alarmEvt[E_DEVICE_LEFT] == E_ALGO_BSD) {
                pedestrianStatus = E_DISPLAY_SHOW;
            } else {
                auto vehicleColor = (level[E_DEVICE_LEFT] == 3 ? E_DISPLAY_RED : E_DISPLAY_WHITE);
                vehicleStatus = Vehicle{vehicleColor, E_DISPLAY_SHOW};
                if (alarmEvt[E_DEVICE_LEFT] != E_RADAR_BSD) {
                    auto num = (uint8_t)(16 * level[E_DEVICE_LEFT]);
                    segNumStatus = SegNum{num, true, E_DISPLAY_WHITE, E_DISPLAY_SHOW};
                }
            }
        }
        if (alarmEvt[E_DEVICE_RIGHT] != E_ALARM_NONE) {
            rightLineStatus = E_DISPLAY_SHOW;
            if (alarmEvt[E_DEVICE_RIGHT] == E_ALGO_BSD) {
                pedestrianStatus = E_DISPLAY_SHOW;
            } else {
                auto vehicleColor = (level[E_DEVICE_RIGHT] == 3 ? E_DISPLAY_RED : vehicleStatus.color);
                vehicleStatus = Vehicle{vehicleColor, E_DISPLAY_SHOW};
                if (alarmEvt[E_DEVICE_RIGHT] != E_RADAR_BSD) {
                    auto num = std::max(segNumStatus.num, (uint8_t)(16 * level[E_DEVICE_RIGHT]));
                    segNumStatus = SegNum{num, true, E_DISPLAY_WHITE, E_DISPLAY_SHOW};
                }
            }
        }
#if DEBUG
        logd("refresh display left %d right %d pedestrian %d segnum %d vehicle %d",
             leftLineStatus,
             rightLineStatus,
             pedestrianStatus,
             segNumStatus.num,
             vehicleStatus.color);
#endif

        setLeftLineStatus(leftLineStatus);
        setRightLineStatus(rightLineStatus);
        setPedestrianStatus(pedestrianStatus);
        setSegNumStatus(segNumStatus);
        setVehicleStatus(vehicleStatus);
        refreshDispStatePack();
    };

    auto handleAudioOutput = [this](AlarmEvt *alarmEvt, uint8_t *level) {
        if (mRadarCarInfo == nullptr) {
            loge("error mRadarCarInfo == nullptr");
            return;
        }
        const static std::map<std::tuple<DeviceLocation, bool /*turnsig*/, uint8_t /*level*/>,
                              std::pair<std::string /*path*/, uint8_t /*priority*/>>
            algoBsdAlarmAudioMap = {
                {{E_DEVICE_LEFT, false, 3}, {"/data/audios/BSD_V_L_S1.wav", 5}},
                {{E_DEVICE_LEFT, false, 2}, {"/data/audios/BSD_V_S2.wav", 4}},
                {{E_DEVICE_LEFT, true, 3}, {"/data/audios/BSD_V_L_T1.wav", 6}},
                {{E_DEVICE_LEFT, true, 2}, {"/data/audios/BSD_V_T2.wav", 4}},
                {{E_DEVICE_LEFT, true, 1}, {"/data/audios/BSD_V_T3.wav", 3}},

                {{E_DEVICE_RIGHT, false, 3}, {"/data/audios/BSD_V_R_S1.wav", 5}},
                {{E_DEVICE_RIGHT, false, 2}, {"/data/audios/BSD_V_S2.wav", 4}},
                {{E_DEVICE_RIGHT, true, 3}, {"/data/audios/BSD_V_R_T1.wav", 6}},
                {{E_DEVICE_RIGHT, true, 2}, {"/data/audios/BSD_V_T2.wav", 4}},
                {{E_DEVICE_RIGHT, true, 1}, {"/data/audios/BSD_V_T3.wav", 3}},
            };
        const static std::map<std::tuple<AlarmEvt, DeviceLocation, uint8_t /*level*/>,
                              std::pair<std::string /*path*/, uint8_t /*priority*/>>
            radarAlarmAudioMap = {
                {{E_RADAR_BSD, E_DEVICE_LEFT, 2}, {"/data/audios/BSD_R_T2.wav", 3}},
                {{E_RADAR_BSD, E_DEVICE_LEFT, 3}, {"/data/audios/BSD_R_T3.wav", 4}},

                {{E_RADAR_BSD, E_DEVICE_RIGHT, 2}, {"/data/audios/BSD_R_T2.wav", 3}},
                {{E_RADAR_BSD, E_DEVICE_RIGHT, 3}, {"/data/audios/BSD_R_T3.wav", 4}},

                {{E_RADAR_FLCA, E_DEVICE_LEFT, 3}, {"/data/audios/LCA_R_LF_T3.wav", 5}},
                {{E_RADAR_RLCA, E_DEVICE_LEFT, 3}, {"/data/audios/LCA_R_LB_T3.wav", 5}},
                {{E_RADAR_FLCA_AND_RLCA, E_DEVICE_LEFT, 3}, {"/data/audios/LCA_R_LB_T3.wav", 5}},

                {{E_RADAR_FLCA, E_DEVICE_RIGHT, 3}, {"/data/audios/LCA_R_RF_T3.wav", 5}},
                {{E_RADAR_RLCA, E_DEVICE_RIGHT, 3}, {"/data/audios/LCA_R_RB_T3.wav", 5}},
                {{E_RADAR_FLCA_AND_RLCA, E_DEVICE_RIGHT, 3}, {"/data/audios/LCA_R_RB_T3.wav", 5}},
            };

        auto& dh = DeviceHub::getInstance();
        auto turnSigL = dh.getTurnLeft();
        auto turnSigR = dh.getTurnRight();
        CarOelTurnSigStatus turnSig = (turnSigL   ? E_CAR_OEL_TURN_SIG_LEFT
                                       : turnSigR ? E_CAR_OEL_TURN_SIG_RIGHT
                                                  : E_CAR_OEL_TURN_SIG_OFF);
        auto audioAlarmDevice = (turnSig == E_CAR_OEL_TURN_SIG_LEFT ? E_DEVICE_LEFT : E_DEVICE_RIGHT);
        auto audioAlarmEvt = E_ALARM_NONE;
        auto audioAlarmLevel = 0;

        if (turnSig != E_CAR_OEL_TURN_SIG_OFF && alarmEvt[audioAlarmDevice] != E_ALARM_NONE) {  // 转弯优先
            audioAlarmEvt = alarmEvt[audioAlarmDevice];
            audioAlarmLevel = level[audioAlarmDevice];
        } else {
            if (alarmEvt[E_DEVICE_RIGHT] != E_ALARM_NONE) {  // 右边优先
                audioAlarmDevice = E_DEVICE_RIGHT;
                audioAlarmEvt = alarmEvt[E_DEVICE_RIGHT];
                audioAlarmLevel = level[E_DEVICE_RIGHT];
            }
            if (alarmEvt[E_DEVICE_LEFT] != E_ALARM_NONE) {
                if (alarmEvt[E_DEVICE_LEFT] == E_ALGO_BSD || alarmEvt[E_DEVICE_LEFT] == E_RADAR_BSD) {
                    if (audioAlarmEvt != E_ALGO_BSD && audioAlarmEvt != E_RADAR_BSD) {  // BSD优先
                        audioAlarmDevice = E_DEVICE_LEFT;
                        audioAlarmEvt = alarmEvt[E_DEVICE_LEFT];
                        audioAlarmLevel = level[E_DEVICE_LEFT];
                    } else if (audioAlarmLevel < level[E_DEVICE_LEFT]) {  // BSD等级高优先
                        audioAlarmDevice = E_DEVICE_LEFT;
                        audioAlarmEvt = alarmEvt[E_DEVICE_LEFT];
                        audioAlarmLevel = level[E_DEVICE_LEFT];
                    }
                } else if (audioAlarmEvt != E_ALGO_BSD && audioAlarmEvt != E_RADAR_BSD &&
                           audioAlarmLevel < level[E_DEVICE_LEFT]) {
                    audioAlarmDevice = E_DEVICE_LEFT;
                    audioAlarmEvt = alarmEvt[E_DEVICE_LEFT];
                    audioAlarmLevel = level[E_DEVICE_LEFT];
                }
            }
        }

        if (mAudioTs.elapsed() >= 3000) {
            if (audioAlarmEvt == E_ALGO_BSD) {
                auto iter =
                    algoBsdAlarmAudioMap.find({audioAlarmDevice, turnSig != E_CAR_OEL_TURN_SIG_OFF, audioAlarmLevel});
                if (iter != algoBsdAlarmAudioMap.end()) {
                    mAudioTs = my::timestamp::now();
                    playAudio(iter->second.first, iter->second.second);
                    logi("play audio: %s", iter->second.first.c_str());
                }
            } else if (audioAlarmEvt != E_ALARM_NONE) {
                auto iter = radarAlarmAudioMap.find({audioAlarmEvt, audioAlarmDevice, audioAlarmLevel});
                if (iter != radarAlarmAudioMap.end()) {
                    mAudioTs = my::timestamp::now();
                    playAudio(iter->second.first, iter->second.second);
                    logi("play audio: %s", iter->second.first.c_str());
                }
            }
        }
    };

    // --

    std::chrono::milliseconds interval(50);
    std::unique_lock<std::mutex> lock(mInfoUpdateMtx);
    while (!exiting()) {
        AlarmEvt alarmEvt[E_DEVICE_MAX] = {E_ALARM_NONE, E_ALARM_NONE};
        uint8_t level[E_DEVICE_MAX] = {0, 0};
        for (int i = E_DEVICE_LEFT; i < E_DEVICE_MAX; i++) {
            if (mAlgoEvt[i].first.elapsed() <= 2000) {  // 算法bsd
                alarmEvt[i] = E_ALGO_BSD;
                level[i] = mAlgoEvt[i].second;
            }

            if (mRadarEvt[i].first == E_RADAR_BSD && mRadarEvt[i].second.second > level[i]) {  // 雷达bsd
                alarmEvt[i] = mRadarEvt[i].first;
                level[i] = mRadarEvt[i].second.second;
            } else if (mRadarEvt[i].first != E_ALGO_BSD && mRadarEvt[i].first != E_ALARM_NONE) {  // 其他
                alarmEvt[i] = mRadarEvt[i].first;
                level[i] = mRadarEvt[i].second.second;
            }
            // logd("device %c alarm %d level %d", i ? 'R' : 'L', alarmEvt[i], level[i]);
        }

        if (mIsCan1Chn) {
            handleCarFlicInfo(alarmEvt, level);
        } else {
            handleRadarCarInfo();
            handleCanDisplay(alarmEvt, level);
            handleAudioOutput(alarmEvt, level);
        }

        mInfoUpdateCond.wait_for(lock, interval);
    }
}

bool SuZhouJinLong::devCheck() {
#define CAR_ACC_STATUS "rw.minieye.power_vol"
    // 开机一分钟之后自检
#if DEBUG
    auto devCheckTime = std::chrono::seconds(0);
#else
    auto devCheckTime = std::chrono::seconds(60);
#endif
    bool devOk;
    do {
        devOk = true;
        logi("devcheck start in %ds", devCheckTime.count());
        std::this_thread::sleep_for(devCheckTime);
        devCheckTime = std::chrono::seconds(10);
        logi("devcheck start!!");

        char propValue[PROPERTY_VALUE_MAX] = {0};
        property_get(CAR_ACC_STATUS, propValue, " ");
        std::string acc(propValue, strlen(propValue));
        auto charV = acc.find(',');
        if (charV + 1 > acc.size() || acc[charV + 1] != '1') {
            ttsPlayInQueue(5, "电源异常");
            loge("devcheck power error");
            mErrorCode = 0xE1;
            devOk = false;
            continue;
        }

        // GPS
        LBS lbs;
        auto &devicehub = DeviceHub::getInstance();
        auto succ = devicehub.getGpsLbs(lbs);
        if (!succ) {
            ttsPlayInQueue(5, "定位模块故障");
            loge("devcheck gps error, lbs.antenna = %d", lbs.antenna);
            mErrorCode = 0xE2;
            devOk = false;
            continue;
        } else if (lbs.antenna == ANTENNA_OPEN) {
            ttsPlayInQueue(5, "定位天线开路");
            loge("devcheck gps antenna open");
            mErrorCode = 0xE3;
            devOk = false;
            continue;
        }

        // 网络
        memset(propValue, 0, sizeof(propValue));
        property_get("persist.minieye.eth0_ip", propValue, "");
        if (strcmp(propValue, "") == 0) {
            property_get("persist.mphone.ping.ip0", propValue, "");
        }
        if (strcmp(propValue, "") == 0) {
            ttsPlayInQueue(5, "设备无网络");
            loge("devcheck network error");
            mErrorCode = 0xE4;
            devOk = false;
            continue;
        }

        // 雷达
        {
            std::lock_guard<std::mutex> lg(mInfoUpdateMtx);
            if (!mRadarRecv) {
                ttsPlayInQueue(5, "雷达未连接");
                loge("devcheck radar error");
                mErrorCode = 0xE5;
                devOk = false;
                continue;
            }
        }

        // 摄像头
        bool bsdWorking = false, hodWorking = false;
        int sec, cams;
        my::conf::ini ini;
        my::conf::ini::load(ini, "/data/minieye/idvr/etc/config.ini");
        property_get("rw.minieye.cam_status", propValue, "");
        stringstream ss(propValue);
        if (ss >> sec >> cams) {
            char name[32] = "";
            int i = 1;
            while (cams) {
                if (cams & 1) {
                    snprintf(name, sizeof(name), "media.ch%d", i);
                    auto str = ini.get(name, "cam.phy", "");
                    if (str == "BSD" || str == "bsd") {
                        bsdWorking = true;
                    }
                    if (str == "HOD" || str == "hod") {
                        hodWorking = true;
                    }
#if DEBUG
                    logd("working camera: %s", str.c_str());
#endif
                }
                cams >>= 1;
                i++;
            }
            std::this_thread::sleep_for(std::chrono::seconds(2));  // 等两秒看看计时是否增加，避免读到过期property
            property_get("rw.minieye.cam_status", propValue, "");
            int nowSec;
            stringstream ss(propValue);
            if (ss >> nowSec) {
                if (nowSec <= sec) {
                    bsdWorking = false;
                }
            } else {
                bsdWorking = false;
            }
        }
        if (!bsdWorking || !hodWorking) {
            ttsPlayInQueue(5, "摄像头故障");
            loge("devcheck camera error");
            mErrorCode = 0xE6;
            devOk = false;
        }

    } while (!devOk);

    mErrorCode = 0;
    ttsPlayInQueue(5, "自检完成注意行车安全");
    loge("devcheck finish");
    return true;
}

SuZhouJinLongCan1::SuZhouJinLongCan1()
    : SuZhouJinLong(true) {
}

SuZhouJinLongCan2::SuZhouJinLongCan2()
    : SuZhouJinLong(false) {
}

std::shared_ptr<SuZhouJinLong::RadarCarInfo> SuZhouJinLong::mRadarCarInfo =
    std::make_shared<SuZhouJinLong::RadarCarInfo>();
std::shared_ptr<SuZhouJinLong::CarFlicInfo> SuZhouJinLong::mCarFlicInfo =
    std::make_shared<SuZhouJinLong::CarFlicInfo>();

std::atomic<bool> SuZhouJinLong::mIsInit;
std::condition_variable SuZhouJinLong::mInfoUpdateCond;
std::mutex SuZhouJinLong::mInfoUpdateMtx;
std::map<uint32_t, my::timestamp> SuZhouJinLong::mCanMsgTs;
int32_t SuZhouJinLong::mCarFlicAlarmExitCnt;
bool SuZhouJinLong::mRadarRecv;
uint8_t SuZhouJinLong::mErrorCode;
my::timestamp SuZhouJinLong::mAudioTs;
std::map<std::string, my::timestamp> SuZhouJinLong::mRadarEvtTsMap;
std::pair<SuZhouJinLong::AlarmEvt, std::pair<my::timestamp, uint8_t>>
    SuZhouJinLong::mRadarEvt[SuZhouJinLong::E_DEVICE_MAX];
std::pair<my::timestamp, uint8_t> SuZhouJinLong::mAlgoEvt[SuZhouJinLong::E_DEVICE_MAX];
std::shared_ptr<LibflowServer> SuZhouJinLong::mLibflowServer;

}  // namespace minieye
