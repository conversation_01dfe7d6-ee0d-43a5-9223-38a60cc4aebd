
#ifndef __MINIEYE_DFH2_TAHSENSOR_H__
#define __MINIEYE_DFH2_TAHSENSOR_H__

#include "cmdline.h"
namespace minieye
{

#define TAH_PACKAGE_SIZE_MIN (5)       //数据头 + 主信令 + 长度 + 校验码 + 结束符
#define TAH_PACKAGE_MAGIC            (0x24)
#define TAH_PACKAGE_END              (0xFF)
#define TAH_CMD_SENSOR_INFO          (0xF2)



typedef struct {
    uint8_t     magic;
    uint8_t     cmd;
    uint8_t     len;
    uint8_t     payload[0];
    //uint8_t     checksum;
} __attribute__((packed)) TAHProtHeaderT;


class TahSensor: public Protocol
{
    public:
        TahSensor(){};
        ~TahSensor(){};

        int32_t onServerConnected(void);
        int32_t onServerDisconnected(void);
        int32_t onDataRecevied(const char *p, uint32_t len);
        std::string setupCmdList(const char * cmdName)
        {
            return "";
        }
        bool runCmd(int argc, char **argv, string &ack);

    private:
        int32_t onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len);
        int32_t onDataParse(const char *p, uint32_t size);
        bool onMessage(TAHProtHeaderT *p, uint32_t len);
        bool onDataValidate(const char *p, uint32_t len);
        int32_t sendCmd(uint8_t cmd, uint8_t *payload=NULL, int32_t len=0);
        uint8_t calBcc(const char *ch, uint16_t len);



    private:
        vector<uint8_t>     mRcvArray;
};

}


#endif
