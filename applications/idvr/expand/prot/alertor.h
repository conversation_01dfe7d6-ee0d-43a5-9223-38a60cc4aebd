#ifndef __MINIEYE_DFH2_ALERTOR_H__
#define __MINIEYE_DFH2_ALERTOR_H__

#include "cmdline.h"
#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>   
#include <unistd.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h> 
#include <termios.h>
#include <arpa/inet.h>
#include <climits>

#include <vector>
#include <string>


using namespace std;

namespace minieye
{
/****************alertor cmd**************/
#define ATCMD_NONE                 0x0
#define ATCMD_HELP                 ATCMD_NONE

#define ATCMD_CTRL_STATE           0x10
#define ATCMD_CTRL_PLAY            0x11
#define ATCMD_CTRL_PAUSE           0x12
#define ATCMD_CTRL_STOP            0x13
#define ATCMD_CTRL_PREV            0x14
#define ATCMD_CTRL_NEXT            0x15
#define ATCMD_CTRL_PLAY_IDX        0x16
#define ATCMD_MUSIC_NUM            0x1D
#define ATCMD_CUR_MUSIC_IDX        0x1E

#define ATCMD_VOL_GET              0x20
#define ATCMD_VOL_SET              0x21
#define ATCMD_VOL_UP               0x22
#define ATCMD_VOL_DOWN             0x23

#define ATCMD_ALL_LOOP             0x30
#define ATCMD_SINGLE_LOOP          0x31
#define ATCMD_FLODER_LOOP          0x32
#define ATCMD_RAND_LOOP            0x33
#define ATCMD_SINGLE_STOP          0x34
#define ATCMD_SEQ                  0x35
#define ATCMD_FLODER_SEQ           0x36
#define ATCMD_FLODER_RAND          0x37

#define ATCMD_LED_ON               0x40
#define ATCMD_LED_OFF              0x41
#define ATCMD_LED_BLINK            0x42
#define ATCMD_LED_LUMI             0x43
#define ATCMD_LED_FRE              0x44

#define ATCMD_WRITE_MUSIC              0x60
#define ATCMD_WRITE_MUSIC_RESULT       0x61

#define ATCMD_AUDIO_LED_START      0x80
#define ATCMD_AUDIO_LED_STOP       0x81

#define ATCMD_GET_VERSION              0x90

#define ATCMD_MCU_UPGRADE              0xA0
#define ATCMD_MCU_BLD_MODE             0xB0
#define ATCMD_MCU_APP_MODE             0xB1
#define ATCMD_MCU_ALONE_MODE           0xB2
#define ATCMD_MCU_RESET                0xF0
/****************alertor cmd end *********/

/* audio status */
#define AUDIO_STOP                   0x00
#define AUDIO_BUSY                   0x01
#define AUDIO_PAUSE                  0x02
#define AUDIO_ERROR                  0x03

#define UART_MAGIC1 0XAA55
#define UART_MAGIC2 0X55AA
typedef struct {
    uint16_t    magic;
    uint8_t     cmd;
    uint8_t     len;
    uint8_t     payload[0];
    //uint8_t     checksum;
} __attribute__((packed)) UartProtHeaderT;

#define ALERTOR_UPGRD_FRAME_SIZE    (128)
#define ALERTOR_UPGRD_FILENAME      "/data/aoAlarm_app.bin.pack"
typedef struct {
    uint16_t crc;
    uint8_t packet_idx;
    uint8_t packet_total;
    uint8_t payload[0];
} __attribute__((packed))UpgradeHeaderT;

#define MUSIC_FRAME_SIZE 128
#define ALERTOR_MUSIC_FILENAME      "/data/alertor.mp3"
typedef struct {
    uint16_t idx_max;
    uint16_t idx;
    uint8_t payload[0];
}__attribute__((packed)) AudioFrameSeqT;

typedef struct {
    uint16_t file_seq;
    uint32_t file_crc;
    uint32_t file_size;
} __attribute__((packed)) UpgradeFrameSeqT;

class Alertor : public Protocol
{
    public:
        Alertor(void);
        ~Alertor();

        bool upgradeInit(void);
        bool sendMusicFileInfo(void);

        int32_t onServerConnected(void);
        int32_t onServerDisconnected(void);
        int32_t onDataRecevied(const char *p, uint32_t len);
		uint32_t getTimeOutVal(void) 
		{
			return INT_MAX;
		}

        std::string setupCmdList(const char * cmdName);
        bool runCmd(int argc, char **argv, string &ack);

    private:
        int32_t sendCmd(uint8_t cmd, uint8_t *payload=NULL, int32_t len=0);
        int32_t onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len);
        uint8_t calSum(const char *ch, uint16_t len);
        bool onUartValidate(const char *p, uint32_t len);
        bool onMessage(UartProtHeaderT *p, uint32_t len);
        int32_t onUartParse(const char *p, uint32_t size);
        uint64_t systime(void);
        void wait_ack(void);

        /* upgrade */
        bool sendUpgradeFrame(void);
        uint16_t UpdateCRC16(uint16_t crcIn, uint8_t byte);
        uint16_t CalCRC16(const uint8_t* data, uint32_t size);
        
        /* music */
        bool sendMusicFrame(void);

    private:
        string mCmdUsage;
        string mInfo;
        pthread_mutex_t mLock;
        pthread_cond_t mCond;


        bool mUpgradeEnable = false;
        bool mUpgradeAck    = false;
        vector<uint8_t>     mRcvArray;
        uint64_t            mUartSendNsec = 0;
        uint32_t mPacketIdx = 0;
        uint32_t mPacketTotal = 0;

        uint32_t mFileIdx = 0;
        uint32_t mFileIdxMax = 0;
};



} //namespace minieye

#endif

