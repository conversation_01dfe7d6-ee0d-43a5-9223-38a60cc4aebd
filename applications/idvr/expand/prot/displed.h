#ifndef __MINIEYE_DFH2_DISPLED_H__
#define __MINIEYE_DFH2_DISPLED_H__

#include "cmdline.h"
namespace minieye
{
#define DISP_BEGIN   '$'
#define DISP_END     '&'

#define DISP_OPEN_SCREEN     "11"
#define DISP_CLEAR_SCREEN    "12"
#define DISP_SET_BRIGHT		 "27"
#define DISP_DEL_MAIL		 "09"
#define DISP_VERSION		 "10"

#define DISP_COLOR_RED "00"
#define DISP_COLOR_GREEN "01"

class dispLed: public Protocol, public my::thread 
{
    public:
        dispLed(void)
        {
        	mSn = 1;
        	mHearBeat = 20;
        	mFlag = false;
        };
        ~dispLed(){};

        int32_t onServerConnected(void);
        int32_t onServerDisconnected(void);
        int32_t onDataRecevied(const char *p, uint32_t len);
        std::string setupCmdList(const char * cmdName)
        {
            return "";
        }
        bool runCmd(int argc, char **argv, string &ack);
        void run();
		const char * name() {
			return "dispLed";
		}
		uint32_t getTimeOutVal(void) 
		{
			return mHearBeat;
		}
    private:
        int32_t onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len);
        int32_t onDataParse(const char *p, uint32_t size);
        bool onMessage(const uint8_t *p, uint32_t len);
        bool onDataValidate(const uint8_t *p, uint32_t len);
        //int32_t sendCmd(uint8_t funcCode, uint8_t id, uint16_t len);
        int32_t send(const uint8_t* data, uint32_t len);
        bool sendCmd(std::string cmd, std::string param);
        bool sendData( std::string color, std::string data);
        bool utf8_2_GB2312(const char* in, int32_t intLen,  char* out, int32_t outLen);
        void dispSpeed(int32_t speed);
        void dispCert(bool isVaild);
    private:
        vector<uint8_t>     mRcvArray;
        string 				mInfo;
        uint32_t 			mSn;
        bool 				mFlag;
        uint32_t 			mHearBeat;
};

}

#endif
