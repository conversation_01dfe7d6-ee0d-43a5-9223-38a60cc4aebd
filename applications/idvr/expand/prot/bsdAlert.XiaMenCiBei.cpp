
#include "bsdAlert.XiaMenCiBei.h"

#include <assert.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <numeric>

#include "expand.h"
#include "system_properties.h"
#include "adas_alert.h"

namespace minieye {
// --- QM092 Alertor protocol begin ---

#define ALERTOR_PROT_HEAD (0xAA55)
#define ALERTOR_PROT_TAIL (0x55AA)

#define ALERTOR_PROT_CHECK_HEAD(p)              ((p)[0] == 0xAA && (p)[1] == 0x55)
#define ALERTOR_PROT_CHECK_TAIL(p, len)         ((p)[(len) - 2] == 0x55 && (p)[(len) - 1] == 0xAA)
#define ALERTOR_PROT_CHECK_CRC(p, len, chkCRC)  ((p)[(len) - 4] == ((uint8_t*)&chkCRC)[1] && (p)[(len) - 3] == ((uint8_t*)&chkCRC)[0])

typedef enum : uint8_t {
    ALERTOR_READ_ADDR           = 0x01,
    ALERTOR_READ_ADDR_RESP      = 0x81,

    ALERTOR_SET_ADDR            = 0x02,
    ALERTOR_SET_ADDR_RESP       = 0x82,

    ALERTOR_READ_BAUDRATE       = 0x03,
    ALERTOR_READ_BAUDRATE_RESP  = 0x83,

    ALERTOR_SET_BAUDRATE        = 0x04,
    ALERTOR_SET_BAUDRATE_RESP   = 0x84,

    ALERTOR_SET_ALERT_MODE      = 0x05,
    ALERTOR_SET_ALERT_MODE_RESP = 0x85,
} ALERTOR_PROT_E;

typedef enum : uint8_t {
    ALERTOR_BAUD_1200           = 0x00,
    ALERTOR_BAUD_2400           = 0x01,
    ALERTOR_BAUD_4800           = 0x02,
    ALERTOR_BAUD_9600           = 0x03,
    ALERTOR_BAUD_19200          = 0x04,
    ALERTOR_BAUD_38400          = 0x05,
    ALERTOR_BAUD_57600          = 0x06,
    ALERTOR_BAUD_115200         = 0x07,
} ALERTOR_BAUD_E;

uint32_t aBaudrate[] = {1200, 2400, 4800, 9600, 19200, 38400, 57600, 115200};

typedef enum : uint8_t {
    ALERTOR_MODE_NO_ALERT           = 0x00,
    ALERTOR_MODE_SOUND1_AND_FLASH   = 0x01,
    ALERTOR_MODE_FLASH              = 0x02,
    ALERTOR_MODE_SOUND1             = 0x03,
    ALERTOR_MODE_SOUND2_AND_FLASH   = 0x04,
    ALERTOR_MODE_SOUND2             = 0x05,
} ALERTOR_MODE_E;

typedef struct AMsg {
    uint16_t magicBgn = ALERTOR_PROT_HEAD;
    uint8_t addr = 1;
    uint8_t cmd = 0;
    uint16_t length = 0;
    my::string payload;
    uint16_t crc = 0;
    uint16_t magicEnd = ALERTOR_PROT_TAIL;

    int32_t encode(my::string &msg);
    int32_t decode(uint8_t *msg, int32_t len);
    static bool onValidate(uint8_t *p, int32_t len);
    static uint16_t checkCRC(uint8_t *p, uint16_t len);
} AMsg;

uint16_t AMsg::checkCRC(uint8_t *updata, uint16_t len) {
    static const uint8_t auchCRCHi[] = {
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1,
        0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1,
        0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40};

    static const uint8_t auchCRCLo[] = {
        0x00, 0xC0, 0xC1, 0x01, 0xC3, 0x03, 0x02, 0xC2, 0xC6, 0x06, 0x07, 0xC7, 0x05, 0xC5, 0xC4, 0x04, 0xCC, 0x0C,
        0x0D, 0xCD, 0x0F, 0xCF, 0xCE, 0x0E, 0x0A, 0xCA, 0xCB, 0x0B, 0xC9, 0x09, 0x08, 0xC8, 0xD8, 0x18, 0x19, 0xD9,
        0x1B, 0xDB, 0xDA, 0x1A, 0x1E, 0xDE, 0xDF, 0x1F, 0xDD, 0x1D, 0x1C, 0xDC, 0x14, 0xD4, 0xD5, 0x15, 0xD7, 0x17,
        0x16, 0xD6, 0xD2, 0x12, 0x13, 0xD3, 0x11, 0xD1, 0xD0, 0x10, 0xF0, 0x30, 0x31, 0xF1, 0x33, 0xF3, 0xF2, 0x32,
        0x36, 0xF6, 0xF7, 0x37, 0xF5, 0x35, 0x34, 0xF4, 0x3C, 0xFC, 0xFD, 0x3D, 0xFF, 0x3F, 0x3E, 0xFE, 0xFA, 0x3A,
        0x3B, 0xFB, 0x39, 0xF9, 0xF8, 0x38, 0x28, 0xE8, 0xE9, 0x29, 0xEB, 0x2B, 0x2A, 0xEA, 0xEE, 0x2E, 0x2F, 0xEF,
        0x2D, 0xED, 0xEC, 0x2C, 0xE4, 0x24, 0x25, 0xE5, 0x27, 0xE7, 0xE6, 0x26, 0x22, 0xE2, 0xE3, 0x23, 0xE1, 0x21,
        0x20, 0xE0, 0xA0, 0x60, 0x61, 0xA1, 0x63, 0xA3, 0xA2, 0x62, 0x66, 0xA6, 0xA7, 0x67, 0xA5, 0x65, 0x64, 0xA4,
        0x6C, 0xAC, 0xAD, 0x6D, 0xAF, 0x6F, 0x6E, 0xAE, 0xAA, 0x6A, 0x6B, 0xAB, 0x69, 0xA9, 0xA8, 0x68, 0x78, 0xB8,
        0xB9, 0x79, 0xBB, 0x7B, 0x7A, 0xBA, 0xBE, 0x7E, 0x7F, 0xBF, 0x7D, 0xBD, 0xBC, 0x7C, 0xB4, 0x74, 0x75, 0xB5,
        0x77, 0xB7, 0xB6, 0x76, 0x72, 0xB2, 0xB3, 0x73, 0xB1, 0x71, 0x70, 0xB0, 0x50, 0x90, 0x91, 0x51, 0x93, 0x53,
        0x52, 0x92, 0x96, 0x56, 0x57, 0x97, 0x55, 0x95, 0x94, 0x54, 0x9C, 0x5C, 0x5D, 0x9D, 0x5F, 0x9F, 0x9E, 0x5E,
        0x5A, 0x9A, 0x9B, 0x5B, 0x99, 0x59, 0x58, 0x98, 0x88, 0x48, 0x49, 0x89, 0x4B, 0x8B, 0x8A, 0x4A, 0x4E, 0x8E,
        0x8F, 0x4F, 0x8D, 0x4D, 0x4C, 0x8C, 0x44, 0x84, 0x85, 0x45, 0x87, 0x47, 0x46, 0x86, 0x82, 0x42, 0x43, 0x83,
        0x41, 0x81, 0x80, 0x40};
    uint8_t uchCRCHi = 0xff;
    uint8_t uchCRCLo = 0xff;
    uint16_t uindex;

    while (len--) {
        uindex = uchCRCHi ^ *updata++;
        uchCRCHi = uchCRCLo ^ auchCRCHi[uindex];
        uchCRCLo = auchCRCLo[uindex];
    }
    return (uchCRCHi << 8 | uchCRCLo);
}

int32_t AMsg::encode(my::string &msg) {
    msg << my::hton;
    length = payload.length();
    msg << magicBgn << addr << cmd << length;
    msg << payload;
    crc = checkCRC((uint8_t *)msg.c_str(), msg.length());
    msg << crc;
    msg << magicEnd;
    return msg.length();
}
int32_t AMsg::decode(uint8_t *msg, int32_t len) {
    if ((len <= 4) || (!ALERTOR_PROT_CHECK_HEAD(msg)) || (!ALERTOR_PROT_CHECK_TAIL(msg, len))) {
        return -1;
    }

    my::constr msgStr((const char *)msg, len);
    msgStr >> my::ntoh;
    msgStr >> magicBgn >> addr >> cmd >> length;
    payload = my::string((const char *)msgStr, msgStr.length() - 4);
    msgStr += payload.length();
    msgStr >> crc;
    msgStr >> magicEnd;

    uint16_t chkCRC = checkCRC(msg, len - 4);
    if (crc != chkCRC) {
        loge("checkCRC error! 0x%04x != 0x%04x", crc, chkCRC);
        return -2;
    }

    return 0;
}

bool AMsg::onValidate(uint8_t *p, int32_t len) {
    if ((len <= 4) || (!ALERTOR_PROT_CHECK_HEAD(p)) || (!ALERTOR_PROT_CHECK_TAIL(p, len))) {
        return false;
    }

    uint16_t chkCRC = checkCRC(p, len - 4);
    return ALERTOR_PROT_CHECK_CRC(p, len, chkCRC);
}

// --- QM092 Alertor protocol end ---

// --- bsd prompter screen protocol begin ---

#define PS_PROT_HEAD (0xBB)
#define PS_PROT_TAIL (0x55)

#define PS_PROT_CHECK_HEAD(p)      ((p)[0] == PS_PROT_HEAD)
#define PS_PROT_CHECK_TAIL(p, len) ((p)[len - 1] == PS_PROT_TAIL)

typedef enum {
    PS_SHOW = 0xB1,
} PS_PROT_E;

typedef enum {
    PS_CREAR_SCREEN = 0x00,
    PS_SHOW_RED_HUMAN_FIGURE = 0x01,
    PS_SHOW_YELLOW_HUMAN_FIGURE = 0x02,
    PS_SHOW_GREEN_HUMAN_FIGURE = 0x03,
} PS_MODE_E;

typedef enum {
    PS_NOT_ACTIVE = 0x00,
    PS_LEFT_DEVICE_ACTIVE = 0xA0,
    PS_RIGHT_DEVICE_ACTIVE = 0xA1,
} PS_ACTIVE_TAG_E;

typedef enum {
    PS_SOUND_LEVEL_NORMAL = 0x33,
    PS_SOUND_LEVEL_LOW    = 0x34,
    PS_SOUND_LEVEL_MUTED  = 0x30
} PS_SOUND_LEVEL_E;

my::string psMode2protData(PS_MODE_E mode, bool isRightDevice) {
    if (mode < PS_CREAR_SCREEN && mode > PS_SHOW_GREEN_HUMAN_FIGURE) {
        logd("not find mode %d", mode);
        return "";
    }

    auto tmp = std::vector<uint8_t>();
    if (isRightDevice) {
        tmp.push_back(PS_NOT_ACTIVE);
        tmp.push_back(PS_NOT_ACTIVE);
        tmp.push_back(PS_RIGHT_DEVICE_ACTIVE);
        tmp.push_back(mode);
    } else {
        tmp.push_back(PS_LEFT_DEVICE_ACTIVE);
        tmp.push_back(mode);
        tmp.push_back(PS_NOT_ACTIVE);
        tmp.push_back(PS_NOT_ACTIVE);
    }

    return my::string((const char *)tmp.data(), tmp.size());
}

typedef struct PSMsg {
    uint8_t magicBgn = PS_PROT_HEAD;
    uint8_t addr = 1;
    uint16_t len = 0;
    uint8_t cmd = PS_PROT_E::PS_SHOW;
    my::string mode;
    uint16_t sum = 0; // low_byte first
    uint8_t magicEnd = PS_PROT_TAIL;

    int32_t encode(my::string &msg);
    int32_t decode(uint8_t *msg, int32_t len);
    uint16_t checkSum();
} PSMsg;

int32_t PSMsg::encode(my::string &msg) {
    msg << my::hton << magicBgn << addr << len << cmd << mode;
    msg << checkSum();
    msg << magicEnd;
    return msg.length();
}
int32_t PSMsg::decode(uint8_t *msg, int32_t len) {
    if ((len <= 1) || (!PS_PROT_CHECK_HEAD(msg)) || (!PS_PROT_CHECK_TAIL(msg, len))) {
        return -1;
    }

    my::constr msgStr((const char *)msg, len);
    msgStr >> my::ntoh;
    msgStr >> magicBgn >> addr >> this->len >> cmd;
    mode = my::string((const char *)msgStr, msgStr.length() - 3);
    msgStr += mode.length();
    msgStr >> sum;
    msgStr >> magicEnd;

    uint16_t chkSum = checkSum();
    if (sum != chkSum) {
        loge("checkSum error! 0x%04x != 0x%04x", sum, chkSum);
        return -2;
    }

    return 0;
}
uint16_t PSMsg::checkSum() {
    uint16_t modeSum = std::accumulate((uint8_t *)mode.c_str(), (uint8_t *)mode.c_str() + mode.length(), 0);
    uint16_t sum = addr + len + cmd + modeSum;

    uint8_t lowByte = sum & 0xFF;
    uint8_t highByte = (sum >> 8) & 0xFF;

    sum = (lowByte << 8) | highByte;
    return sum;
}

// --- bsd prompter screen protocol end ---

BsdAlertXiaMenCiBei::BsdAlertXiaMenCiBei() {
    AlgoManager & am = AlgoManager::getInstance();
    char tmp[256];
    snprintf(tmp, sizeof(tmp), "bsdAlert_XiaMenCiBei%p", this);
    am.addObserver(tmp, this);

    __system_property_set(BSD_ALERT_XIAMENCIBEI_RUN_PROPERTY, "true");
    mpFileLog = new FileLog("/data/minieye/idvr/mlog/xmcb_bsd_alert_log/", 5 * 1024 * 1024, 10);
    mpFileLog->prepare();
    start();
}
int32_t BsdAlertXiaMenCiBei::onServerConnected(void) { return 0; }
int32_t BsdAlertXiaMenCiBei::onServerDisconnected(void) { return 0; }

int32_t BsdAlertXiaMenCiBei::onDataRecevied(const char *p, uint32_t len) {
    // logd("on cmd data %d", len);
    // logd("recv %s", my::hex(my::constr(p, len)).c_str());

    onCmdData(mRcvArray, p, len);
    return 0;
}

int32_t BsdAlertXiaMenCiBei::onCmdData(vector<uint8_t> &recvArray, const char *p, uint32_t len) {
    recvArray.insert(recvArray.end(), p, p + len);
    my::hexdump(my::constr((const char *)recvArray.data(), recvArray.size()), true, "expand.onCmdData");
    int32_t parsed = 0;

    do {
        parsed = onDataParseAMsg((const char *)recvArray.data(), recvArray.size());
        //assert(parsed <= recvArray.size());

        if (recvArray.size() >= (1 << 20)) {
            parsed = recvArray.size();
            logd("frame too long!!\n");
        }

        if (parsed < 0) {
            logd("frame no header, erase all!! %d\n", parsed);
            recvArray.erase(recvArray.begin(), recvArray.begin() + recvArray.size());
            parsed = 0;

        } else if (parsed > 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + parsed);
            //logd("erase size %d\n", parsed);

        } else {
            //logd("alertor frame not complete!!\n");
        }

        if (recvArray.size() <= 0 || parsed <= 0) {
            break;
        }

    } while (1);
    return parsed;
}

int32_t BsdAlertXiaMenCiBei::onDataParseAMsg(const char *p, uint32_t size) {
    int32_t start = -1;
    int32_t end = -1;
    int32_t n = static_cast<int32_t>(size) - 1;

    for (int i = 0; i < n; i++) {
        if (start < 0 && ALERTOR_PROT_CHECK_HEAD(p + i)) {
            start = i;
            // logd("start = %d", i);
        } else if (start >= 0 && (p[i] == 0x55 && p[i + 1] == 0xAA)) {
            end = i + 1;
            // logd("end = %d", i + 1);
            break;
        }
    }

    if ((start >= 0) && (end >= 0)) {
        uint8_t *tmp = (uint8_t *)(p + start);
        uint32_t len = end - start + 1;

        if (AMsg::onValidate(tmp, len)) {
            onMessage(tmp, len);
        } else {
            loge("invalid data:\n");
            // my::hexdump(my::constr((char*)protMsg.data(), protMsg.size()), true, "expand.onDataParse");
            /*to skip dirty data, such as "xx xx xx 7E 7E xx xx 7E"*/
            if (ALERTOR_PROT_CHECK_HEAD(tmp) && ALERTOR_PROT_CHECK_HEAD(tmp + 2)) {
                end = -1;
                start += 2;
            }
        }

    } else {
        logd("******start =  %d, end = %d\n", start, end);
    }

    return (end >= 0) ? (end + 1) : start;
}

bool BsdAlertXiaMenCiBei::onAlgoEvent(std::shared_ptr<Event> e) {
    switch (e->type()) {
        case EVT_TYPE_BSD_Front:
        case EVT_TYPE_BSD_Behind: {
            std::lock_guard<std::mutex> _l_(mSendCmdMtx);
            auto idx = (e->type() == EVT_TYPE_BSD_Front ? E_DEVICE_LEFT : E_DEVICE_RIGHT);
            mLastBsdEvt[idx] = e;
            mNextTriggerDev[idx] = E_DEVICE_ALERTOR;
            mSendCmdCv.notify_one();
            break;
        }
        case EVT_TYPE_BSD_Left: 
        case EVT_TYPE_BSD_Right: {
            std::lock_guard<std::mutex> _l_(mSendCmdMtx);
            auto idx = (e->type() == EVT_TYPE_BSD_Left ? E_DEVICE_LEFT : E_DEVICE_RIGHT);
            mLastBsdEvt[idx] = e;
            mNextTriggerDev[idx] = E_DEVICE_ALERTOR;
            mSendCmdCv.notify_one();
            break;
        }
        case EVT_TYPE_ADAS_CAN700_MSG: {
            my::string data = e->mEvtData["data"];
            ADAS_CAN700 *can700 = (ADAS_CAN700 *)data.c_str();

            if (mHWAlarmTs.elapsed() > 5000 && can700->headway_valid) {
                if (can700->headway_measurement >= 20 && can700->headway_measurement <= 25) { // 2.0s - 2.5s
                    LogCallProxyCmd::sendReq("media", "cmd play /data/audios/HMW25.wav 1"); // Attention! The vehicles ahead!
                    mHWAlarmTs = my::timestamp::now();
                }
            }
            break;
        }
        default:
            break;
    }
    return true;
}

bool BsdAlertXiaMenCiBei::onMessage(uint8_t *p, uint32_t len) {
    mLastDataUtcTime = time(NULL);
    // my::hexdump(my::constr((const char *)p, len), true, "expand.onMsg");

    AMsg msg;
    msg.decode(p, len);
    if (msg.length != 1) {
        logd("Invalid AMsg: length(%d) is not 1", msg.length);
        return true;
    }
    switch (msg.cmd) {
        case ALERTOR_READ_ADDR_RESP: { /*读取地址应答*/
            std::lock_guard<std::mutex> _l_(mAlertorDevInfoMtx);
            logd("ALERTOR_READ_ADDR_RESP succ, addr:%02x!", msg.addr);
            break;
        }
        case ALERTOR_SET_ADDR_RESP: { /*设置地址应答*/
            std::lock_guard<std::mutex> _l_(mAlertorDevInfoMtx);
            if (mAlertorDevInfo[0].addr == msg.addr) {
                mAlertorDevInfo[0].addr = msg.payload[0];
                logd("ALERTOR_SET_ADDR_RESP succ, new leftAlertor addr:%02x!", msg.addr);
            } else if (mAlertorDevInfo[1].addr == msg.addr) {
                mAlertorDevInfo[1].addr = msg.payload[0];
                logd("ALERTOR_SET_ADDR_RESP succ, new rightAlertor addr:%02x!", msg.addr);
            } else {
                logd("ALERTOR_SET_ADDR_RESP succ, unknown alertor addr:%02x!", msg.addr);
            }
            break;
        }

        case ALERTOR_READ_BAUDRATE_RESP: { /*读取波特率应答*/
            if (msg.payload[0] >= ALERTOR_BAUD_1200 && msg.payload[0] <= ALERTOR_BAUD_115200) {
                std::lock_guard<std::mutex> _l_(mAlertorDevInfoMtx);
                if (mAlertorDevInfo[0].addr == msg.addr) {
                    mAlertorDevInfo[0].baudrate = msg.payload[0];
                    logd("ALERTOR_READ_BAUDRATE_RESP succ, leftAlertor baudrate:%02x -> %d!", msg.payload[0],
                         aBaudrate[msg.payload[0]]);
                } else if (mAlertorDevInfo[1].addr == msg.addr) {
                    mAlertorDevInfo[1].baudrate = msg.payload[0];
                    logd("ALERTOR_READ_BAUDRATE_RESP succ, rightAlertor baudrate:%02x -> %d!", msg.payload[0],
                         aBaudrate[msg.payload[0]]);
                } else {
                    logd("ALERTOR_READ_BAUDRATE_RESP succ, unknown alertor baudrate:%02x -> %d!", msg.payload[0],
                         aBaudrate[msg.payload[0]]);
                }
            } else {
                logd("ALERTOR_READ_BAUDRATE_RESP fail, invalid baudrate:%02x!", msg.payload[0]);
            }
            break;
        }
        case ALERTOR_SET_BAUDRATE_RESP: { /*设置波特率应答*/
            if (msg.payload[0] >= ALERTOR_BAUD_1200 && msg.payload[0] <= ALERTOR_BAUD_115200) {
                std::lock_guard<std::mutex> _l_(mAlertorDevInfoMtx);
                if (mAlertorDevInfo[0].addr == msg.addr) {
                    mAlertorDevInfo[0].baudrate = msg.payload[0];
                    logd("ALERTOR_SET_BAUDRATE_RESP succ, new leftAlertor baudrate:%02x -> %d!", msg.payload[0],
                         aBaudrate[msg.payload[0]]);
                } else if (mAlertorDevInfo[1].addr == msg.addr) {
                    mAlertorDevInfo[1].baudrate = msg.payload[0];
                    logd("ALERTOR_SET_BAUDRATE_RESP succ, new rightAlertor baudrate:%02x -> %d!", msg.payload[0],
                         aBaudrate[msg.payload[0]]);
                } else {
                    logd("ALERTOR_SET_BAUDRATE_RESP succ, unknown alertor baudrate:%02x -> %d!", msg.payload[0],
                         aBaudrate[msg.payload[0]]);
                }
            } else {
                logd("ALERTOR_SET_BAUDRATE_RESP fail, invalid baudrate:%02x!", msg.payload[0]);
            }
            break;
        }
        case ALERTOR_SET_ALERT_MODE_RESP: { /*设置报警模式应答*/
            if (msg.payload[0] >= ALERTOR_MODE_NO_ALERT && msg.payload[0] <= ALERTOR_MODE_SOUND2) {
                std::lock_guard<std::mutex> _l_(mAlertorDevInfoMtx);
                if (mAlertorDevInfo[0].addr == msg.addr) {
                    if (mAlertorDevInfo[0].alertMode != msg.payload[0]) {
                        logd("test fail!!!!");
                    }
                    mAlertorDevInfo[0].alertMode = msg.payload[0];
                    logd("ALERTOR_SET_ALERT_MODE_RESP succ, new leftAlertor alertMode:%02x", msg.payload[0]);
                } else if (mAlertorDevInfo[1].addr == msg.addr) {
                    if (mAlertorDevInfo[1].alertMode != msg.payload[0]) {
                        logd("test fail!!!!");
                    }
                    mAlertorDevInfo[1].alertMode = msg.payload[0];
                    logd("ALERTOR_SET_ALERT_MODE_RESP succ, new rightAlertor alertMode:%02x", msg.payload[0]);
                } else {
                    logd("ALERTOR_SET_ALERT_MODE_RESP succ, unknown alertor alertMode:%02x", msg.payload[0]);
                }
            } else {
                logd("ALERTOR_SET_ALERT_MODE_RESP fail, invalid mode:%02x!", msg.payload[0]);
            }
            break;
        }
        default: {
            logd("ALERTOR_RESP_CHECK fail, unknown cmd:%02x!", msg.cmd);
            break;
        }
    }

    return true;
}

#define BSD_ALERT_CMD_HELP 0
#define BSD_ALERT_CMD_SHOW 1
#define BSD_ALERT_CMD_SET_ALERTOR_MODE 2
#define BSD_ALERT_CMD_SET_PROMPTER_MODE 3

static const std::vector<CmdStrT> gBsdAlertCmds = {
    {"help", BSD_ALERT_CMD_HELP, "show this usage.\n"},
    {"show", BSD_ALERT_CMD_SHOW, "show last alert data.\n"},
    {"alertor", BSD_ALERT_CMD_SET_ALERTOR_MODE, "set alertor mode.\n"},
    {"prompter", BSD_ALERT_CMD_SET_PROMPTER_MODE, "set prompter screen mode.\n"}};

std::string BsdAlertXiaMenCiBei::setupCmdList(const char *cmdName) {
    mCmdUsage = CmdStrT::setupCmdList(gBsdAlertCmds, cmdName);
    return mCmdUsage;
}
bool BsdAlertXiaMenCiBei::runCmd(int argc, char **argv, string &ack) {
    uint32_t cmd = CmdStrT::strToCmd(gBsdAlertCmds, argv[0]);

    switch (cmd) {
        case BSD_ALERT_CMD_HELP: {
            ack = mCmdUsage;
            return true;
        }

        case BSD_ALERT_CMD_SHOW: {
            ack = "\n";
            APPEND_STR_MSG(ack, "Msg UTC", 16, "%ld", mLastDataUtcTime);

            my::string t;
            t.assignf(R"(
         Alertor:
          - left : addr:0x%02x, baudrate:0x%02x, alertMode:0x%02x
          - right: addr:0x%02x, baudrate:0x%02x, alertMode:0x%02x
         Prompter:
          - left : addr:0x%02x, baudrate:0x%02x, alertMode:0x%02x
          - right: addr:0x%02x, baudrate:0x%02x, alertMode:0x%02x
)",
                      mAlertorDevInfo[0].addr, mAlertorDevInfo[0].baudrate, mAlertorDevInfo[0].alertMode,
                      mAlertorDevInfo[1].addr, mAlertorDevInfo[1].baudrate, mAlertorDevInfo[1].alertMode,
                      mPrompterDevInfo[0].addr, mPrompterDevInfo[0].baudrate, mPrompterDevInfo[0].alertMode,
                      mPrompterDevInfo[1].addr, mPrompterDevInfo[1].baudrate, mPrompterDevInfo[1].alertMode);

            ack.append(t.c_str());

            return true;
        }
        case BSD_ALERT_CMD_SET_ALERTOR_MODE: {
            ack = "\n";
            AMsg msg;
            auto idx = 0;
            uint8_t data = ALERTOR_MODE_NO_ALERT;
            auto addr = mAlertorDevInfo[0].addr;
            if (argc > 2) {
                idx = !!atoi(argv[1]);
                data = (ALERTOR_MODE_E)atoi(argv[2]);
            }
            msg.addr = mAlertorDevInfo[idx].addr;
            msg.cmd = ALERTOR_SET_ALERT_MODE;
            msg.payload << my::hton << data;
            my::string s;
            msg.encode(s);
            msgEnque((void *)s.c_str(), s.length());
            {
                std::lock_guard<std::mutex> _l_(mAlertorDevInfoMtx);
                mAlertorDevInfo[idx].lastAlertTs = my::timestamp::now();
                mAlertorDevInfo[idx].alertMode = data;
            }
            return true;
        }
        case BSD_ALERT_CMD_SET_PROMPTER_MODE: {
            ack = "\n";
            PSMsg msg;
            auto idx = 0;
            auto mode = PS_CREAR_SCREEN;
            auto addr = mPrompterDevInfo[0].addr;
            if (argc > 2) {
                idx = !!atoi(argv[1]);
                mode = static_cast<PS_MODE_E>(atoi(argv[2]));
            }
            msg.addr = mPrompterDevInfo[idx].addr;
            msg.mode = psMode2protData(mode, idx);
            msg.cmd = PS_SHOW;
            msg.len = 6 + msg.mode.length();
            my::string s;
            msg.encode(s);
            msgEnque((void *)s.c_str(), s.length());
            {
                std::lock_guard<std::mutex>_l_(mPrompterDevInfoMtx);
                mPrompterDevInfo[idx].lastRefreshTs = my::timestamp::now();
                mPrompterDevInfo[idx].alertMode = mode;
            }
            return true;
        }
        default: {
            ack = mCmdUsage;
        }
    }

    return false;
}

void BsdAlertXiaMenCiBei::run() {
    constexpr int PROMPTER_REFRESH_INTERVAL_MS = 1000;
    constexpr int ALERTOR_REFRESH_INTERVAL_MS = 1000;
    constexpr int PROMPTER_SEND_CMD_INTERVAL_MS = 5;
    constexpr int ALERTOR_SEND_CMD_INTERVAL_MS = 300;
    constexpr int DEFAULT_WAIT_TIME_MS = 100;
    auto sendCmd2Prompter = [this](int32_t idx, PS_MODE_E mode) -> bool {
        if (mLastPrompterCmdTs.elapsed() < PROMPTER_SEND_CMD_INTERVAL_MS) {
            logd("too frequent cmd!");
            return false;
        } else {
            mLastPrompterCmdTs = my::timestamp::now();
            mPrompterDevInfo[idx].lastRefreshTs = my::timestamp::now();
            mPrompterDevInfo[idx].alertMode = mode;
        }

        PSMsg msg;
        msg.addr = mPrompterDevInfo[idx].addr;
        msg.mode = psMode2protData(mode, idx);
        msg.cmd = PS_SHOW;
        msg.len = 6 + msg.mode.length();
        my::string s;
        msg.encode(s);
        // my::hexdump(my::constr(s.c_str(), s.length()), false);
        msgEnque((void *)s.c_str(), s.length());
        return true;
    };
    auto sendCmd2Alertor = [this](int32_t idx, uint8_t mode) -> bool {
        if (mLastAlertorCmdTs.elapsed() < ALERTOR_SEND_CMD_INTERVAL_MS) {
            logd("too frequent cmd!");
            return false;
        } else {
            mLastAlertorCmdTs = my::timestamp::now();
            mAlertorDevInfo[idx].lastAlertTs = my::timestamp::now();
            mAlertorDevInfo[idx].alertMode = mode;
        }

        AMsg msg;
        msg.cmd = ALERTOR_SET_ALERT_MODE;
        msg.addr = mAlertorDevInfo[idx].addr;
        msg.payload << my::hton << mode;
        my::string s;
        msg.encode(s);
        msgEnque((void *)s.c_str(), s.length());
        return true;
    };

    prctl(PR_SET_NAME, "bsdAlertXiaMenCiBei");

    std::unique_lock<std::mutex> sendCmdLock(mSendCmdMtx);
    while (!exiting()) {
        std::chrono::milliseconds nextWakeupMs(DEFAULT_WAIT_TIME_MS);
        do {
            for (int i = E_DEVICE_LEFT; i <= E_DEVICE_RIGHT; i++) {
                if (mLastBsdEvt[i] != nullptr) {
                    if (mNextTriggerDev[i] == E_DEVICE_ALERTOR) {
                        std::lock_guard<std::mutex> _l_(mAlertorDevInfoMtx);
                        if (sendCmd2Alertor(i, ALERTOR_MODE_SOUND1_AND_FLASH)) {
                            mNextTriggerDev[i] = E_DEVICE_PROMPTER;
                        } else {
                            break;
                        }
                    }
                    if (mNextTriggerDev[i] == E_DEVICE_PROMPTER) {
                        std::lock_guard<std::mutex> _l_(mPrompterDevInfoMtx);
                        auto mode = (mLastBsdEvt[i]->c.level == 1   ? PS_SHOW_RED_HUMAN_FIGURE
                                     : mLastBsdEvt[i]->c.level == 2 ? PS_SHOW_YELLOW_HUMAN_FIGURE
                                                                    : PS_SHOW_GREEN_HUMAN_FIGURE);
                        if (sendCmd2Prompter(i, mode)) {
                            mLastBsdEvt[i].reset();
                        } else {
                            nextWakeupMs = std::chrono::milliseconds(PROMPTER_SEND_CMD_INTERVAL_MS);
                            break;
                        }
                    }
                }
            }
        } while (0);

        do {
            std::lock_guard<std::mutex> _l_(mPrompterDevInfoMtx);
            for (int i = E_DEVICE_LEFT; i <= E_DEVICE_RIGHT; i++) {
                int64_t elapsed = mPrompterDevInfo[i].lastRefreshTs.elapsed();
                if (elapsed >= PROMPTER_REFRESH_INTERVAL_MS) {
                    sendCmd2Prompter(i, PS_CREAR_SCREEN);
                    break;
                } else {
                    nextWakeupMs =
                        std::min(nextWakeupMs, std::chrono::milliseconds(PROMPTER_REFRESH_INTERVAL_MS + 1 - elapsed));
                }
            }
        } while (0);

        do {
            std::lock_guard<std::mutex> _l_(mAlertorDevInfoMtx);
            for (int i = E_DEVICE_LEFT; i <= E_DEVICE_RIGHT; i++) {
                int64_t elapsed = mAlertorDevInfo[i].lastAlertTs.elapsed();
                if (elapsed >= ALERTOR_REFRESH_INTERVAL_MS) {
                    sendCmd2Alertor(i, ALERTOR_MODE_NO_ALERT);
                    nextWakeupMs = std::chrono::milliseconds(PROMPTER_SEND_CMD_INTERVAL_MS);
                    break;
                } else {
                    nextWakeupMs =
                        std::min(nextWakeupMs, std::chrono::milliseconds(ALERTOR_REFRESH_INTERVAL_MS + 1 - elapsed));
                }
            }
        } while (0);

        mSendCmdCv.wait_for(sendCmdLock, nextWakeupMs);
    }
}

}  // namespace minieye
