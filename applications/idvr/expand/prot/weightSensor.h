
#ifndef __MINIEYE_DFH2_WEIGHTERSENSOR_H__
#define __MINIEYE_DFH2_WEIGHTERSENSOR_H__

#include "protocol.h"
namespace minieye
{

#define SAHX_120B_BEGIN         0x3a
#define SAHX_120B_END_1         0x0d
#define SAHX_120B_END_2         0x0a  

#define SAHX_120B_ADDR          0X71

#define SAHX_120B_FUNC_CODE_READ_PARAM    0x03
#define SAHX_120B_FUNC_CODE_READ_DATA     0x04
#define SAHX_120B_FUNC_CODE_WRITE_PARAM   0x10

#define SAHX_120B_MESSAGE_ID_UNIT         0x3d
#define SAHX_120B_MESSAGE_ID_AD           0x08
#define SAHX_120B_MESSAGE_ID_WEIGHT       0X04
#define SAHX_120B_MESSAGE_ID_CALIB        0x80

#define SAHX_120B_LENGTH_UNIT             0x01
#define SAHX_120B_LENGTH_AD               0x02
#define SAHX_120B_LENGTH_WEIGHT           0x02

#define CALIB_JSON_PATH "/data/minieye/idvr/etc/calib.json"

typedef struct {
    uint8_t     addr;
    uint8_t     funcCode;
    uint8_t     ID[2];
    uint16_t    len;
    uint8_t     check;
} __attribute__((packed)) SAHX_120B_Send_Msg;

typedef struct {
    uint32_t    AD;
    uint32_t    weight;
}__attribute__((packed)) SAHX_120B_Clib_Data;

typedef struct {
    uint8_t             addr;
    uint8_t             funcCode;
    uint8_t             ID[2];
    uint16_t            dataNum;
    uint8_t             byteNum;
    uint16_t            totalNum;
    uint16_t            index;
    SAHX_120B_Clib_Data data[10];
    uint8_t             check;
} __attribute__((packed)) SAHX_120B_Clib_Msg;


typedef struct {
    uint8_t     addr;
    uint8_t     funcCode;
    uint8_t     len;
    uint8_t     data[0];
    //uint8_t     check;
} __attribute__((packed)) SAHX_120B_RecvMsg;

#if 0
typedef struct {
    uint8_t     head[4];
    uint8_t     cmd[3];
    uint8_t     ID[3];
    uint8_t     check[2];
    uint8_t     end[2];
} __attribute__((packed)) SAHX_120B_RecvMsg;
#endif

//有为称重传感器
class  weightSensor_SAHX_120B: public Protocol, public my::thread 
{
    public:
        weightSensor_SAHX_120B(void);
        ~weightSensor_SAHX_120B();

        int32_t onServerConnected(void);
        int32_t onServerDisconnected(void);
        int32_t onDataRecevied(const char *p, uint32_t len);
        std::string setupCmdList(const char * cmdName)
        {
            return "";
        }
        bool runCmd(int argc, char **argv, string &ack);
        void run();
		const char * name() {
			return "weightSensor_SAHX_120B";
		}

    private:
        int32_t onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len);
        int32_t onDataParse(const char *p, uint32_t size);
        bool onMessage(SAHX_120B_RecvMsg *p, uint32_t len);
        bool onDataValidate(const uint8_t *p, uint32_t len);
        int32_t sendCmd(uint8_t funcCode, uint8_t id, uint16_t len);
        uint8_t calLRC(const uint8_t *ch, uint32_t len);
        bool onConvert(const uint8_t* inData, vector<uint8_t>& outData, int len);
        bool onUnConvert(const uint8_t* inData, vector<uint8_t>& outData, int len);
        bool sendWeightCmd(void);
        bool sendFlowMessage(void);
        bool wait_ack(void);
        bool sendUnitCmd(void);
        bool sendADCmd(void);
        bool sendCalibCmd(void);
        bool parseCalibDataFromJson(void);
        bool dumpCalibData(void);

    private:
        bool mParseWeightFlag;
        vector<uint8_t>     mRcvArray;
        uint16_t mUnit;
        uint32_t mWeight;
        uint32_t mWeightAD;
        bool mStatus;
        bool mIsCalib;
        string mInfo;
        pthread_mutex_t mLock;
        pthread_cond_t mCond;
        std::map<int, int> mCalibData;
        std::string mSensorType;
};

//重优称重传感器
class  weightSensor_ZZH_201: public Protocol, public my::thread {
    public:
        weightSensor_ZZH_201();
        ~weightSensor_ZZH_201();
        int32_t onServerConnected(void);
        int32_t onServerDisconnected(void);
        int32_t onDataRecevied(const char *p, uint32_t len);
        std::string setupCmdList(const char * cmdName) {
            return "";
        }
        bool runCmd(int argc, char **argv, string &ack);
        bool onDataValidate(const uint8_t *p, uint32_t len);
        uint8_t calSUM(const uint8_t *ch, uint32_t len);
        bool sendWeightCmd(void);
		bool sendADCmd(void);
        bool sendCalibCmd(void);
        bool sendFlowMessage(void);
        void run();
        bool wait_ack(void);
        int32_t onDataParse(const uint8_t *p, uint32_t size);
        bool onMessage(const uint8_t *p, uint32_t len);
        int32_t onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len);
        bool parseCalibDataFromJson(void);
        bool dumpCalibData(void);
		const char * name() {
			return "weightSensor_ZZH_201";
		}
private:
        vector<uint8_t>     mRcvArray;
        uint16_t mUnit;
        uint32_t mWeight;
        uint32_t mWeightAD;
        bool mStatus;
        bool mIsCalib;
        string mInfo;
        pthread_mutex_t mLock;
        pthread_cond_t mCond;
        std::map<int, int> mCalibData;
        std::string mSensorType;
};


/* 中寰载重传感器 */
typedef enum
{
    MSG_STEP_VALID,
    MSG_STEP_SEND,
    MSG_STEP_BACK,
    MSG_STEP_END,
}CAN_MSG_STEP;
typedef enum
{
    CORECT_STEP_TORQUE,
    CORECT_STEP_ORIENTATION,
    CORECT_STEP_ACCELERATED_FILTER,
    CORECT_STEP_ACCELERATED_INITIAL, 
    CORECT_STEP_LOAD_LIMIT,
    CORECT_STEP_LOAD_CALIBRATION_COEFFICIENT,
    CORECT_STEP_TRANSMISSION_TYPE,
    CORECT_STEP_TRANSMISSION_STATUS,
    CORECT_STEP_SAVE,
    CORECT_STEP_END,
}CORRECT_STEP;
struct LOAD_CORRECT{
    CORRECT_STEP correctStep = CORECT_STEP_TORQUE;
    CAN_MSG_STEP msgStep = MSG_STEP_VALID;

    void nextStep(){
        switch(correctStep) {
            case CORECT_STEP_TORQUE:
                correctStep = CORECT_STEP_ORIENTATION;
                break;
            case CORECT_STEP_ORIENTATION:
                correctStep = CORECT_STEP_ACCELERATED_FILTER;
                break;
            case CORECT_STEP_ACCELERATED_FILTER:
                correctStep = CORECT_STEP_ACCELERATED_INITIAL;
                break;
            case CORECT_STEP_ACCELERATED_INITIAL:
                correctStep = CORECT_STEP_LOAD_LIMIT;
                break;
            case CORECT_STEP_LOAD_LIMIT:
                correctStep = CORECT_STEP_LOAD_CALIBRATION_COEFFICIENT;
                break;
            case CORECT_STEP_LOAD_CALIBRATION_COEFFICIENT:
                correctStep = CORECT_STEP_TRANSMISSION_TYPE;
                break;
            case CORECT_STEP_TRANSMISSION_TYPE:
                correctStep = CORECT_STEP_TRANSMISSION_STATUS;
                break;
            case CORECT_STEP_TRANSMISSION_STATUS:
                correctStep = CORECT_STEP_SAVE;
                break;
            case CORECT_STEP_SAVE:
                correctStep = CORECT_STEP_END;
                break;
            default:
                break;
        }
    }
};
    
typedef struct {
    uint16_t torque;       /* 扭矩 200~3000Nm */
    uint8_t  orientation; /* 1:朝向驾驶室 2:朝向车尾 */
    uint16_t acceleratedFilter; /* 加速度系数50~800       */
    uint16_t impactFilter;    /* 冲击度系数 50~800      */
    uint16_t acceleratedInitial; /* 加速度初值 -1000~1000 发给载重传感器需加上偏移量1000即 -1000发送给载重传感器0（-1000+1000=0）      */
    uint16_t fullLoadLimit; /* 满载值   1000~60000kg  */
    uint16_t halfLoadLimit; /* 半载值   1000~60000kg  */
    uint16_t emptyLoadLimit; /* 空载值   1000~60000kg  */
    uint8_t fullLoadLimitCoefficient; /* 满载标定系数0~200         */
    uint8_t halfLoadLimitCoefficient; /* 半载标定系数0~200         */
    uint8_t emptyLoadLimitCoefficient; /* 空载标定系数0~200         */
    uint8_t transmissionType;         /* 0:手动档    1:自动档 */
    uint8_t transmissionStatus;     /* 0:传动系信号0为断开 1:传动系信号1为断开                 */
    uint32_t canLoadFrameId;            /* 载重上报can id */
    uint32_t canCalibReqFrameId;            /* 校准请求can id */
    uint32_t canCalibRspFrameId;            /* 校准响应can id */

    uint32_t canSysInfoReqFrameId;         /* 获取系统信息请求can id */
    uint32_t canSysInfoRspFrameId;         /* 系统信息上报can id */
}CalibParam;


struct sysInfoFrame_t{
    bool        start = false;
    uint32_t    frameNum = 0;    
    uint32_t    dataBits = 0;
    uint32_t    recNum = 0;
    uint32_t    lastFrameId = 0;

    my::string sysInfoData;  

    void reset()
    {
        start = false;
        frameNum = 0;    
        dataBits = 0;
        recNum = 0;
        lastFrameId = 0;
        sysInfoData.clear();
    }
};

class  weightSensor_ZHF03: public Protocol, public my::thread {
public:
    weightSensor_ZHF03();
    ~weightSensor_ZHF03(){;}
    bool correctLoadSensor();
    const char * name() {
		return "weightSensor_ZHF03";
	}
    int32_t onServerConnected(void);
    int32_t onServerDisconnected(void);
    int32_t onDataRecevied(const char *p, uint32_t len);
    bool runCmd(int argc, char **argv, string &ack);
    std::string setupCmdList(const char * cmdName) {
        return "";
    }
    virtual void setChannelId(int channelId) {
        mCanChId = channelId;
        logd("setChannelId!\n");
        loadConfig();
        loadCalibConfig();
    }    
protected:
    bool sendFlowMessage(void);
    void run();
private:
    void clearFilter();
    void setFilter(uint32_t canId);
    void setCanMode(std::string mode);
    bool loadConfig();
    bool loadCalibConfig();
    bool setLoadSensor(const CORRECT_STEP loop);
    int32_t onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len);
    int32_t onDataParse(const uint8_t *p, uint32_t size);
    void correctResponseParse(const uint8_t * canData);
    int canDataDeal(const uint32_t canID, const uint8_t *canData);
    void defaultCalibConfig() ;

    int32_t comeInGetSysInfo();
    bool reqSysInfo();
    int32_t comeInWorkMode();
    bool parseSysInfo(const uint8_t * data);
    
    bool            mIsCalib = false;        /* 默认未校正 */
    LOAD_CORRECT    mLoadCorrectLoop;
    my::timestamp   mMsgTm;
    my::timestamp   mLoadErrorTm; /* 载重传感器不可用告警 */
    int             mWeight = -1;
    bool            mStatus = 0; /* 载重值是否稳定 0:不稳定 1:稳定 */
    uint16_t        mRspTorque = 0;
    uint8_t         mVersion = 0;
    uint8_t         mRaw[8];
    vector<uint8_t> mRcvArray;
    int             mCanChId = -1;
    bool            mCanEnable = false;
    std::string     mCanBaudRate;
    std::string     mCanMode;
    CalibParam      mCalibParam;
    bool            mCanInfoInit = false;

    std::mutex      mLock;
    bool            mSysInfoInit = false;
    my::timestamp   mGetSysInfo;
    sysInfoFrame_t  mSysInfo;
    int             mWorkMode = 0;  /* 获取系统信息模式 */
};
}
#endif


