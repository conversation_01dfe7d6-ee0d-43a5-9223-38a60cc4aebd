#pragma once

#include "cmdline.h"
#include "FileLog.h"
#include "expand.message.h"
#include "algo.h"
#include "canDisplay.h"
// #include "ipcAgent.h"

namespace minieye {

class AlinkAlertor : public Protocol, my::thread, public IALGO_OBSERVER {
 public:
    enum AlinkAlertorPlayContent {
        E_CONTENT_NONE = 0,
        E_BSD_ALERT = 1,
        E_TURN_LEFT = 2,
        E_TURN_RIGHT = 3,
        E_REVERSE = 4,
    };

    struct AlinkMsg;

    explicit AlinkAlertor();
    virtual ~AlinkAlertor() noexcept;
    AlinkAlertor &operator=(const AlinkAlertor &other) = delete;
    AlinkAlertor(const AlinkAlertor &other) = delete;

 protected:
    virtual bool onAlgoEvent(std::shared_ptr<Event> evt);
    virtual int32_t onServerConnected(void);
    virtual int32_t onServerDisconnected(void);
    virtual int32_t onDataRecevied(const char *p, uint32_t len);
    virtual std::string setupCmdList(const char *cmdName);
    virtual bool runCmd(int argc, char **argv, string &ack);
    virtual void run();

 private:
   //  std::shared_ptr<IpcClient> mIpcClient;
    std::string mCmdUsage;

    my::timestamp mLastAlertUpdateTs;
    my::timestamp mLastBsdAlarmTs;
    bool mIsReverseOn = false;
    bool mIsTurnOn = false;
};

}  // namespace minieye