#include "idvr.media.h"
#include "IpcMsgHandler.h"

#ifndef MEDIA_CAMERA_MAX_NUM
#define MEDIA_CAMERA_MAX_NUM 9
#endif

static bool init()
{
    IpcMsgHandler * pimh = IpcMsgHandler::getInstance();
    AlgoManager & am = AlgoManager::getInstance();
    am.enablePlayAudio();
    am.addObserver("ipcMsgHdlr", pimh);
    std::vector<std::string> funcList;
    for (int32_t ch = 0; ch < MEDIA_CAMERA_MAX_NUM; ch++) {
        const char * ai = mediaGetChnAlgoName(ch);
        if (ai != nullptr) {
            funcList.push_back(std::string(ai));
            if (!strcmp(ai, "dms")) {
                funcList.push_back("faceid_v2");
            }
        }
    }
    am.start(funcList);
    pimh->start();
    return true;
}

static bool notifyPowerDownPending()
{
    IpcMsgHandler::getInstance()->notifyPowerDownPending();
    return true;
}

static bool notifyCVBSCtrlOn()
{
    IpcMsgHandler::getInstance()->notifyCVBSCtrl(true);
    return true;
}

static bool notifyCVBSCtrlOff()
{
    IpcMsgHandler::getInstance()->notifyCVBSCtrl(false);
    return true;
}

static bool notifyPowerDown()
{
    IpcMsgHandler::getInstance()->notifyPowerDown();
    return true;
}

static bool release()
{
    // IpcMsgHandler::getInstance()->stop();
    return true;
}

__attribute((constructor)) void before_main()
{
    __system_properties_init();
}

static bool notifySetGpsLbs(const mpegts::tsFrameInfo_t& tsFrameInfo)
{
    LBS lbs;
    memset(&lbs, 0, sizeof(LBS));
    lbs.rtkData.lat = tsFrameInfo.tsGps.la / 100000000.0;
    lbs.rtkData.lng = tsFrameInfo.tsGps.lo / 100000000.0;
    lbs.rtkData.sig = tsFrameInfo.tsGps.gpsState;
    lbs.alt_x10 = tsFrameInfo.tsGps.alt;
    lbs.dir_x100 = tsFrameInfo.tsGps.dirAngle;
    lbs.speed_x10 = tsFrameInfo.speed * 10;

#if 0
    static my::timestamp _LastTime = my::timestamp::now();
    if (_LastTime.elapsed() >= 1000) {
        logd("rtkData.la:   %.8lf", lbs.rtkData.lat);
        logd("rtkData.lo:   %.8lf", lbs.rtkData.lng);
        logd("rtkData.sig:  %d", lbs.rtkData.sig);
        logd("alt:          %d", lbs.alt_x10);
        logd("dir:          %d", lbs.dir_x100);
        logd("speed:        %d", lbs.speed_x10);
        _LastTime = my::timestamp::now();
    }
#endif

    IpcMsgHandler::getInstance()->notifySetGpsLbs(lbs);

    return true;
}

int32_t main(int32_t argc, char **argv)
{
    my::log::setLogTag(LOG_NAME);

    callbackfn_t callbackfn;
    callbackfn.initCb = init;
    callbackfn.notifyPowerDownPendingCb = notifyPowerDownPending;
    callbackfn.notifyCVBSCtrlOnCb = notifyCVBSCtrlOn;
    callbackfn.notifyCVBSCtrlOffCb = notifyCVBSCtrlOff;
    callbackfn.notifyPowerDownCb = notifyPowerDown;
    callbackfn.releaseCb = release;
    callbackfn.rTsExtractCb = notifySetGpsLbs;

    return mediaMainLoop(argc, argv, callbackfn);
}
