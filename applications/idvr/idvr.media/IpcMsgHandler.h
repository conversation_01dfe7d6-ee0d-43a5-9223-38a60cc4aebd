#ifndef __IPC_MSG_HANDLER_H__
#define __IPC_MSG_HANDLER_H__

#include "algo.h"
#include "ipcMessage.h"
#include "McuMessage.h"
#include "DrivingStatus.h"
#include "libflow.h"
#include "jsonUtil.h"

class IpcMsgHandler
    : public my::thread
    , public IALGO_OBSERVER
{
public:
    bool start();
    void stop();

    bool notifyPowerDown();
    bool notifyPowerDownPending();
    bool notifyCVBSCtrl(bool on);
    bool notifySetGpsLbs(LBS lbs);

    static IpcMsgHandler *getInstance();

private:
    virtual bool onAlgoEvent(std::shared_ptr<Event> evt);
    virtual void run();

private:
    IpcMsgHandler();
    ~IpcMsgHandler();
    bool init();
    bool onRecv(McuMessage *msg);
    bool commSend(McuMsgTypeE cmd, uint8_t *data, int32_t len);

private:
    IpcClient * mClient = nullptr;
};

#endif
