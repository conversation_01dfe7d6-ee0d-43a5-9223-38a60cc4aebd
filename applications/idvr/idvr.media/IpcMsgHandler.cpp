#include "IpcMsgHandler.h"
#include "properties.h"
#include "MiniUiComm.h"
#include "idvr.media.h"

static IpcMsgHandler *_mcuMsgHandler = nullptr;

IpcMsgHandler::IpcMsgHandler()
{
}

IpcMsgHandler::~IpcMsgHandler()
{
    stop();
    if (mClient != nullptr) {
        delete mClient;
    }
}
bool IpcMsgHandler::init()
{
    if (mClient == nullptr) {
        mClient = new IpcClient("idvr.media", SH_NAME_HOSTIO);
    }

    return (mClient != nullptr);
}

bool IpcMsgHandler::start()
{
    if ((mClient == nullptr) ||
        (my::thread::start() < 0)) {
        loge("start error ! mClient %p", mClient);
        return false;
    }

    char propValue[PROP_VALUE_MAX] = { 0 };
    int propLen = __system_property_get(PROP_PERSIST_MINIEYE_DEVICEID, propValue);
    if (propLen > 10) {
        std::string devId = std::string(propValue, propLen);
        devId = devId.substr(10);
        setDrivingStatusDeviceId(devId);
    }

    return true;
}

void IpcMsgHandler::stop()
{
    my::thread::stop();
}

bool IpcMsgHandler::commSend(McuMsgTypeE cmd, uint8_t *data, int32_t len)
{
    if (mClient != nullptr) {
        uint8_t buf[MCU_MSG_MAX_SIZE];
        McuMessage *pMsg = (McuMessage *)&buf[0];
        memset(buf, 0, sizeof(buf));
        pMsg->ipcMsg.type = cmd;
        pMsg->ipcMsg.len = len;
        if (data && len) {
            memcpy(&pMsg->u.u8Array[0], data, len);
        }
        return mClient->send(&(pMsg->ipcMsg));
    }
    return false;
}

bool IpcMsgHandler::notifyPowerDown()
{
    uint8_t buf[1] = {PWR_DOWN_THREAD_MEDIA};
    return commSend(MCU_MSG_TYPE_PWR_DOWN, buf, 1);;
}

bool IpcMsgHandler::notifyPowerDownPending()
{
    uint8_t buf[1] = {PWR_DOWN_THREAD_MEDIA};
    commSend(MCU_MSG_TYPE_PWR_DOWN_PENDING, buf, 1);
    return true;
}

bool IpcMsgHandler::notifyCVBSCtrl(bool on)
{
    uint8_t buf[2] = {MINOR_GPIO_PWR_12V_OFF, MINOR_GPIO_PWR_12V_ON};
    commSend(MCU_MSG_TYPE_IO_CTRL, &buf[(uint8_t) on], 1);
    return true;
}

bool IpcMsgHandler::notifySetGpsLbs(LBS lbs)
{
    uint8_t buf[MCU_MSG_MAX_SIZE] = { 0 };
    commSend(MCU_MSG_TYPE_F9KGPS_LIBS, (uint8_t *) &lbs, sizeof(lbs));
    return true;
}

void IpcMsgHandler::run()
{
    prctl(PR_SET_NAME, "IpcMsgHandler");

    while (! exiting()) {
        uint8_t buf[MCU_MSG_MAX_SIZE] = {0};
        McuMessage *pMsg = (McuMessage *) &buf[0];

        //logd("IpcMsgHandler read");

        if (mClient->recv((IpcMessage *) pMsg)) {
            onRecv(pMsg);
        } else {
            loge("mcu client read error !");
        }
    }
}

bool IpcMsgHandler::onRecv(McuMessage *msg)
{
    switch (msg->ipcMsg.type) {
        case MCU_MSG_TYPE_STAT: {
            if (MCU_MSG_SIZE_STAT == msg->ipcMsg.len) {
                // 配置了RTK模块使用RTK数据
                bool enRTK = msg->u.stat[0].lbs.rtkData.enable;
                setDrivingStatusGpsMode(enRTK);
                if (enRTK) {
                    setDrivingStatusGps(msg->u.stat[0].lbs.rtkData.lng, msg->u.stat[0].lbs.rtkData.lat,
                        msg->u.stat[0].lbs.sat, msg->u.stat[0].lbs.antenna);
                    setDrivingStatusSpeed(msg->u.stat[0].lbs.speed_x10 / 10);
                    setDrivingStatusGpsState(msg->u.stat[0].lbs.rtkData.sig);

                } else {
                    setDrivingStatusGps(msg->u.stat[0].lbs.lng_x1kw / 10000000.0, msg->u.stat[0].lbs.lat_x1kw / 10000000.0,
                        msg->u.stat[0].lbs.sat, msg->u.stat[0].lbs.antenna);
                    setDrivingStatusSpeed(msg->u.stat[0].speed_x10 / 10.0);
                    setDrivingStatusGpsState(msg->u.stat[0].lbs.sig_level);
                }

                setDrivingStatusAlt(msg->u.stat[0].lbs.alt_x10);
                setDrivingStatusDir(msg->u.stat[0].lbs.dir_x100);

                if (!msg->u.stat[0].vehicle_io.acc) {
                    char propval[PROP_VALUE_MAX] = {0};
                    __system_property_get(PROP_RW_MINIEYE_WAKEUP_STATUS, propval);
                    if (propval[0]) {
                        if (!strcmp(propval, "true")) {
                            setDrivingStatusAccEnable(true);
                        } else {
                            setDrivingStatusAccEnable(false);
                        }
                    } else {
                        setDrivingStatusAccEnable(false);
                    }
                } else {
                    setDrivingStatusAccEnable(msg->u.stat[0].vehicle_io.acc);
                }

                setDrivingStatusPowerOff(msg->u.stat[0].sys.power_off);
                setDrivingStatusBraking(msg->u.stat[0].vehicle_io.brake);
                setDrivingStatusSteering(msg->u.stat[0].turnl ?
                    SteeringWheel::LEFT : (msg->u.stat[0].turnr ?
                    SteeringWheel::RIGHT : SteeringWheel::STRAIGHT));
                setDrivingStatusEmergencyAlarm(msg->u.stat[0].vehicle_io.emergency_alarm);
                setDrivingStatusICCardInserted(msg->u.stat[0].dvr_io.iccard_inserted);
                setDrivingStatusPanelDoorOpened(msg->u.stat[0].dvr_io.panel_door_open);
                setDrivingStatusPrinterHot(msg->u.stat[0].dvr_io.printer_hot);
                setDrivingStatusPrinterNoPaper(msg->u.stat[0].dvr_io.printer_no_paper);
                setDrivingCanLossStatus(msg->u.stat[0].vehicle_io.can_signal_loss);
                bool network = false;
                char propValue[PROP_VALUE_MAX] = { 0 };
                __system_property_get("rw.minieye.connected", propValue);
                network = !!atoi(propValue);
                setDrivingStatusNetwork(network);
            }
            break;
        }
        case MCU_MSG_TYPE_IC_CARD: {
            //loge("mcu client MCU_MSG_TYPE_IC_CARD");
            if (MCU_MSG_SIZE_ICCARD == msg->ipcMsg.len) {
                ICCardWrBuf* icCard = msg->u.icCard;
            }
            break;
        }
        case MCU_MSG_TYPE_RECORD_STATUS: {
            //logd("mcu client MCU_MSG_TYPE_RECORD_STATUS: 0x%X", msg->u.int32[0]);
            setDrivingStatusRecordStatus((uint32_t) msg->u.int32[0]);
            break;
        }
        case MCU_MSG_TYPE_RAW_CAN0: {
            if (msg->u.canMsg[0].id == 0x1d1) {
                logd("recv can data: %x-%x-%x-%x-%x-%x-%x-%x", msg->u.canMsg[0].data[0], msg->u.canMsg[0].data[1],
                    msg->u.canMsg[0].data[2], msg->u.canMsg[0].data[3],msg->u.canMsg[0].data[4], msg->u.canMsg[0].data[5], 
                    msg->u.canMsg[0].data[6], msg->u.canMsg[0].data[7]);

                std::vector<uint8_t> data;
                for (int32_t i = 0; i < 8; i++) {
                    data.push_back(msg->u.canMsg[0].data[i]);
                }
                setDrivingRadarDistance(data);
            }
            break;
        }
        default: {
            //loge("unknown mcu msg type %d, len %d", msg->type, msg->len);
            break;
        }
    }

    return true;
}

IpcMsgHandler *IpcMsgHandler::getInstance()
{
	if (_mcuMsgHandler == nullptr) {
		_mcuMsgHandler = new IpcMsgHandler();
        if (_mcuMsgHandler != nullptr) {
            _mcuMsgHandler->init();
        }
	}
	return _mcuMsgHandler;
}
bool IpcMsgHandler::onAlgoEvent(std::shared_ptr<Event> evt)
{
    switch(evt->type()) {
        case EVT_TYPE_CAM_OCCLUSION_INFO: {
            auto it = evt->mEvtDataInt.find("chBitTbl");
            if (it != evt->mEvtDataInt.end()) {
                setDrivingStatusChnOcclusionStatus(it->second);
            }
            break;
        }
        case EVT_TYPE_OBJP_INFO: {
            if (!access("/data/minieye/idvr/etc/.eur_gsr_custom", R_OK)) { /*GSR:151/159定制显示*/
                std::unordered_map<std::string, std::unordered_map<int32_t, std::string>> dir2img = {
                    {"front",       {{2, "/system/etc/img/infoFront.bmp"},  {1, "/system/etc/img/warnFront.bmp"}}},
                    {"right_front", {{2, "/system/etc/img/infoRF.bmp"},     {1, "/system/etc/img/warnRF.bmp"}}},
                    {"right",       {{2, "/system/etc/img/infoRight.bmp"},  {1, "/system/etc/img/warnRight.bmp"}}},
                    {"right_rear",  {{2, "/system/etc/img/infoRR.bmp"},     {1, "/system/etc/img/warnRR.bmp"}}},
                };
                std::unordered_map<std::string, std::pair<int32_t, int32_t>> dir2xy = {
                    {"front",       {380, 300}},
                    {"right_front", {450, 300}},
                    {"right",       {380, 370}},
                    {"right_rear",  {450, 370}},
                };
                std::unordered_map<std::string, int32_t> dir2evt = {
                    {"front",       1},
                    {"right_front", 2},
                    {"right",       3},
                    {"right_rear",  4},
                };
                int scale = 100;
                char value[PROP_VALUE_MAX] = {0};
                if (__system_property_get("rw.minieye.objp_warn_bmp_scale", value) > 0) {
                    scale = atoi(value);
                }
                for (auto r : evt->mEvtDataInt) {
                    char cmd[256] = {0};
                    const char * pBmp = nullptr;
                    auto it = dir2img.find(r.first);
                    if (it != dir2img.end()) {
                        auto it2 = it->second.find(r.second);
                        if (it2 != it->second.end()) {
                            pBmp = it2->second.c_str();
                        }
                    }
                    if (pBmp && !access(pBmp, R_OK)) {
                        int32_t x = dir2xy[r.first].first;
                        int32_t y = dir2xy[r.first].second;
                        snprintf(cmd, sizeof(cmd), "cmd tip %d %s 1 1 %d %d %d", dir2evt[r.first], pBmp, x, y, scale);
                        vector<char> resp;
                        LogCallProxyCmd::sendReq("media", cmd, resp, 1);
                    }
                }
            }
            break;
        }
        case EVT_TYPE_FBSD_RGN_INFO: {
            int32_t chn = evt->c.srcChn;
            if (chn < 0) {/*旧方案用FBSD算法*/
                const std::string channel = evt->algoName();
                std::string aiFunc = "";
                if (channel == "LBSD") {
                    aiFunc = "adas"; /*fbsd配置为前方摄像头*/
                } else if (channel == "RBSD") {
                    aiFunc = "fbsd"; /*fbsd配置为后方摄像头*/
                }

                if (aiFunc == "") {
                    loge("invalid channel %s", channel.c_str());
                    return false;
                }

                chn = mediaGetAlgoChn(aiFunc.c_str());
            } else {
                /*新方案用的OBJP算法，直接给出通道号*/
            }
            if (chn < 0) {
                //loge("chn = %d", chn);
                return false;
            }
            if (evt->c.event == "calibLines") {
                //logd("calibLines, chn %d, points %d", chn, evt->mEvtDataInt.size());
                CalibLines_t cl;
                cl.body_y   = evt->c.u.hl.body_y;
                cl.level1_y = evt->c.u.hl.level1_y;
                cl.level2_y = evt->c.u.hl.level2_y;
                cl.level3_y = evt->c.u.hl.level3_y;
                cl.ts = evt->c.ts;
                for (int32_t i = 0; i < (evt->mEvtDataInt.size() / 2); i++) {
                    char xn[8];
                    snprintf(xn, sizeof(xn), "x%d", i);
                    auto itx = evt->mEvtDataInt.find(xn);
                    if (itx != evt->mEvtDataInt.end()) {
                        char yn[8];
                        snprintf(yn, sizeof(yn), "y%d", i);
                        auto ity = evt->mEvtDataInt.find(yn);
                        if (ity != evt->mEvtDataInt.end()) {
                            std::pair<int32_t, int32_t> pt;
                            pt.first  = itx->second;
                            pt.second = ity->second;
                            cl.polyArea.push_back(pt);
                        }
                    }
                }
                setChnCalibLines(chn, cl);
            } else if (evt->c.event == "rect") {
                ObjectRect_t rect;
                miniui::color_t color;

                rect.x = evt->c.u.objRect.x1;
                rect.y = evt->c.u.objRect.y1;
                rect.w = ::abs(evt->c.u.objRect.x2 - evt->c.u.objRect.x1);
                rect.h = ::abs(evt->c.u.objRect.y2 - evt->c.u.objRect.y1);
                rect.ts = evt->c.ts;

                if (evt->c.u.objRect.level == 1) {
                    color = COLOR_RED;
                } else if (evt->c.u.objRect.level == 2) {
                    color = COLOR_YELLOW;
                } else if (evt->c.u.objRect.level == 3) {
                    color = COLOR_GREEN;
                } else {
                    break;
                }
                setChnObjectRect(chn, rect);
            } else if (evt->c.event == "line") {
                ObjectLine_t line;
                miniui::color_t color;

                line.x1 = evt->c.u.objRect.x1;
                line.y1 = evt->c.u.objRect.y1;
                line.x2 = evt->c.u.objRect.x2;
                line.y2 = evt->c.u.objRect.y2;
                line.ts = evt->c.ts;
                setChnObjectLine(chn, line);
            }
            break;
        }

        default: {
            break;
        }
    }

    return true;
}

