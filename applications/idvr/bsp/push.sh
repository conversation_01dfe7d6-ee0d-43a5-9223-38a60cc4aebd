#!/bin/bash

adb_dev=""
if [ "$1" != "" ]; then
    adb_dev="-s $1"
fi

adb_version=`adb version | awk -F " " '{print $5}'`
echo "$adb_version"

adb="adb $adb_dev"

if [ "$adb_version" = "1.0.32" ]; then
    #adb_low_then_1.0.32=true
    adb_version_lower=1
    echo should use 'adb(1.0.32)' push
else
    adb_version_lower=0
    echo should use 'adb(1.0.39)' push
fi

mkdir -p system/bin/
mkdir -p system/lib32/
mkdir -p system/etc/
mkdir -p system/fonts/

chmod -R 755 system/bin

cp -uv ../../../out/bin/* ./system/bin/
cp -uv ../../../out/*.so ./system/lib32/
cp -uv ../../../out/*.so.* ./system/lib32/
cp -ruv ../../../out/prebuilts/bin/* ./system/bin/
cp -ruv ../../../out/prebuilts/etc/* ./system/etc/
cp -ruv ../../../out/prebuilts/fonts/* ./system/fonts/

$adb remount

#$adb shell mkdir -p /system/usr/icu/
#$adb push system/usr/icu/* /system/usr/icu/

#$adb push system/etc/config.ini /data/minieye/idvr/etc/

if [ ${adb_version_lower} -eq 1 ]; then
    $adb push system/bin/ /system/bin/
    $adb push system/lib32/ /system/lib/
    $adb push system/etc/ /system/etc/
    $adb push system/fonts/ /system/fonts/
else
    $adb push system/bin/* /system/bin/
    $adb push system/lib32/* /system/lib/
    $adb push system/etc/* /system/etc/
    $adb push system/fonts/* /system/fonts/
fi

$adb shell sync
$adb shell sync
$adb shell sync
