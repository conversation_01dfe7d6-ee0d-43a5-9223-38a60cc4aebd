#include "system_properties.h"
#include "json.hpp"
#include "manager.h"
#include "controller.h"
#include "expand.receiver.h"
#include "prot.config.helper.h"
#include "location.db.helper.h"
#include "prot.jtt808-1078.tcpclient.h"

ComController::ComController(Config& config):config(config)
{
}

bool ComController::init()
{
    return true;
}

int ComController::start()
{
    const my::conf::ini& ini = InputService::getInstance().ini;

    my::string imei, iccid;
    getSimInfo(imei, iccid);

    char devid[PROP_VALUE_MAX] = {0};
    __system_property_get(PROP_PERSIST_MINIEYE_DEVICEID, devid);

    char bsp_version[128]={0};
    FILE * version_stream = fopen("/system/etc/minieye/bsp.version.txt","r");
    if(version_stream){
        if(NULL == fgets(bsp_version, sizeof(bsp_version), version_stream)){
            loge("ComController start: get_version fgets error!");
        }
        fclose(version_stream);
    } else {
        loge("ComController start: get_version fopen error!");
    }

    MY_SPINLOCK_X(lock);
    // 检查客户定制
    bool bHenanTelecomCustom = false;
    for (int i = 1; i <= MAX_CLIENTS; i++)
    {
        char tag[32];
        snprintf(tag, 32, "conn%d", i);

        // 支持多中心+多协议
        if (!ini.get(tag)) {
            continue;
        }

        const my::string* protocol = ini.get(tag, "protocol");
        LOG_RETURN_IF(!protocol, -2, loge, "[ComController::start] Field 'protocol' is not specified for client%d.", i);

        const my::string defaultSubtype = "";
        const my::string * subtype = ini.get(tag, "prot_subtype");
        if (!subtype) {
            subtype = &defaultSubtype;
        }
        const my::string * subtypeOld = ini.get("mprot", "prot_subtype");
        if (!subtype) {
            subtype = subtypeOld;
        }

        if ((*protocol == "jtt808-1078") && (*subtype == "henanTelecom")) {
            bHenanTelecomCustom = true;
        }
    }

    // 根据配置加载协议服务
    for (int i = 1; i <= MAX_CLIENTS; i++)
    {
        char tag[32];
        snprintf(tag, 32, "conn%d", i);
        // 支持多中心+多协议
        if (!ini.get(tag)) {
            continue;
        }

        // 校验参数
        ServerInfo si;
        const my::string* protocol = ini.get(tag, "protocol");
        LOG_RETURN_IF(!protocol, -2, loge, "[ComController::start] Field 'protocol' is not specified for client%d.", i);
        si.prot = *protocol;
        const my::string defaultSubtype = "";
        const my::string * subtype = ini.get(tag, "prot_subtype");
        const my::string * subtypeOld = ini.get("mprot", "prot_subtype");
        if (!subtype) {
            if (subtypeOld) {
                subtype = subtypeOld;
            } else {
                subtype = &defaultSubtype;
            }
        }
        si.prot_subtype = *subtype;
        si.tag = tag;
        const my::string defaultSim = "000000000001";
        const my::string * connSim = ini.get(tag, "sim");
        const my::string * baseSim = ini.get("base", "sim");
        if (!connSim) {
            if (baseSim) {
                connSim = baseSim;
            } else {
                connSim = &defaultSim;
            }
        }
        si.sim = *connSim;
        const my::string* id = ini.get(tag, "id");
        if (id && id->length() > 3) {
            LOG_RETURN_IF(!id, -2, loge, "[ComController::start] Field 'id' is not specified for client%d.", i);

            si.id = *id;
        }
        else {

            int idLen = strlen(devid);
            if (idLen >= 7) {
                si.id = &devid[idLen - 7];
                logd("si.id = %s\n", si.id.c_str());
            }
        }

        const my::string* auth = ini.get(tag, "auth");
        LOG_RETURN_IF(!auth, -2, loge, "[ComController::start] Field 'auth' is not specified for client%d.", i);
        si.auth = *auth;

        for (int j = 0; j < 3; j++) {
            const my::string* ip, *port;
            char key[128];

            snprintf(key, sizeof(key), "ip%d", j + 1);
            ip = ini.get(tag, key);
            if (!ip || !ip->length()) {
                loge("[ComController::start] Field 'ip' is not specified for client%d.", j);
                break;
            }
            if (!j) {
                char prop[PROP_NAME_MAX]   = {0};
                char value[PROP_VALUE_MAX] = {0};

                snprintf(prop, sizeof(prop), "persist.mphone.ping.ip%d", i - 1);
                if (__system_property_get(prop, value) > 0) {
                    if (value[0]) {
                        logd("%s = %s", prop, value);
                        char force[PROP_VALUE_MAX] = {0};
                        __system_property_get("persist.ping.808.as.default", force);
                        if (!strcasecmp(force, "true")) {
                            __system_property_set(prop, ip->c_str());
                        }
                    }

                } else {
                    __system_property_set(prop, ip->c_str());
                }
            }
            snprintf(key, sizeof(key), "port.tcp%d", j + 1);
            port = ini.get(tag, key);
            if (!port || !port->length()) {
                loge("[ComController::start Field 'tcp.port' is not specified for client%d.", j);
                break;
            }

            si.ip[j] = *ip;
            si.port[j] = atoi(port->c_str());

            IP_CTL_BITS * pctl = (IP_CTL_BITS*)&si.ipCtlBits[j];
            snprintf(key, sizeof(key), "ip%d.lock", j + 1);
            const my::string * ipLock = ini.get(tag, key);
            if (ipLock) {
                pctl->lock = !!atoi(ipLock->c_str());
            } else {
                pctl->lock = bHenanTelecomCustom && ((*subtype == "huoyun") || (*subtype == "henanTelecom"));//
            }
            snprintf(key, sizeof(key), "ip%d.attLock", j + 1);
            const my::string * attLock = ini.get(tag, key);
            if (attLock) {
                pctl->attLock = !!atoi(attLock->c_str());
            } else {
                pctl->attLock = 1;
            }
            snprintf(key, sizeof(key), "ip%d.sendAtt", j + 1);
            const my::string * attEnUpload = ini.get(tag, key);
            if (attEnUpload) {
                pctl->attEnUpload = !!atoi(attEnUpload->c_str());
            } else {
                pctl->attEnUpload = bHenanTelecomCustom ? (*subtype == "henanTelecom")/*定制, 默认只有henanTelecom传附件*/ : 1;
            }

            logd("%d protocol->c_str() %s, %s, [%d] : %s:%d, %s", i, protocol->c_str(), tag, j, si.ip[j].c_str(), si.port[j], subtype->c_str());

            const char * algoName[] = {"dms", "adas", "hod", "bsd", "fbsd"};
            for (int i = 0; i < ARRAY_SIZE(algoName); i++) {
                snprintf(key, sizeof(key), "%s.disableAttUpload", algoName[i]);
                const my::string * disAttUpload = ini.get(tag, key);
                if (disAttUpload) {
                    std::vector<my::constr> list = disAttUpload->split(',', true);
                    std::vector<std::string> res;
                    for (auto li : list) {
                        res.push_back(li.str().c_str());
                    }
                    si.disAlgoAttUpload[algoName[i]] = res;
                }
            }
        }

        si.imei = imei;
        si.bsp_version = bsp_version;

        const my::string* attSndProtVer = ini.get(tag, "attSndProtVer");
        if (attSndProtVer) {
            si.attSndProtVer = atoi(attSndProtVer->c_str());
        }

        // 创建协议app
        apps[i - 1] = ComService::create(protocol->c_str(), tag, connSim->c_str(), subtype->c_str());
        LOG_RETURN_IF(!apps[i - 1], -3, loge, "[ComController::start] Failed to create '%s'(%d) instance.", protocol->c_str(), i);

        const my::string * protVersion = ini.get(tag, "prot_version");
        if (protVersion) {
            int ver = atoi(protVersion->c_str());
            apps[i - 1]->setProtVersion(ver);
        }

        /*河南电信连上后，其他才能连接各自平台*/
        if (bHenanTelecomCustom &&
            !!access("/data/dis_prot_bootdummy", R_OK) &&
            (*subtype != "henanTelecom")) {
            char sBind[PROP_VALUE_MAX] = {0};
            bool bBindSimStat = false;
            if (__system_property_get(PROP_RW_HENAN_TELECOM_BIND_STAT, sBind) > 0) {
                bBindSimStat = !!atoi(sBind);
                logd("bBindSimStat %d", bBindSimStat);
            }
            if (!bBindSimStat) {/*未上线过，非绑定状态*/
                apps[i - 1]->dummy(true);
            }
        }
        // 启动app
        int r = apps[i - 1]->start(si);
        LOG_RETURN_IF(r, -4, loge, "[ComController::start] Failed to start '%s'(client%d) instance.", protocol->c_str(), i);

        logi("[ComController::start] Start '%s'(client%d) successfully.", protocol->c_str(), i);
    }
    return 0;
}

void ComController::stop()
{
    MY_SPINLOCK_X(lock);
    for (int i = 0; i < MAX_CLIENTS; i++) {
        if (apps[i]) {
            apps[i]->stop();
            apps[i].reset();
        }
    }
}

void  ServiceGuard::reload_service(ComService::Ptr com_service, const ServerInfo * si)
{
    MY_SPINLOCK_X(lock);

    for (auto it = queue.begin(); it != queue.end(); ++it) {
        if (it->second == si) {
            it->first = com_service;
            return;
        }
    }
    queue.push_back(std::make_pair(com_service, si));
}


void ServiceGuard::run()
{
    ComService::Ptr p;
    ServerInfo si;
    prctl(PR_SET_NAME, "ServiceGuard");
    while (!exiting())
    {
        {
            MY_SPINLOCK_X(lock);
            if (!queue.empty())
            {
            p = queue.front().first;
            si = *queue.front().second;
            queue.erase(queue.begin());
            }
        }

        if (p != NULL) {
            int ret = p->reload(si);
            LOG_IF(ret != 0, logw,
            "[ServiceGuard::run] failed to reload com service, tag=[%s], server1=[%s:%d], server2=[%s:%d], server3=[%s:%d]",
            p->get_type().c_str(), si.ip[0].c_str(), si.port[0],
            si.ip[1].c_str(), si.port[1], si.ip[2].c_str(), si.port[2]);
            p = NULL;
        } else {
            my::thread::sleep(1);
        }
    }
}
StateController::StateController(ComController::Ptr & p)
{
    char value[PROP_VALUE_MAX] = {0};
    int ret = __system_property_get(PROP_PERSIST_MINIEYE_TRIPOSDCOLOR, value);
    if(ret) {
        char *end = NULL;
        int n = strtol(value, &end, 0);
        if (end && !*end) {
            mColorTripOsd = n;
        }
    }
    mpComCtlr = p;


    if (!access("/data/turnOver_test", R_OK)) {
        mTurnover = true;
    }

    mImuHdlr = new IMUClient("stateCtlr", "127.0.0.1", "12580", "imu.server.test");
    mbImuInited = !!mImuHdlr;
}
void StateController::imu_proc(std::vector<GsensorData>& vector)
{
    static my::timestamp mLastImuPushTs;
    for (auto gdata : vector)
    {
        if (mLastImuPushTs.elapsed() >= 50) {
            mLastImuPushTs = my::timestamp::now();

            IdvrImuDataT imuData;
            imuData.timestamp = gdata.timestamp;
            memcpy(imuData.gyro, gdata.gyro, sizeof(imuData.gyro));
            memcpy(imuData.accel, gdata.accel, sizeof(imuData.accel));

            float acc[3];
            float aRes = 8.0 / 32768;
            acc[0] = gdata.accel[0] * aRes;
            acc[1] = gdata.accel[1] * aRes;
            acc[2] = gdata.accel[2] * aRes;

            imuData.temp = (gdata.temp - 21) / 333.87 + 21; // T= ((寄存器读出来int16值 -21)/333.87 +21) 摄氏度
            imuData.roll = atan2(-acc[1], -acc[2]) * 180/3.14;
            imuData.pitch = atan(acc[0] / sqrt(acc[2] * acc[2] + acc[1] * acc[1])) * 180/3.14;

            Manager & m = Manager::getInstance();
            std::shared_ptr<struct STAT_DATA> sp = make_shared<struct STAT_DATA>();
            sp->flag = STAT_DATA_FLAG_IMU;
            sp->ctx.imu = imuData;
            m.state_controller->push(sp);
        }
    }
}

int StateController::start()
{
    Manager & m = Manager::getInstance();
    mUpdateExtendAlarmTs = my::timestamp::now();
    int ret = sg.start();
    mImuHdlr->start(m.state_controller->imu_proc);
    LOG_RETURN_IF(ret != 0, ret, loge, "[StateController::start] Failed to start service guard, ret=[%d]", ret);
    return thread::start();
}

void StateController::stop()
{
    thread::stop();
    sg.stop();
}

void StateController::PFM(uint8_t doorBits)
{
    char propval[PROP_VALUE_MAX] = {0};
    int32_t ret = __system_property_get(PASSENGER_FLOW_METER_RUN_PROPERTY, propval);
    if ((ret <= 0) || strcmp(propval, "true")) {
        return;
    }

    // 开关门通知
    ExpandReceiver::getInstance().send(PASSENGER_FLOW_METER_LIBFLOW_TOPIC, (const char *)&doorBits, 1);

    // 关门五秒后查询
    int32_t reverseBits = mLastDoorBits ^ doorBits;
    mLastDoorBits = doorBits;

    //logd("mLastDoorBits %02xh, doorBits %02xh, open2closeBits %02xh", mLastDoorBits, doorBits, open2closeBits);
    constexpr int32_t doorCnt = 2;  // 门的数量
    constexpr int32_t doorCntBits = (1 << (doorCnt << 1)) - 1;
    if (reverseBits & doorCntBits) {
        for (int32_t i = 0; i < doorCnt; i++) {
            const int32_t closeDoorBit = (1 << ((i << 1) + 1));
            if ((reverseBits & closeDoorBit) && (doorBits & closeDoorBit)) {
                //logd("door%d", i);
                mLastDrPfmTsMap[i + 1] = std::make_pair(my::timestamp::now(), false);
            }
        }
    }
    for (auto& [doorId, task] : mLastDrPfmTsMap) {
        if ((task.first.elapsed() > 5000) && (!task.second)) {
            task.second = true;
            std::vector<char> resp;
            my::string pfmCmd;
            pfmCmd.assignf("cmd get %d", doorId);
            if (!LogCallProxyCmd::sendReq("passengerFlowMeter", pfmCmd.c_str(), resp)) {
                loge("cmd failed %s", pfmCmd.c_str());
            }
        }
    }
}

void StateController::SOS(IdvrIOStat * iostat)
{
    Manager & mgr = Manager::getInstance();
    ComController::Ptr cc = mgr.com_controller;
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();

    if (!iostat->emergency_alarm) {
        mLastSOS = my::timestamp::now();
        mbLastSOS = false;
    }

    if (mbCpSosMedia &&
        (iostat->emergency_alarm) &&
        ((config.sys.snap_alarm_enBits & 1)) &&
        (mLastSOS.elapsed() >= 50) && !mbLastSOS) {
        logd("iostat->emergency_alarm %d", iostat->emergency_alarm);
        mbLastSOS = true;
        char dateStr[128] = {0};
        mLastSOS.YYYYMMDDHHMMSS(dateStr);
        my::string path = "/mnt/obb/.sos/";
        my::string backPath = "/tmp/media_rw/sdcard2/MNEYE/SOS/";
        path += dateStr;
        backPath += dateStr;
        
        my::file::mkdir(path.c_str());

        Media m;
        /* 生成video */
        my::string shotCmd;
        my::uint64 ts = time(NULL);
        shotCmd.assignf("cmd snapshot 0 %" FMT_LLD " %d %s %" FMT_LLD " %" FMT_LLD "",
            ts, 1, path.c_str(), ts - 5, ts + 5);

        std::vector<char> resp;
        if (!LogCallProxyCmd::sendReq("muxer", shotCmd.c_str(), resp)) {//ask muxer to product media files
            loge("cmd failed %s",  shotCmd.c_str());
            return;
        }
        
        logd("cmd %s",  shotCmd.c_str());
        ServiceHelper &serviceHelper  = ServiceHelper::getInstance();
        /* 存储MP4的多媒体信息 */
        m.id = mediaIdx++;//(my::uint)((my::uint64)my::timestamp::now());
        m.type = 2; // 视频
        m.ch = 1; // 通道
        m.event = 2;/*抢劫报警触发*/
        m.path = (path + "/" + my::to_string((int)m.ch) + ".mp4").c_str();  // 路径
        m.name = my::to_string((int)m.ch) + ".mp4";
        m.backPath = backPath;
        m.time = ts;// 时间
        m.loc = serviceHelper.getCurrentLocation(serviceHelper.getStatus());// 获取当前位置
        if (!(config.sys.snap_storage_enBits & 1)) { // 实时上传
            m.save = 0;
        } else {
            m.save = 1;
        }

        for (int i = 0; i < MAX_CLIENTS; i++) {
            ComService::Ptr cs = (*cc)[i];
            if (cs) {
                cs->addMedia2Send(m);
            }
        }

        /* 生成jpeg */
        my::string lmsCmd;
        my::string fileName;
        
        fileName.assignf("%d.jpg", (int)m.ch);
        my::string pathName = path + "/" + fileName;
        
        lmsCmd.assignf("cmd lms %d %s %d %d %d", 1, pathName.c_str(), 1280, 720, 1);

        if (!LogCallProxyCmd::sendReq("media", lmsCmd.c_str(), resp)) {//ask media to product picture files
           loge("cmd failed %s", lmsCmd.c_str());
           // return;
        }
           loge("cmd %s", lmsCmd.c_str());
        /* 存储jpg的多媒体信息 */
        my::uint id = mediaIdx++;//(my::uint)((my::uint64)my::timestamp::now());
        m.id = ((id != m.id) ? id : (id + 1));
        m.type = 0; // jpeg
        m.ch = 1; // 通道
        m.event = 2;/*抢劫报警触发*/
        m.path = pathName;// 路径
        m.name = fileName;
        m.backPath = backPath;
        m.time = ts;// 时间
        m.loc = serviceHelper.getCurrentLocation(serviceHelper.getStatus());// 获取当前位置
        if (!(config.sys.snap_storage_enBits & 1)) { // 实时上传
            m.save = 0;
        } else { // 存储
            m.save = 1;
        }

        for (int i = 0; i < MAX_CLIENTS; i++) {
            ComService::Ptr cs = (*cc)[i];
            if (cs) {
                cs->addMedia2Send(m);
            }
        }
        
    }
}


bool StateController::dealIOStat(IdvrIOStat * iostat)
{
    Manager & m = Manager::getInstance();
    ComController::Ptr cc = m.com_controller;
    OutputService & os = OutputService::getInstance();
    InputService & is = InputService::getInstance();
    Current * current = is.getCurrent();
    Config & config = is.getConfig();
    bool bPlayLoadSpch = true, bHenanTelecom = false;

    for (int i = 0; i < MAX_CLIENTS; i++) {
        ComService::Ptr cs = (*cc)[i];
        if (cs) {
             if (!access("/data/benlongtest", R_OK)) {
                static bool audioPlay[2] = {false};
                if (iostat->normal_light && audioPlay[0] == false) {
                    audioPlay[0] = true; 
                    logi("normal_light for benlong tts start");
                    os.ttsGBK("欢迎乘坐本次班次", 10);
                } else if (!iostat->normal_light) {
                    //logi("normal_light for benlong tts end");
                     audioPlay[0] = false; 
                }
                if (iostat->far_light && audioPlay[1] == false) {
                    audioPlay[1] = true; 
                    logi("far_light for benlong tts start");
                    os.ttsGBK("终点到了，欢迎下次乘坐", 10);
                } else if (!iostat->far_light) {
                    //logi("far_light for benlong tts end");
                    audioPlay[1] = false; 
                }
            }
            if (cs->si[0].prot_subtype == "henanTelecom") {
                bHenanTelecom = true;
            }
        }
    }
    if (bHenanTelecom) {
        int useBit = 0;
        if (access("/data/disable_carload_spch", R_OK) && bPlayLoadSpch) {
            char propval[PROP_VALUE_MAX] = {0};
            __system_property_get(PROP_PERSIST_HENANTELCOM_LOADSIGBIT, propval);
            useBit = atoi(propval);
            if (useBit < 1 || useBit > 16) {
                useBit = 1;/*use the sos sig default*/
            }
            iostat->car_load_stat = (iostat->value & (1 << useBit)) ? 3 : 0;
            current->setStateLoad(iostat->car_load_stat);
            if (mFullLoad == -1 || mFullLoad != (3 == iostat->car_load_stat)) {
                bPlayLoadSpch = false;
                mFullLoad = (3 == iostat->car_load_stat);
                logd("mFullLoad %d, iostat->car_load_stat %d", mFullLoad, iostat->car_load_stat);
                if (mFullLoad) {
                    ttsPlayRightNow("车辆满载");
                } else {
                    ttsPlayRightNow("车辆空载");
                }
            }
            if (1 == useBit) {
                current->setAlarmEmergency(false);
            } else {
                current->setAlarmEmergency(iostat->emergency_alarm);
            }
        }
    }else {
        current->setStateLoad(iostat->car_load_stat);
        current->setAlarmEmergency(iostat->emergency_alarm);
    }

    if (iostat->can_signal_loss && !access("/data/rpt_can_lost", R_OK)) {
        if (!mLastCanLossRpt && m.getProtStat(0)) {
            logd("--- can_signal_loss ---");
            m.triggerAdasAlarm("BLUR", 0);
            mLastCanLossRpt = time(NULL);
        }
        if (!mLastCanLossAudio || (time(NULL) - mLastCanLossAudio) >= 5) {
            if (m.state_controller.get()) {
                LogCallProxyCmd::sendReq("media", "cmd play /data/audios/fail.wav 1");
            }
            mLastCanLossAudio = time(NULL);
        }
    }
    else {
        mLastCanLossRpt = 0;
        mLastCanLossAudio = 0;
    }

    /* GPIO全屏逻辑 */
    conf_t &cfg =  config.sys;
    if (iostat->backwards && cfg.io.fullsc[IDVR_GPIO_IN_BACK] > 0) {
           os.camFullScreen((cfg.io.fullsc[IDVR_GPIO_IN_BACK]), 3);
    } else if (iostat->normal_light && cfg.io.fullsc[IDVR_GPIO_IN_NEAR] > 0) {
           os.camFullScreen((cfg.io.fullsc[IDVR_GPIO_IN_NEAR]), 3);
    } else if (iostat->far_light && cfg.io.fullsc[IDVR_GPIO_IN_FAR] > 0) {
        // os.camFullScreen((cfg.io.fullsc[IDVR_GPIO_IN_FAR]), 3);

        // 此处逻辑，xiashi的需求 - sb需求
        // face match没有没有成功，则不要将对应通道全屏，避免在配置通道和dms之间频繁切换。
        bool faceMatch = false;
        char propValue[PROP_VALUE_MAX] = {0};
        if(__system_property_get("rw.minieye.faceIdMatch", propValue) > 0 ) {
            faceMatch = atoi(propValue) == 1 ? true : false;
        }

        bool fullScr = false;
        memset(propValue, 0, sizeof(propValue));

        if(__system_property_get(PROP_PERSIST_ENABLE_FULL_SRC, propValue) > 0 ) {
            fullScr = atoi(propValue) == 1 ? true : false;
        }

        // 开启人脸识别 并且 没有识别成功时，dms需要全屏,
        if (!(fullScr && !faceMatch)) {
            os.camFullScreen((cfg.io.fullsc[IDVR_GPIO_IN_FAR]), 3);
        }
           
    } else if (iostat->left_turn && cfg.io.fullsc[IDVR_GPIO_IN_LEFT] > 0) {
           os.camFullScreen((cfg.io.fullsc[IDVR_GPIO_IN_LEFT]), 3);
    } else if (iostat->right_turn && cfg.io.fullsc[IDVR_GPIO_IN_RIGHT] > 0) {
           os.camFullScreen((cfg.io.fullsc[IDVR_GPIO_IN_RIGHT]), 3);
    } else if (iostat->brake && cfg.io.fullsc[IDVR_GPIO_IN_BRAKE] > 0) {
           os.camFullScreen((cfg.io.fullsc[IDVR_GPIO_IN_BRAKE]), 3);
    } else if (iostat->door && cfg.io.fullsc[IDVR_GPIO_IN_DOOR] > 0) {
           os.camFullScreen((cfg.io.fullsc[IDVR_GPIO_IN_DOOR]), 3);
    }

    current->setRightTurn(iostat->far_light);

    SOS(iostat);

    if (!mbUseCanDoorSig) {
        uint8_t bits = (iostat->right_turn) | (iostat->left_turn << 1) | (iostat->normal_light << 2) | (iostat->far_light << 3);
        PFM(bits);
    }
    return true;
}

bool StateController::gpsDataProc(const IdvrLocation& loc)
{
    Manager & m = Manager::getInstance();
    ComController::Ptr cc = m.com_controller;

    for (int i = 0; i < MAX_CLIENTS; i++) {
        ComService::Ptr cs = (*cc)[i];
        if (cs) {
            cs->gpsDataProc(loc);
        }
    }

    return true;
}

bool StateController::canDataProc(int canIdx, CanData & data)
{
    Manager & m = Manager::getInstance();
    ComController::Ptr cc = m.com_controller;

    for (int i = 0; i < MAX_CLIENTS; i++) {
        ComService::Ptr cs = (*cc)[i];
        if (cs) {
            cs->canDataProc(canIdx, data);
        }
    }
    return true;
}

bool StateController::onInputIdvrMsg(std::shared_ptr<struct STAT_DATA> & sp)
{
    return !push(sp);
}

//获取摄像头状态
int StateController::getCamStatus(int total_ch)
{
    int camStatus = 0, temp = 0, updateTm = 0;
    static int lastUpdateTm = 0;
    char prop[PROP_VALUE_MAX] = {0};
    if (__system_property_get(PROP_RW_MINIEYE_CAM_STATUS, prop)) {
        sscanf(prop, "%d %d", &updateTm, &temp);

        for (int i = 0; i < total_ch; i++) {
            if (!((temp >> i) & 0x01)) {
                camStatus |= (1 << i);
            }
        }
    }
    if (updateTm == lastUpdateTm) {/*check idvr.media is alive*/
        for (int i = 0; i < total_ch; i++) {
            camStatus |= (1 << i);
        }
    }
    lastUpdateTm = updateTm;
    return camStatus;
}

//获取摄像头遮挡状态
int StateController::getCamOcclusionStatus(int total_ch)
{
    int camStatus = 0, temp = 0, updateTm = 0;
    static int lastUpdateTm = 0;
    char prop[PROP_VALUE_MAX] = {0};
    if (__system_property_get(PROP_RW_MINIEYE_OCCLU, prop)) {
        sscanf(prop, "%d %d", &updateTm, &temp);

        for (int i = 0; i < total_ch; i++) {
            if (((temp >> i) & 0x01)) {
                camStatus |= (1 << i);
            }
        }
    }
    if (updateTm == lastUpdateTm) {/*check idvr.media is alive*/
        for (int i = 0; i < total_ch; i++) {
            camStatus &= ~(1 << i);
        }
    }
    lastUpdateTm = updateTm;
    return camStatus;
}


unsigned short StateController::getStorageStatus()
{
    unsigned short storageStatus = 0;
    if ((0 == access(TF1_PATH, R_OK)) && (0 == access(TF1_PATH_BLOCK, R_OK))) {
        storageStatus = 0;

    } else {
        storageStatus |= 0x01;
    }
#if 0
    /* M5pro无硬盘 */
    if ((0 == access(DISK1_PATH, R_OK)) && (0 == access(DISK1_PATH_BLOCK, R_OK))) {
        storageStatus = 0;

    } else {
        storageStatus |= 0x02;
    }
#endif    
    
    if (storageStatus) {
        mStorageFailCnt++;
        if (mStorageFailCnt < 5) {//累计防抖，规避平台事件太多的问题
            storageStatus = 0;
        }
    } else {
        mStorageFailCnt = 0;
    }
    return storageStatus;
}


// 获取扩展报警
void StateController::updateExtendAlarm()
{
    InputService & is = InputService::getInstance();
    Current * current = is.getCurrent();
    Config & config = is.getConfig();
    Current st = current->copy();

    if (!st.getStateAcc()) {
        mUpdateExtendAlarmTs = my::timestamp::now();
    }

    if (mUpdateExtendAlarmTs.elapsed() > 60000) { //进程启动或ACC ON 1分钟后检测
        int total_ch = config.sys.cameras;
        current->setExtendAlarm(getCamStatus(total_ch), getCamOcclusionStatus(total_ch), getStorageStatus(), current->getSpecialRecordOverFlow());
    } else {
        current->setExtendAlarm(0, 0, 0, 0);
    }
}

void StateController::run()
{
    double latitude     = -1;    // 纬度
    double longitude    = -1;    // 经度
    double speed        = -1;    // 速度
    string carid        = "?";
    string machine      = "?";

    prctl(PR_SET_NAME, "StateController");
    while (!exiting())
    {
        Manager & m = Manager::getInstance();
        InputService & is = InputService::getInstance();
        OutputService & os = OutputService::getInstance();
        Current * current = is.getCurrent();
        Config & config = is.getConfig();
        std::shared_ptr<struct STAT_DATA> sp;
        bool ret = wait(sp, std::chrono::microseconds(10000));
        if (ret) {
            my::uint64 ts = my::timestamp::utc_milliseconds();
            if ((ts - sp->ts) > 1000) {
                //logd("StateController> ts delta = %dms, flag %d", __FUNCTION__, sp->flag);
            }
            //logd("[StateController::run]: recv %d", sp->flag);
            switch(sp->flag) {
                case STAT_DATA_FLAG_RECORD_STATUS: {
                    break;
                }
                case STAT_DATA_FLAG_SOCKET_CMD: {
                    LogCallProxyCmd::sendReq(sp->ctx.cmd.mod, sp->ctx.cmd.cmd);
                    break;
                }
                case STAT_DATA_FLAG_IOSTATUS: {
                    struct STAT_DATA & s = *sp.get();
                    struct IdvrStat stat = s.ctx.stat;
                    struct IdvrLBS * lbs = &stat.lbs;

                    dealIOStat(&(stat.vehicle_io));

                    struct IdvrLocation loc;
                    loc.alarm_tag = current->getCurAlarm();
                    loc.status = current->getCurState();
                    loc.latitude = lbs->lat_x1kw / 10000000.0;
                    loc.longitude = lbs->lng_x1kw / 10000000.0;
                    loc.height = lbs->alt_x10 / 10.0;
                    loc.speed = stat.speed_x10 / 10.0;
                    loc.direction = lbs->dir_x100 / 100.0;
                    loc.time = lbs->time;

                    /*更新IPC的OSD*/
                    if ((config.sys.ipcs) &&
                        ((loc.speed != mLoc.speed) ||
                        (loc.latitude != mLoc.latitude) ||
                        (loc.longitude != mLoc.longitude))) {
                        Current c = current->copy();
                        if (speed != c.lbs.speed) {
                            string cmd = "cmd setOsd ";
                            cmd += "speed ";
                            cmd += std::to_string(c.lbs.speed) + "km/h";
                            if (LogCallProxyCmd::sendReq("IPC", cmd.c_str())) {
                                speed = c.lbs.speed;
                            }
                        }
                        if (latitude != c.lbs.lat) {
                            string cmd = "cmd setOsd ";
                            cmd += "La La:";
                            cmd += std::to_string(c.lbs.lat);
                            if (LogCallProxyCmd::sendReq("IPC", cmd.c_str())) {
                                latitude = c.lbs.lat;
                            }
                        }
                        if (longitude != c.lbs.lng) {
                            string cmd = "cmd setOsd ";
                            cmd += "Lo Lo:";
                            cmd += std::to_string(c.lbs.lng);
                            if (LogCallProxyCmd::sendReq("IPC", cmd.c_str())) {
                                longitude = c.lbs.lng;
                            }
                        }
                        if (carid != config.sys.vehicle.plate_num.c_str()) {
                            string cmd = "cmd setOsd ";
                            cmd += "carid ";
                            cmd += config.sys.vehicle.plate_num;
                            if (LogCallProxyCmd::sendReq("IPC", cmd.c_str())) {
                                carid = config.sys.vehicle.plate_num.c_str();
                            }
                        }
                        string tmp = "制动:";
                        tmp += c.sensor.brake ? "开,转向:" : "关,转向:";
                        if (c.sensor.left_turn) {
                            tmp += "左";
                        } else if (c.sensor.right_turn) {
                            tmp += "右";
                        } else {
                            tmp += "无";
                        }
                        char gbk[256] = {0};
                        my::utf8ToGbk((char*)tmp.c_str(), gbk);
                        tmp = gbk;

                        if (machine != tmp) {
                            string cmd = "cmd setOsd ";
                            cmd += "machine ";
                            cmd += tmp;
                            if (LogCallProxyCmd::sendReq("IPC", cmd.c_str())) {
                                machine = tmp;
                            }
                        }
                    }
                    /*记录GPS数据*/
                    if (mLoc.time != loc.time) {
                        gpsDataProc(loc);
                    }
                    mLoc = loc;
                    break;
                }
                case STAT_DATA_FLAG_TRIPINFO: {
                    struct STAT_DATA & s = *sp.get();
                    IdvrTripInfo & tripInfo = s.ctx.tripInfo;

                    char cmd[256];
                    int32_t tripSec = (int32_t)(tripInfo.endTime - tripInfo.startTime);
                    int32_t tripWarn = (config.sys.warn.overtime.limit * 360 + config.sys.warn.overtime.remainder);
                    int32_t tripAlarm = tripWarn - config.sys.warn.overtime.alarm * 60 - config.sys.warn.overtime.alarmRmndr;
                    if ((tripSec >= 0) && (!mLastTmTripOsdSet || abs(time(NULL) - mLastTmTripOsdSet) >= 10)) {
                        int32_t hour = tripSec / 3600;
                        int32_t min = (tripSec % 3600) / 60;
                        int color = mColorTripOsd;
                        if (mColorTripOsd >= 0) {
                            if (tripAlarm <= tripSec && tripSec < tripWarn) {
                                color = 0x80dc;
                            } else if (tripSec >= tripWarn) {
                                color = 0xff;
                            }
                            snprintf(cmd, sizeof(cmd), "cmd setUiTips 行驶:%02dh%02dm %d", hour, min, color);
                            if (LogCallProxyCmd::sendReq("media", cmd)) {
                                mLastTmTripOsdSet = time(NULL);
                            }
                        } else if (mLastTmTripOsdSet) {
                            snprintf(cmd, sizeof(cmd), "cmd clearUiTips");
                            if (LogCallProxyCmd::sendReq("media", cmd)) {
                                mLastTmTripOsdSet = 0;
                            }
                        }
                    }
                    break;
                }
                case STAT_DATA_FLAG_CAN0: {
                    canDataProc(0, *((CanData*)sp->ctx.data));
                    break;
                }
                case STAT_DATA_FLAG_CAN1: {
                    canDataProc(1, *((CanData*)sp->ctx.data));
                    break;
                }
                case STAT_DATA_FLAG_SET_CAN_FILTER: {
                    struct STAT_DATA & s = *sp.get();
                    McuMsgCanFilterT buf;
                    buf.enable = 1;
                    buf.canIdx = s.ctx.data[0];
                    buf.canIds[0] = *((uint32_t*)&s.ctx.data[1]);
                    m.sendCanFilterMsgCycle((uint8_t *)&buf, sizeof(McuMsgCanFilterT));
                    break;
                }
                case STAT_DATA_FLAG_IPC_AGT: {
                    
                    break;
                }
#if 1
                case STAT_DATA_FLAG_IMU: {
                    std::lock_guard<std::mutex> lock(mImuMutex);
                    IdvrImuDataT * p = &sp->ctx.imu;
                    if (mTurnover) {
                        if (mCrashDet.detect(p, (const collisionParam_t *)&config.sys.collisionParam)) {
                            // 触发碰撞
                            std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
                            amsg->setWhat(EVT_TYPE_PROT_CAR_CRASH);
                            m.postMsg(amsg);
                        } else {
                            current->setAlarmCollisionWarning(false);
                        }
                    }
                    if (!access("/data/turnOver_test", R_OK)) {
                        short rollAngle = 0;
                        if ((1 == rollCheck.CheckTurnOver(p, rollAngle)) && (rollAngle > 0)) {
                            // 触发侧翻
                            std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
                            amsg->setWhat(EVT_TYPE_PROT_CAR_ROLL_OVER);
                            amsg->setInt32("angle", rollAngle);
                            m.postMsg(amsg);
                        } else {
                            current->setAlarmRolloverWarning(false);
                        }

                        mImuDataMap[p->timestamp] = *p;
                        unsigned long bgn = p->timestamp - 10 * 1000 * 1000;
                        auto i = mImuDataMap.begin();
                        while (i != mImuDataMap.end()) {
                            if (i->first < bgn) {
                                i = mImuDataMap.erase(i);
                            } else {
                                break;
                            }
                        }
                    }

                    break;
                }
#endif
                case STAT_DATA_FLAG_CARINFO: {
                    struct STAT_DATA & s = *sp.get();
                    IdvrCarInfo & carInfo = s.ctx.carInfo;
                    /* GPIO全屏逻辑 */
                    conf_t &cfg =  config.sys;
                    //logd("carInfo.canDoorBits %02x", carInfo.canDoorBits);
                    if (0xff != carInfo.canDoorBits) {
                        if ((carInfo.canDoorBits & 1) && cfg.io.fullsc[IDVR_FRONT_DOOR_OPEN] > 0) {
                            os.camFullScreen((cfg.io.fullsc[IDVR_FRONT_DOOR_OPEN]), 3);
                        }
                        if ((carInfo.canDoorBits & 2) && cfg.io.fullsc[IDVR_MIDDLE_DOOR_OPEN] > 0) {
                            os.camFullScreen((cfg.io.fullsc[IDVR_MIDDLE_DOOR_OPEN]), 3);
                        }
                        if ((carInfo.canDoorBits & 4) && cfg.io.fullsc[IDVR_BACK_DOOR_OPEN] > 0) {
                            os.camFullScreen((cfg.io.fullsc[IDVR_BACK_DOOR_OPEN]), 3);
                        }
                        if (carInfo.canDoorBits) {
                            if (!mbUseCanDoorSig) {
                                mbUseCanDoorSig = true;
                                mLastDoorBits = 0;
                            }
                        }

                        if (mbUseCanDoorSig) {
                            PFM(carInfo.canDoorBits);
                        }
                    }
                    break;
                }
                case STAT_DATA_FLAG_PROT_DUMMY: {
                    struct STAT_DATA & s = *sp.get();
                    int idx = s.ctx.data[0];
                    bool bDummy = !!s.ctx.data[1];
                    ComController * cc = mpComCtlr.get();
                    ComService::Ptr p = (*cc)[idx];
                    p->dummy(bDummy);
                    break;
                }
                case STAT_DATA_FLAG_PROT_RELOAD: {
                    struct STAT_DATA & s = *sp.get();
                    ServerInfo * p = (ServerInfo*)s.ctx.ptr;
                    ComController * cc = mpComCtlr.get();
                    ComService::Ptr cs = nullptr;
                    for (int i = 0; i < MAX_CLIENTS; i++) {
                        cs = (*cc)[i];
                        if (&cs->si[0] == p) {
                            break;
                        }
                    }
                    if (cs.get()) {
                        sg.reload_service(cs, p);
                    }
                    break;
                }
                case STAT_DATA_FLAG_SET_PWR: {
                    struct STAT_DATA & s = *sp.get();
                    McuMsgHandler & h = McuMsgHandler::getInstance();
                    if (s.ctx.data[0]) {
                        loge("set power on!\n");
                        h.sendIOCtrl(MINOR_GPIO_PWR_SYS_ON);
                        my::thread::msleep(500);
                        h.sendIOCtrl(MINOR_GPIO_PWR_5V_ON);
                        my::thread::msleep(500);
                        h.sendIOCtrl(MINOR_GPIO_PWR_12V_ON);
                    } else {
                        loge("set power off!\n");
                        h.sendIOCtrl(MINOR_GPIO_PWR_12V_OFF);
                        my::thread::msleep(500);
                        h.sendIOCtrl(MINOR_GPIO_PWR_5V_OFF);
                        my::thread::msleep(500);
                        h.sendIOCtrl(MINOR_GPIO_PWR_SYS_OFF);
                    }
                    break;
                }
                case STAT_DATA_FLAG_SPDING_ALARM: {
                    struct STAT_DATA & s = *sp.get();
                    SpeedingAlarmParam param;
                    param.type.value = s.ctx.data[0];
                    param.state = s.ctx.data[1];
                    param.spdLimit = s.ctx.data[2];
                    param.protBitTbl = s.ctx.data[3];
                    m.triggerSpeedingAlarm(param);
                    if (SPEEDING_STATUS_END == param.state) {
                        os.mAlarmInfoRecord->deletAlarmTips(my::string("speeding"));
                    } else if ((SPEEDING_STATUS_START == param.state) ||
                        (SPEEDING_STATUS_CONTINUE == param.state)) {
                        os.mAlarmInfoRecord->insertAlarmTips(my::string("speeding"), my::timestamp::milliseconds_from_19700101());
                    }
                    break;
                }
                case STAT_DATA_FLAG_SAT_INFO: {
                    break;
                }
                default: {
                    loge("%s> invalid STAT_DATA_FLAG %d", __FUNCTION__, sp->flag);
                    break;
                }
            }
        }
    }
}

int RollOver::sensorCalib(const IdvrImuDataT *pImu) 
{
    mSensor.x = pImu->accel[0];
    mSensor.y = pImu->accel[1];
    mSensor.z = pImu->accel[2];
    if (mSensor.calibFlag==0) {//初始化处理
        /* 填充角速度 */
        mSensor.x_aver += mSensor.x;
        mSensor.y_aver += mSensor.y;
        mSensor.z_aver += mSensor.z;    
        if (++mSensor.sensor_num > 9) {                   // 累计10条数据.
            mSensor.x_aver /= mSensor.sensor_num;//* 求平均.
            mSensor.y_aver /= mSensor.sensor_num;
            mSensor.z_aver /= mSensor.sensor_num;
            //* 求模.
            mSensor.scalefactor =(4096*4 + sqrt((mSensor.x_aver * mSensor.x_aver) + (mSensor.y_aver * mSensor.y_aver) + (mSensor.z_aver * mSensor.z_aver)))/5;
            //* 求三轴分量和合加速度的夹角.
            mSensor.dx = (short)(HDTODU*atan2(mSensor.x_aver, sqrt(mSensor.y_aver * mSensor.y_aver + mSensor.z_aver * mSensor.z_aver)));
            mSensor.dy = (short)(HDTODU*atan2(mSensor.y_aver, sqrt(mSensor.x_aver * mSensor.x_aver + mSensor.z_aver * mSensor.z_aver)));
            mSensor.dz = (short)(HDTODU*atan2(mSensor.z_aver, sqrt(mSensor.x_aver * mSensor.x_aver + mSensor.y_aver * mSensor.y_aver)));      
            mSensor.calibFlag = 1;
            mSensor.sensor_num = 0;
        } else {
            mSensor.sensor_num++;
        }
    }
    return 0;
}

/* return 0:正常 1:侧翻 */
uint8_t RollOver::CheckTurnOver(const IdvrImuDataT *pImu, short & rollAngle)
{
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();
    short LDux = 0, LDuy = 0, LDuz = 0;
    short LX = 0,LY = 0,LZ = 0;

    sensorCalib(pImu);
    std::string tmp = pImu->toJson();
    //logd("%s", tmp.c_str());
    LX = mSensor.x;
    LY = mSensor.y;
    LZ = mSensor.z;

    LDux = (short)(HDTODU * atan2((float)LX, sqrt(LY * LY + LZ * LZ)));        // 侧翻方向的度判断.
    LDuy = (short)(HDTODU * atan2((float)LY, sqrt(LX * LX + LZ * LZ)));        // 侧翻方向的度判断.
    LDuz = (short)(HDTODU * atan2((float)LZ, sqrt(LY * LY + LX * LX)));        // 侧翻方向的度判断.
    rollAngle = 0;
    short dx = abs(LDux - mSensor.dx);
    short dy = abs(LDuy - mSensor.dy);
    short dz = abs(LDuz - mSensor.dz);
    if (dx > config.sys.rollOverAngle) {
        if (rollAngle < dx) {
            rollAngle = dx;
        }
    }
    if (dy > config.sys.rollOverAngle) {
        if (rollAngle < dy) {
            rollAngle = dy;
        }
    }

    if (dz > config.sys.rollOverAngle) {
        if (rollAngle < dz) {
            rollAngle = dz;
        }
    }

    if ((config.sys.rollOverAngle) &&
        (rollAngle > config.sys.rollOverAngle)) {
        logd("turn over x %d, y %d, z %d, thres %d!\n", dx, dy, dz, config.sys.rollOverAngle);
        LTurnResult = 1;                                                   // 发生翻转.
    } else {
        LTurnResult = 0;
        //logd("turn over x %d, y %d, z %d, thres %d!\n", dx, dy, dz, config.sys.rollOverAngle);
    }

    //* 翻转状态判定逻辑.
    if ((0 == LTurnState) && (1 == LTurnResult)) {  //状态直到被确认
        if(LTurnCount++ > config.sys.rollOverCnt) {
            logd("turn over %d, thres %d!\n", rollAngle, config.sys.rollOverAngle);
            LTurnState = 1;
            LTurnCount = 0;
        }
    } else if ((1 == LTurnState) && (0 == LTurnResult)) {  //清除数据状态//状态被取消
        if(LTurnOffCount++ > (config.sys.rollOverCnt / 4)) {
          LTurnState = 0;
          LTurnOffCount = 0;
        }
    } else {//判断状态与当前状态一致
        LTurnOffCount = 0;
        LTurnCount = 0;
    }
    return LTurnState;
}

