#include "mcu.msg.handler.h"

SINGLETON_STATIC_INSTANCE(McuMsgHandler);

McuMsgHandler::McuMsgHandler()
{
    mbRun = true;
    mClient = new IpcClient("idvr.ccu", SH_NAME_HOSTIO);
    memset(&mLoc, 0, sizeof(mLoc));
    mLastIOStat.value = 0;
    mEnableFake.value = 0;
    mFakeStatus.value = 0;

    comm_send(MCU_MSG_TYPE_IC_CARD_RD, NULL, 0);
    mLastIOStatTm = my::timestamp::now();
    mTimeTSCan0_0110 = my::timestamp::now();
    mTimeTSCan0_0111 = my::timestamp::now();

    mTimeTSCan1_0110 = my::timestamp::now();
    mTimeTSCan1_0111 = my::timestamp::now();

    mTimeTSCan0 = my::timestamp::now();
    mTimeTSCan1 = my::timestamp::now();
}

int McuMsgHandler::init()
{
    OutputService & os = OutputService::getInstance();
    os.addMsgHdlr("mcuMsgHdlr", this);
    return my::thread::start();
}
void McuMsgHandler::fini()
{
    mbRun = false;
    my::thread::stop();
}

bool McuMsgHandler::sendMcuMsg(McuMsgTypeE cmd, uint8_t *data, int32_t len)
{
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];
    memset(buf, 0, sizeof(buf));
    pMsg->ipcMsg.type = cmd;
    pMsg->ipcMsg.len = len;

    if (data && len) {
        memcpy(&pMsg->u.u8Array[0], data, len);
    }

    return mClient->send(&(pMsg->ipcMsg));
}
int McuMsgHandler::sendCan0Msg(uint32_t id, uint8_t* data)
{
    typedef struct 
    {
        uint32_t id;
        uint8_t  len;
        uint8_t  data[8];
    }  __attribute__((packed)) UrtpCanT;

    UrtpCanT msg;
    msg.id = id;
    msg.len = 8;
    for (int i = 0; i < 8; i++) {
        msg.data[i] = data[i];
    }

    return sendMcuMsg(MCU_MSG_TYPE_RAW_CAN0, (uint8_t *)&msg, sizeof(UrtpCanT));
}

int McuMsgHandler::sendCan1Msg(uint32_t id, uint8_t* data)
{
    typedef struct 
    {
        uint32_t id;
        uint8_t  len;
        uint8_t  data[8];
    }  __attribute__((packed)) UrtpCanT;

    UrtpCanT msg;
    msg.id = id;
    msg.len = 8;
    for (int i = 0; i < 8; i++) {
        msg.data[i] = data[i];
    }

    return sendMcuMsg(MCU_MSG_TYPE_RAW_CAN1, (uint8_t *)&msg, sizeof(UrtpCanT));
}

int McuMsgHandler::sendIOCtrl(McuIOTypeE type)
{
    uint8_t cmd[1];
    cmd[0] = type;
    return sendMcuMsg(MCU_MSG_TYPE_IO_CTRL, cmd, 1);
}


void McuMsgHandler::enableFakeStat(const char * name, int enable) {
    if (!name) {
        mEnableFake.value = enable ? 0xffff : 0;
    }
    else {
         const vector<string> statName = {"acc", "emerg", "back", "normalLight", "farLight", "left", "right", "brake",
            "door", "canLoss", "crash", "roll", "load"};
         auto it = statName.begin();
         int i = 0;
         while (it != statName.end()) {
            if (*it == name) {
                if (*it == "load") {
                    if (enable) {
                        enable = 3;
                    } else {
                        enable = 0;
                    }
                }
                mEnableFake.value &= ~(enable << (i));
                mEnableFake.value |= (((int)enable) << (i));
                break;
            }
            it++;
            i++;
         }
    }
}


void McuMsgHandler::setFakeStatus(const char * name, int value)
{
    if (name) {
         const vector<string> statName = {"acc", "emerg", "back", "normalLight", "farLight", "left", "right", "brake",
            "door", "canLoss", "crash", "roll", "load"};
         auto it = statName.begin();
         int i = 0;
         while (it != statName.end()) {
            if (*it == name) {
                if (*it == "load") {
                    mFakeStatus.value &= ~(3 << (i));
                    mFakeStatus.value |= ((value & 3) << (i));
                } else {
                    mFakeStatus.value &= ~(1 << (i));
                    mFakeStatus.value |= ((!!value) << (i));
                }
                break;
            }
            it++;
            i++;
         }
    }
}

void McuMsgHandler::run()
{
    prctl(PR_SET_NAME, "McuMsgHandler");
    while (mbRun) {
        uint8_t buf[MCU_MSG_MAX_SIZE];
        McuMessage *pMsg = (McuMessage *)&buf[0];
        //printf("McuMsgHandler read\n");
        if (mClient->recv(&(pMsg->ipcMsg))) {
            onMcuMsg(pMsg);
        }
        else {
            loge("mcu client read fail!\n");
        }
    }
}

bool McuMsgHandler::onCanMsg(McuMessage * message, int32_t canIdx)
{
    InputService & is = InputService::getInstance();
    std::shared_ptr<struct STAT_DATA> sp = make_shared<struct STAT_DATA>();
    if (0 == canIdx) {
        sp->flag = STAT_DATA_FLAG_CAN0;
    } else if (1 == canIdx){
        sp->flag = STAT_DATA_FLAG_CAN1;
    }
    CanData * data = reinterpret_cast<CanData *>(sp->ctx.data);
    data->canID = message->u.canMsg[0].id;
    data->canID &= ~(1 << 29); // 0：原始数据， 1：区间平均值
    //标准帧：000-7FF 0000 扩展帧：0000-1FFFFFFF
    if (message->u.canMsg[0].id <= 0x7FF) {
        data->canID &= ~(1 << 30);//0标准帧 1扩展帧
    } else {
        data->canID |= (1 << 30);//0标准帧 1扩展帧
    }
    if (canIdx) {
        data->canID |= (1 << 31);  // 0：can1， 1：can2
    } else {
        data->canID &= ~(1 << 31);  // 0：can1， 1：can2
    }
    memcpy(&data->canData, message->u.canMsg[0].data, 8);
    is.push(sp);
    return true;
}

bool McuMsgHandler::onMcuMsg(McuMessage * msg) {
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();

    switch(msg->ipcMsg.type)
    {
        case MCU_MSG_TYPE_TRIP_INFO: {
            std::shared_ptr<struct STAT_DATA> sp = make_shared<struct STAT_DATA>();
            sp->flag = STAT_DATA_FLAG_TRIPINFO;
            struct STAT_DATA & s = *sp.get();
            s.ctx.tripInfo.startTime = msg->u.trip[0].startTime;
            s.ctx.tripInfo.endTime   = msg->u.trip[0].endTime;
            s.ctx.tripInfo.todayTotal = msg->u.trip[0].todayTotal;
            is.push(sp);
            logd("MCU_MSG_TYPE_TRIP_INFO %d, %d, %d", msg->u.trip[0].startTime, msg->u.trip[0].endTime, msg->u.trip[0].todayTotal);
            break;
        }
        case MCU_MSG_TYPE_STAT: {
            if (MCU_MSG_SIZE_STAT == msg->ipcMsg.len) {
                //m.current->mMcuStat = msg->u.stat[0];
                if (mLastIOStatTm.elapsed() >= 30)
                {
                    mLastIOStatTm = my::timestamp::now();
                    std::shared_ptr<struct STAT_DATA> sp = make_shared<struct STAT_DATA>();
                    sp->flag = STAT_DATA_FLAG_IOSTATUS;
                    struct STAT_DATA & s = *sp.get();
                    McuMsgMcuStatT * stat = &msg->u.stat[0];
                    s.ctx.stat.vehicle_io.acc = stat->vehicle_io.acc;
                    s.ctx.stat.vehicle_io.emergency_alarm = stat->vehicle_io.emergency_alarm;
                    s.ctx.stat.vehicle_io.backwards = stat->vehicle_io.backwards;
                    s.ctx.stat.vehicle_io.normal_light = stat->vehicle_io.normal_light;
                    s.ctx.stat.vehicle_io.far_light = stat->vehicle_io.far_light;
                    s.ctx.stat.vehicle_io.left_turn = stat->vehicle_io.left_turn;
                    s.ctx.stat.vehicle_io.right_turn = stat->vehicle_io.right_turn;
                    s.ctx.stat.vehicle_io.brake = stat->vehicle_io.brake;

                    s.ctx.stat.vehicle_io.door = stat->vehicle_io.door;
                    s.ctx.stat.vehicle_io.can_signal_loss = 0;
                    //s.ctx.stat.vehicle_io.crash_warn = 0;
                    //s.ctx.stat.vehicle_io.roll_alarm = 0;
                    s.ctx.stat.vehicle_io.car_load_stat = 0;
                    s.ctx.stat.vehicle_io.iccard_inserted = stat->dvr_io.iccard_inserted;
                    s.ctx.stat.dvr_io.value = stat->dvr_io.value;
                    if (mEnableFake.value) {/*设置假状态，测试用*/
                        s.ctx.stat.vehicle_io.value &= ~(mEnableFake.value);
                        s.ctx.stat.vehicle_io.value |= mFakeStatus.value;
                        logd("MCU_MSG_TYPE_STAT 0x%x, 0x%x, 0x%x", s.ctx.stat.vehicle_io.value, mEnableFake.value, mFakeStatus.value);
                    }
                    s.ctx.stat.lbs.sat = stat->lbs.sat;
                    s.ctx.stat.lbs.status = stat->lbs.status;
                    s.ctx.stat.lbs.sig_level = stat->lbs.sig_level;
                    s.ctx.stat.lbs.antenna = stat->lbs.antenna;
                    s.ctx.stat.lbs.lat_x1kw = stat->lbs.lat_x1kw;
                    s.ctx.stat.lbs.lng_x1kw = stat->lbs.lng_x1kw;
                    s.ctx.stat.lbs.alt_x10 = stat->lbs.alt_x10;
                    s.ctx.stat.lbs.dir_x100 = stat->lbs.dir_x100;
                    s.ctx.stat.lbs.speed_x10 = stat->lbs.speed_x10;
                    if (stat->lbs.time) {
                        s.ctx.stat.lbs.time = stat->lbs.time;
                    } else {
                        s.ctx.stat.lbs.time = time(NULL);
                    }
                    s.ctx.stat.lbs.HDOP =  stat->lbs.HDOP;
                    s.ctx.stat.lbs.rtk.enable = stat->lbs.rtkData.enable;
                    s.ctx.stat.lbs.rtk.sig = stat->lbs.rtkData.sig;
                    s.ctx.stat.lbs.rtk.lat = stat->lbs.rtkData.lat;
                    s.ctx.stat.lbs.rtk.lng = stat->lbs.rtkData.lng;
                    
                    s.ctx.stat.signal4G = stat->csq[0];

                    s.ctx.stat.pulses_speed_x10 = stat->pulses_speed_x10;
                    s.ctx.stat.pulses_turnl = stat->pulses_turnl;
                    s.ctx.stat.pulses_turnr = stat->pulses_turnr;

                    s.ctx.stat.can_speed_x10 = stat->can_speed_x10;
                    s.ctx.stat.can_turnl = stat->can_turnl;
                    s.ctx.stat.can_turnr = stat->can_turnr;

                    s.ctx.stat.speed_x10 = stat->speed_x10;
                    s.ctx.stat.turnl = stat->turnl;
                    s.ctx.stat.turnr = stat->turnr;

                    s.ctx.stat.total_mileage = stat->total_mileage;
                    //s.ctx.stat.ad1Raw = stat->adc1Raw;// * (stat->adc1Raw > 100);  /*小于约1.23V默认为0*/
                    //s.ctx.stat.ad2Raw = stat->adc2Raw;// * (stat->adc2Raw > 100);  /*小于约1.23V默认为0*/
                    //s.ctx.stat.ad1Vol = stat->adcVol[ADC_CHN_EXT_1];
                    //s.ctx.stat.ad2Vol = stat->adcVol[ADC_CHN_EXT_2];
                    s.ctx.stat.accOff_event = stat->accOff_event;

                    s.ctx.stat.pwr_stat.acc = stat->pwr_set.acc;
                    s.ctx.stat.pwr_stat.sys_pwr = stat->pwr_set.pwrSys;
                    s.ctx.stat.pwr_stat.pwr_ext_12v = stat->pwr_set.pwrExt12v;
                    s.ctx.stat.pwr_stat.pwr_ext_5v = stat->pwr_set.pwrExt5v;
                    s.ctx.stat.pwr_stat.bat_discharging = stat->pwr_set.batDischar;
                    s.ctx.stat.pwr_stat.bat_charging = stat->pwr_set.batEnchar;

                    s.ctx.stat.pwr_stat.power_low = stat->sys.power_low;
                    s.ctx.stat.pwr_stat.power_off = stat->sys.power_off;
                    s.ctx.stat.pwr_stat.reserved = 0;

                    is.push(sp);
                }
            }
            break;
        }

        case MCU_MSG_TYPE_IC_CARD_LOGIN: {
            if (MCU_MSG_SIZE_ICCARD == msg->ipcMsg.len) {
                std::shared_ptr<struct STAT_DATA> sp = make_shared<struct STAT_DATA>();
                sp->flag = STAT_DATA_FLAG_IC_CARD_LOGIN;
                struct STAT_DATA & s = *sp.get();
                ICCardInfo & icInfo = s.ctx.icCard;
                ICCardWrBuf & icCard = msg->u.icCard[0];
                memcpy(icInfo.id, icCard.id, sizeof(icInfo.id));
                memcpy(icInfo.expiry_bcd, icCard.expiry_bcd, sizeof(icInfo.expiry_bcd));
                memcpy(icInfo.cert, icCard.cert, sizeof(icInfo.cert));

                memcpy(icInfo.name, icCard.name, sizeof(icInfo.name));
                memcpy(icInfo.code, icCard.code, sizeof(icInfo.code));
                memcpy(icInfo.org, icCard.org, sizeof(icInfo.org));
                is.push(sp);
                logd("MCU_MSG_TYPE_IC_CARD_LOGIN");
            }
            break;
        }
        case MCU_MSG_TYPE_IC_CARD_LOGOUT: {
            std::shared_ptr<struct STAT_DATA> sp = make_shared<struct STAT_DATA>();
            sp->flag = STAT_DATA_FLAG_IC_CARD_LOGOUT;
            is.push(sp);
            logd("MCU_MSG_TYPE_IC_CARD_LOGOUT");
            break;
        }
        case MCU_MSG_TYPE_RECORD_STATUS: {
            std::shared_ptr<struct STAT_DATA> sp = make_shared<struct STAT_DATA>();
            sp->flag = STAT_DATA_FLAG_RECORD_STATUS;
            struct STAT_DATA & s = *sp.get();
            s.ctx.u32 = (uint32_t)msg->u.int32[0];
            is.push(sp);
            break;
        }
        case MCU_MSG_TYPE_CAR_INFO: {
            if (MCU_MSG_SIZE_CAR_INFO == msg->ipcMsg.len) {
                if (mCarInfoTS.elapsed() >= 500) {
                    mCarInfoTS = my::timestamp::now();
                    std::shared_ptr<struct STAT_DATA> sp = make_shared<struct STAT_DATA>();
                    sp->flag = STAT_DATA_FLAG_CARINFO;
                    struct STAT_DATA & s = *sp.get();
                    IdvrCarInfo & ci = s.ctx.carInfo;
                    McuMsgCarInfoT & carInfo = msg->u.carInfo[0];
                    ci.canDoorBits      = carInfo.canDoorBits;
                    ci.accelerator      = carInfo.accelerator;
                    ci.canTurn          = carInfo.canTurn;
                    ci.canBrake         = carInfo.canBrake;
                    ci.gear             = carInfo.gear;
                    ci.canReverse       = carInfo.canReverse;
                    ci.EHB_state        = carInfo.EHB_state;
                    ci.EHB_park_req     = carInfo.EHB_park_req;
                    ci.EHB_park_done    = carInfo.EHB_park_done;
                    ci.FLWheelSpdStat   = carInfo.FLWheelSpdStat;
                    ci.FRWheelSpdStat   = carInfo.FRWheelSpdStat;
                    ci.BLWheelSpdStat   = carInfo.BLWheelSpdStat;
                    ci.BRWheelSpdStat   = carInfo.BRWheelSpdStat;
                    ci.FLWheelSpd       = carInfo.FLWheelSpd;
                    ci.FRWheelSpd       = carInfo.FRWheelSpd;
                    ci.BLWheelSpd       = carInfo.BLWheelSpd;
                    ci.BRWheelSpd       = carInfo.BRWheelSpd;
                    ci.canSpeed         = carInfo.canSpeed;
                    ci.canMileTotal     = carInfo.canMileTotal;
                    ci.canMileSingle    = carInfo.canMileSingle;
                    ci.canFuelTotal     = carInfo.canFuelTotal;
                    ci.canFuelAvg       = carInfo.canFuelAvg;
                    ci.canTurnSpeed     = carInfo.canTurnSpeed;
                    is.push(sp);
                }
                //logd("MCU_MSG_SIZE_CAR_INFO");
            }
            break;
        }
        case MCU_MSG_TYPE_RAW_CAN0: {
            if (access("/data/canRpt", R_OK)) {
                break;
            }
#if 0
            logd("recv can++ msg=%d, currentID = %d, collectID = %d - %d, collectInterval=%d, canChannel %d, interval %d - %d", MCU_MSG_TYPE_RAW_CAN0,
                msg->u.canMsg[0].id, config.sys.can_setting_0110.frameID, config.sys.can_setting_0111.frameID, config.sys.canParam.can1CollectInterval, config.sys.can_setting_0110.canChannel,
                config.sys.can_setting_0110.frameInterval, config.sys.can_setting_0111.frameInterval);
#endif
            //0110 can单独设置
            if (0 == config.sys.can_setting_0110.canChannel && config.sys.can_setting_0110.frameID == msg->u.canMsg[0].id) { //收集到的canID与平台下发的canID一致
                if (config.sys.can_setting_0110.frameInterval != 0) {// && mTimeTSCan0_0110.elapsed() > config.sys.can_setting_0110.frameInterval) { // canID采集间隔不为0 并且 时间间隔大于采集间隔
                    mTimeTSCan0_0110 = my::timestamp::now();
                    logd("recv can msg=%d, currentID = %d, collectID = %d - %d, collectInterval=%d", MCU_MSG_TYPE_RAW_CAN0,
                        msg->u.canMsg[0].id, config.sys.can_setting_0110.frameID, config.sys.can_setting_0111.frameID, config.sys.canParam.can1CollectInterval);
                    onCanMsg(msg, 0);
                }
                break;
            }
            
            //0111 can单独设置
            if (0 == config.sys.can_setting_0111.canChannel && config.sys.can_setting_0111.frameID == msg->u.canMsg[0].id) {
                if (config.sys.can_setting_0111.frameInterval != 0) { // && mTimeTSCan0_0111.elapsed() > config.sys.can_setting_0111.frameInterval) {
                    mTimeTSCan0_0111 = my::timestamp::now();
                    logd("recv can msg=%d, currentID = %d, collectID = %d - %d, collectInterval=%d", MCU_MSG_TYPE_RAW_CAN0,
                        msg->u.canMsg[0].id, config.sys.can_setting_0110.frameID, config.sys.can_setting_0111.frameID, config.sys.canParam.can1CollectInterval);
                    onCanMsg(msg, 0);
                }
                break;
            }

            //其它
            if (mTimeTSCan0.elapsed() > 0/*config.sys.canParam.can1CollectInterval*/) {
                mTimeTSCan0 = my::timestamp::now();
                onCanMsg(msg, 0);
            }
            break;
        }
        case MCU_MSG_TYPE_RAW_CAN1: {
            if (access("/data/canRpt", R_OK)) {
                break;
            }
#if 0
            logd("recv can++ msg=%d, currentID = %d, collectID = %d - %d, collectInterval=%d, canChannel %d, interval %d - %d", MCU_MSG_TYPE_RAW_CAN0,
                msg->u.canMsg[0].id, config.sys.can_setting_0110.frameID, config.sys.can_setting_0111.frameID, config.sys.canParam.can1CollectInterval, config.sys.can_setting_0110.canChannel,
                config.sys.can_setting_0110.frameInterval, config.sys.can_setting_0111.frameInterval);
#endif
            //0110 can单独设置
            if (1 == config.sys.can_setting_0110.canChannel && msg->u.canMsg[0].id == config.sys.can_setting_0110.frameID) {
                if (config.sys.can_setting_0110.frameInterval != 0) { // && mTimeTSCan1_0110.elapsed() > config.sys.can_setting_0110.frameInterval) {
                    mTimeTSCan1_0110 = my::timestamp::now();
                    logd("recv can++ msg=%d, currentID = %d, collectID = %d - %d, collectInterval=%d", MCU_MSG_TYPE_RAW_CAN1, 
                        msg->u.canMsg[0].id, config.sys.can_setting_0110.frameID, config.sys.can_setting_0111.frameID,  config.sys.canParam.can2CollectInterval);
                    onCanMsg(msg, 1);
                }
                break;
            }
            
            //0111 can单独设置
            if (1 == config.sys.can_setting_0111.canChannel && msg->u.canMsg[0].id == config.sys.can_setting_0111.frameID) {
                if (config.sys.can_setting_0111.frameInterval != 0) {// && mTimeTSCan1_0111.elapsed() > config.sys.can_setting_0111.frameInterval) {
                    mTimeTSCan1_0111 = my::timestamp::now();
                    logd("recv can++ msg=%d, currentID = %d, collectID = %d - %d, collectInterval=%d", MCU_MSG_TYPE_RAW_CAN1, 
                        msg->u.canMsg[0].id, config.sys.can_setting_0110.frameID, config.sys.can_setting_0111.frameID,  config.sys.canParam.can2CollectInterval);
                    onCanMsg(msg, 1);
                }
                break;
            }

            //其它
            if (mTimeTSCan1.elapsed() > 0/*config.sys.canParam.can2CollectInterval*/) {
                mTimeTSCan1 = my::timestamp::now();
                onCanMsg(msg, 1);
            }
            break;
        }

        case MCU_MSG_TYPE_GPS_CMD: {/*NMEA RAW data*/
            //todo
            break;
        }
        case MCU_MSG_TYPE_SAT_INFO: {
            struct SatellitesInfo & si = msg->u.satInfo[0];
            std::shared_ptr<struct STAT_DATA> sp = make_shared<struct STAT_DATA>();
            sp->flag = STAT_DATA_FLAG_SAT_INFO;
            struct STAT_DATA & st = *sp.get();
            struct IdvrSatellitesInfo & s = st.ctx.satInfo;
            s.num = si.num;
            for (int i = 0; i < si.num; i++) {
                //logd("%d : type %d, idx %d", i, si.s[i].type, si.s[i].idx);
                s.s[i].type = si.s[i].type;
                s.s[i].idx = si.s[i].idx;
                s.s[i].elevation = si.s[i].elevation;
                s.s[i].cnr = si.s[i].cnr;
                s.s[i].bearing = si.s[i].bearing;
            }
            is.push(sp);
            break;
        }
        default: {
            //loge("unknown mcu msg type %d, len %d", msg->type, msg->len);
            break;
        }
    }
    return true;
}


