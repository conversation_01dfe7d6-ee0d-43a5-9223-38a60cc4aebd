#ifndef __MCU_MSG_HANDLER_H__
#define __MCU_MSG_HANDLER_H__
#include "mystd.h"
#include "ipcAgent.h"
#include "McuMessage.h"
#include "event.h"
#include "in.service.h"
#include "out.service.h"
#include "service.data.h"

// hostIO mcu消息处理器
class McuMsgHandler
    : public my::thread
    , public IOutputMsgCb
    , public my::Singleton<McuMsgHandler>
{
    friend class my::Singleton<McuMsgHandler>;

private:
    McuMsgHandler();
    void comm_send(McuMsgTypeE cmd, uint8_t *data, int32_t len)
    {
        uint8_t buf[MCU_MSG_MAX_SIZE];
        McuMessage *pMsg = (McuMessage *)&buf[0];
        memset(buf, 0, sizeof(buf));
        pMsg->ipcMsg.type = cmd;
        pMsg->ipcMsg.len = len;
        if (data && len) {
            memcpy(&pMsg->u.u8Array[0], data, len);
        }
        mClient->send(&(pMsg->ipcMsg));
    }

public:
    int init();
    void fini();

public:
    virtual bool sendMcuMsg(McuMsgTypeE cmd, uint8_t *data, int32_t len);
    virtual bool sendLbsMsg(IdvrLBS & l)
    {
        LBS lbs ;
        lbs.sat = l.sat;
        lbs.status = l.status;
        lbs.sig_level = l.sig_level;
        lbs.antenna = l.antenna;
        lbs.lat_x1kw = l.lat_x1kw;
        lbs.lng_x1kw = l.lng_x1kw;
        lbs.alt_x10 = l.alt_x10;
        lbs.dir_x100 = l.dir_x100;
        lbs.speed_x10 = l.speed_x10;
        lbs.time = l.time;
        lbs.rtkData.enable = l.rtk.enable;
        lbs.rtkData.sig = l.rtk.sig;
        lbs.rtkData.lat = l.rtk.lat;
        lbs.rtkData.lng = l.rtk.lng;
        return sendMcuMsg(MCU_MSG_TYPE_EXT_LBS, (uint8_t *)&lbs, sizeof(lbs));
    }
#if 0
    virtual bool fetchGb19056(uint8_t * param, int param_len)
    {
        return sendMcuMsg(MCU_MSG_TYPE_MCU_GB19056_FETCH, param, param_len);
    }
#endif
    virtual bool setOilIO(bool high)
    {
        return sendIOCtrl(high ? MINOR_GPIO_OIL_HIGH : MINOR_GPIO_OIL_LOW);
    }
    virtual bool  setSocSysPwr(bool on)
    {
        if (on) {
            return sendIOCtrl(MINOR_GPIO_PWR_SYS_ON) &&
                sendIOCtrl(MINOR_GPIO_PWR_12V_ON) &&
                sendIOCtrl(MINOR_GPIO_PWR_5V_ON);
        } 
        return sendIOCtrl(MINOR_GPIO_PWR_SYS_OFF);
    }

    virtual int sendCan0Msg(uint32_t id, uint8_t* data);
    virtual int sendCan1Msg(uint32_t id, uint8_t* data);
    virtual int sendIOCtrl(McuIOTypeE type);
    virtual int sendIOCtrl(int type){
        return sendIOCtrl((McuIOTypeE)type);
    }

    void enableFakeStat(const char * name, int enable);
    void setFakeStatus(const char * name, int value);

protected:
    void run();

    bool onMcuMsg(McuMessage * msg);

    bool onCanMsg(McuMessage * message, int32_t canIdx);


private:
    IpcClient * mClient;
    bool mbRun;
    IdvrLocation mLoc;
    IdvrSatellitesInfo mSatInfo;

    my::timestamp mLastIOStatTm;
    IOStatus mLastIOStat;
    IOStatus mEnableFake;
    IOStatus mFakeStatus;

    my::timestamp mTimeTSCan0_0110;
    my::timestamp mTimeTSCan0_0111;
    
    my::timestamp mTimeTSCan1_0110;
    my::timestamp mTimeTSCan1_0111;

    my::timestamp mTimeTSCan0;
    my::timestamp mTimeTSCan1;
    
    my::timestamp mCarInfoTS;
};
#endif

