#ifndef __IDVRCORE_SERVICE_H__
#define __IDVRCORE_SERVICE_H__
#include "AMessage.h"
#include "event.h"
#include "service.data.h"
#include "prot.jtt808-1078.data.h"

class ComService: public std::enable_shared_from_this<ComService>
{
public:
    typedef std::shared_ptr<ComService> Ptr;
public:
    ComService() {}
    virtual ~ComService() {}

    // 启动服务(可能是客户端, 也可能是服务端)
    virtual int start(const ServerInfo& si) 
    {
        return -1;
    }
    virtual int reload(const ServerInfo& si)
    {
        return -1;
    }
    // 停止服务
    virtual void stop()
    {
    }
    
    virtual int protVersion()
    {
        return mProtVersion;
    }
    
    virtual void setProtVersion(int v)
    {
        mProtVersion = v;
    }

    virtual void dummy(bool enable) = 0;

    virtual my::string get_type() = 0;
    virtual int reportBsdAlarm(std::shared_ptr<Event> evt, int ch) = 0;
    virtual int reportDmsAlarm(std::shared_ptr<Event> evt, int ch) = 0;
    virtual int reportAdasAlarm(std::shared_ptr<Event> evt, int ch) = 0;
    virtual int reportOverHeightAlarm(std::shared_ptr<Event> evt, int ch) = 0;
    virtual int reportHardDrivingAlarm(std::shared_ptr<Event> evt, int ch) = 0;
    virtual int procEvt(std::shared_ptr<Event> evt, int ch) = 0;
    virtual std::string showInfo(int type) = 0;
    virtual bool connected(int idx = 0) = 0;
    virtual int postMsg(std::shared_ptr<minieye::AMessage> & spMsg)
    {
        loge("Not impl !!!");
        return -1;
    }

    virtual bool gpsDataProc(const IdvrLocation& loc)
    {
        return false;
    }
    
    virtual bool canDataProc(int canIdx, CanData & data)
    {
        return false;
    }
    virtual void addMedia2Send(const Media &m)
    {
    }
public:
    // 创建协议对象
    static ComService::Ptr create(const char* protocol, const char* tag, const char * sim, const char * protSubType);

public:
    ServerInfo si[2];

protected:
    int mProtVersion = 0;
};

#endif
