
executable("idvr.ccu") {
    sources = [
        "mcu.msg.handler/mcu.msg.handler.cpp",
        "sms.recieve.handler/sms.recieve.handler.cpp",
        "main.cpp",
        "manager.cpp",
        "controller.cpp",
        "service.cpp",
        "service.jtt808-1078.cpp",
        "service.private.cpp",
    ]


    include_dirs = [
        ".",
        "./mcu.msg.handler",
        "./sms.recieve.handler",
        "//applications/idvr/include/idvr",
    ]


    deps = [
        "//applications/protocol/algo",
        "//applications/protocol/prot.jtt",  
        "//applications/protocol/prot.anncr.sanfeng",
        "//applications/protocol/prot.middle.ware",
        "//applications/protocol/prot.jtt808-1078",      
        "//applications/protocol/road.net/libroadnet:roadnet",
        "//foundation/base/core/mystd",
        "//foundation/base/core/filelog",
        "//foundation/base/service/tts/libtts",
        "//foundation/base/service/imu/libimu:imu",
        "//foundation/communication/ipcAgent",
        "//foundation/communication/socketcmd",
        "//foundation/communication/message:message",
        "//foundation/communication/ringbuf:ringbuf",
        "//foundation/communication/libflow:flowWrap",
        "//foundation/communication/property:property",
        "//third_party/curl:curl_static",
        "//third_party/msgpack-c:msgpackc",
        "//third_party/liboss:liboss_static",
        "//third_party/SQLiteCpp:SQLiteCpp",
        "//foundation/base/service/libphone",
        "//foundation/communication/libevservice",
        "//foundation/communication/fileprop:fileprop",
        "//foundation/multimedia/libsrc/libfallocate",
    ]

    cflags_cc = [
        "-Wno-psabi",
        "-Wno-unused-parameter",
    ]

    defines = [ 
        "LOG_TAG_STR=${target_name}",
     ]

    libs = [
    ]

    sigmastar_sdk_dir = rebase_path("//applications/vendor/8838_sdk4/lib/share/")

    ldflags = [
        "-L${sigmastar_sdk_dir}/opencv_9.1.0",
        "-lopencv_imgcodecs",
        "-lopencv_imgproc",
        "-lopencv_core",
        "-ldl",
    ]
}
