#ifndef __IDVRCORE_MANAGER_H__
#define __IDVRCORE_MANAGER_H__

#include "mystd.h"
#include "service.data.h"
#include "service.h"
#include "current.h"
#include "config.h"
#include "controller.h"
#include "algo.h"
#include "AMessage.h"
#include "mcu.msg.handler.h"
#include "rec.alarm.manager.h"
#include "io.service.h"
#include "prot.jtt808-1078.data.h"

#define MSG_APPEND_KEY_VAL(msg, key, fmt, val ...) APPEND_STR_MSG(msg, key, 16, fmt, ## val)

//配置文件接收类
class MediaConfigReceiver : public CmdBroadcastReceiver
{

public:
   MediaConfigReceiver() : CmdBroadcastReceiver("config") {}
   bool OnBroadcastCallback(vector<string>& vec);
};

class Manager
    : public my::thread
    , public IALGO_OBSERVER
    , public my::Singleton<Manager>
{
    friend class my::Singleton<Manager>;
public:
    virtual ~Manager();

    // 启停内核
    int start();
    int restart();
    void stop();
    bool refresh(char* section,char* key,char* value);

    int postMsg(std::shared_ptr<minieye::AMessage> & msg);

    int protDeregister(int idx, const char * protName = "jtt808-1078");
    int queryServerTime(int idx);
    /* chuangbiao ext */
    int responseDriverInfo(int idx);
    int rptGnss(int idx);
    int triggerEbill(int protIdx);

    int triggerAdasAlarm(const char *e, int idx);
    int triggerBsdAlarm(const char * e, int idx);
    int triggerDmsAlarm(const char * e, int idx);
    int triggerDumpperAlarm(const char* evt_name, int idx);
    int triggerZTCAlarm(const char* evt_name, int idx);

    /* 川标超速告警 */
    int triggerSpeedingAlarm(SpeedingAlarmParam & param);

    /* 川标超过限高告警 */
    int triggerOverHeightAlarm(const my::uchar alarm_status, int idx, my::ushort limits);

    /* 川标超过限重告警 */
    int triggerOverLoadAlarm(const my::uchar alarmType, const my::uchar alarmStatus, int idx, my::ushort loadVal, my::ushort limits);
    /* 川标激烈驾驶告警*/
    int triggerHardDrivingAlarm(const char* e, int idx);
    /* 驾驶员识别 */
    int triggerFaceMatch_v2();
    bool getProtStat(int idx, int sidx = 0);
    std::string getProtAlarmInfo(int idx);
    int sendMcuMsg(McuMsgTypeE cmd, uint8_t * data, int32_t len);
    int sendCanFilterMsg(uint8_t * data, int32_t len);
    int sendCanFilterMsgCycle(uint8_t * data, int32_t len);
#if 0
    void getImuData(std::vector<ImuDataT> & imuList) {
        state_controller->getImuData(imuList);
    }
#endif
    void enableFakeStat(const char * name, int enable);
    void setFakeStatus(const char * name, int value);

    void displayWarnBmp(int level);
    bool dummyProt(int idx, int enable);

private:

    int send_adas_can_0x700(uint64_t can_msg)
    {
        McuMsgHandler & h = McuMsgHandler::getInstance();
        h.sendMcuMsg(MCU_MSG_TYPE_ADAS_CAN700, (uint8_t *)&can_msg, 8);
        return 0;
    }
    bool onCanMsg(McuMessage * message, int32_t canIdx);

    Manager();
    void initTTsIdx();
    void findLastFile(const my::string &filePath, std::pair<uint64_t, uint32_t> &fileInfo);
    void init();
    // 启停协议app
    int start_app();
    void stop_app();

    bool smsParse();
    bool spdingChk();
    bool forbidDrvTmChk();

protected:
    bool alarmInterval(std::shared_ptr<Event> e);
    virtual bool onAlgoEvent(std::shared_ptr<Event> e);
    virtual void run();

public:
    void insertAlarmTips(my::string evt_name, my::uint64 tm_ms);
    void deletAlarmTips(my::string evt_name);

public:
    bool mTrigDrvReg = false;
    RecAlarmManager *mRecAlarmManager = nullptr;

    ComController::Ptr com_controller;
    StateController::Ptr state_controller;

private:
    IdvrLocation mLoc;
    time_t   mLastIOStatTm = 0;
    IOStatus mLastIOStat;
    IOStatus mEnableFake;
    IOStatus mFakeStatus;

    my::timestamp mTimeTSCan0_0110;
    my::timestamp mTimeTSCan0_0111;

    my::timestamp mTimeTSCan1_0110;
    my::timestamp mTimeTSCan1_0111;

    my::timestamp mTimeTSCan0;
    my::timestamp mTimeTSCan1;

    my::timestamp mCarInfoTS;

    MediaConfigReceiver mConfigReceiver;
    my::timestamp curStatBgnTmMs;
    my::timestamp mLastSmsParseTmMs = 0;
    int32_t spdingTipCnt, wSpdingTipCnt, stat = -1, lastStat = -1;
    my::timestamp spdingTipTmMs, wSpdingTipTmMs;
    std::map<uint32_t, my::constr> mCanIdData[MCU_CAN_IDX_CAN_MAX];
    McuMsgCanFilterT mCanFileter[2];

    std::unordered_map<int, my::timestamp> mLastAlarmTbl;
    std::unordered_map<int, std::string>   mLastEvtTbl;
    std::unordered_map<int, int>           mLastEvtLvl;
    uint32_t mAlarmIntervalMs;
};

#endif
