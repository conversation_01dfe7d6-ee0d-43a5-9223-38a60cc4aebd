include ../mk_include
TARGET_NAME=idvr.ccu
TARGET=$(OUT_BIN_DIR)/$(TARGET_NAME)

LIB_FLAGS=-ldl -lpthread -lz -lmystd -lflowWrap -lflow -lmsgpackc -std=c++11 -lipcAgent -lmessage -lsocketcmd -lfilelog\
	-lringbuf -ltts -limu -lRoadNetAgent -lproperty -lcurl

CXX_FLAGS += -DLOG_TAG_STR=$(TARGET) -Wno-psabi

LIB_FLAGS+=-L$(LIB_DIR)/system \
	-L$(LIB_DIR)/thirdparty \
	-L$(LIB_DIR)/thirdparty/curl

STATIC_LIBS += $(SSC_8838G_DIR)/lib/static/opencv_9.1.0/libopencv_videoio.a \
	$(SSC_8838G_DIR)/lib/static/opencv_9.1.0/libopencv_imgcodecs.a \
	$(SSC_8838G_DIR)/lib/static/opencv_9.1.0/libopencv_imgproc.a \
	$(SSC_8838G_DIR)/lib/static/opencv_9.1.0/libopencv_calib3d.a \
	$(SSC_8838G_DIR)/lib/static/opencv_9.1.0/libopencv_core.a \
	$(SSC_8838G_DIR)/lib/static/opencv_9.1.0/opencv4/3rdparty/libade.a \
	$(SSC_8838G_DIR)/lib/static/opencv_9.1.0/opencv4/3rdparty/libittnotify.a \
	$(SSC_8838G_DIR)/lib/static/opencv_9.1.0/opencv4/3rdparty/libjasper.a \
	$(SSC_8838G_DIR)/lib/static/opencv_9.1.0/opencv4/3rdparty/libjpeg-turbo.a \
	$(SSC_8838G_DIR)/lib/static/opencv_9.1.0/opencv4/3rdparty/libpng.a \
	$(SSC_8838G_DIR)/lib/static/opencv_9.1.0/opencv4/3rdparty/libprotobuf.a \
	$(SSC_8838G_DIR)/lib/static/opencv_9.1.0/opencv4/3rdparty/libtiff.a \
	$(SSC_8838G_DIR)/lib/static/opencv_9.1.0/opencv4/3rdparty/libwebp.a \
	$(SSC_8838G_DIR)/lib/static/opencv_9.1.0/opencv4/3rdparty/libquirc.a \
	$(SSC_8838G_DIR)/lib/static/opencv_9.1.0/opencv4/3rdparty/libtegra_hal.a \
	$(SSC_8838G_DIR)/lib/static/opencv_9.1.0/opencv4/3rdparty/libz.a \
	$(LIB_DIR)/thirdparty/sqlite/libSQLiteCpp.a\
	$(LIB_DIR)/thirdparty/sqlite/libsqlite3.a


INCS=	-I$(TOP_DIR)/include/mystd \
		-I$(TOP_DIR)/include/system/libflowWrap \
		-I$(TOP_DIR)/include/system/idvr \
		-I$(TOP_DIR)/include/system/message \
		-I$(TOP_DIR)/include/system/libflow \
		-I$(TOP_DIR)/include/system/ringbuf \
		-I$(TOP_DIR)/include/system/filelog \
		-I$(TOP_DIR)/include/system/ipcAgent \
		-I$(TOP_DIR)/include/system/socketcmd \
		-I$(TOP_DIR)/include/system/libtts \
		-I$(TOP_DIR)/include/system/libimu \
		-I$(TOP_DIR)/include/system/expand \
		-I$(TOP_DIR)/include/system/ \
		-I$(TOP_DIR)/include/utils/ \
		-I$(TOP_DIR)/include/thirdparty/ \
		-I$(TOP_DIR)/include/thirdparty/mp4v2 \
		-I$(TOP_DIR)/include/thirdparty/opencv/ \
		-I$(TOP_DIR)/include/thirdparty/nlohmann_json/ \
		-I$(TOP_DIR)/include/thirdparty/sqlitecpp/include/ \
		-I$(TOP_DIR)/include/thirdparty/curl/ \
		-I$(TOP_DIR)/include/thirdparty/msgpack-c/ \
		-I$(TOP_DIR)/include/system/libroadNet/ \
		-I$(TOP_DIR)/include/system/libimu/ \
		-I$(TOP_DIR)/include/system/libproperty \
		-I $(SSC_8838G_DIR)/include \
		-I.

SRCS=	$(wildcard *.cpp)

OUT_OBJ_DIR=$(OUT_DIR)/obj/$(TARGET_NAME)

OBJECTS = $(patsubst %.cpp,%.o, $(SRCS))
OBJS_OUT = $(addprefix $(OUT_OBJ_DIR)/,$(notdir $(OBJECTS)))

.PHONY:all

all:$(OUT_OBJ_DIR) $(TARGET)

$(OUT_OBJ_DIR):
	mkdir -p $@

$(OUT_OBJ_DIR)/%.o:%.cpp
	$(CXX) $(CXX_FLAGS) $(INCS) -MM -MT $@ -MF $(patsubst %.o, %.d, $@) $<
	$(CXX) $(CXX_FLAGS) -DEXECUTABLE $(INCS) -c $< -o $@

$(TARGET):$(OBJS_OUT)
	$(CXX) $(CXX_FLAGS) $(BIN_FLAGS) $(OBJS_OUT) $(LIB_FLAGS) -o $@ $(STATIC_LIBS)
ifneq ($(DEBUG), y)
	$(STRIP) $(TARGET)
endif

clean:
	rm $(OUT_OBJ_DIR)/*;rm $(TARGET);

DEP = $(OBJS_OUT:%.o=%.d)
-include $(DEP)
