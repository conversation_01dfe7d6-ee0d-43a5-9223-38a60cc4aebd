#ifndef __IDVRCORE_SERVICE_PRIVITE_H__
#define __IDVRCORE_SERVICE_PRIVITE_H__

#include "mystd.h"
#include "service.h"

// 私有协议
class ServicePrivate : public ComService
{
public:
	ServicePrivate();
	~ServicePrivate();

	// 启动服务(可能是客户端, 也可能是服务端)
	int start(const char* ip, int port);

	// 停止服务
	void stop();

    void dummy(bool enable)
    {
    }

    my::string get_type()
    {
        return "private";
    }
    int reportBsdAlarm(std::shared_ptr<Event> evt, int ch){ return 0;}
    int reportDmsAlarm(std::shared_ptr<Event> evt, int ch){ return 0;}
    int reportAdasAlarm(std::shared_ptr<Event> evt, int ch){return 0;}
    int reportOverHeightAlarm(std::shared_ptr<Event> evt, int ch){return 0;}
    int reportHardDrivingAlarm(std :: shared_ptr < Event > evt, int ch) {return 0;}
    int procEvt(std::shared_ptr<Event> evt, int ch){return 0;}
    std::string showInfo(int type) {return "";}
    bool connected(int idx = 0){ return false;}
};

#endif
