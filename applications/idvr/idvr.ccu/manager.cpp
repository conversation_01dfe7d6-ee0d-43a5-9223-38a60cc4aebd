#include <time.h>

#include "jttType.h"
#include "json.hpp"
using json = nlohmann::json;
#include "ttsPlay.h"
#include "CmdListener.h"
#include "expand.receiver.h"
#include "prot.config.helper.h"
#include "service.jtt808-1078.h"
#include "mcu.msg.handler.h"
#include "roadnetManager.h"
#include "car.status.cache.h"
#include "wakeup.manager.h"
#include "io.service.h"

#include "manager.h"
#include "sms.recieve.handler.h"

#include "iPhone.h"
#include "service.database.xunjian.h"

SINGLETON_STATIC_INSTANCE(Manager); // 单例模式

CMD_LISTENER_SINGLETON();

#define PROP_SUBIAO_STATUS "rw.subiao.status"

#define THIS_CMD_SOCKET_NAME "prot"
class ThisCmd : public LogCallProxyCmd
{
    public:
        ThisCmd()    : LogCallProxyCmd("cmd")
        {
            std::string cmdName = getCommand();
            std::string mCmdList[][3] = {
                {cmdName, "help",       "show this usage.\n",},
                {cmdName, "alarm_info", "get prot alarm media info.\n"},
                {cmdName, "can_filter",     "set can filter, [can idx] [can id].\n",},
                {cmdName, "config",     "show config.\n",},
                {cmdName, "fakespd",    "set fake speed for test.\n",},
                {cmdName, "fake_status","set fake io status, [enable] [value].\n"},
                //{cmdName, "imu",        "dump imu data.\n"},
                {cmdName, "log_level",  "get/set log level. '0 off 1 err 2 warn 3 dbg 4 info'.\n"},
                {cmdName, "prot_stat",  "get prot connection status.\n"},
                {cmdName, "qst",        "query server time, [prot idx].\n"},
                {cmdName, "register_face", "register face\n"},
                {cmdName, "register_face_v2", "register face v2\n"},
                {cmdName, "rptGnss",          "report gnss once\n"},
                {cmdName, "trigger_match_v2",  "trigger match face v2\n"},
                {cmdName, "show",       "show prot info.\n"},
                {cmdName, "trigger",    "manual trigger adas alarm, [prot idx] [evt val].\n",},
                {cmdName, "triggerDms", "manual trigger dms alarm, [prot idx] [evt val].\n",},
                {cmdName, "triggerBsd", "manual trigger bsd alarm, [prot idx].\n",},
                {cmdName, "triggerdumpper",  "full/empty lift/down cover/open\n"},
                {cmdName, "triggerEbill", "manual trigger upload ebill, [prot idx].\n",},
                {cmdName, "unregProt",  "send unreg cmd to prot server, [prot idx] [jtt808-1078].\n"},
                {cmdName, "area_info",  "get area info\n"},
                {cmdName, "waybill_info",  "get waybill info\n"},
                {cmdName, "triggerZTCalarm",  "trigger ztc alarm\n"},
                {cmdName, "triggerSpeedAlarm",  "trigger speeding alarm\n"},
                {cmdName, "triggerOverHeight",  "trigger over height alarm\n"},
                {cmdName, "triggerOverLoad",    "trigger over load alarm\n"},
                {cmdName, "showAlarm",    "show alarm info\n"},
                {cmdName, "dummy",    "dummy prot\n"},
                {cmdName, "dumpXunjianInfo", "dump xunjian alarm info\n"},
                {cmdName, "mgParking", "manager parking info\n"},
                {cmdName, "oilIO", "set voltage level of oilIO(PIN10), [high|low]\n"},
                {"", "", "",},
            };
            int i = 0;
            mUsage = "\n";

            while (mCmdList[i][0] != "") {
                mUsage += mCmdList[i][0] + " " + mCmdList[i][1] + " - " + mCmdList[i][2];
                setupCmd(THIS_CMD_SOCKET_NAME, mCmdList[i][1].c_str());
                i++;
            }
        }
        virtual ~ThisCmd() {};

        int help(SocketClient *c, int argc, char ** argv)
        {
            sendRsp(c, MMM_CMD_OK, mUsage.c_str(), false);
            return 0;
        }
        int fake_status(SocketClient *c, int argc, char ** argv)
        {
            const char * statNameTbl = "\nall acc emerg back normalLight farLight left right brake door canLoss crash roll load";
            Manager & m = Manager::getInstance();
            char * name = NULL;
            bool enable = 0;
            int value = 0;
            int ret = 0;

            if (argc < 5) {
                ret = -1;
            } else {
                name = argv[2];
                enable = atoi(argv[3]) > 0;
                value = atoi(argv[4]);
                if (!strcmp(name, "all")) {
                    m.enableFakeStat(NULL, enable);
                } else {
                    m.enableFakeStat(name, enable);
                    m.setFakeStatus(name, value);
                }
            }
            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_MISSING_PARAM, (0 == ret) ? NULL : statNameTbl, false);
            return 0;
        }
        int dumpConfig(SocketClient *c, int argc, char ** argv)
        {
            int ret = -1;
            std::string printRes = "dump config : ...";
            ret = 0;
            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, printRes.c_str(), false);
            return ret;
        }
        int rptGnss(SocketClient *c, int argc, char ** argv)
        {
            Manager & m = Manager::getInstance();
            int protIdx = 0, ret = -1;
            char prn[256] = {0};

            if (argc >= 3) {
                protIdx = atoi(argv[2]);
                snprintf(prn, sizeof(prn), "rptGnss to prot %d", protIdx);
                ret = m.rptGnss(protIdx);
            }

            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, prn, false);
            return ret;
        }
#define SHOW_EVT_NAME(algo) do {\
    std::string evtList = "\n";\
    auto it = mEvtList.find(algo);\
    if (it != mEvtList.end()) {\
        for (int32_t i = 0; i < (int32_t)(it->second.size()); i++) {\
            if (it->second[i].name && it->second[i].name_zh) {\
                char nameLmt[32];\
                snprintf(nameLmt, sizeof(nameLmt), "%-*s", 16, it->second[i].name);\
                evtList += nameLmt;\
                evtList += " : ";\
                evtList += it->second[i].name_zh;\
                evtList += "\n";\
            }\
        }\
    }\
    return sendRsp(c, MMM_CMD_ILLEGAL_PARAM, evtList.c_str(), false);\
} while (0)
        int triggerAdasAlarm(SocketClient *c, int argc, char ** argv)
        {
            Manager & m = Manager::getInstance();
            int protIdx = 0, ret = -1;
            char prn[256] = {0};

            if (argc >= 3) {
                protIdx = atoi(argv[2]);
            }

            const char * e = NULL;

            if (argc >= 4) {
                e = (const char *)(argv[3]);

            } else {
                SHOW_EVT_NAME("adas");
            }

            if (protIdx >= 0) {
                snprintf(prn, sizeof(prn), "trigger protIdx %d, e = %s", protIdx, e);
                std::string strEvt = e;
                if (strEvt == "HAW" || strEvt == "HCW" || strEvt == "HDW") {
                    ret = m.triggerHardDrivingAlarm(e, protIdx);
                } else {
                    ret = m.triggerAdasAlarm(e, protIdx);
                }
            } else {
                AlgoManager & am = AlgoManager::getInstance();
                am.triggerEvent("adas", e, 54);//ServiceHelper::getInstance().getStatus().lbs.speed);
            }

            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, prn, false);
            return ret;
        }
        int triggerBsdAlarm(SocketClient *c, int argc, char ** argv)
        {
            Manager & m = Manager::getInstance();
            int protIdx = 0, ret = -1;
            char prn[256] = {0};

            if (argc >= 3) {
                protIdx = atoi(argv[2]);
            }

            const char * e = NULL;

            if (argc >= 4) {
                e = (const char *)(argv[3]);

            } else {
                SHOW_EVT_NAME("bsd");
            }

            snprintf(prn, sizeof(prn), "trigger protIdx %d, e = %s", protIdx, e);

            if (e) {
                ret = m.triggerBsdAlarm(e, protIdx);
            }

            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, prn, false);
            return ret;
        }
        int triggerDmsAlarm(SocketClient *c, int argc, char ** argv)
        {
            int protIdx = 0, ret = -1;
            char prn[256] = {0};

            if (argc >= 3) {
                protIdx = atoi(argv[2]);
            }

            const char * evt_name = NULL;

            if (argc >= 4) {
                evt_name = (const char *)(argv[3]);

            } else {
                SHOW_EVT_NAME("dms");
            }

            snprintf(prn, sizeof(prn), "trigger protIdx %d, e = %s", protIdx, evt_name);

            if (evt_name) {
                Manager &m = Manager::getInstance();
                if (protIdx >= 0) {
                    ret = m.triggerDmsAlarm(evt_name, protIdx);
                } else {
                    AlgoManager & am = AlgoManager::getInstance();
                    am.triggerEvent("dms", evt_name, ServiceHelper::getInstance().getStatus().lbs.speed);
                }
            }

            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, prn, false);
            return ret;
        }

        int triggerSpeedingAlarm(SocketClient *c, int argc, char ** argv)
        {
            int protIdx = 0, ret = -1;
            char prn[256] = {0};

            if (argc >= 3) {
                protIdx = atoi(argv[2]);
            }

            int alarmType = 0;
            if (argc >= 4) {
                alarmType = atoi(argv[3]);

            } else {
                SHOW_EVT_NAME("speeding");
            }
            
            int alarmStatus = 0;
            if (argc >= 5) {
                alarmStatus = atoi(argv[4]);

            } else {
                SHOW_EVT_NAME("speeding");
            }

            int speedLimit = 0;
            if (argc >= 6) {
                speedLimit = atoi(argv[5]);

            } else {
                SHOW_EVT_NAME("speeding");
            }

            SpeedingAlarmParam param;
            param.type.value = 0;
            if (road_net_speed_type_basic == alarmType) {
                param.type.speedingLimits = 1;    
            } else if (road_net_speed_type_actual == alarmType) {
                param.type.roadSpeedingLimits = 1;
            }
            param.state = alarmStatus;
            param.spdLimit = speedLimit;
            param.protBitTbl = (1<<protIdx);

            Manager &m = Manager::getInstance();
            m.triggerSpeedingAlarm(param);

            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, prn, false);
            return ret;
        }

        int triggerOverHeightAlarm(SocketClient *c, int argc, char ** argv)
        {
            int protIdx = 0, ret = -1;
            char prn[256] = {0};

            if (argc >= 3) {
                protIdx = atoi(argv[2]);
            }

            int alarmStatus = 0;
            if (argc >= 4) {
                alarmStatus = atoi(argv[3]);

            } else {
                SHOW_EVT_NAME("overHeight");
            }
            logd("triggerOverHeightAlarm alarmStatus:%d!\n", alarmStatus);
            my::ushort limits = 40;
            if (argc >= 5) {
                limits = atoi(argv[4]);

            } else {
                SHOW_EVT_NAME("overHeight");
            }
            Manager &m = Manager::getInstance();
            ret = m.triggerOverHeightAlarm(alarmStatus, protIdx, limits);

            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, prn, false);
            return ret;
        }

        int triggerOverLoadAlarm(SocketClient *c, int argc, char ** argv) {
            int protIdx = 0, ret = -1;
            char prn[256] = {0};

            if (argc >= 3) {
                protIdx = atoi(argv[2]);
            }

            int type = 1;
            if (argc >= 4) {
                type = atoi(argv[3]);

            } else {
                SHOW_EVT_NAME("overHeight");
            }
            logd("triggerOverLoadAlarm type:%d!\n", type);
            
            int alarmStatus = 1;
            if (argc >= 5) {
                alarmStatus = atoi(argv[4]);

            }

            my::ushort loadVal = 450;
            if (argc >= 6) {
                loadVal = atoi(argv[5]);

            }
            
            my::ushort limits = 600;
            if (argc >= 7) {
                limits = atoi(argv[6]);

            } else {
                SHOW_EVT_NAME("overLoad");
            }
            Manager &m = Manager::getInstance();
            ret = m.triggerOverLoadAlarm(type, alarmStatus, protIdx, loadVal, limits);

            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, prn, false);
            return ret;
        }

        int triggerFaceReg(SocketClient *c, int argc, char ** argv)
        {
            Manager & m = Manager::getInstance();
            m.mTrigDrvReg = true;
            char value[PROP_VALUE_MAX];
            memset(value, 0, sizeof(value));
            snprintf(value, sizeof(value), "%d", 2);

            sendRsp(c, MMM_CMD_OK, "OK", false);
            return 0;
        }

        int triggerFaceReg_v2(SocketClient *c, int argc, char ** argv)
        {
            if (argc < 3) {
                logd("lost param,sample:ndc prot cmd register_face_v2 update/insert!\n");
                sendRsp(c, MMM_CMD_OPERATION_FAILED, "OK", false);
                return -1;
            }
                       
            AlgoManager & am = AlgoManager::getInstance();
            int ret = -1;
            std::string action;
            if (argc >= 3) {
               action = argv[2]; 
            } 
  
            std::string imgId;
            Current st = ServiceHelper::getInstance().getStatus();
            if (st.ic.state == 1 && st.ic.ic_status == 0) {
                imgId += st.ic.id;
                imgId += "_local";
            } else {
                if (argc < 4) {
                    logd("ic card not insert,do not allow to register driver!\n");
                    sendRsp(c, MMM_CMD_OPERATION_FAILED, "OK", false); 
                }
            }            
            if (argc >= 4){
                imgId = argv[3]; 
            }

            std::string mode = "camera";
            if (argc >= 5){
                mode = argv[4]; 
            }

            std::string param;
            if (argc >= 6){
                param = argv[5]; 
            }

            if (!param.length()) {
                ret = am.faceRegister_V2(imgId, action,  mode);
            } else {
                ret = am.faceRegister_V2(imgId, action, mode, param);
            }
            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, "OK", false);
            return ret;
        }
        
        int triggerFaceMatch_v2(SocketClient *c, int argc, char ** argv)
        {
            AlgoManager & am = AlgoManager::getInstance();
            int ret = am.faceMatch_V2();
            logd("come in triggerFaceMatch_v2!\n");
            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, "OK", false);
            return ret;
        }
        
        int fakeSpeed(SocketClient *c, int argc, char ** argv)
        {
            int ret = 0;
            char * msg = NULL;

            if (argc < 3) {
                ret = -1;

            } else {
                msg = argv[2];
                int spd = atoi(argv[2]);

                if (spd >= 0) {
                    __system_property_set(PROP_RW_MINIEYE_FAKESPEED, argv[2]);
                }
            }

            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, msg, false);
            return 0;
        }
        int prot_stat(SocketClient *c, int argc, char ** argv)
        {
            Manager & m = Manager::getInstance();
            int ret = 0;
            int idx = 0, sidx = 0;

            if (argc < 3) {
                ret = -1;

            } else {
                if (argc > 2) {
                    idx = atoi(argv[2]);
                }

                if (argc > 3) {
                    sidx = atoi(argv[3]);
                }

                if (m.getProtStat(idx, sidx)) {
                    ret = 0;

                } else {
                    ret = -1;
                }

            }

            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, NULL, false);
            return 0;
        }
        int alarm_info(SocketClient *c, int argc, char ** argv)
        {
            Manager & m = Manager::getInstance();
            std::string rs;
            int ret = 0;
            int idx = 0;

            if (argc < 3) {
                ret = -1;

            } else {
                idx = atoi(argv[2]);
                rs = m.getProtAlarmInfo(idx);
            }

            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, rs.c_str(), false);
            return 0;
        }
#if 0
        int imu(SocketClient *c, int argc, char ** argv)
        {
            std::string rs;
            int ret = 0;
            Manager & m = Manager::getInstance();
            std :: vector < ImuDataT > imuList;
            m.getImuData(imuList);
            rs = "\n imu = ";
            rs += std::to_string(imuList.size()) + "\n";
            for (auto i : imuList) {
                rs += i.toJson();
            }
            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_MISSING_PARAM, rs.c_str(), false);
            return 0;
        }
#endif
        int log_level(SocketClient *c, int argc, char ** argv)
        {
            std::string rs;
            int ret = 0, level = -1;
            if (argc > 2) {
                level = atoi(argv[2]);
            }
            level = my::log::log_level(level);
            rs = "\n current level = ";
            rs += ::to_string(level);
            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_MISSING_PARAM, rs.c_str(), false);
            return 0;
        }

        int set_can_filter(SocketClient *c,int argc, char ** argv)
        {
            Manager & m = Manager::getInstance();
            int ret = 0;
            std::string rs = "Ok";

            if (argc < 4) {
                ret = -1;
                rs = "failed";
            }

            McuMsgCanFilterT buf;
            buf.enable = 1;
            buf.canIdx = atoi(argv[2]);
            buf.useListMode = true;
            buf.canIds[0] = atoi(argv[3]);
            logd("canID %d, frameID %x", buf.canIdx, buf.canIds[0]);
            m.sendCanFilterMsg((uint8_t *)&buf, sizeof(McuMsgCanFilterT));
            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_MISSING_PARAM, rs.c_str(), false);
            return 0;
        }
        int show(SocketClient *c, int argc, char ** argv)
        {
            int ret = 0;
            int idx = 0, sidx = 0;
            std::string msg = "\n";

            if (argc < 3) {
                Manager & m = Manager::getInstance();
                ComController::Ptr cc = m.com_controller;
                for (int i = 0; i < MAX_CLIENTS; i++) {
                    ComService::Ptr cs = (*cc)[i];
                    if (cs) {
                        msg += "\n";
                        MSG_APPEND_KEY_VAL(msg, "index", "%d", i);
                        MSG_APPEND_KEY_VAL(msg, "protocol", "%s", cs->si[0].prot.c_str());
                        MSG_APPEND_KEY_VAL(msg, "subtype",  "%s", cs->si[0].prot_subtype.c_str());
                        MSG_APPEND_KEY_VAL(msg, "version",  "%d", cs->protVersion());
                        MSG_APPEND_KEY_VAL(msg, "sim",      "%s", cs->si[0].sim.c_str());
                        MSG_APPEND_KEY_VAL(msg, "tag",      "%s", cs->si[0].tag.c_str());
                        MSG_APPEND_KEY_VAL(msg, "id",       "%s", cs->si[0].id.c_str());
                        MSG_APPEND_KEY_VAL(msg, "auth",     "%s", cs->si[0].auth.c_str());
                        if (cs->si[0].ip[0].length()) {
                            MSG_APPEND_KEY_VAL(msg, "server", "%s:%d", cs->si[0].ip[0].c_str(), cs->si[0].port[0]);
                        }
                        if (cs->si[0].ip[1].length()) {
                            MSG_APPEND_KEY_VAL(msg, "server", "%s:%d", cs->si[0].ip[1].c_str(), cs->si[0].port[1]);
                        }
                        if (cs->si[0].ip[2].length()) {
                            MSG_APPEND_KEY_VAL(msg, "server", "%s:%d", cs->si[0].ip[2].c_str(), cs->si[0].port[2]);
                        }
                        for (auto it : cs->si[0].disAlgoAttUpload) {
                            std::string evtList;
                            for (auto ie : it.second) {
                                if (evtList.length()) {
                                    evtList += ", ";
                                }
                                logd("%s", ie.c_str());
                                evtList += ie;
                            }
                            if (evtList.length()) {
                                MSG_APPEND_KEY_VAL(msg, it.first.c_str(), "disableAtt > %s", evtList.c_str());
                            }
                        }
                    }
                }
            } else {
                if (argc > 2) {
                    idx = atoi(argv[2]);
                }

                if (argc > 3) {
                    sidx = atoi(argv[3]);
                }
            }

            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, msg.c_str(), false);
            return 0;
        }

        int triggerDumpperAlarm(SocketClient *c, int argc, char ** argv)
        {
            Manager & m = Manager::getInstance();
            int protIdx = 0, ret = -1;
            char prn[256] = {0};

            if (argc >= 3) {
                protIdx = atoi(argv[2]);
            }

            const char * e = NULL;

            if (argc >= 4) {
                e = (const char *)(argv[3]);

            } else {
                SHOW_EVT_NAME("dumper");
            }

            snprintf(prn, sizeof(prn), "trigger protIdx %d, e = %s", protIdx, e);

            if (e) {
                ret = m.triggerDumpperAlarm(e, protIdx);
            }

            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, prn, false);
            return ret;

        }

        int unregProt(SocketClient *c, int argc, char ** argv)
        {
            Manager & m = Manager::getInstance();
            int protIdx = 0, ret = -1;
            const char * protName = "jtt808-1078";
            char prn[256] = {0};

            if (argc >= 3) {
                protIdx = atoi(argv[2]);
            }
            if (argc >= 4) {
                protName = argv[3];
            }
            snprintf(prn, sizeof(prn), "unreg protIdx %d %s", protIdx, protName);
            ret = m.protDeregister(protIdx, protName);
            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, prn, false);
            return ret;
        }

        int getAreaInfo(SocketClient *c, int argc, char ** argv)
        {
            OutputService & os = OutputService::getInstance();
            int ret = os.getAreaInfo();
            
            sendRsp(c,  (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, "OK", true);
            return 0;
        }
        
        int getWayBillInfo(SocketClient *c, int argc, char ** argv)
        {
            OutputService & os = OutputService::getInstance();
            int ret = os.getWayBillInfo();
            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, "OK", true);
            return 0;
        }

        int queryServerTime(SocketClient *c, int argc, char ** argv)
        {
            Manager & m = Manager::getInstance();
            int protIdx = 0, ret = -1;
            char prn[256] = {0};

            if (argc >= 3) {
                protIdx = atoi(argv[2]);
            }

            snprintf(prn, sizeof(prn), "queryServerTime protIdx %d", protIdx);
            ret = m.queryServerTime(protIdx);
            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, prn, false);
            return ret;
        }

        int triggerEbill(SocketClient *c, int argc, char ** argv)
        {
            Manager & m = Manager::getInstance();
            int protIdx = 0, ret = -1;
            char prn[256] = {0};

            if (argc >= 3) {
                protIdx = atoi(argv[2]);
            }

            snprintf(prn, sizeof(prn), "triggerEbill protIdx %d", protIdx);
            ret = m.triggerEbill(protIdx);
            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, prn, false);
            return ret;
        }

        int triggerZTCAlarm(SocketClient *c, int argc, char ** argv)
        {
            Manager & m = Manager::getInstance();
            int protIdx = 0, ret = -1;
            char prn[256] = {0};

            if (argc >= 3) {
                protIdx = atoi(argv[2]);
            }

            const char * e = NULL;

            if (argc >= 4) {
                e = (const char *)(argv[3]);

            } else {
                SHOW_EVT_NAME("adas");
            }

            snprintf(prn, sizeof(prn), "trigger protIdx %d, e = %s", protIdx, e);

            if (e) {
                ret = m.triggerZTCAlarm(e, protIdx);
            }

            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, prn, false);

            return ret;
        }

        int triggerTurnOver(SocketClient *c, int argc, char ** argv){
            Manager & m = Manager::getInstance();
            std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
            amsg->setWhat(EVT_TYPE_PROT_CAR_ROLL_OVER);/*faceFeatureGet*/
            amsg->setInt32("angle", 30);
            m.postMsg(amsg);  

            int ret = 0;
            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, "triggerTurnOver", false);
            return ret;
        }

        int showAlarm(SocketClient *c, int argc, char ** argv)
        {
            Manager &m = Manager::getInstance();

            int ret = 0;
            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, m.mRecAlarmManager->dumpAlarmInfo().c_str(), false);
            return true;
        }
        int dummy(SocketClient *c, int argc, char ** argv)
        {
            Manager & m = Manager::getInstance();
            int ret = 0;
            int idx = 0, enable = 0;

            if (argc < 3) {
                ret = -1;

            } else {
                if (argc > 2) {
                    idx = atoi(argv[2]);
                }

                if (argc > 3) {
                    enable = atoi(argv[3]);
                }

                if (m.dummyProt(idx, enable)) {
                    ret = 0;

                } else {
                    ret = -1;
                }

            }

            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, NULL, false);
            return 0;
        }
        int dumpXunjianInfo(SocketClient *c, int argc, char ** argv)
        {
            int ret = -1;
            char prn[256] = {0};
            if (argc >= 5) {
                my::uint64 startMs = my::timestamp::seconds_from_19700101(argv[2]) * 1000;
                my::uint64 endMs = my::timestamp::seconds_from_19700101(argv[3]) * 1000;
                loge("startMs:%" FMT_LLD ", endMs:%" FMT_LLD "!\n", startMs, endMs);
                if (startMs != 0 && endMs != 0) {
                    my::string outFile = argv[4];
                    XinJianDBHelper &dh = XinJianDBHelper::getInstance();
                    ret = dh.dumpInfo(startMs, endMs, outFile);
                }                
            }

            if (ret != 0) {
                snprintf(prn, sizeof(prn), "dumpXunjianInfo = %d,cmd sample:prot.cmd.dumpXunjianInfo 230131104100 " \
                                            "230131124100 /tmp/202301311041000_230131124100.csv", ret);
            }
            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, prn, false);
            return ret;
        }      
        
        int mgParking(SocketClient *c, int argc, char ** argv)
        {
            int ret = -1;
            my::string respMsg;

            if (argc == 3) {
                my::string parkingFile = argv[2];
                XinJianDBHelper &dh = XinJianDBHelper::getInstance();
                ret = dh.loadParkingFile(parkingFile);
                respMsg = "add parking";
            } else if (argc == 4) {
                if (!memcmp(argv[2], "del", 3)) {
                    my::uint id = atoi(argv[3]);
                    XinJianDBHelper &dh = XinJianDBHelper::getInstance();
                    ret = dh.delParking(id);
                    respMsg = "delet parking";
                } else {
                    respMsg.appendf("mgParking input argv[2]=%s", argv[2]);
                }
            } else {
                respMsg.appendf("mgParking input argc=%d", argc);
            }

            if (ret) {
                my::string errorMsg;
                errorMsg.appendf("ret = %d, cmd sample:prot.cmd.mgParking /tmp/parking.csv or prot.cmd.mgParking del 1", ret);
                respMsg += errorMsg;
            }
            sendRsp(c, (0 == ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, respMsg.c_str(), false);
            return ret;
        }

        int oilIO(SocketClient *c, int argc, char ** argv)
        {
            int ret = -1;
            my::string respMsg;

            if (argc == 3) {
                my::string level = argv[2];
                if (level == "high" || level == "low") {
                    McuMsgHandler &h = McuMsgHandler::getInstance();
                    ret = h.setOilIO(level == "high");
                    respMsg.assignf("set oilIO %s, ret = %d", level.c_str(), ret);
                }
            }

            if (ret == -1) {
                my::string errorMsg;
                errorMsg.appendf("ret = %d, cmd sample:prot.cmd.oilIO high or prot.cmd.oilIO low", ret);
                respMsg += errorMsg;
            }
            sendRsp(c, (-1 != ret) ? MMM_CMD_OK : MMM_CMD_OPERATION_FAILED, respMsg.c_str(), false);
            return ret;
        }
        
        int onRunCommand(SocketClient *c, int argc, char ** argv)
        {
            std::string usage = "\n";
            if ((argc < 2) || !strcmp(argv[1], "help")) {
                return help(c, argc, argv);
            } else if (!strcmp(argv[1], "config")) {
                return dumpConfig(c, argc, argv);
            } else if (!strcmp(argv[1], "trigger")) {
                return triggerAdasAlarm(c, argc, argv);
            } else if (!strcmp(argv[1], "triggerDms")) {
                return triggerDmsAlarm(c, argc, argv);
            } else if (!strcmp(argv[1], "triggerBsd")) {
                return triggerBsdAlarm(c, argc, argv);
            } else if (!strcmp(argv[1], "triggerSpeedAlarm")) {
                return triggerSpeedingAlarm(c, argc, argv);
            } else if (!strcmp(argv[1], "triggerOverHeight")) {
                return triggerOverHeightAlarm(c, argc, argv);
            } else if (!strcmp(argv[1], "triggerOverLoad")) {
                return triggerOverLoadAlarm(c, argc, argv);
            } else if (!strcmp(argv[1], "triggerdumpper")) {
                return triggerDumpperAlarm(c, argc, argv);
            } else if (!strcmp(argv[1], "triggerEbill")) {
                return triggerEbill(c, argc, argv);
            } else if (!strcmp(argv[1], "fakespd")) {
                return fakeSpeed(c, argc, argv);
            } else if (!strcmp(argv[1], "prot_stat")) {
                return prot_stat(c, argc, argv);
            } else if (!strcmp(argv[1], "alarm_info")) {
                return alarm_info(c, argc, argv);
            } else if (!strcmp(argv[1], "fake_status")) {
                return fake_status(c, argc, argv);
            } else if (!strcmp(argv[1], "can_filter")) {
                return set_can_filter(c, argc, argv);
            //} else if (!strcmp(argv[1], "imu")) {
            //    return imu(c, argc, argv);
            } else if (!strcmp(argv[1], "log_level")) {
                return log_level(c, argc, argv);
            } else if (!strcmp(argv[1], "show")) {
                return show(c, argc, argv);
            } else if (!strcmp(argv[1], "register_face")) {
                triggerFaceReg(c, argc, argv);
            } else if (!strcmp(argv[1], "register_face_v2")) {
                return triggerFaceReg_v2(c, argc, argv);
            } else if (!strcmp(argv[1], "rptGnss")) {
                return rptGnss(c, argc, argv);
            } else if (!strcmp(argv[1], "trigger_match_v2")) {
                return triggerFaceMatch_v2(c, argc, argv);
            } else if (!strcmp(argv[1], "unregProt")) {
                return unregProt(c, argc, argv);
            } else if (!strcmp(argv[1], "area_info")) {
                return getAreaInfo(c, argc, argv);
            } else if (!strcmp(argv[1], "waybill_info")) {
                return getWayBillInfo(c, argc, argv);
            } else if (!strcmp(argv[1], "qst")) {
                return queryServerTime(c, argc, argv);
            } else if (!strcmp(argv[1], "triggerZTCalarm")) {
                return triggerZTCAlarm(c, argc, argv);
            } else if (!strcmp(argv[1], "triggerTurnOver")) {
                return triggerTurnOver(c, argc, argv);
            } else if (!strcmp(argv[1], "showAlarm")) {
                return showAlarm(c, argc, argv);
            } else if (!strcmp(argv[1], "dummy")) {
                return dummy(c, argc, argv);
            } else if (!strcmp(argv[1], "dumpXunjianInfo")) {
                return dumpXunjianInfo(c, argc, argv);
            } else if (!strcmp(argv[1], "mgParking")) {
                return mgParking(c, argc, argv);
            } else if (!strcmp(argv[1], "oilIO")) {
                return oilIO(c, argc, argv);
            }

            return sendRsp(c, MMM_CMD_INVALID_CMD, NULL, false);
        }
    private:
        std::string mUsage;

};

Manager::Manager()
{
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();

    srand((unsigned int)time(0));

    curStatBgnTmMs = my::timestamp::now();
    spdingTipCnt = 0, wSpdingTipCnt = 0, stat = 0;
    spdingTipTmMs = wSpdingTipTmMs = 0;

    mConfigReceiver.startRecevier();

    mRecAlarmManager = &RecAlarmManager::getInstance();

    __system_property_set(PROP_RW_MINIEYE_WAKEUP_STATUS, "false");

    memset(&mLoc, 0, sizeof(mLoc));
    mLastIOStat.value = 0;
    mEnableFake.value = 0;
    mFakeStatus.value = 0;
    mTimeTSCan0_0110 = my::timestamp::now();
    mTimeTSCan0_0111 = my::timestamp::now();

    mTimeTSCan1_0110 = my::timestamp::now();
    mTimeTSCan1_0111 = my::timestamp::now();

    mTimeTSCan0 = my::timestamp::now();
    mTimeTSCan1 = my::timestamp::now();

    char propValue[PROP_VALUE_MAX] = {0};
    int32_t len = __system_property_get(PROP_PERSIST_ALARM_INTERVAL, propValue);
    if (len > 0) {
        mAlarmIntervalMs = atoi(propValue);
    } else {
        mAlarmIntervalMs = 5000;
    }
    
}

Manager::~Manager()
{
    stop();
}

void Manager::findLastFile(const my::string &filePath, std::pair<uint64_t, uint32_t> &fileInfo)
{
    my::file f;
    int64_t size = f.open(filePath.c_str(), "rb");
    if (size > 0) {
        char tmp[2048] = {0};
        int rdsize = f.gets(tmp, sizeof(tmp));
        if (size != rdsize) {
            loge("read %s error! %s", strerror(errno));
        }

        if (rdsize > 0) {
            if (rdsize < sizeof(tmp)) {
                tmp[rdsize] = '\n';
            }
            std::string data = tmp;
            int pos = data.find(" : ");
            my::string msgTm;
            int32_t idx = 0;
            sscanf(filePath.c_str(), "/data/tts//%d.gbk", &idx);
            msgTm.append(tmp + 1, pos - 1);
            int year = 0, month = 0, day = 0, hour = 0, minute = 0, second = 0;
            int num = sscanf(msgTm.c_str(), "%04d%02d%02d %02d:%02d:%02d", &year, &month, &day, &hour, &minute, &second);
            if (num != 6) {
                return;
            }
            tm st;
            st.tm_year = year - 1900;
            st.tm_mon = month - 1;
            st.tm_mday = day;
            st.tm_hour = hour;
            st.tm_min = minute;
            st.tm_sec = second;
            st.tm_isdst = -1;
            uint64_t tmS = mktime(&st);
            loge("%s %d time:%s,%lld!\n", filePath.c_str(), idx, msgTm.c_str(), tmS);
            if (tmS > fileInfo.first) {
               fileInfo = {tmS, idx}; 
            }
        }
    }
    
    if (size >= 0) {
        f.close();
    }
}

void Manager::initTTsIdx()
{
    char propValue[PROP_VALUE_MAX] = {0};        
    if (__system_property_get(PROP_RW_MINIEYE_808TTS_IDX, propValue) > 0) {
        return;
    }

    /* 遍历目录初始化idx */
    std::vector<my::string> files;
    my::file::ls("/data/tts/", ".gbk", 1, files);
    std::pair<uint64_t, uint32_t> fileInfo = {0, 0};

    for(auto item : files) {
        loge("%s", item.c_str());
        findLastFile(item, fileInfo);
    }

    loge("last file time:%ld, idx:%d", fileInfo.first, fileInfo.second);
    if (fileInfo.second != 0) {
        char val[10] = {0};
        snprintf(val, 10, "%d", fileInfo.second);
        __system_property_set(PROP_RW_MINIEYE_808TTS_IDX, val);
    }
 }

void Manager::init()
{
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();
    com_controller.reset(new ComController(config));
    state_controller.reset(new StateController(com_controller));
    is.addMsgHdlr("stateCtl", state_controller.get());
    
    curStatBgnTmMs = my::timestamp::now();

    CmdListener::getInstance().init(THIS_CMD_SOCKET_NAME);
    CmdListener::getInstance().regCmd(new ThisCmd());
    CmdListener::getInstance().startCmdListener();

    mConfigReceiver.startRecevier();

    mRecAlarmManager = &RecAlarmManager::getInstance();

    __system_property_set(PROP_RW_MINIEYE_WAKEUP_STATUS, "false");

}

// 停止app
void Manager::stop_app()
{
    com_controller->stop();
    state_controller->stop();

    AlgoManager & algoMngr = AlgoManager::getInstance();
    algoMngr.delObserver("Manager");
    algoMngr.stop();
}


// 启动app
int Manager::start_app()
{
    // 先停止
    stop_app();

    AlgoManager & algoMngr = AlgoManager::getInstance();
    algoMngr.addObserver("Manager", this);


    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();
    bool ivsFlg = false;
    std::vector<std::string> funcList;
    for (int ch = 0; ch < (config.sys.cameras + config.sys.ipcs); ch++) {
        if (config.sys.ch[ch].ai.enable) {
            if (!strcmp(config.sys.ch[ch].ai.func, "ivs_back") || !strcmp(config.sys.ch[ch].ai.func, "ivs_front")) {
                ivsFlg = true;
            } else {
                funcList.push_back(std::string(config.sys.ch[ch].ai.func));
            }
        }    
    }
    if (ivsFlg) {
        funcList.push_back(std::string("ivs"));
    }

    funcList.push_back(std::string("faceid_v2"));

    int ret = state_controller->start();
    LOG_RETURN_IF(ret != 0, ret, loge, "[Manager::start_app] Failed to start state controller, ret=[%d]", ret);

    ExpandReceiver::getInstance().start();

    ret = com_controller->start();

    algoMngr.start(funcList);
    return ret;
}


void Manager::stop()
{
    // 停止协议服务
    stop_app();
}

int Manager::start()
{
    init();
    // 先安全停止
    stop();
    // 加载配置

    if (com_controller.get()) {
        com_controller->init();
    } 

    /* 初始化PROP_RW_MINIEYE_808TTS_IDX */
    initTTsIdx();
    my::thread::start();
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();

    for (int i = 0; i < MAX_CLIENTS; i++) {
        if (config.sys.net[i].prot_subtype == "sichuan") {
            /* 启动车辆状态信息存储 */
            CarStatusCache::getInstance().init(1000, 20000);    
            
            if (!access("/data/app/road_net", R_OK)) {
                /* 启动路网地图 */
                RoadnetManager::getInstance().init();
            }

            /* 启动aps */
            if (!access("/data/face_id_v2", R_OK)) {
                //ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
                __system_property_set("ctl.start", "aps");
            }

            /* 设置can参数确保can接口载重传感器使用 */
            if (!access("/data/load_transducer", R_OK)) {
                McuMsgCanFilterT buf;
                memset(&buf, 0, sizeof(buf));
                buf.enable = 1;
                buf.useListMode = true;
                buf.canIdx = config.sys.can1ID.canChannel;
                buf.canIds[0] = config.sys.can1ID.frameID;
                buf.canIds[1] = config.sys.can1ID.frameIDSecond;
                buf.canIds[2] = config.sys.can1ID.frameIDThird;
                sendCanFilterMsg((uint8_t *)&buf, sizeof(McuMsgCanFilterT));
            }
            break;
        }    
    }
    
    // 开启协议app
    return start_app();
}

int Manager::restart()
{
    init();
    // 先安全停止
    stop();
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();

    logi("[Manager::restart] idvr.core: bin=[%s], etc=[%s], data=[%s].",
         config.path.bin.c_str(), config.path.etc.c_str(), config.path.data.c_str());

    // 开启协议app
    return start_app();
}

bool Manager::smsParse()
{
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();

    char telNum[PROP_VALUE_MAX] = {0};        
    __system_property_get(PROP_PERSIST_MINIEYE_SMS_SUM, telNum);
    if (!telNum[0]) {
        return false;
    }
        
    SmsRecieveHandler &smsHandler = SmsRecieveHandler::getInstance();
    my::string phoneNum;
    my::string smsMsg;    
    while (smsHandler.getOneSms(phoneNum, smsMsg)) {
        if (!strstr(telNum, phoneNum.c_str())) {
            /* 无效短信 */
            continue;
        } else {
            if (strstr(smsMsg.c_str(), "heartbeat")) {
                JttMsg msg(config.sys.sim, 0x0002, false);
                my::constr strMsg((char *)&msg.header, sizeof(msg.header));
                my::string hexStr = my::hex(strMsg, false);
#if 0
/* 暂无发短信功能 */
                string cmd = "send_sms.sh ";
                cmd += telNum;
                cmd += " 'hb:7E ";
                cmd += hexStr + " 7E'";
                system(cmd.c_str());
              
                PROT_MLOG("send %s to %s\n", cmd.c_str(), telNum);
#endif                  
            } else if (strstr(smsMsg.c_str(), "location")) {
                X0200 req;
                ServiceHelper &svHelper = ServiceHelper::getInstance();
                Current st = svHelper.getStatus();
                req.lbi = svHelper.getLocationBaseInfo(st);
                my::string t;
                bcd2numstr(t, req.lbi.time, sizeof(req.lbi.time));
                JttMsg msg = req.encode(config.sys.sim, false);
                my::constr strHd((char *)&msg.header, sizeof(msg.header));
                my::constr strMsg(msg.body);
                my::string hexStr = my::hex(strHd + strMsg, false);
#if 0
/* 暂无发短信功能 */

                string cmd = "send_sms.sh ";
                cmd += telNum;
                cmd += " 'loc:7E ";
                cmd += hexStr + " 7E'";
                system(cmd.c_str());
                PROT_MLOG("send %s to %s\n", cmd.c_str(), telNum);
#endif                
            }else if (strstr(smsMsg.c_str(), "[wakeup]")) {
                std::string wakeupTm(smsMsg.c_str(), smsMsg.length());
                std::size_t pos = wakeupTm.find_last_of("[");
                if (pos > 0) {
                    std::string tmStr = wakeupTm.substr(pos + 1, wakeupTm.length() - 1);
                    loge("wakeup sms:%s,wakeup duration tm:%s!\n", smsMsg.c_str(), tmStr.c_str());
                    Wakeup &wakeup = Wakeup::getInstance();
                    my::uint tmDurationS = (my::uint)atoi(tmStr.c_str());
                    wakeup.manulWakeupChk(tmDurationS);
                }
            }
        }
    }
    return true;
}

//speeding
bool Manager::spdingChk()
{
    OutputService & os = OutputService::getInstance();
    InputService & is = InputService::getInstance();
    Current * current = is.getCurrent();
    Config & config = is.getConfig();

    double willSpdingThres = (double)is.getSpdingAlarm();
    double spdingThres = (double)is.getSpdingLimit();
    bool bRpt = false;

    //Current::AlarmBits * pw = (Current::AlarmBits*)&current->alarm;
    double curSpeed = current->getSpeed();

    //logd("cur %f, [ %f, %f ]", curSpeed, willSpdingThres, spdingThres);
    if (willSpdingThres > 0) {
        if (curSpeed < willSpdingThres) {
            if (stat > 0) {
                stat = 0;
            } else {
                stat = -1;
            }
        } else if ((willSpdingThres <= curSpeed) &&
                   (curSpeed < spdingThres)) {
            stat = 1;

        } else {
            stat = 2;
        }

        if (stat != lastStat) {
            curStatBgnTmMs = my::timestamp::now();
            //logd("spding-stat %d 2 %d, %f", lastStat, stat, curStatBgnTmMs.elapsed());
            lastStat = stat;
        }

#if 0
        logd("cur stat %d, (curStatBgnTmMs elapsed %f), (%f, %f), pw->speeding %d, pw->speedingAlarm %d, curSpeed %.1f",
             stat, curStatBgnTmMs.elapsed(),
             willSpdingThres, spdingThres,
             pw->speeding, pw->speedingAlarm, curSpeed);
#endif

        switch (stat) {
            case -1: {
                    if (current->getAlarmOverspeed() || current->getAlarmBeforeOverspeed()) {
                        current->setAlarmOverspeed(false);
                        current->setAlarmBeforeOverspeed(false);
                        //pw->speeding = pw->speedingAlarm = 0;
                        bRpt = true;
                    }
                    spdingTipTmMs = wSpdingTipTmMs = 0;

                    break;
                }
            case 0: {
                    stat = -1;
                    ComController::Ptr cc = com_controller;
                    for (int i = 0; i < MAX_CLIENTS; i++) {
                        ComService::Ptr cs = (*cc)[i];
                        if (cs) {
                            if ((cs->si[0].prot == "jtt808-1078") && (cs->si[0].prot_subtype == "chongqing")) {
                                os.ttsGBK("您的行驶速度已恢复正常", 10);
                                break;
                            }
                        }
                    }
                    break;
                }
            case 1: {
                    if (curStatBgnTmMs.elapsed() >= config.sys.warn.overspeed.alarm_spch_keep * 1000) {

                        if (current->getAlarmOverspeed()) {
                            current->setAlarmOverspeed(false);
                            bRpt = true;
                        }

                        if (!current->getAlarmBeforeOverspeed()) {
                            current->setAlarmBeforeOverspeed(true);
                            //pw->speedingAlarm = 1;
                            spdingTipCnt = 0;
                            bRpt = true;
                        }
                    }

                    spdingTipTmMs = 0;
                    if ((!(wSpdingTipTmMs - my::timestamp()) || (wSpdingTipTmMs.elapsed() >= (config.sys.warn.overspeed.alarm_spch_gap * 60 * 1000)))) {
                        logd("1elapsed %fs, wSpdingTipCnt %d", wSpdingTipTmMs.elapsed(), wSpdingTipCnt);
                        wSpdingTipCnt++;

                        if ((wSpdingTipCnt >= config.sys.warn.overspeed.alarm_spch_tms) &&
                            (255 != config.sys.warn.overspeed.alarm_spch_tms)) {
                            wSpdingTipTmMs = my::timestamp::now();
                            wSpdingTipCnt = 0;
                        }

                        if (config.sys.warn.overspeed.alarm) {
                            os.ttsGBKQueue(config.sys.warn.overspeed.alarm_spch);
                        }

                    } else {
                        logd("elapsed %dms, alarm_spch_gap %d, wSpdingTipCnt %d, alarm_spch_tms %d\n",
                             (uint32_t)wSpdingTipTmMs.elapsed(), config.sys.warn.overspeed.alarm_spch_gap,
                             wSpdingTipCnt, config.sys.warn.overspeed.alarm_spch_tms);
                    }

                    break;
                }

            case 2: {
                    if (curStatBgnTmMs.elapsed() >= config.sys.warn.overspeed.spdKeepTm * 1000) {

                        if (!current->getAlarmOverspeed()) {
                            current->setAlarmBeforeOverspeed(false);
                            current->setAlarmOverspeed(true);
                            //pw->speedingAlarm = 0;
                            //pw->speeding = 1;
                            wSpdingTipCnt = 0;
                            bRpt = true;

                            char temp[10] = {0};
                            static int spding_alarm_count = 0;
                            spding_alarm_count++;
                            sprintf(temp, "%d", spding_alarm_count);
                            __system_property_set(PROP_RW_MINIEYE_SPDING_COUNT, temp);
                        }
                    }

                    wSpdingTipTmMs = 0;
                    if ((!(spdingTipTmMs - my::timestamp()) || (spdingTipTmMs.elapsed() >= (config.sys.warn.overspeed.warn_spch_gap * 60 * 1000)))) {
                        logd("2elapsed %fs, spdingTipCnt %d", spdingTipTmMs.elapsed(), spdingTipCnt);
                        spdingTipCnt++;

                        if ((spdingTipCnt >= config.sys.warn.overspeed.warn_spch_tms) &&
                            (255 != config.sys.warn.overspeed.warn_spch_tms)) {
                            spdingTipTmMs = my::timestamp::now();
                            spdingTipCnt = 0;
                        }

                        os.ttsGBKQueue(config.sys.warn.overspeed.warn_spch);
                    }

                    break;
                }

            default: {
                    loge("INVALID stat = %d", stat);
                    break;
                }
        }

    } else {
        curStatBgnTmMs = my::timestamp::now();
        spdingTipCnt = wSpdingTipCnt = stat = 0;
        spdingTipTmMs = wSpdingTipTmMs = 0;
        current->setAlarmBeforeOverspeed(false);
        current->setAlarmOverspeed(false);

        //pw->speeding = 0;
        //pw->speedingAlarm = 0;
    }

    return bRpt;
}

int Manager::protDeregister(int idx, const char * protName)
{
    int ret = -1;

    if ((0 <= idx && idx < MAX_CLIENTS) && com_controller.get()) {
        if ((*com_controller)[idx]) {
            if ((*com_controller)[idx]->get_type() == protName) {//"jtt808-1078") {
                ServiceJtt808_1078 * prot = (ServiceJtt808_1078 *)(*com_controller)[idx].get();
                logd("send dereg msg! %d", idx);
                ret = prot->deregister();
            }
        }
    }

    return ret;
}


int Manager::queryServerTime(int idx)
{
    int ret = -1;

    if ((0 <= idx && idx < MAX_CLIENTS) && com_controller.get()) {
        if ((*com_controller)[idx]) {
            if ((*com_controller)[idx]->get_type() == "jtt808-1078") {
                ServiceJtt808_1078 * prot = (ServiceJtt808_1078 *)(*com_controller)[idx].get();
                ret = prot->queryServerTime();
            }
        }
    }

    return ret;
}

int Manager::responseDriverInfo(int idx)
{
    int ret = -1;

    if ((0 <= idx && idx < MAX_CLIENTS) && com_controller.get()) {
        if ((*com_controller)[idx]) {
            if ((*com_controller)[idx]->get_type() == "jtt808-1078") {
                ServiceJtt808_1078 * prot = (ServiceJtt808_1078 *)(*com_controller)[idx].get();
                ret = prot->responseDriverInfo();
            }
        }
    }

    return ret;
}

int Manager::rptGnss(int idx)
{
    int ret = -1;

    if ((0 <= idx && idx < MAX_CLIENTS) && com_controller.get()) {
        if ((*com_controller)[idx]) {
            if ((*com_controller)[idx]->get_type() == "jtt808-1078") {
                ServiceJtt808_1078 * prot = (ServiceJtt808_1078 *)(*com_controller)[idx].get();
                ret = prot->rptGnss();
            }
        }
    }

    return ret;
}

int Manager::triggerEbill(int idx)
{
    int ret = -1;

    if ((0 <= idx && idx < MAX_CLIENTS) && com_controller.get()) {
        if ((*com_controller)[idx]) {
            if ((*com_controller)[idx]->get_type() == "jtt808-1078") {
                ServiceJtt808_1078 * prot = (ServiceJtt808_1078 *)(*com_controller)[idx].get();
                my::uchar  tmp[1024] = {0};
                srand(time(NULL));
                for (int i = 0; i < sizeof(tmp); i++) {
                    tmp[i] = rand() % 0xFF + 1;
                }
                my::string testEbill = my::string((const char*)tmp, sizeof(tmp));
                ret = prot->uploadEbill(testEbill);
            }
        }
    }

    return ret;
}

int Manager::triggerAdasAlarm(const char * e, int idx)
{
    InputService & is = InputService::getInstance();
    int ret = -1;
    int algoCh = is.getAlgoChIdx("adas");

    if (!strcmp(e, "overstaffing")) {
        algoCh = 6;
    }

    if ((0 <= idx && idx < MAX_CLIENTS) && com_controller.get()) {
        if ((*com_controller)[idx]) {
            if ((*com_controller)[idx]->get_type() == "jtt808-1078") {
                ServiceJtt808_1078 * prot = (ServiceJtt808_1078 *)(*com_controller)[idx].get();
                ret = prot->triggerAdasAlarm(e, algoCh + 1);
            }
        }
    }

    return ret;
}

int Manager::triggerBsdAlarm(const char * e, int idx)
{
    InputService & is = InputService::getInstance();
    int ret = -1;
    int algoCh = -1;
    if (strstr(e, "FBsdWarning")) {
        algoCh = is.getAlgoChIdx("fbsd");
    } else {
        algoCh = is.getAlgoChIdx("bsd");
    }

    if (algoCh >= 0) {
        if ((0 <= idx && idx < MAX_CLIENTS) && com_controller.get()) {
            if ((*com_controller)[idx]) {
                if ((*com_controller)[idx]->get_type() == "jtt808-1078") {
                    ServiceJtt808_1078 * prot = (ServiceJtt808_1078 *)(*com_controller)[idx].get();
                    ret = prot->triggerBsdAlarm(e, algoCh + 1);
                }
            }
        }
    }

    return ret;
}

int Manager::triggerDmsAlarm(const char* evt_name, int idx)
{
    InputService & is = InputService::getInstance();
    int ret = -1;
    int algoCh = is.getAlgoChIdx("dms");

    if (!strcmp(evt_name, "handsoff")) {
        algoCh = is.getAlgoChIdx("hod");
    }

    if ((0 <= idx && idx < MAX_CLIENTS) && com_controller.get()) {
        if ((*com_controller)[idx]) {
            if ((*com_controller)[idx]->get_type() == "jtt808-1078") {
                ServiceJtt808_1078 * prot = (ServiceJtt808_1078 *)(*com_controller)[idx].get();

                if (algoCh >= 0) {
                    ret = prot->triggerDmsAlarm(evt_name, algoCh + 1);
                }
            }
        }
    }

    return ret;
}

int Manager::triggerDumpperAlarm(const char* evt_name, int idx)
{
    int ret = -1;
    if ((0 <= idx && idx < MAX_CLIENTS) && com_controller.get()) {
        if ((*com_controller)[idx]) {
            if ((*com_controller)[idx]->get_type() == "jtt808-1078") {
                ServiceJtt808_1078 * prot = (ServiceJtt808_1078 *)(*com_controller)[idx].get();

                //if (algoCh >= 0) {
                //    ret = prot->triggerDumpperAlarm(evt_name, algoCh + 1);
                //}
                ret = prot->triggerDumpperAlarm(evt_name, 1);
            }
        }
    }

    return ret;
}

int Manager::triggerZTCAlarm(const char* evt_name, int idx)
{
    int ret = -1;
    if ((0 <= idx && idx < MAX_CLIENTS) && com_controller.get()) {
        if ((*com_controller)[idx]) {
            if ((*com_controller)[idx]->get_type() == "jtt808-1078") {
                ServiceJtt808_1078 * prot = (ServiceJtt808_1078 *)(*com_controller)[idx].get();

                ret = prot->triggerZTCAlarm(evt_name, idx);
            }
        }
    }

    return ret;
}

int Manager::triggerSpeedingAlarm(SpeedingAlarmParam & param)
{
    int ret = -1;
    for (int idx = 0; (idx < MAX_CLIENTS) && com_controller.get(); idx++) {
        if (((1 << idx) & param.protBitTbl) && (*com_controller)[idx]) {
            if ((*com_controller)[idx]->get_type() == "jtt808-1078") {
                ServiceJtt808_1078 * prot = (ServiceJtt808_1078 *)(*com_controller)[idx].get();
                ret = prot->triggerSpeedingAlarm(param);
            }
        }
    }

    return ret;
}

int Manager::triggerOverHeightAlarm(const my::uchar alarm_status, int idx, my::ushort limits)
{
    int ret = -1;
    if ((0 <= idx && idx < MAX_CLIENTS) && com_controller.get()) {
        if ((*com_controller)[idx]) {
            if ((*com_controller)[idx]->get_type() == "jtt808-1078") {
                ServiceJtt808_1078 * prot = (ServiceJtt808_1078 *)(*com_controller)[idx].get();
                ret = prot->triggerOverHeightAlarm(alarm_status, idx, limits);                
            }
        }
    }

    return ret;
}

int Manager::triggerOverLoadAlarm(const my::uchar alarmType, const my::uchar alarmStatus, int idx, my::ushort loadVal, my::ushort limits)
{
    int ret = -1;
    if ((0 <= idx && idx < MAX_CLIENTS) && com_controller.get()) {
        if ((*com_controller)[idx]) {
            if ((*com_controller)[idx]->get_type() == "jtt808-1078") {
                ServiceJtt808_1078 * prot = (ServiceJtt808_1078 *)(*com_controller)[idx].get();
                ret = prot->triggerOverLoadAlarm(alarmType, alarmStatus, idx, loadVal, limits);                
            }
        }
    }

    return ret;
}

int Manager::triggerHardDrivingAlarm(const char* e, int idx)
{
    InputService & is = InputService::getInstance();
    int ret = -1;
    int algoCh = is.getAlgoChIdx("adas");
    if ((0 <= idx && idx < MAX_CLIENTS) && com_controller.get()) {
        if ((*com_controller)[idx]) {
            if ((*com_controller)[idx]->get_type() == "jtt808-1078") {
                ServiceJtt808_1078 * prot = (ServiceJtt808_1078 *)(*com_controller)[idx].get();
                ret = prot->triggerHardDrivingAlarm(e, algoCh + 1);
            }
        }
    }

    return ret;
}

int Manager::triggerFaceMatch_v2()
{
    AlgoManager & am = AlgoManager::getInstance();
    int ret = am.faceMatch_V2();
    
    return ret;
}


bool Manager::getProtStat(int idx, int sidx)
{
    int ret = -1;

    if (idx == -1) {
        char propValue[PROP_VALUE_MAX] = {0};
        if (__system_property_get(PROP_SUBIAO_STATUS, propValue) > 0) {
            if (strcmp(propValue, "connected") == 0) {
                return true;
            }
        }
        return false;
    }

    if ((0 <= idx && idx < MAX_CLIENTS) && com_controller.get()) {
        if ((*com_controller)[idx]) {
            ComService * prot = ((*com_controller)[idx]).get();
            return prot->connected(sidx);
        }
    }

    return false;
}
bool Manager::dummyProt(int idx, int enable)
{
    int ret = -1;

    if ((0 <= idx && idx < MAX_CLIENTS) && com_controller.get()) {
        if ((*com_controller)[idx]) {
            ComService * prot = ((*com_controller)[idx]).get();
            prot->dummy(!!enable);
            return true;
        }
    }

    return false;
}

std::string Manager::getProtAlarmInfo(int idx)
{
    if ((0 <= idx && idx < MAX_CLIENTS) && com_controller.get()) {
        if ((*com_controller)[idx]) {
            ComService * prot = ((*com_controller)[idx]).get();
            return prot->showInfo(0);
        }
    }

    return "";
}

int Manager::postMsg(std::shared_ptr<minieye::AMessage> & spMsg)
{
    for (int idx = 0; idx < MAX_CLIENTS; idx++) {
        if ((*com_controller)[idx]) {
            const char * protName = (*com_controller)[idx]->get_type();
            ComService * prot = (*com_controller)[idx].get();
            std::shared_ptr<minieye::AMessage> msgCp = spMsg->dup();
            prot->postMsg(msgCp);
        }
    }
    return 0;
}

void Manager::enableFakeStat(const char * name, int enable)
{
    McuMsgHandler & mmh = McuMsgHandler::getInstance();
    mmh.enableFakeStat(name, enable);
}

void Manager::setFakeStatus(const char * name, int value)
{
    McuMsgHandler & mmh = McuMsgHandler::getInstance();
    mmh.setFakeStatus(name, value);
}

void Manager::displayWarnBmp(int level)
{
    const char * bmp[] = {"/system/etc/img/red.bmp", "/system/etc/img/yellow.bmp", "/system/etc/img/green.bmp"};
    const char * pBmp = bmp[0];
    if ((level > 0) && (level <= sizeof(bmp) / sizeof(bmp[0]))) {
        pBmp = bmp[level - 1];
    }
    int scale = 100;
    char value[PROP_VALUE_MAX] = {0};
    if (__system_property_get(PROP_RW_MINIEYE_BSD_WARN_BMP_SCALE, value) > 0) {
        scale = atoi(value);
    }

    char cmd[256] = {0};
    snprintf(cmd, sizeof(cmd), "cmd alarm 1 %s 10 1 %d", pBmp, scale);
    vector<char> resp;
    LogCallProxyCmd::sendReq("media", cmd, resp, 1);
    return;
}

#define FOR_EACH_PROT(__FN__, __EVT__, __CH__) do {\
    for (int idx = 0; idx < MAX_CLIENTS; idx++) { \
        ComService * prot = (*com_controller)[idx].get(); \
        if (prot) { \
            const char * protName = prot->get_type(); \
            prot->__FN__(__EVT__,__CH__);\
        } \
    }\
} while (0)

bool Manager::alarmInterval(std::shared_ptr<Event> e)
{
    bool ret =  true;
    switch(e->type()) {
        case EVT_TYPE_ZF_BSD:
        case EVT_TYPE_ADAS_CAR_MATCH:
        case EVT_TYPE_ADAS_PLATE_MATCH:
        case EVT_TYPE_ADAS_POSTION_UPLOAD:
        case EVT_TYPE_BACKROAD_INSP_RAW:
        case EVT_TYPE_BACKROAD_INSP: 
        case EVT_TYPE_BACKROAD_INSP_GATHER:    {
            /* 道路巡检、ai巡检告警间隔又可能会低于5s不使用默认告警间隔5s的限制 */
            ret = false;
            break;
        }
        case EVT_TYPE_BSD_Behind:
        case EVT_TYPE_BSD_Left:
        case EVT_TYPE_BSD_Right:
        case EVT_TYPE_BSD_Front: {
            if ((mLastEvtTbl[e->type()] != e->c.event) ||
                ((mLastEvtTbl[e->type()] == e->c.event) && (mLastEvtLvl[e->type()] != e->c.level))) {
                ret = false;
            }
            break;
        }
        default: {
            break;
        }
    }

    return ret;
}


#define REC_ALGO_EVT(evt, fmt, args...)  do {\
    if (evt < EVT_TYPE_PROT_CUSTOM_BGN) {\
        PROT_MLOG("%d, " # fmt, evt, ##args);\
    }\
} while(0)

bool Manager::onAlgoEvent(std::shared_ptr<Event> e)
{
    bool ret = false;
    InputService & is = InputService::getInstance();
    OutputService & os = OutputService::getInstance();
    McuMsgHandler & mmh = McuMsgHandler::getInstance();
    if ((EVT_TYPE_ADAS_CAN700_MSG != e->type()) &&
        (EVT_TYPE_DISPLAY_CAN_MSG != e->type()) &&
        (EVT_TYPE_SPEED_CAN_MSG != e->type())) {
        auto last = mLastAlarmTbl.find(e->type());
        if (last != mLastAlarmTbl.end()) {
            my::timestamp lastTs = last->second;
            if (alarmInterval(e)
                && lastTs.elapsed() < mAlarmIntervalMs) {
                // REC_ALGO_EVT(e->type(), "warn %s > last elapsed %.3fs, skip!", value2name(e->algoName().c_str(), e->type()), lastTs.elapsed() / 1000);
                logd("warn %s > last elapsed %.3fs, skip!", value2name(e->algoName().c_str(), e->type()), lastTs.elapsed() / 1000);
                return -1;
            } else {
                // REC_ALGO_EVT(e->type(), "warn %s > last elapsed %.3fs!", value2name(e->algoName().c_str(), e->type()), lastTs.elapsed() / 1000);
                logd("warn %s > last elapsed %.3fs!", value2name(e->algoName().c_str(), e->type()), lastTs.elapsed() / 1000);
            }
        } else {
            // REC_ALGO_EVT(e->type(), "warn %s!", value2name(e->algoName().c_str(), e->type()));
            logd("warn %s!", value2name(e->algoName().c_str(), e->type()));
        }
        mLastAlarmTbl[e->type()] = my::timestamp::now();
        mLastEvtTbl[e->type()] = e->c.event;
        mLastEvtLvl[e->type()] = e->c.level;
    }
    os.mAlarmInfoRecord->recordAlarm(e);
    int channel = e->c.srcChn;
    if (channel < 0) {
        channel = is.getAlgoChIdx(e->algoName().c_str());
    }
    if (channel >= 0) {
        channel += 1;
    }

    switch (e->type()) {
        case EVT_TYPE_DMS_SNAP:
        case EVT_TYPE_DMS_FATIGUE:
        case EVT_TYPE_DMS_FATIGUE_Eye:
        case EVT_TYPE_DMS_FATIGUE_YAWN:
        case EVT_TYPE_DMS_LOOK_AROUND:
        case EVT_TYPE_DMS_LOOK_DOWN:
        case EVT_TYPE_DMS_LOOK_UP:
        case EVT_TYPE_DMS_PHONE_CALL:
        case EVT_TYPE_DMS_SMOKE:
        case EVT_TYPE_DMS_ABSENCE:
        case EVT_TYPE_DMS_CAM_OCCLUSION:
        case EVT_TYPE_DMS_EYE_OCCLUSION:
        case EVT_TYPE_DMS_DRIVER_MATCH:
        case EVT_TYPE_DMS_DRIVER_NOT_MATCH:
        case EVT_TYPE_DMS_DRIVER_CHG:

        case EVT_TYPE_DMS_OVER_TIME:
        case EVT_TYPE_DMS_NOT_BELT:
        case EVT_TYPE_DMS_MASK:
        case EVT_TYPE_DMS_DRINK:
        case EVT_TYPE_DMS_PLAY_WIRH_PHONE: {
                FOR_EACH_PROT(reportDmsAlarm, e, channel);
                if ((EVT_TYPE_DMS_DRIVER_CHG == e->type()) ||
                    (EVT_TYPE_DMS_ABSENCE == e->type())) {
                    std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
                    amsg->setWhat(e->type());
                    postMsg(amsg);
                }
                //记录疲劳报警次数保存到prop
                if ((EVT_TYPE_DMS_FATIGUE_Eye == e->type()) ||
                    (EVT_TYPE_DMS_FATIGUE_YAWN == e->type())) {
                    char temp[10] = {0};
                    static int fatigue_alarm_count = 0;
                    fatigue_alarm_count++;
                    sprintf(temp, "%d", fatigue_alarm_count);
                    __system_property_set(PROP_RW_MINIEYE_FATIGUE_COUNT, temp);
                }

                break;
            }

        case EVT_TYPE_DMS_HANDSOFF: {
                FOR_EACH_PROT(reportDmsAlarm, e, channel);
                break;
            }
        case EVT_TYPE_DMS_FACEID_MATCH_RESULT_V2:{
            logd("***********EVT_TYPE_DMS_FACEID_MATCH_RESULT_V2!\n");
            e->c.speed = ServiceHelper::getInstance().getStatus().lbs.speed;
            FOR_EACH_PROT(reportDmsAlarm, e, channel);
            break;
        }

        case EVT_TYPE_ADAS_SNAP:
        case EVT_TYPE_ADAS_LeftLDW:
        case EVT_TYPE_ADAS_RightLDW:
        case EVT_TYPE_ADAS_FCW:
        case EVT_TYPE_ADAS_HW:
        case EVT_TYPE_ADAS_TSRW: /*超限*/
        case EVT_TYPE_ADAS_TSR: /* 识别路牌 */
        case EVT_TYPE_ADAS_CamOcclusion:

        case EVT_TYPE_ADAS_OVERSTAFFING:
        case EVT_TYPE_ADAS_SLLC: 
        case EVT_TYPE_ADAS_POZ:
        case EVT_TYPE_ADAS_ZCO:{
            FOR_EACH_PROT(reportAdasAlarm, e, channel);
            break;
        }
        case EVT_TYPE_ADAS_PCW: {
            FOR_EACH_PROT(reportAdasAlarm, e, channel);
            // break;
        }
        case EVT_TYPE_ADAS_ZCW: {
            std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
            amsg->setString("transfer", e->mEvtData["transfer"].c_str(), e->mEvtData["transfer"].length());
            amsg->setFloat("speed", e->c.speed);
            amsg->setWhat(e->type());
            postMsg(amsg);
            break;
        }
        case EVT_TYPE_ADAS_SpdingOnCross:
        case EVT_TYPE_ADAS_ZebraCrossingPCW: {
                break;
            }

        case EVT_TYPE_BSD_Front:
        case EVT_TYPE_BSD_Behind: {
                FOR_EACH_PROT(reportBsdAlarm, e, channel);
                break;
            }

        case EVT_TYPE_BSD_Left:
        case EVT_TYPE_BSD_Right: {
                FOR_EACH_PROT(reportBsdAlarm, e, channel);
                break;
            }

        case EVT_TYPE_DUMPER_DETAIL: {
                FOR_EACH_PROT(procEvt, e, channel);
                break;
            }

        case EVT_TYPE_ADAS_HAW:/*急加速*/
        case EVT_TYPE_ADAS_HCW:/*急转弯*/
        case EVT_TYPE_ADAS_HDW: {/*急减速*/
                Current st = ServiceHelper::getInstance().getStatus();
                if (st.getStateAcc() /*&& (int)st.getSpeed() > 0*/) {
                    FOR_EACH_PROT(reportHardDrivingAlarm, e, channel);
                }
                break;
            }
        case EVT_TYPE_OVER_HEIGHT: { /* 限高 */
                FOR_EACH_PROT(reportOverHeightAlarm, e, channel);
                break;
            }
        case EVT_TYPE_ADAS_CAN700_MSG: {
            uint64_t * pCanByte = (uint64_t *)e->mEvtData["data"].c_str();
            // 发送给hostio
            McuMsgHandler & h = McuMsgHandler::getInstance();
            h.sendMcuMsg(MCU_MSG_TYPE_ADAS_CAN700, (uint8_t *)pCanByte, 8);
            break;
        }
        case EVT_TYPE_DISPLAY_CAN_MSG:
        case EVT_TYPE_SPEED_CAN_MSG: {
            McuMsgCanIdxE canidx = MCU_CAN_IDX_CAN0_SPEED;
            if (EVT_TYPE_SPEED_CAN_MSG == e->type()) {
                canidx = MCU_CAN_IDX_CAN0_SPEED;

            } else if (EVT_TYPE_DISPLAY_CAN_MSG == e->type()) {
                canidx = MCU_CAN_IDX_CAN1_DISP;
            } else {
                break;
            }

            my::string canId = e->mEvtData["id"];
            my::string rawData = e->mEvtData["data"];
            my::string customer = e->mEvtData["customer"];
            uint32_t id = strtoul(canId.c_str(), NULL, 16);
            if (id && (mCanIdData[canidx].find(id) == mCanIdData[canidx].end())) {
                mCanIdData[canidx][id] = "";

                if (mCanIdData[canidx].size() > 4) {
                    loge("too many can id! %d", mCanIdData[canidx].size());
                }

                McuMsgCanFilterT filter;
                memset(&filter, 0, sizeof(filter));
                filter.enable = 1;
                filter.canIdx = canidx;
                int idx = 0;

                for (auto i : mCanIdData[canidx]) {
                    filter.canIds[idx++] = i.first;

                    if (CAN_ID_ARRAY_SIZE == idx) {
                        break;
                    }
                }

                sendCanFilterMsg((uint8_t *)&filter, sizeof(McuMsgCanFilterT));
            }

            mCanIdData[canidx][id] = rawData;
            logd("id 0x%x size %d, [%02x %02x %02x %02x %02x %02x %02x %02x]", id, rawData.length(),
                 (unsigned char)rawData[0], (unsigned char)rawData[1], (unsigned char)rawData[2], (unsigned char)rawData[3],
                 (unsigned char)rawData[4], (unsigned char)rawData[5], (unsigned char)rawData[6], (unsigned char)rawData[7]);

            if (MCU_CAN_IDX_CAN0_SPEED == canidx) {
                mmh.sendCan0Msg(id, (uint8_t*)rawData.c_str());

            } else if (MCU_CAN_IDX_CAN1_DISP == canidx) {
                mmh.sendCan1Msg(id, (uint8_t*)rawData.c_str());
            }
            break;
        }

        case EVT_TYPE_DMS_FACEID_DRIVER_READY:
        case EVT_TYPE_DMS_FACEID_DETECT_RESP:
        case EVT_TYPE_DMS_FACEID_MATCH_RESULT:
        case EVT_TYPE_DMS_FACEID_V2_DELETE_RESP:
        case EVT_TYPE_DMS_FACEID_IMG_TO_FACE_ID_RESP: 
        case EVT_TYPE_DMS_FACEID_V2_MATCH_FAIL: {
            std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
            amsg->setWhat(e->type());
            for (auto param : e->mEvtData) {
                amsg->setString(param.first.c_str(), param.second.c_str(), param.second.length());
            }
            postMsg(amsg);
            break;
        }
        case EVT_TYPE_DMS_FACEID_V2_MATCH_SUSSCE: {
            std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
            amsg->setWhat(e->type());
            for (auto param : e->mEvtData) {
                if (param.first == "similarity") {
                    amsg->setFloat("similarity", atof(param.second.c_str()));
                } else {
                    amsg->setString(param.first.c_str(), param.second.c_str(), param.second.length());
                }
            }
            postMsg(amsg);
            break;
        }
        case EVT_TYPE_ZF_BSD:{
            std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
            amsg->setWhat(e->type());
            amsg->setString("bsdReport", e->c.event.c_str(), e->c.event.length());
            amsg->setInt32("level", e->c.level);
            amsg->setInt32("speed", e->c.speed);
            amsg->setInt32("channel", e->c.srcChn);
            postMsg(amsg);
            break;
        }
        case EVT_TYPE_ADAS_CAR_MATCH:        // 检测到车信息
        case EVT_TYPE_ADAS_PLATE_MATCH: {    // 检测到车牌信息
            std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
            amsg->setWhat(e->type());
            for (auto param : e->mEvtData) {
                amsg->setString(param.first.c_str(), param.second.c_str(), param.second.length());
            }
            postMsg(amsg);
            break;
        }
        case EVT_TYPE_BACKROAD_INSP_RAW: 
        case EVT_TYPE_BACKROAD_INSP_GATHER:    
        case EVT_TYPE_BACKROAD_INSP: {       // 道路巡检
            std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
            amsg->setWhat(e->type());
            for (auto param : e->mEvtData) {
                amsg->setString(param.first.c_str(), param.second.c_str(), param.second.length());
            }
            postMsg(amsg);
            break;
        }
        default: {
                break;
            }
    }

    return ret;
}

bool Manager::forbidDrvTmChk()
{
    InputService & is = InputService::getInstance();
    Current * current = is.getCurrent();
    Config & config = is.getConfig();

    static my::timestamp lastTs = 0;
    double curSpeed = current->getSpeed();

    if (curSpeed < 5) {
        if (config.sys.warn.overtime.maxParkSec) {
            uint32_t lastTripEnd = 0;
            IdvrTripInfo *pt = current->get_trip();
            if (pt) {
               lastTripEnd = pt->endTime; 
            }
            if (1514736000 < lastTripEnd && lastTripEnd < 1893427200) {
                int32_t diff = my::timestamp::utc_seconds() - lastTripEnd;
                if (diff > config.sys.warn.overtime.maxParkSec) {
                    current->setAlarmStopOvertime((curSpeed < 5.0));
                    if (lastTs.elapsed() > 10000) {
                        lastTs = my::timestamp::now();
                        ttsPlayRightNow("停车超时");
                    }
                } else {
                    current->setAlarmStopOvertime(false);
                }
            }
        } else {
            current->setAlarmStopOvertime(false);
        }
        current->setAlarmUnlawDrv(false);
    } else {
        current->setAlarmStopOvertime(false);
        if (config.sys.warn.forbid_drv_tm) {
            int bgn = 16*3600 + 3600 * (config.sys.warn.forbid_drv_tm >> 24) + 60 * ((config.sys.warn.forbid_drv_tm >> 16) & 0xff);
            int end = 16*3600 + 3600 * ((config.sys.warn.forbid_drv_tm >> 8) & 0xff) + 60 * (config.sys.warn.forbid_drv_tm & 0xff);;

            bool bWarn  = (curSpeed > 5) && inDayTmSeg(bgn, end);
            if (bWarn) {
                current->setAlarmUnlawDrv(true);
                if (lastTs.elapsed() > 8000) {
                    lastTs = my::timestamp::now();
                    ttsPlayRightNow("进入禁行时段");
                }
            } else {
                current->setAlarmUnlawDrv(false);
            }
        } else {
            current->setAlarmUnlawDrv(false);
        }
    }
    return true;
}

#if 0
void Manager::updateEmergencyEvt() {
    my::uint64 evtBits = mRecAlarmManager->getEvtBit();
    evtBits |= (my::uint64)0x1; 
    mRecAlarmManager->sendSetEvtBitsCmd(evtBits);    
}
#endif


void Manager::run()
{
    int acc = -1;
    uint32_t loopCnt = 1;
    ServiceJtt808_1078 *prot = NULL;
    prctl(PR_SET_NAME, "statusChk");

    while (!exiting()) {
        for (int i = 0; i < MAX_CLIENTS; i++) {
            ComService::Ptr cs = (*com_controller)[i];
            if (cs) {
                if ((cs->si[0].prot == "jtt808-1078") && (cs->si[0].prot_subtype == "sichuan")) {
                    prot = (ServiceJtt808_1078 *)(*com_controller)[i].get();                        
                    break;
                }
            }
        }

        /* 限速 */        
        if (prot) {
            /* chuanbiao ext */
            prot->spdingChk();            
        } else {
            /* 超速 */
            spdingChk();
        }

        /* 禁行 */
        forbidDrvTmChk();

        /* 特殊报警录像达到存储阈值 */
        mRecAlarmManager->alarmRecProportionCheck();

        /* 休眠唤醒 */
        Wakeup & w = Wakeup::getInstance();
        w.run();

        /* 录像报警标记 */
        mRecAlarmManager->alarmRecManager();

        /* 短信接收 */
        iPhone::getInstance()->chkcnt();
        smsParse();

        Current st = ServiceHelper::getInstance().getStatus();
        if ((-1 == acc) || (st.getStateAcc() != acc)) {
            acc = st.getStateAcc();
            loopCnt = 0;
        }
        if (!(loopCnt % 6)) {
            state_controller->updateExtendAlarm();
        }
        loopCnt++;

        usleep(5e5);
    }
}


int Manager::sendMcuMsg(McuMsgTypeE cmd, uint8_t * data, int32_t len)
{
    McuMsgHandler & h = McuMsgHandler::getInstance();
    h.sendMcuMsg(cmd, data, len);
    return 0;
}


int Manager::sendCanFilterMsg(uint8_t * data, int32_t len)
{
    McuMsgHandler & h = McuMsgHandler::getInstance();
    h.sendMcuMsg(MCU_MSG_TYPE_SET_CAN_FILTER, data, len);
    return 0;
}

/* 仅对过滤模式 cycle 循环覆盖之前设置的canid */
int Manager::sendCanFilterMsgCycle(uint8_t * data, int32_t len)
{
    uint8_t *sendData = data;
    McuMsgCanFilterT *canFilter = (McuMsgCanFilterT *)data;

    uint8_t ch = canFilter->canIdx;
    if (ch < 2) {
        bool lastIsStd = (mCanFileter[ch].canIds[0] < MCU_CAN_STD_ID_MAX) ? true : false;
        bool currentIsStd = (canFilter->canIds[0] < MCU_CAN_STD_ID_MAX) ? true : false;
        if (canFilter->useListMode == mCanFileter[ch].useListMode
            && canFilter->enable == mCanFileter[ch].enable
            && (lastIsStd == currentIsStd)) {
            /* 相同通道相同过滤模式使能状态相同且帧格式相同 */
            int holdCnt = 0;
            for (int i = 0; i < CAN_ID_ARRAY_SIZE; i++) {
                if (!canFilter->canIds[i]) {
                    if (mCanFileter[ch].canIds[holdCnt]) {
                        canFilter->canIds[i] = mCanFileter[ch].canIds[holdCnt];
                        holdCnt++;
                    } else {
                        break;
                    }
                }
            }
        }

        /* 更新can过滤器器信息 */   
        memcpy(&mCanFileter[ch], canFilter, sizeof(McuMsgCanFilterT));
    }

    McuMsgHandler & h = McuMsgHandler::getInstance();
    return h.sendMcuMsg(MCU_MSG_TYPE_SET_CAN_FILTER, sendData, len);
}

bool Manager::refresh(char* section, char* key, char* value)
{
    logi("[refresh] section=%s,key=%s,value=%s", section, key, value);
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();

    if (my::constr("base") == section) {
        if (my::constr("model") == key) {
            config.sys.product.model = value;

        } else if (my::constr("ccc") == key) {
            config.sys.product.ccc = value;

        } else if (my::constr("manufacturer") == key) {
            config.sys.product.vendor = value;

        } else if (my::constr("manufacture.date") == key) {
            config.sys.product.date = (my::uint)atoi(value);
        }
        else if (my::constr("sim") == key) {
            config.sys.sim = value;
        }
        else if (my::constr("vehicle.city") == key) {
            config.sys.vehicle.city = (my::ushort)atoi(value);

        } else if (my::constr("vehicle.plate_color") == key) {
            config.sys.vehicle.plate_color = (my::ushort)atoi(value);

        } else if (my::constr("vehicle.plate_num") == key) {
            config.sys.vehicle.plate_num = value;

        } else if (my::constr("vehicle.plate_type") == key) {
            config.sys.vehicle.plate_type = value;

        } else if (my::constr("vehicle.province") == key) {
            config.sys.vehicle.province = (my::ushort)atoi(value);

        } else if (my::constr("vehicle.vin") == key) {
            config.sys.vehicle.vin = value;
        }
    } else if (my::constr("warn") == section) {
        if (my::constr("overspeed.delta") == key) {
            config.sys.warn.overspeed.delta = atoi(value);
            config.sys.warn.overspeed.alarm = config.sys.warn.overspeed.limit - config.sys.warn.overspeed.delta / 10;

        } else if (my::constr("overspeed.enable") == key) {
            config.sys.warn.overspeed.enable = (char)atoi(value);

        } else if (my::constr("overspeed.limit") == key) {
            config.sys.warn.overspeed.limit = atoi(value);
        }

        else if ((my::constr("overtime.limit") == key) || (my::constr("overspeed.max_drv_tm_thres") == key)) {
            config.sys.warn.overtime.limit = atoi(value);

        } else if (my::constr("overtime.remainder") == key) {
            config.sys.warn.overtime.remainder = atoi(value);

        } else if (my::constr("overtime.rest") == key) {
            config.sys.warn.overtime.rest = atoi(value);
        }
    } else if (my::constr("io") == section) {
        const char *fullsc_name[IDVR_GPIO_IN_MAX] = {
                "gpio.back.fullsc", "gpio.near.fullsc", "gpio.far.fullsc", "gpio.left.fullsc",
                "gpio.right.fullsc", "gpio.brake.fullsc", "gpio.door.fullsc"};
        for (int32_t i = IDVR_GPIO_IN_MIN; i < IDVR_GPIO_IN_MAX; i++) {
            if (my::constr(fullsc_name[i]) == key) {
                sscanf(value, "ch%d", &(config.sys.io.fullsc[i]));
                if (config.sys.io.fullsc[i] > config.sys.cameras) {                
                    loge("Bad io.fullsc[i] ch = %d", i, config.sys.io.fullsc[i]);
                    config.sys.io.fullsc[i] = 0;
                }
            }
        }
    } else if (my::constr("carctrl") == section) {
        if (my::constr("car_mode") == key) {
            config.sys.carCtrl.carMode = atoi(value);
        } else if (my::constr("limit_speed") == key) {
            config.sys.carCtrl.limitSpeed = atoi(value);
        } else if (my::constr("lock_car") == key) {
            config.sys.carCtrl.lockCar = atoi(value);
        } else if (my::constr("limit_lift") == key) {
            config.sys.carCtrl.limitLift = atoi(value);
        }
    }

    return 0;
}

bool MediaConfigReceiver::OnBroadcastCallback(vector<string>& vec)
{
    Manager & m = Manager::getInstance();
    char section[64] = {0};
    char key[64] = {0};
    char value[64] = {0};

    for (auto it = vec.begin(); it != vec.end(); it++) {
        sscanf((*it).c_str(), "%s %s %s", section, key, value);
        m.refresh(section, key, value);
    }

    return 0;
}








