#include <string.h>
#include "service.h"

#include "service.jtt808-1078.h"
#include "service.private.h"

ComService::Ptr ComService::create(const char* protocol, const char* tag, const char * sim, const char * protSubType)
{
    if (!strcmp(protocol, "jtt808-1078")) return ComService::Ptr(new ServiceJtt808_1078(tag, sim, protSubType));
    if (!strcmp(protocol, "private")) return ComService::Ptr(new ServicePrivate());

    return 0;
}


