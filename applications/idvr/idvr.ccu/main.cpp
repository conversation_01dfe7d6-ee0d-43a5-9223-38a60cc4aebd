// ccu.cpp : 此文件包含 "main" 函数。程序执行将在此处开始并结束。
//

#include <iostream>
#include "mystd.h"
#include "manager.h"
#include "prot.subiao.h"
#include "prot.anncr.sanfeng.h"
#include "transporter.tcp.h"
#include "system_properties.h"

const char* usage = " Usage: idvr.ccu -c config_path -d data_path\n";

static void sig_trace(int sig)
{
    logd("[idvr.ccu] sig_trace = %d", sig);
}

#ifndef ANDROID
__attribute((constructor)) void before_main()
{
    __system_properties_init();
}
#endif

int main(int argc, char* argv[])
{
    //捕捉系统相关信号进行处理
    signal(SIGPIPE, sig_trace);
    trace_signal(SIGSEGV);
    trace_signal(SIGFPE);
    trace_signal(SIGABRT);

    system("rm -r /mnt/obb/mprot/*");

    my::string etc = "./etc";
    my::string data = etc;
    for (int i = 1; i < argc; i++) {
        char* cfg = argv[i];
        if (!strcmp(cfg, "-h")) {
            std::cout << usage;
            return 0;
        } else if(!strcmp(cfg, "-c")) {
            if (++i < argc) {
                etc.assignf("%s/etc", argv[i]);
            }
        } else if (!strcmp(cfg, "-d")) {
            if (++i < argc) {
                data.assignf("%s/data", argv[i]);
            }
        }
    }
    
    InputService & is = InputService::getInstance();
    is.init("", etc.c_str(), data.c_str());

    OutputService & os = OutputService::getInstance();
    os.start();

    Manager & m = Manager::getInstance();
    int ret = m.start();
    LOG_RETURN_IF(ret != 0, ret, loge, "Failed to start main program, error=[%d].", ret);

    McuMsgHandler & mcuMsgHlr = McuMsgHandler::getInstance();
    mcuMsgHlr.init();

    PROT_MLOG("**************idvr.ccu is started.******************");
    ProtServer<ANNCR_PROT> * mpAnncrServer = nullptr;
    std::shared_ptr<ProtBase> spProt = nullptr;
    std::shared_ptr<TransporterIf> spTrans = nullptr;
    char propValue[PROP_VALUE_MAX] = {0};
    constexpr int dftSleepInterval = 1000; // 1s
    int sleepInterval;

    while (true) {
        Config & config = is.getConfig();

        if (config.sys.anncrServer) {
            if (!mpAnncrServer) {
                mpAnncrServer = new ProtServer<ANNCR_PROT>(8640);
                if (mpAnncrServer) {
                    mpAnncrServer->start();
                    logd("server prot start!!!!!!!!!!!!!!!!!!!!!!!");
                }
            }
        }

        if (__system_property_get("persist.subiao.sleep_interval", propValue) > 0) {
            try {
                sleepInterval = atoi(propValue);
            } catch (...) {
                sleepInterval = dftSleepInterval;
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(std::max(sleepInterval, dftSleepInterval)));
        /* [subiao]
           enable=true
           server=**************
           port=8888
        */
        if ((config.sys.subiao.enable) && (spProt == nullptr) && (spTrans == nullptr)) {
            std::shared_ptr<ProtSubiao> spSubiao = std::make_shared<ProtSubiao>(config.sys.subiao.ignore_spd, config.sys.subiao.associate_ch);
            spProt = spSubiao;
            if (spProt != nullptr) {
                spProt->start();
                spTrans = std::make_shared<TcpIpTrans>(spProt, config.sys.subiao.server.c_str(), config.sys.subiao.port);
                if (spTrans != nullptr) {
                    spTrans->start();
                    AlgoManager & am = AlgoManager::getInstance();
                    am.addObserver("subiao_adas", spSubiao.get());
                }
            }
        }
    }
    return 0;
}
