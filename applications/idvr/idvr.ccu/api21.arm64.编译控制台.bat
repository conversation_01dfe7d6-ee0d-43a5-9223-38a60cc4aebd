@echo off

set HOME=C:\local\src\armcc\bin
set BIN=%HOME%\msys1.0\bin;%HOME%\api21-arm64\bin
set path=%BIN%;%path%

@doskey g++=aarch64-linux-android-clang++ -fPIE -fPIC -pie -static-libstdc++ $*
@doskey gcc=aarch64-linux-android-clang -fPIE -fPIC -pie -static-libstdc++ $*
@doskey ld=aarch64-linux-android-ld $*
@doskey ar=aarch64-linux-android-ar $*
@doskey as=aarch64-linux-android-as $*
@doskey readelf=aarch64-linux-android-readelf $*
@doskey ranlib=aarch64-linux-android-ranlib $*
@doskey strip=aarch64-linux-android-strip $*
@doskey c++filt=aarch64-linux-android-c++filt $*
@doskey nm=aarch64-linux-android-nm $*
@doskey objdump=aarch64-linux-android-objdump $*

set CXX=aarch64-linux-android-clang++ -fPIE -fPIC -pie -static-libstdc++
set CC=aarch64-linux-android-clang -fPIE -fPIC -pie
set LD=aarch64-linux-android-ld
set AR=aarch64-linux-android-ar
set AS=aarch64-linux-android-as
set READELF=aarch64-linux-android-readelf
set RANLIB=aarch64-linux-android-ranlib
set STRIP=aarch64-linux-android-strip
set NM=aarch64-linux-android-nm
set OBJDUMP=aarch64-linux-android-objdump

C:\Windows\System32\cmd.exe
pause