#ifndef __CONTROLLER_H__
#define __CONTROLLER_H__
#include <memory>
#include <vector>
#include "mystd.h"
#include "config.h"
#include "CRingBuf.h"
#include "service.h"
#include "imu_client.h"
#include "io.service.h"
#include "McuMessage.h"
#include "base/jttType.h"

// 通信控制器
class ComController
{
    public:
        typedef std::shared_ptr<ComController> Ptr;

        ComController(Config& config);

        bool init();
        int start();

        void stop();
        ComService::Ptr operator[](int idx)
        {
            MY_SPINLOCK_X(lock);
            return apps[idx];
        }
        
        ComService::Ptr operator[](my::string & ip)
        {
            MY_SPINLOCK_X(lock);

            for (int i = 0; i < MAX_CLIENTS; i++) {
                for (int j = 0; j < ARRAY_SIZE(apps[i]->si[0].ip); j++) {
                    if (apps[i]->si[0].ip[j] == ip) {
                        return apps[i];
                    }
                }
            }

            return NULL;
        }

    private:
        my::spinlock lock;
        Config& config;
        ComService::Ptr apps[MAX_CLIENTS]; // 管理多app实例

};


// 通信服务守护
class ServiceGuard : public my::thread
{
        friend class StateController;
    public:
        void  reload_service(ComService::Ptr com_service, const ServerInfo * si);

    protected:
        void run();
        ServiceGuard()
        {
        }

    private:
        my::spinlock lock;
        std::vector<std::pair<ComService::Ptr, const ServerInfo*> > queue;
};

// 侧翻检测
class RollOver {
public:
    #define   HDTODU                         (57.29578)  
    typedef struct{
        char calibFlag;     //首次初始化标志，求模以及初始设备角度
        char sensor_num;
        int x_aver;       //首次初始化均值
        int y_aver;       //首次初始化均值
        int z_aver;       //首次初始化均值
        short dx;       //单位度，初始
        short dy;       //单位度，初始
        short dz;       //单位度，初始
        short scalefactor;   // 比例因子,静态模
        short x;       //实时值  
        short y;       //实时值
        short z;       //实时值
    }sensor_calib_t;
public:
    RollOver()
    {
        memset(&mSensor, 0, sizeof(mSensor));
    }
    
    ~RollOver()
    {

    }
    
    uint8_t CheckTurnOver(const IdvrImuDataT *pImu, short & rollAngle);
    int sensorCalib(const IdvrImuDataT *pImu);
    
private:
    sensor_calib_t  mSensor;
    uint8_t         LTurnState = 0;           // 翻转状态.  
    uint8_t         LTurnResult = 0;          // 本次翻转判定结果
    uint16_t        LTurnCount = 0;           // 翻转计数.
    uint16_t        LTurnOffCount = 0;        // 不翻转计数.
};

class CrashDet
{
    public:
        CrashDet() {
        }
        ~CrashDet() {
        }

        bool detect(const IdvrImuDataT *pImu, const collisionParam_t   * param) {
            bool ret = false;
            if (!mbInit) {
                gx = pImu->gyro[0];
                gy = pImu->gyro[1];
                gx = pImu->gyro[2];
                mbInit = true;
            } else if (mImuTs.elapsed() >= param->set.tm_ms * 2) {
                short dx = abs(pImu->gyro[0] - gx);
                short dy = abs(pImu->gyro[1] - gy);
                short dz = abs(pImu->gyro[2] - gz);

                gx = pImu->gyro[0];
                gy = pImu->gyro[1];
                gx = pImu->gyro[2];

                if ((dx > param->set.acc) ||
                    (dy > param->set.acc) ||
                    (dz > param->set.acc)){
                    logd("Crash detected! dx %d, dy %d, dz %d", dx, dy, dz);
                    return true;
                }
            }
            mImuTs = my::timestamp::now();
            return ret;
        }
    private:
        bool mbInit = false;
        short gx = 0, gy = 0, gz = 0;
        my::timestamp mImuTs;
};
// 状态控制器
class StateController
    : public my::BufferQueue<std::shared_ptr<struct STAT_DATA>>
    , public IInputMsgHandler
    , public my::thread {
public:
        typedef std::shared_ptr<StateController> Ptr;
        StateController(ComController::Ptr & p);
        int start();
        void stop();
        int push(std::shared_ptr<struct STAT_DATA> sp)
        {
            return my::BufferQueue<std::shared_ptr<struct STAT_DATA>>::push(sp, 100, erase_condition);
        }
        void updateExtendAlarm();
        static void imu_proc(std::vector<GsensorData>& vector);
public:
        virtual bool onInputIdvrMsg(std::shared_ptr<struct STAT_DATA> & sp);
        
protected:
        void run();

private:
        static bool erase_condition(std::shared_ptr<struct STAT_DATA> & sp)
        {
            my::uint64 cur = my::timestamp::utc_milliseconds();

            if ((cur - sp->ts) > 1000) {
                logd("erase timeout msg!");
                return true;
            }

            return false;
        }
        virtual void dumpBufferQueueMsg(const std::shared_ptr<struct STAT_DATA> & sp, const char * prefix) {
            loge("%s, %s", prefix, sp->dump());
        }
        void SOS(IdvrIOStat * iostat);
        void PFM(uint8_t doorBits);
        bool dealIOStat(IdvrIOStat * iostat);
        bool canDataProc(int canIdx, CanData & data);
        bool gpsDataProc(const IdvrLocation& loc);
        int getCamStatus(int total_ch);
        int getCamOcclusionStatus(int total_ch);
        unsigned short getStorageStatus();
public:
        ServiceGuard sg;
        ComController::Ptr mpComCtlr;

        /* chuanbiao ext */
        RollOver rollCheck;
        CrashDet mCrashDet;
private:
        bool mbLastSOS = false;
        my::timestamp mLastSOS = 0;
        my::timestamp mLastRecEvtBits = 0;
        time_t mLastCanLossRpt = 0;
        time_t mLastCanLossAudio = 0;
        time_t mLastLoadSpchTm = 0;
        time_t mLastTmTripOsdSet = 0;
        int mColorTripOsd = -1;
        int mFullLoad = -1;

        IOStatus mLastIOStat;
        struct IdvrLocation mLoc;

        std::mutex mImuMutex;
        IMUClient *  mImuHdlr;
        bool        mbImuInited;
        std::map<unsigned long, IdvrImuDataT> mImuDataMap;
private:
        my::timestamp mUpdateExtendAlarmTs;
        int mStorageFailCnt = 0;
        int mediaIdx = 1;

        bool mbCpSosMedia = false;

        bool mTurnover = false;

        bool    mbUseCanDoorSig = false;
        uint8_t mLastDoorBits = 0;
        std::map<int32_t, std::pair<my::timestamp/*关门时间*/, bool/*是否已查询*/>> mLastDrPfmTsMap;
    };
#endif // !__CONTROLLER_H__



