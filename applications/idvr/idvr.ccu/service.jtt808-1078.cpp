#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <fcntl.h>
#include <termios.h>
#include <errno.h>
#include <string.h>

#include "json.hpp"
#include "manager.h"
#include "CmdListener.h"
#include "prot.config.helper.h"

#include "service.jtt808-1078.h"
using json = nlohmann::json;


ServiceJtt808_1078::ServiceJtt808_1078(const char* tag, const char * sim, const char * protSubType)
{
    if (!strcmp(protSubType, "chongqing")) {
        client = make_shared<JttTcpClient_chongqing>(sim, tag);
        second_client = make_shared<JttTcpClient_chongqing>(sim, tag);
    } else if (!strcmp(protSubType, "guangdong")) {
        client = make_shared<JttTcpClient_guangdong>(sim, tag);
        second_client = make_shared<JttTcpClient_guangdong>(sim, tag);
    } else if (!strcmp(protSubType, "nanjing")) {
        client = make_shared<JttTcpClient_NanJing>(sim, tag);
        second_client = make_shared<JttTcpClient_NanJing>(sim, tag);
    } else if (!strcmp(protSubType, "huoyun")) {
        client = make_shared<JttTcpClient_huoyun>(sim, tag);
        second_client = make_shared<JttTcpClient_huoyun>(sim, tag);
    } else if (!strcmp(protSubType, "henanTelecom")) {
        client = make_shared<JttTcpClient_HeNanTelecom>(sim, tag);
        second_client = make_shared<JttTcpClient_HeNanTelecom>(sim, tag);
    } else if (!strcmp(protSubType, "TTX")) {
        client = make_shared<JttTcpClient_TTX>(sim, tag);
        second_client = make_shared<JttTcpClient_TTX>(sim, tag);
    } else if (!strcmp(protSubType, "ningbozhatu")) {
        client = make_shared<JttTcpClient_NingBoZhaTu>(sim, tag);
        second_client = make_shared<JttTcpClient_NingBoZhaTu>(sim, tag);
    } else if (!strcmp(protSubType, "ZTC_MiniEye")) {
        client = make_shared<JttProtZTCMiniEye>(sim, tag);
        second_client = make_shared<JttProtZTCMiniEye>(sim, tag);
    } else if (!strcmp(protSubType, "hualanTec")) {
        client = make_shared<JttTcpClient_hualanTec>(sim, tag);
        second_client = make_shared<JttTcpClient_hualanTec>(sim, tag);
    } else if (!strcmp(protSubType, "sichuan")) {
        client = make_shared<JttTcpClient_sichuan>(sim, tag);
        second_client = make_shared<JttTcpClient_sichuan>(sim, tag);
    } else if (!strcmp(protSubType, "lvcheng")) {
        client = make_shared<JttTcpClient_LvCheng>(sim, tag);
        second_client = make_shared<JttTcpClient_LvCheng>(sim, tag);
    } else if (!strcmp(protSubType, "wuxiTianYi")) {
        client = make_shared<JttTcpClient_wuxiTianYi>(sim, tag);
        second_client = make_shared<JttTcpClient_wuxiTianYi>(sim, tag);
    } else if (!strcmp(protSubType, "jiangsuPuHuo")) {
        client = make_shared<JttTcpClient_jiangsuPuHuo>(sim, tag);
        second_client = make_shared<JttTcpClient_jiangsuPuHuo>(sim, tag);
//    } else if (!strcmp(protSubType, "nanjingSD_DATA")) {
//        client = make_shared<JttTcpClient_NanJing_Data>(sim, tag);
//        second_client = make_shared<JttTcpClient_NanJing_Data>(sim, tag);
    } else if (!strcmp(protSubType, "zhoushan")) {
        client = make_shared<JttTcpClient_ZhouShan>(sim, tag);
        second_client = make_shared<JttTcpClient_ZhouShan>(sim, tag);
    } else if (!strcmp(protSubType, "zfbsd")) {
        client = make_shared<JttTcpClient_zfbsd>(sim, tag);
        second_client = make_shared<JttTcpClient_zfbsd>(sim, tag);
    } else if (!strcmp(protSubType, "xunjian")) {
        client = make_shared<JttTcpClient_XunJian>(sim, tag);
        second_client = make_shared<JttTcpClient_XunJian>(sim, tag);
    } else if (!strcmp(protSubType, "chengwei")) {
        client = make_shared<JttTcpClient_chengwei>(sim, tag);
        second_client = make_shared<JttTcpClient_chengwei>(sim, tag);
    } else if (!strcmp(protSubType, "backroadINSP")) {
        client = make_shared<JttTcpClient_BackroadINSP>(sim, tag);
        second_client = make_shared<JttTcpClient_BackroadINSP>(sim, tag);
    } else if (!strcmp(protSubType, "xiashi")) {
        client = make_shared<JttTcpClient_XiaShi>(sim, tag);
        second_client = make_shared<JttTcpClient_XiaShi>(sim, tag);
    // } else if (!strcmp(protSubType, "suzhouJinLong")) {
    //     client = make_shared<JttTcpClient_suzhouJinLong>(sim, tag);
    //     second_client = make_shared<JttTcpClient_suzhouJinLong>(sim, tag);
    } else if (!strcmp(protSubType, "udas")) {
        client = make_shared<JttTcpClient_udas>(sim, tag);
        second_client = make_shared<JttTcpClient_udas>(sim, tag);
    } else if(!strcmp(protSubType,"sanyi")){
        client = make_shared<JttTcpClient_sanyi>(sim, tag);
        second_client = make_shared<JttTcpClient_sanyi>(sim, tag);
    } else if (!strcmp(protSubType, "crosswalkDecel")) {
        client = make_shared<JttTcpClient_crosswalkDecel>(sim, tag);
        second_client = make_shared<JttTcpClient_crosswalkDecel>(sim, tag);
    } else {
        client = make_shared<JttTcpClient>(sim, tag);
        second_client = make_shared<JttTcpClient>(sim, tag);
    }
    if (!access("/data/cp2sdcard", R_OK)) {
        mAtCp2disk = new JttAtCp2Disk();
        mAtCp2disk->start();
        client->regAtCper(mAtCp2disk);
        second_client->regAtCper(mAtCp2disk);
    }

    mAtTskMgr = new JttAtTskMgr();
    mAtTskMgr->start();
    client->regAtSnder(mAtTskMgr);
    second_client->regAtSnder(mAtTskMgr);

    /* 设置数据存储路径 */
    std::string path = idvrGetDataPath();
    client->setDataPath(path);
    second_client->setDataPath(path);
}

ServiceJtt808_1078::~ServiceJtt808_1078()
{
    stop();
}

// 启动服务(可能是客户端, 也可能是服务端)
int ServiceJtt808_1078::start(const ServerInfo& si)
{
    InputService & is = InputService::getInstance();
    Config & config = is.getConfig();
    logi("[ServiceJtt808_1078::start] id=[%s], auth=[%s], sim=[%s], addr0=[%s], addr1=[%s], add2=[%s]",
         si.id.c_str(), si.auth.c_str(), config.sys.sim.c_str(),
         si.get_main_server().c_str(),
         si.get_backup_server().c_str(),
         si.get_secondary_server().c_str());
    this->si[0] = this->si[1]= si;
    this->si[1].auth = "";
    client->set_srv_info(&this->si[0]);
    client->set_main_server();
    int ret = client->start(si.ip[0], si.port[0], si.ip[1], si.port[1]);
    LOG_RETURN_IF(ret != 0, ret, loge, "[ServiceJtt808_1078::start] failed to start main client, ret=[%d]", ret);
    second_client->set_srv_info(&this->si[1]);

    if (!si.ip[2].empty() && si.ip[2] != "0.0.0.0" && si.port[2] != 0) {
        return second_client->start(si.ip[2], si.port[2]);
    }

    return 0;
}

// 重新加载地址
int ServiceJtt808_1078::reload(const ServerInfo& si)
{
    int ret = client->reload(si.ip[0].c_str(), si.port[0], si.ip[1].c_str(), si.port[1]);
    LOG_IF(ret != 0, loge, "failed to reload main client, ret=[%d]", ret);
    ret = second_client->reload(si.ip[2].c_str(), si.port[2]);
    LOG_IF(ret != 0, loge, "failed to reload secondary client, ret=[%d]", ret);
    return ret;
}

// 停止服务
void ServiceJtt808_1078::stop()
{
    mAtTskMgr->stop();
    second_client->stop();
    client->stop();
}

my::string ServiceJtt808_1078::get_type()
{
    return "jtt808-1078";
}



