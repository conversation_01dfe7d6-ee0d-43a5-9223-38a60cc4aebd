#ifndef __SMS_RECIEVE_HANDLER_H__
#define __SMS_RECIEVE_HANDLER_H__
#include "mystd.h"
#include "iPhone.h"

class SmsRecieveHandler : public iPhone::Impl
                                  , public my::Singleton<SmsRecieveHandler>
{
    friend class my::Singleton<SmsRecieveHandler>;
public:
    struct stSmsInfo {
        my::string                    time;            
        my::string                    telNum;
        my::string                    msg;
    };

    void onRecvedShortMsg(const char *phoneNum, const char *datetime, const char *text) override;
    bool getOneSms(my::string &phoneNum, my::string &msg);
private:
        SmsRecieveHandler();
        //从尾部删除
        bool popOneSms(stSmsInfo &info)
        {
            MY_SPINLOCK_X(mSmsLock);
            if (mSmsList.size() > 0) {
                info = mSmsList.back();
                mSmsList.pop_back();
                return true;
            }
            return false;
        }

        // 往头部插入
        void insertOneSms(stSmsInfo &info)
        {
            MY_SPINLOCK_X(mSmsLock);
            mSmsList.push_front(info);
        }
        uint32_t sizeSms()
        {
            MY_SPINLOCK_X(mSmsLock);
            return mSmsList.size();
        }
private:        
        my::spinlock                    mSmsLock;
        std::deque<stSmsInfo>           mSmsList;
};
#endif

