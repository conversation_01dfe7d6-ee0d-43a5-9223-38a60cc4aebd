#include "sms.recieve.handler.h"

SINGLETON_STATIC_INSTANCE(SmsRecieveHandler);

SmsRecieveHandler::SmsRecieveHandler()
{
    iPhone::getInstance()->init(this);
}

void SmsRecieveHandler::onRecvedShortMsg(const char *phoneNum, const char *datetime, const char *text)
{
    loge("%s recieve from %s sms:%s!\n", datetime, phoneNum, text);
    SmsRecieveHandler::stSmsInfo info;
    info.telNum = phoneNum;
    info.time = datetime;
    info.msg = text;
    insertOneSms(info);
}

bool SmsRecieveHandler::getOneSms(my::string &phoneNum, my::string &msg)
{
    SmsRecieveHandler::stSmsInfo sms;
    if (popOneSms(sms)) {
        phoneNum = sms.telNum;
        msg = sms.msg;
        return true;
    } else {
        return false;
    }
}

