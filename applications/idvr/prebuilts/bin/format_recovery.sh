#!/system/bin/sh

function ALOGD() {
    echo "`date` format_recovery.sh : $1"
    # $LOG_BIN "\"$1\""
}

function runCmd() {
    ALOGD "$1"
    CMD_RES_STR=`$1`
    if [ "$CMD_RES_STR" != "" ]; then
        ALOGD "$CMD_RES_STR"
    fi
}

function usage() {
    echo "recovery  格式化灾备设备"
    echo "          用法：format_recovery.sh recovery [log]"
}

if [ "$1" == "-h" ]; then
    usage
    exit 0
else
    diskType=$1
    resultFile=$2
    if [ "$diskType" == "" ]; then
        usage
        exit -1
    fi
fi

devPath=""
dev1Path=""
dev2Path=""

MKFS_FAT_BIN="falloc.mkfs.fat"
FALLOCATE_TOOL_BIN="fallocate_tool"
EXT4_CHECK_BIN="e2fsck"
LOG_BIN="logger -p d -t \"format_fallocate.sh\""

ERROR=255
SUCCESS=0

CMD_RES_STR=""

function sayHello() {
    if [ "$diskType" == "recovery" ]; then
        runCmd "tts -queue \"请注意,灾备设备格式化预计需要1到3分钟,请勿关闭电源\""
    else
        return $ERROR
    fi

    return $SUCCESS
}

function sayBeybey() {
    if [ "$diskType" == "recovery" ]; then
        runCmd "tts -queue \"灾备设备格式化$1\""
    else
        return $ERROR
    fi

    return $SUCCESS
}

function stopService() {
    # runCmd "stop idvr_media"
    runCmd "stop recorder_recovery"
    runCmd "stop mdisks"
    runCmd "sleep 1"

    runCmd "umount /tmp/media_rw/rdisk0"
    runCmd "umount /tmp/media_rw/rdisk1"
    runCmd "umount /tmp/media_rw/rdisk2"
    runCmd "umount /tmp/media_rw/rdisk3"
    runCmd "umount /tmp/media_rw/rdisk4"

    runCmd "sleep 1"
}

function startService() {
    runCmd "start mdisks"
    runCmd "start recorder_recovery"
    # runCmd "start idvr_media"
}

function checkRet() {
    if [ "$?" == "$ERROR" ]; then
        ALOGD "excutor failt !"
        startService
        sayBeybey "失败"
        runCmd "echo error > $resultFile"
        exit $ERROR
    fi
}

function status() {
    runCmd "echo $1 > $resultFile"
}

function findDev() {
    if [ "$diskType" == "recovery" ]; then
        devPath=`ls /tmp/rldisk -l | awk -F " " '{print $11}'`
        dev1Path=$devPath"1"
        dev2Path=$devPath"2"
    else
        ALOGD "unkown disk type: $diskType !"
        return $ERROR
    fi

    if [ "$devPath" == "" ] || [ ! -e "$devPath" ]; then
        ALOGD "device path not exist !"
        return $ERROR
    fi

    ALOGD "dev: $devPath, dev1: $dev1Path, dev2: $dev2Path"

    return $SUCCESS
}

function partition() {
    local tryCnt=0
    runCmd "$FALLOCATE_TOOL_BIN -clearPartition $devPath"
    runCmd "echo 1 > /sys/devices/soc0/soc/soc:sdmmc/sdmmc0_reset"
    while true; do
        if [ ! -e "$dev1Path" ] && [ ! -e "$dev2Path" ]; then
            break
        fi

        if [ $tryCnt -eq 5 ]; then
            ALOGD "clear partition error !"
            return $ERROR
        fi

        let tryCnt=$tryCnt+1
        sleep 1
    done

    runCmd "$FALLOCATE_TOOL_BIN -setPartition $devPath"
    runCmd "echo 1 > /sys/devices/soc0/soc/soc:sdmmc/sdmmc0_reset"

    tryCnt=0
    while true; do
        if [ -e "$dev1Path" ] && [ -e "$dev2Path" ]; then
            break
        fi

        if [ $tryCnt -eq 5 ]; then
            ALOGD "set partition $dev1Path error !"
            return $ERROR
        fi

        let tryCnt=$tryCnt+1
        sleep 1
    done

    return $SUCCESS
}

function formatExt4() {
    local tryCnt=0
    while true; do
        runCmd "$FALLOCATE_TOOL_BIN -formatExt4 $dev2Path"
        runCmd "$EXT4_CHECK_BIN -y $dev2Path | grep \"Bad\""
        local checkRes="$CMD_RES_STR"
        if [ "$checkRes" == "" ]; then
            ALOGD "format ext4 success"
            break
        fi

        let tryCnt=$tryCnt+1
        if [ $tryCnt -eq 2 ]; then
            ALOGD "format ext4 error !"
            return $ERROR
        fi
    done

    return $SUCCESS
}

function fallocate() {
    local tryCnt=0
    while true; do
        runCmd "$MKFS_FAT_BIN -R 128 -s 128 -F 32 $dev1Path"
        runCmd "$FALLOCATE_TOOL_BIN -fallocate $dev1Path"
        runCmd "$FALLOCATE_TOOL_BIN -check $dev1Path | grep \"success\""
        local checkRes="$CMD_RES_STR"
        if [ "$checkRes" != "" ]; then
            ALOGD "fallocate success"
            break
        fi

        let tryCnt=$tryCnt+1
        if [ $tryCnt -eq 2 ]; then
            ALOGD "fallocate error !"
            return $ERROR
        fi
    done

    return $SUCCESS
}

#--------------------------------------

sayHello

status "正在初始化 ..."

runCmd "mkdir -p `dirname "$resultFile"`"

ALOGD "-------- stopService ----------"
stopService

ALOGD "-------- findDev ----------"
findDev
checkRet "$?"

status "删除所有分区 ..."

ALOGD "-------- partition ----------"
partition
checkRet "$?"

status "分区1格式化中 ..."

ALOGD "-------- formatExt4 ----------"
formatExt4
checkRet "$?"

status "分区2预分配中 ..."

runCmd "tts -queue \"格式化中,请勿关闭电源\""

ALOGD "-------- fallocate ----------"
fallocate
checkRet "$?"

ALOGD "-------- startService ----------"

runCmd "setprop persist.minieye.record_mode fallocate"
startService

sayBeybey "成功"

status "格式化成功"

echo "success"

exit 0
