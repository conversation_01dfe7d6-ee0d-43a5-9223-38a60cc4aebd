#!/system/bin/sh
ntp_server_cfg_file=/data/time_server.txt
log_file="/tmp/ntp.log"
reconnect_count=5
echo start ntp_server > $log_file


function check_time()
{
    hwclock_year=$(hwclock -r|tr -s ' ' | cut -d ' ' -f 5)
    let delta_year=$hwclock_year-"2017"
    if [ $delta_year -gt 0 ];then
        hwclock -us
    fi
    echo "RTC time is:$hwclock_year"
}

function wait_ipaddr()
{
    ip_addr=`ifconfig usb0 | grep inet`
    while [ -z "$ip_addr" ]
    do
        sleep 1
        ip_addr=`ifconfig usb0 | grep inet`
    done
    sleep 1
    echo netcard usb0 init success
}

function update_clock()
{
    if [ ! -f "$ntp_server_cfg_file" ]; then
        ntp_server=https://setup.minieye.cc/special/time/unix
        reconnect_count=600
        echo ntp_server use default
    else
        ntp_server=$(cat $ntp_server_cfg_file)
        echo ntp_server use $ntp_server
    fi

    minieye_time=$(curl -m 5 -kf $ntp_server 2>>$log_file)
    while [[ $? != 0 && $reconnect_count -ne 0 ]]
    do
        sleep 2
        echo reconnect $ntp_server
        let reconnect_count--
        minieye_time=$(curl -m 5 -kf $ntp_server 2>>$log_file)
    done

    if [ -z "$minieye_time" ];then
        echo Fetch minieye time NG
    else
        echo Fetch minieye time OK
        minieye_epoch=$(echo $minieye_time | cut -d '.' -f 1)
        echo minieye.cc $minieye_time $minieye_epoch

        #2018年09月25日
        let delta_epoch=$minieye_epoch-"1537880000"
        if [ $delta_epoch -gt 0 ]; then
            echo minieye server time OK
            busybox date -s "@${minieye_epoch}"
            hwclock -wu
        else
            echo minieye server time NG
        fi
    fi
}

function default_ntpclient()
{
    ntp_servers="\
        1.cn.pool.ntp.org
        2.cn.pool.ntp.org
        0.asia.pool.ntp.org
        0.europe.pool.ntp.org
        time.windows.com
        "

    for retry in $(seq 0 100);do
        ntp_ok="0"
        for ns in $ntp_servers;do
            echo "Try $ns"
            timeout 5 ntpclient -s -t -i 10 -h $ns
            if [ $? != 0 ];then
                echo "$ns failed"
            else
                echo "$ns OK"
            ntp_ok="1"
                break
            fi
        done
        if [[ "$ntp_ok" == "1" || $delta_year -gt 0 ]];then
             break;
        else
            sleep 5
        fi
    done
}

function log()
{
    echo -e "$1" |busybox tee -a $log_file
}


log "`check_time`"
log "`wait_ipaddr`"
log "`update_clock`"
#log "`default_ntpclient`"

