#!/system/bin/sh

FALLOCATE_TOOL_BIN="/system/bin/fallocate_tool"

part1_name=$1"1"
part2_name=$1"2"

part1_total=`df | grep $part1_name | busybox awk -F " " '{print $2}'`
part1_used=`df | grep $part1_name | busybox awk -F " " '{print $3}'`

part2_total=`df | grep $part2_name | busybox awk -F " " '{print $2}'`
part2_used=`df | grep $part2_name | busybox awk -F " " '{print $3}'`

if [ "$1" == "disk" ]; then
    dev_node=`ls /tmp/ldisk -l | busybox awk -F " " '{print $11}'`
    dev_part1=$dev_node"1"
    dev_part2=$dev_node"2"

    chkdev=`busybox fdisk -l $dev_node`

elif [ "$1" == "sdcard" ]; then
    dev_node=`ls /tmp/lsdcard -l | busybox awk -F " " '{print $11}'`
    dev_part1=$dev_node"p1"
    dev_part2=$dev_node"p2"

    chkdev="sdcard"
else
    exit 0
fi

json_str=$json_str"\"part1Total\":\"$part1_total\""
json_str=$json_str",\"part1Used\":\"$part1_used\""
json_str=$json_str",\"part2Total\":\"$part2_total\""
json_str=$json_str",\"part2Used\":\"$part2_used\""

#0 已挂载，1挂载中，2未挂载，3格式化中，4未格式化，5分区中，6未分区，7检测不到硬件

if [ ! -e "$dev_node" ] || [ "$chkdev" == "" ]; then
    json_str=$json_str",\"status\":\"7\""
else
    if [ ! -e "$dev_part1" ] || [ ! -e "$dev_part2" ]; then
        json_str=$json_str",\"status\":\"6\""
    else
        if [ "`$FALLOCATE_TOOL_BIN -check $dev_part1 | grep \"success\"`" == "" ]; then
            json_str=$json_str",\"status\":\"4\""
        else
            if [ "$part1_total" == "" ] || [ "$part1_used" == "" ]; then
                json_str=$json_str",\"status\":\"2\""
            else
                json_str=$json_str",\"status\":\"0\""
            fi
        fi
    fi
fi

echo "{ $json_str }"
