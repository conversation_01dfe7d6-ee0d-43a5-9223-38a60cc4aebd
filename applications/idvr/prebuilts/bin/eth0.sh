#!/system/bin/sh

LOG_TAG="eth0.sh"
ETH_IP_SET=("***************" "*************")
ETH_IP_Custom=`getprop persist.minieye.eth0_ip`
ETH_GW_Custom=`getprop persist.minieye.eth0_gw`

ETH_NID="103"
ETH_IF_NAME="eth0"

function ALOGD()
{
	msg="$1"
	echo "$msg"
	logger -t $LOG_TAG "$msg"
}

#$1 - inetface, eth0
#$2 - ip
#$3 - index
function doConfig()
{
	SUBNET="${2%.*}.0/24"
    TGT_IP="$2"
	#CFGED=`ifconfig | grep $1 -A 2 | grep "$2"`
	ETH_NAME=$1
	if [ $3 -gt 0 ] ; then
		ETH_NAME="$ETH_IF_NAME:""$3"
	fi
    if [ ! -z ${ETH_IP_Custom} ]; then
        NETSEG="${2%.*}"
        if [ "${NETSEG}" == "${ETH_IP_Custom%.*}" ]; then
            TGT_IP="${ETH_IP_Custom}"
            ETH_IP_Custom=""
        fi
    fi
	echo "$ETH_NAME, cfg=$CFGED"
	if [ "${CFGED}" == "" ] ; then
		ifconfig ${ETH_NAME} ${TGT_IP} netmask *************
		#ip rule add from ${SUBNET} table ${ETH_IF_NAME}
		#ip rule add to   ${SUBNET} table ${ETH_IF_NAME}
		#ip route add ${SUBNET} via ${TGT_IP} table eth0
		ALOGD "${ETH_NAME} = ${TGT_IP}"
	fi
}
function config_eth0()
{
	idx=0
	for ip in ${ETH_IP_SET[@]}
	do
		doConfig eth0 ${ETH_IP_SET[$idx]} ${idx}
		let idx=idx+1
	done
    if [ ! -z ${ETH_IP_Custom} ]; then
        doConfig eth0 ${ETH_IP_Custom} ${idx}
    fi
    if [ ! -z ${ETH_GW_Custom} ]; then
        route add default gw ${ETH_GW_Custom}
    fi
}
function unConfig()
{
	SUBNET="${2%.*}.0/24"
	ETH_NAME=$1
	if [ $3 -gt 0 ] ; then
		ETH_NAME="$1:$3"
	fi
	#ip rule del from ${SUBNET} table ${ETH_IF_NAME}
	#ip rule del to   ${SUBNET} table ${ETH_IF_NAME}
	#ip route del ${SUBNET} via $2 table eth0
}
function unConfig_eth0()
{
	idx=0
	for ip in ${ETH_IP_SET[@]}
	do
		unConfig eth0 ${ETH_IP_SET[$idx]} ${idx}
		let idx=idx+1
	done
}

function ifup()
{
	#stop c4
	setprop rw.minieye.eth0 true
	ALOGD "ifup eth0"
	#ndc network create $ETH_NID
	#ndc network interface add ${ETH_NID} ${ETH_IF_NAME}
	config_eth0
	#ndc resolver setnetdns eth0 "" *************** *******
	#start c4
}

function ifdown()
{
#	stop c4
	setprop rw.minieye.eth0 false
	ALOGD "ifdown eth0"
	unConfig_eth0
	#ndc network destroy $ETH_NID
	#ndc resolver clearnetdns eth0
#	start c4
}

function usage()
{
	echo "Usage: $0 up|down"
}

action=$1
if [[ "up" == $action ]];then
	ifup
elif [[ "down" == $action ]];then
	ifdown
else
	usage
fi

