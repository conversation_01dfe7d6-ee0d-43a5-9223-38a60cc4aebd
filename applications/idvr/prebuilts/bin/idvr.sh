#!/system/bin/sh

board_id_prop="persist.minieye.board.name"
config_file="/system/etc/config.ini"

mkdir -p /data/minieye/idvr/data
mkdir -p /data/minieye/idvr/etc
mkdir -p /data/minieye/idvr/mlog
mkdir -p /system/algo

if [ ! -f /data/minieye/idvr/etc/config.ini ]; then
    cp ${config_file} /data/minieye/idvr/etc/.config.ini;
    mv /data/minieye/idvr/etc/.config.ini /data/minieye/idvr/etc/config.ini;
    sync;
fi

if [ ! -f /data/minieye/idvr/etc/expand.json ]; then
    cp /system/etc/expand.json /data/minieye/idvr/etc/.expand.json;
    mv /data/minieye/idvr/etc/.expand.json /data/minieye/idvr/etc/expand.json;
    sync;
fi

start idvr_config

