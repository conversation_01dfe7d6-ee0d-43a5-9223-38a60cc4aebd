#!/system/bin/sh
LCFG=`grep ui.string.conf -r /data/minieye/idvr/etc/config.ini|grep en`
LANG=zh
if [ -z "$LCFG" ]; then
	        echo default_zh
	else
		        LANG=en
fi

touch /tmp/.fmt
function ALOGD() {
    echo "`date` format_fallocate.sh : $1"
    # $LOG_BIN "\"$1\""
}

function runCmd() {
    ALOGD "$1"
    CMD_RES_STR=`$1`
    if [ "$CMD_RES_STR" != "" ]; then
        ALOGD "$CMD_RES_STR"
    fi
}

function usage() {
    echo "ln        指定节点格式化"
    echo "          用法：format_fallocate.sh ln sda disk [log]"
    echo "          用法：format_fallocate.sh ln mmcblk1 sdcard [log]"
    echo "disk      格式化已挂载的盘"
    echo "          用法：format_fallocate.sh disk [log]"
    echo "sdcard    格式化已挂载的sdcard"
    echo "          用法：format_fallocate.sh sdcard [log]"
}

if [ "$1" == "ln" ]; then
    devNode=$2
    diskType=$3
    resultFile=$4
    if [ "$devNode" == "mmcblk0" ]; then
        ALOGD "the devices node \"$devNode\" is invalid !"
        ALOGD "----------------------------------------"
        usage
        exit -1
    fi

    if [ "$devNode" != "" ]; then
        if [ "$diskType" == "disk" ]; then
            runCmd "rm /tmp/ldisk"
            runCmd "ln -s /dev/$devNode /tmp/ldisk"
        elif [ "$diskType" == "sdcard" ]; then
            runCmd "rm /tmp/lsdcard"
            runCmd "ln -s /dev/$devNode /tmp/lsdcard"
        else
            usage
            exit -1
        fi
    fi
elif [ "$1" == "-h" ]; then
    usage
    exit 0
else
    diskType=$1
    resultFile=$2
    if [ "$diskType" == "" ]; then
        usage
        exit -1
    fi
fi

devPath=""
dev1Path=""
dev2Path=""

MKFS_FAT_BIN="falloc.mkfs.fat"
FALLOCATE_TOOL_BIN="fallocate_tool"
EXT4_CHECK_BIN="e2fsck"
LOG_BIN="logger -p d -t \"format_fallocate.sh\""

ERROR=255
SUCCESS=0

CMD_RES_STR=""
function countTick() {
    tick=1
    while [ -f /tmp/.fmt ]
    do
        media.cmd.setUiTips $tick 16777215 1 65280
        let tick=tick+1
        sleep 1
    done
}
function sayHello() {
    if [ "$LANG" == "zh" ] ; then
        if [ "$diskType" == "disk" ]; then
            runCmd "tts -queue \"请注意,硬盘格式化预计需要1到3分钟,请勿关闭电源\""
        elif [ "$diskType" == "sdcard" ]; then
            runCmd "tts -queue \"请注意,SD卡格式化预计需要1到3分钟,请勿关闭电源\""
        else
            return $ERROR
        fi
    else
        if [ "$diskType" == "disk" ]; then
            media.cmd.setUiTips disk 16777215 3 65280
        elif [ "$diskType" == "sdcard" ]; then
            media.cmd.setUiTips tfcard 16777215 3 65280
        else
            media.cmd.setUiTips type.error! 16777215 3 16711680
            return $ERROR
        fi
        media.cmd.setUiTips formatting... 16777215 2 65280
    fi
    return $SUCCESS
}

function sayByeBye() {
    rm /tmp/.fmt
    if [ "$LANG" == "zh" ] ; then
        if [ "$diskType" == "disk" ]; then
            runCmd "tts -queue \"硬盘格式化$1\""
        elif [ "$diskType" == "sdcard" ]; then
            runCmd "tts -queue \"SD卡格式化$1\""
        else
            return $ERROR
        fi
    else
        if [ "$1" == "成功" ]; then
            media.cmd.setUiTips format.succ! 16777215 2 65280
        elif [ "$1" == "失败" ]; then
            media.cmd.setUiTips format.fail! 16777215 2 16711680
        else
            media.cmd.setUiTips type.error! 16777215 2 16711680
            return $ERROR
        fi
    fi
    return $SUCCESS
}

function stopService() {
    # runCmd "stop idvr_media"
    runCmd "stop idvr_ccu"
    runCmd "stop recorder_normal"
    runCmd "stop mdisks"
    runCmd "sleep 1"

    runCmd "umount /tmp/media_rw/disk0"
    runCmd "umount /tmp/media_rw/disk1"
    runCmd "umount /tmp/media_rw/disk2"
    runCmd "umount /tmp/media_rw/disk3"
    runCmd "umount /tmp/media_rw/disk4"

    runCmd "umount /tmp/media_rw/sdcard0"
    runCmd "umount /tmp/media_rw/sdcard1"
    runCmd "umount /tmp/media_rw/sdcard2"
    runCmd "umount /tmp/media_rw/sdcard3"
    runCmd "umount /tmp/media_rw/sdcard4"

    runCmd "sleep 1"
}

function startService() {
    runCmd "start mdisks"
    runCmd "start recorder_normal"
    runCmd "start idvr_ccu"
    # runCmd "start idvr_media"
}

function checkRet() {
    if [ "$?" == "$ERROR" ]; then
        ALOGD "excutor failt !"
        startService
        sayByeBye "失败"
        runCmd "echo error > $resultFile"
        exit $ERROR
    fi
}

function status() {
    runCmd "echo $1 > $resultFile"
}

function findDev() {
    if [ "$diskType" == "disk" ]; then
        devPath=`ls /tmp/ldisk -l | awk -F " " '{print $11}'`
        dev1Path=$devPath"1"
        dev2Path=$devPath"2"
    elif [ "$diskType" == "sdcard" ]; then
        devPath=`ls /tmp/lsdcard -l | awk -F " " '{print $11}'`
        dev1Path=$devPath"p1"
        dev2Path=$devPath"p2"
    else
        ALOGD "unkown disk type: $diskType !"
        return $ERROR
    fi

    if [ "$devPath" == "" ] || [ ! -e "$devPath" ]; then
        ALOGD "device path not exist !"
        return $ERROR
    fi

    ALOGD "dev: $devPath, dev1: $dev1Path, dev2: $dev2Path"

    return $SUCCESS
}

function partition() {
    local tryCnt=0
    runCmd "$FALLOCATE_TOOL_BIN -clearPartition $devPath"
    runCmd "echo 1 > /sys/devices/soc0/soc/soc:sdmmc/sdmmc0_reset"
    while true; do
        if [ ! -e "$dev1Path" ] && [ ! -e "$dev2Path" ]; then
            break
        fi

        if [ $tryCnt -eq 5 ]; then
            ALOGD "clear partition error !"
            return $ERROR
        fi

        let tryCnt=$tryCnt+1
        sleep 1
    done

    runCmd "$FALLOCATE_TOOL_BIN -setPartition $devPath"
    runCmd "echo 1 > /sys/devices/soc0/soc/soc:sdmmc/sdmmc0_reset"

    tryCnt=0
    while true; do
        if [ -e "$dev1Path" ] && [ -e "$dev2Path" ]; then
            break
        fi

        if [ $tryCnt -eq 5 ]; then
            ALOGD "set partition $dev1Path error !"
            return $ERROR
        fi

        let tryCnt=$tryCnt+1
        sleep 1
    done

    return $SUCCESS
}

function formatExt4() {
    local tryCnt=0
    while true; do
        runCmd "$FALLOCATE_TOOL_BIN -formatExt4 $dev2Path"
        runCmd "$EXT4_CHECK_BIN -y $dev2Path | grep \"Bad\""
        local checkRes="$CMD_RES_STR"
        if [ "$checkRes" == "" ]; then
            ALOGD "format ext4 success"
            break
        fi

        let tryCnt=$tryCnt+1
        if [ $tryCnt -eq 2 ]; then
            ALOGD "format ext4 error !"
            return $ERROR
        fi
    done

    return $SUCCESS
}

function fallocate() {
    local tryCnt=0
    while true; do
        runCmd "$MKFS_FAT_BIN -R 128 -s 128 -F 32 $dev1Path"
        runCmd "$FALLOCATE_TOOL_BIN -fallocate $dev1Path"
        runCmd "$FALLOCATE_TOOL_BIN -check $dev1Path | grep \"success\""
        local checkRes="$CMD_RES_STR"
        if [ "$checkRes" != "" ]; then
            ALOGD "fallocate success"
            break
        fi

        let tryCnt=$tryCnt+1
        if [ $tryCnt -eq 2 ]; then
            ALOGD "fallocate error !"
            return $ERROR
        fi
    done

    return $SUCCESS
}

#--------------------------------------

sayHello

status "正在初始化 ..."

runCmd "mkdir -p `dirname "$resultFile"`"

ALOGD "-------- stopService ----------"
stopService

ALOGD "-------- findDev ----------"
findDev
checkRet "$?"

status "删除所有分区 ..."

ALOGD "-------- partition ----------"
partition
checkRet "$?"

status "分区1格式化中 ..."

ALOGD "-------- formatExt4 ----------"
formatExt4
checkRet "$?"

status "分区2预分配中 ..."

if [ "$LANG" == "zh" ] ; then 
    runCmd "tts -queue \"格式化中,请勿关闭电源\""
else
    countTick &
fi

ALOGD "-------- fallocate ----------"
fallocate
checkRet "$?"

ALOGD "-------- startService ----------"

runCmd "setprop persist.minieye.record_mode fallocate"
startService

sayByeBye "成功"

status "格式化成功"

echo "success"
sleep 8
media.cmd.clearUiTips
exit 0
