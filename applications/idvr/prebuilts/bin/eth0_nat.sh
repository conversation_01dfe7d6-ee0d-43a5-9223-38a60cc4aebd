#!/system/bin/sh
PROP_NAME=rw.minieye.nat2dev
TARGET_DEV=xxx
LOG_TAG="eth0_nat"
function ALOGD()
{
        msg="$1"
        echo "$msg"
	logger -t $LOG_TAG "$msg"

}

ETH_IP=""
function wait4EthCfged()
{
	while true
	do
		ETH_IP=`ifconfig $1 |grep "inet addr:" |cut -d ':' -f 2|cut -d ' ' -f 1`
		if [ -z "${ETH_IP}" ] ; then
			ALOGD "wait configure $1 done ..."
			sleep 3
		else
			break
		fi
	done
}

function NAT()
{
	echo "1" > /proc/sys/net/ipv4/ip_forward
	iptables -F
	iptables -P INPUT ACCEPT
	iptables -P FORWARD ACCEPT
	iptables -t nat -A POSTROUTING -o $1 -j MASQUERADE
}

wait4EthCfged eth0
ip rule add to ${ETH_IP%.*}/24 table main pref 100

while true
do
	TMP=`getprop ${PROP_NAME}`
	TMP=`echo -n ${TMP}`
	TARGET_DEV=`ifconfig |grep usb|cut -d ' ' -f 1`
	if [ "${TARGET_DEV}" != "${TMP}" ] ; then
		ALOGD "NAT TARGET_DEV is changed ! [${TMP}] to [${TARGET_DEV}]"
		wait4EthCfged ${TARGET_DEV}
		NAT ${TARGET_DEV}
		setprop ${PROP_NAME} ${TARGET_DEV}
	else
		ALOGD "NAT TARGET_DEV ${TARGET_DEV}"
	fi

	sleep 30
done

