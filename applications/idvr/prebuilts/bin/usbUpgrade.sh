#!/system/bin/sh
LCFG=`grep ui.string.conf -r /data/minieye/idvr/etc/config.ini|grep en`
LANG=zh
if [ -z "$LCFG" ]; then
	echo default_zh
else
	LANG=en
fi

function ALOGD()
{
    msg="$1"
    #echo "$msg" 
    logger -p user.debug -t "usbOfflineUpgrade" "$msg"
}


function upgradeFromUSB()
{
	UPGRADE_FOLDER="/mpk/"
	SN=`cat /data/c2/c2.json |grep id|cut -d '"' -f 4`
	ALOGD "SN:"$SN
	if [ "$SN" == "" ] ; then
		ALOGD "Not found SN in c2.json!"
		return
	fi

	while true
	do
		UDISK_MNT_FOLDER=`mount|grep udisk|head -n 1|cut -d ' ' -f 3| xargs echo -n`
		if [ "$UDISK_MNT_FOLDER" == "" ] ; then
			UDISK_MNT_FOLDER=`mount|grep sdcard|head -n 1|cut -d ' ' -f 3| xargs echo -n`
			if [ "$UDISK_MNT_FOLDER" == "" ] ; then
				#if [ "$LANG" == "zh" ] ; then
				#	tts -now "未找到移动存储设备"
				#else
				#	tts -now "Neither.udisk.nor.sdcard.is.found!"
				#fi
				ALOGD "Neither udisk nor sdcard is found!"
				sleep 10
				continue
			fi
		fi
        	ALOGD "UDISK_MNT_FOLDER:"$UDISK_MNT_FOLDER
		
		MPK_PATH=$UDISK_MNT_FOLDER/$UPGRADE_FOLDER
		if [ ! -d $MPK_PATH ] ; then
			#if [ "$LANG" == "zh" ] ; then
			#	tts -now "未找到mpk目录"
			#else
			#	tts -now "no.any.mpk.folder!"
			#fi
			ALOGD "NO 'mpk' folder on usb disk!"
			sleep 10
			continue
		fi
        	ALOGD "MPK_PATH:"$MPK_PATH

		SN_MD5_MPK="$MPK_PATH/..md5_mpk/$SN"
		if [ ! -e $SN_MD5_MPK ] ; then
			mkdir -p $SN_MD5_MPK;
		fi
        	ALOGD "SN_MD5_MPK:"$SN_MD5_MPK
        
		MPK_LIST=`cd $MPK_PATH && ls *.mpk | grep -v md5_mpk`
		if [ "$MPK_LIST" == "" ] ; then
			ALOGD "No mpk found!"
			sleep 10
			continue
		fi
        	ALOGD "MPK_LIST:"$MPK_LIST

		SUCC_COUNT=0
		for file in $MPK_LIST
		do
            		ALOGD "file:"$file
			MD5_VAL=`cd $MPK_PATH/; md5sum $file |sed 's/[ ][ ]*/_/g' | xargs echo -n`
			if [ -e "$SN_MD5_MPK/$MD5_VAL" ] ; then
				ALOGD "Skip old version! $file"
				continue
			fi
            		ALOGD "MD5_VAL:"$MD5_VAL
			if [ "$LANG" == "zh" ] ; then
				tts -now "升级中，请勿断电"
				media.cmd.setUiTips 升级中       16777215 3 65280
				media.cmd.setUiTips 请勿断电     16777215 2 65280
			else
				#tts -now "To.upgrade,do.not.power.off!"
				media.cmd.setUiTips system       16777215 3 65280
				media.cmd.setUiTips upgrading... 16777215 2 65280
			fi
			cd $MPK_PATH; install_mpk.sh $MPK_PATH/$file;
			if [ $? -eq 0 ] ; then
				let SUCC_COUNT=SUCC_COUNT+1
				ALOGD "file=$file"
				touch "$SN_MD5_MPK/$MD5_VAL"
				#tts -now "$file install SUCCESS!"
				MD5_OLD_VALS=`ls $SN_MD5_MPK/*_$file|grep -v $MD5_VAL|xargs echo -n`
				if [ "$MD5_OLD_VALS" != "" ] ; then
					for val in "$MD5_OLD_VALS"
					do
						rm /data/$val
					done
				fi
			else
				if [ "$LANG" == "zh" ] ; then
					tts -now "$file安装失败"
					media.cmd.setUiTips $file    16777215 1 16711680
					media.cmd.setUiTips 安装失败! 16777215 0 16711680
				else
					#tts -now "$file.fail!"
					media.cmd.setUiTips $file 16777215 1 16711680
					media.cmd.setUiTips fail! 16777215 0 16711680
				fi
			fi
		done
		if [ $SUCC_COUNT -ne 0 ] ; then
			sync
			sleep 3
			if [ "$LANG" == "zh" ] ; then
				tts -now "成功安装$SUCC_COUNT个升级包,重启中"
				media.cmd.setUiTips 成功安装 16777215 3 65280
				media.cmd.setUiTips $SUCC_COUNT个升级包 16777215 2 65280
				media.cmd.setUiTips 重启中 16777215 1 65280
			else
				#tts -now "upgrade.successfull,reboot.now!"
				media.cmd.setUiTips upgrade 16777215 3 65280
				media.cmd.setUiTips successfull 16777215 2 65280
				media.cmd.setUiTips reboot.now 16777215 1 65280
			fi
			ALOGD "$SUCC_COUNT done."
			sleep 8
			reboot
			return
		else
			if [ "$LANG" == "zh" ] ; then
				tts -now "没有安装任何升级包"
			else
				#tts -now "No.any.package.installed!"
				media.cmd.setUiTips no.any.package 16777215 3 16711680
				media.cmd.setUiTips installed!     16777215 2 16711680
			fi
			sleep 8
			media.cmd.clearUiTips
			return
		fi
	done
}

upgradeFromUSB
