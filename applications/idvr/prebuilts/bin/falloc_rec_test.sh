#!/system/bin/sh

FILE_NAME="$1"
AWK="busybox awk"

DIR="/tmp/falloc_test"

rm -rf $DIR
mkdir $DIR

function select_ch {

	temp=$1
	$AWK -F: "/^$temp/" $FILE_NAME | $AWK '{print $5"->"$6" "$7"->"$8}'|sort > "$DIR/sortch$1"
	test_ch $1
}

function test_ch {

	i=1
	bgn_time="null"
	end_time="null"
	while read line; do
		for col in $line; do
			if [ $i -eq 1 ]; then
				i=2
				if [ ${bgn_time} == "null" ]; then
					bgn_time=$col
					echo -n $col
					echo -n " "
				elif [ $col != ${end_time} ]; then
					echo $end_time
					echo -n $col
					echo -n " "
				fi
			elif [ $i -eq 2 ]; then
                    end_time=$col
					i=1
			fi
		done
	done < "$DIR/sortch$1"
	echo "$end_time"
	rm "$DIR/sortch$1"
}

function main {

	line_num=`cat $FILE_NAME | $AWK -F " " '{print $1}' | sort -u| wc -l`
	line_num=`expr $line_num - 1`
	cat $FILE_NAME | $AWK -F " " '{print $1}' | sort -u | head -n $line_num > "$DIR/sortcol"

	while read line; do
		echo "=================================================="
		echo "ch$line:"
		select_ch $line
	done < "$DIR/sortcol"
}

main
