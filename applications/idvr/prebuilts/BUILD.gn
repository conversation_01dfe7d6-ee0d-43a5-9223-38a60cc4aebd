

group("prebuilts_idvr_build") {
    deps = [
        ":copy_idvr_prebuilts_bin",
        ":copy_idvr_prebuilts_etc",
        ":copy_idvr_prebuilts_fonts",
    ]
}

action("copy_idvr_prebuilts_bin") {
    script = "//build/minieye/copy_files.py"
    outputs = [ "$target_out_dir/copy_idvr_prebuilts_bin_log.txt" ]
    src = rebase_path("./bin")
    dest_dir = rebase_path("${root_out_dir}/prebuilts/bin")
    args = [
        "--src=${src}",
        "--dest_dir=${dest_dir}"
    ]
}

action("copy_idvr_prebuilts_etc") {
    script = "//build/minieye/copy_files.py"
    outputs = [ "$target_out_dir/copy_idvr_prebuilts_etc_log.txt" ]
    src = rebase_path("./etc")
    dest_dir = rebase_path("${root_out_dir}/prebuilts/etc")
    args = [
        "--src=${src}",
        "--dest_dir=${dest_dir}"
    ]
}

action("copy_idvr_prebuilts_fonts") {
    script = "//build/minieye/copy_files.py"
    outputs = [ "$target_out_dir/copy_idvr_prebuilts_fonts_log.txt" ]
    src = rebase_path("./fonts")
    dest_dir = rebase_path("${root_out_dir}/prebuilts/fonts")
    args = [
        "--src=${src}",
        "--dest_dir=${dest_dir}"
    ]
}
