
service adbd /system/bin/adbd
	class core
	user root

service mdisks /system/bin/idvr.mdisks
	class core
	user root

service tts_server /system/bin/idvr.tts.server
	class main
	user root

service netconfig /system/bin/netconfig
	class main
	user root

service idvr_ccu /system/bin/idvr.ccu -c /data/minieye/idvr/ -d /data/minieye/idvr/
	class main
	user root

service idvr_config /system/bin/idvr.config /data/minieye/idvr/etc/config.ini
	disabled
	class main
	user root

service hostio /system/bin/hostio /data/minieye/idvr/
	class main
	user root

service idvr_sh /system/bin/sh /system/bin/idvr.sh
	class core
	user root
	oneshot

service idvr_media /system/bin/idvr.media /data/minieye/idvr/etc/config.ini
	class main
	user root


service idvr_muxer /system/bin/idvr.muxer /data/minieye/idvr/etc/config.ini
	class main
	user root

service recorder_normal /system/bin/idvr.recorder.fallocate /data/minieye/idvr/etc/config.ini
	class main
	user root

#service recorder_recove /system/bin/idvr.recorder.recovery /data/minieye/idvr/etc/config.ini
#	class main
#	user root

service stress_test /system/bin/sh /system/bin/main.sh
	class core
	user root

service c8 /system/bin/c8 -c /system/etc/c8/app.conf
	class main
	user root

service ntp /system/bin/sh /system/bin/ntp.sh
	class main
	user root
	oneshot

service transfer_tools /system/bin/sh /system/bin/algo.sh transfer_tools
	class main
	user root

service calib /system/bin/sh /system/bin/algo.sh calib
	class main
	user root

service cam_occ /system/bin/sh /system/bin/algo.sh occ
        class main
        user root

service adas /system/bin/sh /system/bin/algo.sh adas
	disabled
	class main
	user root

service dms /system/bin/sh /system/bin/algo.sh dms
	disabled
	class main
	user root

service expand /system/bin/expand
	class main
	user root

service fbsd /system/bin/sh /system/bin/algo.sh fbsd
	disabled
	class main
	user root

service bsd /system/bin/sh /system/bin/algo.sh bsd
	disabled
	class main
	user root

service aps /system/bin/sh /system/bin/algo.sh aps
	disabled
	class main
	user root

service ivs /system/bin/sh /system/bin/algo.sh ivs
	disabled
	class main
	user root

service objp /system/bin/sh /system/algo/bsd/start.sh
	disabled
	class main
	user root

service custom /system/bin/sh /system/bin/custom.sh
	class main
	user root

service imu_server /system/bin/imu_server
	class main
	user root

service usbupgrade /system/bin/sh /system/bin/usbUpgrade.sh
    class core
    user root
    oneshot

service road_check /system/bin/sh /system/bin/road_check.sh
	disabled
	class main
	user root

service eth0_nat /system/bin/sh /system/bin/eth0_nat.sh
        disabled
        class main
        user root

on property:persist.minieye.eth0.nat=1
        start eth0_nat

service cve_service /system/bin/cve.service
        disabled
        class main
        user root

on property:persist.minieye.cve.service=1
        start cve_service

