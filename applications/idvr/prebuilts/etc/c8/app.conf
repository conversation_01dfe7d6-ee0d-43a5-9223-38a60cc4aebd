# 配置文件格式
# [section]
# key = value

appname = c8
copyrequestbody = true
maxmemory = 1024

# c8 服务器在 httpaddr : httpport 上监听。
httpaddr = 0.0.0.0
httpport = 12306

# 运行模式：dev，prod，test
# dev 模式可以跳过权限认证
runmode = dev

# 认证类型
# basic: HTTP Basic Auth 认证，每个 HTTP 请求都要在 Header 带上
#        username 和 password
# token: 先发送从服务器获取到的 token 给 c8，c8 会返回一个 randstring，
#        之后每个请求都要带着这个 randstring。
# all: basic 和 token 同时打开。
authtype = token

# 公钥存放路径
publickeypath = /home/<USER>/a/git/minieye/c8/conf/pubkey.pem

# log-level 日志等级 verbose, debug, info, warn, error
log-level = "info"
# log-file 日志文件，没有设置的话，日志默认输出到 stdout
log-file =

# 账号列表，每行代表一个账号
# 认证使用 HTTP Basic Auth
# key 是用户名
# value 是密码
[account]
c8minieye = minieye666
c8minieye1 = minieye6661
c8minieye2 = minieye6662
c8minieye3 = minieye6663
