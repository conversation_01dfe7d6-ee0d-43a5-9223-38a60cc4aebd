#!/.ini
[this]

[base]
model=M5PRO
ccc=C000000

manufacture.vendor=44030072504
manufacture.date=20200101
manufacture.id=72504
sim=12345678900

init.date=1584629076
init.mileage=0.0

vehicle.city=123
vehicle.plate_color=1
vehicle.plate_num=粤B11111
vehicle.plate_type=小客车
vehicle.province=25
vehicle.vin=54321

[media]
cameras=4
display.switch=true
storage=tf1,disk
acc.time.on=4
acc.time.off=30
manufacture.speaker=2
storage.fileDuration=600
lock.record.switch=false
ui.string.conf=default_string_id_zh
ui.field.switch.conf=default_ui_field_switch
ui.widgets.conf=default_ui_widgets

[media.ch1]
av=3
label=DMS
save=3
cam.res=1280x720
cam.phy=DMS
video0.resl=1280x720
video0.fps=25
video0.bps=1048576
video1.resl=640x480
video1.fps=25
video1.bps=1048576

record.audio.input=mic-1
record.video.input=video0

ai.enable=true
ai.input=video0
ai.func=dms
ai.color=y
ai.attachment=video1
ai.fps=12

[media.ch2]
av=3
label=ADAS
save=3
cam.res=1280x720
cam.phy=ADAS
video0.resl=1280x720
video0.fps=25
video0.bps=1048576
video1.resl=640x480
video1.fps=25
video1.bps=1048576

record.audio.input=mic-2
record.video.input=video0

ai.enable=true
ai.input=video0
ai.func=adas
ai.color=y
ai.attachment=video1
ai.fps=12

[media.ch3]
av=3
label=HOD
save=3
cam.res=1280x720
cam.phy=HOD
video0.resl=1280x720
video0.fps=25
video0.bps=1048576
video1.resl=640x480
video1.fps=25
video1.bps=1048576

record.audio.input=mic-4
record.video.input=video0

ai.enable=false
ai.input=video0
ai.func=ch
ai.color=y
ai.attachment=video1
ai.fps=12

[media.ch4]
av=3
label=BSD
save=3
cam.res=1280x720
cam.phy=BSD
video0.resl=1280x720
video0.fps=25
video0.bps=1048576
video1.resl=640x480
video1.fps=25
video1.bps=1048576

record.audio.input=mic-3
record.video.input=video0

ai.enable=false
ai.input=video0
ai.func=bsd
ai.color=y
ai.attachment=video1
ai.fps=12

[media.ch5]
av=2
label=LVDS
save=2
cam.res=1280x720
cam.phy=LVDS
video0.resl=1280x720
video0.fps=25
video0.bps=1048576
video1.resl=640x480
video1.fps=25
video1.bps=1048576

record.audio.input=mic-0
record.video.input=video0

ai.enable=false
ai.input=video0
ai.func=bsd
ai.color=y
ai.attachment=video1
ai.fps=12

[display]
win1=ch1
win2=ch2
win3=ch3
win4=ch4
win5=ch5
win6=ch6

[sound]
mic1.gain=4
mic2.gain=4
mic3.gain=4
mic4.gain=4

[clock]
src=3

[wifi]
mode=0
ssid=
passwd=

[io]
gpio.D0.labels=ACC
gpio.D1.labels=紧急报警
gpio.D2.labels=倒车
gpio.D3.labels=近光
gpio.D4.labels=远光
gpio.D5.labels=右转
gpio.D6.labels=左转
gpio.D7.labels=刹车
gpio.back.fullsc=ch0
gpio.near.fullsc=ch0
gpio.far.fullsc=ch0
gpio.left.fullsc=ch0
gpio.right.fullsc=ch0
gpio.brake.fullsc=ch0
gpio.door.fullsc=ch0
speed.pulse_coef=3600
speed.src=2

[warn]
overspeed.alarm=100
overspeed.enable=1
overspeed.limit=120
overtime.alarm=15
overtime.enable=1
overtime.limit=40
overtime.rest=20

[conn1]
protocol=jtt808-1078
auth=
ip1=**************
port.tcp1=6608
port.udp1=
ip2=
port.tcp2=
port.udp2=
