executable("get_soc_uuid") {
    sources = [
        "uuid_main.cpp",
    ]
    
    include_dirs = [
        ".",
        "//applications/vendor/8838_sdk4/include",
    ]
    
    sigmastar_sdk_dir = rebase_path("//applications/vendor/8838_sdk4/lib/share/")
    
    ldflags = [
        "-L${sigmastar_sdk_dir}",
        "-L${sigmastar_sdk_dir}/opencv_9.1.0",
        "-ldl",
        "-lpthread",
        "-lopencv_imgcodecs",
        "-lopencv_imgproc",
        "-lopencv_core",
        "-lcam_fs_wrapper",
        "-lcam_os_wrapper",
        "-lmi_ai",
        "-lmi_ao",
        "-lmi_common",
        "-lmi_disp",
        "-lmi_fb",
        "-lmi_rgn",
        "-lmi_scl",
        "-lmi_sensor",
        "-lmi_sys",
        "-lmi_vdec",
        "-lmi_venc",
        "-lmi_vif",
    ]

    cflags_cc = [
        "-Wno-psabi",
        "-Wno-unused-parameter",
    ]
}
