[core]
	repositoryFormatVersion = 1
	filemode = true
[filter "lfs"]
	smudge = git-lfs smudge --skip -- %f
	process = git-lfs filter-process --skip
[remote "origin"]
	url = ********************:hzd/apps/manifest.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[manifest]
	platform = auto
[extensions]
	preciousObjects = true
[branch "default"]
	remote = origin
	merge = refs/heads/m5pro
[repo]
	existingprojectcount = 10
	newprojectcount = 0
[repo "syncstate.main"]
	synctime = 2025-07-19T03:51:35.090867+00:00
	version = 1
[repo "syncstate.sys"]
	argv = ['/home/<USER>/project/m5pro/.repo/repo/main.py', '--repo-dir=/home/<USER>/project/m5pro/.repo', '--wrapper-version=2.54', '--wrapper-path=/usr/bin/repo', '--', 'sync', '-c']
[repo "syncstate.options"]
	jobs = 8
	outermanifest = true
	jobsnetwork = 8
	jobscheckout = 8
	mpupdate = true
	currentbranchonly = true
	clonebundle = true
	retryfetches = 0
	prune = true
	repoverify = true
	quiet = false
	verbose = false
	repoupgraded = true
[repo "syncstate.remote.origin"]
	url = ********************:hzd/apps/manifest.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[repo "syncstate.branch.default"]
	remote = origin
	merge = refs/heads/m5pro
[repo "syncstate.repo"]
	existingprojectcount = 10
	newprojectcount = 0
