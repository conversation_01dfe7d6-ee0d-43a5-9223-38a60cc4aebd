{"core.repositoryformatversion": ["0"], "core.filemode": ["true"], "filter.lfs.smudge": ["git-lfs smudge --skip -- %f"], "filter.lfs.process": ["git-lfs filter-process --skip"], "remote.gitlab.url": ["ssh://********************/hzd/apps/build_minieye"], "remote.gitlab.projectname": ["build_minieye"], "remote.gitlab.fetch": ["+refs/heads/*:refs/remotes/gitlab/*"], "branch.master.remote": ["gitlab"], "branch.master.merge": ["refs/heads/master"]}