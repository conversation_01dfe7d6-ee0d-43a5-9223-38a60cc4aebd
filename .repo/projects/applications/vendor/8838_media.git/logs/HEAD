0000000000000000000000000000000000000000 597d71d2366c7b4bd15a27fa2fb064f641c19815 xu<PERSON>esen <<EMAIL>> 1735132331 +0800	checkout: moving from master to 597d71d2366c7b4bd15a27fa2fb064f641c19815
597d71d2366c7b4bd15a27fa2fb064f641c19815 597d71d2366c7b4bd15a27fa2fb064f641c19815 xujiesen <<EMAIL>> 1735132344 +0800
597d71d2366c7b4bd15a27fa2fb064f641c19815 597d71d2366c7b4bd15a27fa2fb064f641c19815 xujiesen <<EMAIL>> 1744797684 +0800	reset: moving to HEAD
597d71d2366c7b4bd15a27fa2fb064f641c19815 a0f932db39cf336e016e0044ff4b1cd22dc78d34 xujiesen <<EMAIL>> 1744856372 +0800	commit: fix(libidvr.media): 修复快照生成失败问题：yuv2jpeg失败时重试2次
a0f932db39cf336e016e0044ff4b1cd22dc78d34 e5f337926a430b9c37840b06ced4d4a0fbffcbf4 xujiesen <<EMAIL>> 1744948050 +0800	merge e5f337926a430b9c37840b06ced4d4a0fbffcbf4: Fast-forward
e5f337926a430b9c37840b06ced4d4a0fbffcbf4 06f076a8e4151f269127a91f6cb4b537f8ca5652 xujiesen <<EMAIL>> 1752896793 +0800	merge 06f076a8e4151f269127a91f6cb4b537f8ca5652: Fast-forward
