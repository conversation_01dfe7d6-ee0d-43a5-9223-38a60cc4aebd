[core]
	repositoryformatversion = 0
	filemode = true
[filter "lfs"]
	smudge = git-lfs smudge --skip -- %f
	process = git-lfs filter-process --skip
[remote "gitlab"]
	url = ssh://********************/hzd/apps/m5pro_apps
	projectname = m5pro_apps
	fetch = +refs/heads/*:refs/remotes/gitlab/*
[branch "master"]
	remote = gitlab
	merge = refs/heads/master
	vscode-merge-base = gitlab/master
	vscode-merge-base = gitlab/master
[pull]
	rebase = true
