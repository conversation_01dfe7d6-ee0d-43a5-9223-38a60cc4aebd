{"core.repositoryformatversion": ["0"], "core.filemode": ["true"], "filter.lfs.smudge": ["git-lfs smudge --skip -- %f"], "filter.lfs.process": ["git-lfs filter-process --skip"], "remote.gitlab.url": ["ssh://********************/hzd/apps/m5pro_apps"], "remote.gitlab.projectname": ["m5pro_apps"], "remote.gitlab.fetch": ["+refs/heads/*:refs/remotes/gitlab/*"], "branch.master.remote": ["gitlab"], "branch.master.merge": ["refs/heads/master"], "branch.master.vscode-merge-base": ["gitlab/master", "gitlab/master"], "pull.rebase": ["true"]}