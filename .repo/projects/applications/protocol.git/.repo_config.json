{"core.repositoryformatversion": ["0"], "core.filemode": ["true"], "filter.lfs.smudge": ["git-lfs smudge --skip -- %f"], "filter.lfs.process": ["git-lfs filter-process --skip"], "remote.gitlab.url": ["ssh://********************/hzd/apps/protocol"], "remote.gitlab.projectname": ["protocol"], "remote.gitlab.fetch": ["+refs/heads/*:refs/remotes/gitlab/*"], "branch.master.remote": ["gitlab"], "branch.master.merge": ["refs/heads/master"], "branch.master.vscode-merge-base": ["gitlab/master"], "branch.M5PRO_CIBEI_ZHONGTONGZHILI.remote": ["gitlab"], "branch.M5PRO_CIBEI_ZHONGTONGZHILI.merge": ["refs/heads/M5PRO_CIBEI_ZHONGTONGZHILI"], "branch.M5PRO_CIBEI_ZHONGTONGZHILI.vscode-merge-base": ["gitlab/M5PRO_CIBEI_ZHONGTONGZHILI"], "pull.rebase": ["true"]}