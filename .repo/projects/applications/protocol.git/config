[core]
	repositoryformatversion = 0
	filemode = true
[filter "lfs"]
	smudge = git-lfs smudge --skip -- %f
	process = git-lfs filter-process --skip
[remote "gitlab"]
	url = ssh://********************/hzd/apps/protocol
	projectname = protocol
	fetch = +refs/heads/*:refs/remotes/gitlab/*
[branch "master"]
	remote = gitlab
	merge = refs/heads/master
	vscode-merge-base = gitlab/master
[branch "M5PRO_CIBEI_ZHONGTONGZHILI"]
	remote = gitlab
	merge = refs/heads/M5PRO_CIBEI_ZHONGTONGZHILI
	vscode-merge-base = gitlab/M5PRO_CIBEI_ZHONGTONGZHILI
[pull]
	rebase = true
