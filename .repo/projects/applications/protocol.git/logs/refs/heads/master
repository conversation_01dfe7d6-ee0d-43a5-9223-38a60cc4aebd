0000000000000000000000000000000000000000 5d949774e05a493bf11172adbc54422330657a9a xujiesen <xuji<PERSON><EMAIL>> 1735132344 +0800
5d949774e05a493bf11172adbc54422330657a9a 77901a6c7206a9d33d7b650a180d1e132ee3f66d xujiesen <<EMAIL>> 1741332226 +0800	commit: feat(libprot.jtt808-1078): 三一bsd警告等级上传
77901a6c7206a9d33d7b650a180d1e132ee3f66d c7a0848cc6f882d56afa66831819bce7f159264e xujiesen <<EMAIL>> 1741339618 +0800	pull: Fast-forward
c7a0848cc6f882d56afa66831819bce7f159264e 2ea7c20e417170f8ab08e730e5f15bf6cdabed1e xujiesen <<EMAIL>> 1741764759 +0800	commit: feat(libalgo): objp支持leftFront和rightFront标识
2ea7c20e417170f8ab08e730e5f15bf6cdabed1e 47d67ce0a2de7c824f8f304a8437f8d6ff38398b xujiesen <<EMAIL>> 1741864917 +0800	pull: Fast-forward
47d67ce0a2de7c824f8f304a8437f8d6ff38398b fd555718d8f30dde9225cb5c099f4a9b5ed49d3a xujiesen <<EMAIL>> 1741864998 +0800	commit: feat(libprot.jtt808-1078): 硖石媒体输出windows修改为四个窗口，适配objp
fd555718d8f30dde9225cb5c099f4a9b5ed49d3a 47d67ce0a2de7c824f8f304a8437f8d6ff38398b xujiesen <<EMAIL>> 1742201399 +0800	reset: moving to HEAD^
47d67ce0a2de7c824f8f304a8437f8d6ff38398b 8801ba2de26389ce713387c67ed5e6c5ff36e200 xujiesen <<EMAIL>> 1744012344 +0800	pull --tags gitlab master: Fast-forward
8801ba2de26389ce713387c67ed5e6c5ff36e200 1a2164dded02e330e9f95f6eb75c37c0e42221df xujiesen <<EMAIL>> 1744012639 +0800	commit: feat(libprot.jtt808-1078): 硖石3v bsd告警适配
1a2164dded02e330e9f95f6eb75c37c0e42221df df0a64b924a8cf0d8d9e51b6ba2015ac06f1c9ac xujiesen <<EMAIL>> 1744337976 +0800	pull --tags gitlab master: Fast-forward
df0a64b924a8cf0d8d9e51b6ba2015ac06f1c9ac ba13269ddabb26488508d049363594cb677a54f5 xujiesen <<EMAIL>> 1744715154 +0800	commit: feat(imu/gnss): 苏标设备间协议bug修复，添加磁北定制协议
ba13269ddabb26488508d049363594cb677a54f5 1cc758680188aa2b084df93d20d8e9f415ba1697 xujiesen <<EMAIL>> 1744806966 +0800	pull: Fast-forward
1cc758680188aa2b084df93d20d8e9f415ba1697 2f4f3e5377cceb4b9582382ec2346edcb88e9aae xujiesen <<EMAIL>> 1744948452 +0800	commit: feat(libalgo): cb, 中通智利特殊需求
2f4f3e5377cceb4b9582382ec2346edcb88e9aae 1cc758680188aa2b084df93d20d8e9f415ba1697 xujiesen <<EMAIL>> 1744948515 +0800	reset: moving to HEAD^
1cc758680188aa2b084df93d20d8e9f415ba1697 6ca5a2cf41f23ce92219112095f3af18959a973e xujiesen <<EMAIL>> 1744948551 +0800	commit: feat(libalgo): m5pro,厦门磁北, 中通智利特殊需求
6ca5a2cf41f23ce92219112095f3af18959a973e 1cc758680188aa2b084df93d20d8e9f415ba1697 xujiesen <<EMAIL>> 1744956691 +0800	reset: moving to HEAD^
1cc758680188aa2b084df93d20d8e9f415ba1697 3cddc0d773e1e307a5fa665d6d06605501c567da xujiesen <<EMAIL>> 1747227423 +0800	commit: feat(libjtt808-1078/protocol): 添加连云港公交，斑马线减速子协议
3cddc0d773e1e307a5fa665d6d06605501c567da da39615a3dca1b3d20b99f2e4dfff27481d2cadc xujiesen <<EMAIL>> 1747289540 +0800	pull --tags gitlab master: Fast-forward
da39615a3dca1b3d20b99f2e4dfff27481d2cadc 57c84ffe82024bacf9bb6e75b6885f120a01ffb1 xujiesen <<EMAIL>> 1747396970 +0800	commit: feat(libjtt808-1078/algo): 斑马线功能支持三急，libalgo防止多次启动线程导致卡死
57c84ffe82024bacf9bb6e75b6885f120a01ffb1 bdd8be75b959b23488d0e283bc56e0f6f4ed6a90 xujiesen <<EMAIL>> 1747826440 +0800	pull --tags gitlab master: Fast-forward
bdd8be75b959b23488d0e283bc56e0f6f4ed6a90 7fe7b99e8f7660d77ff313218774085770725bb8 xujiesen <<EMAIL>> 1748607691 +0800	cherry-pick: feat(libprot.jtt): 苏标bsd适配fbsd
7fe7b99e8f7660d77ff313218774085770725bb8 39b5ecdcaffb6fae9121f6be8491ab7ce8eaec05 xujiesen <<EMAIL>> 1748607704 +0800	cherry-pick: feat(libprot.jtt): 苏标fbsd映射为左右通道
39b5ecdcaffb6fae9121f6be8491ab7ce8eaec05 74958450c037fcf27c810bcfe20b5eeae5d65724 xujiesen <<EMAIL>> 1748607714 +0800	cherry-pick: feat(libprot.jtt): 添加磁北自定义协议：自检、bsd等级
74958450c037fcf27c810bcfe20b5eeae5d65724 21ded01268b16ddb79cd3936566a647530167176 xujiesen <<EMAIL>> 1748956810 +0800	commit: fix(libprot.jtt): 磁北苏标适配bsd
21ded01268b16ddb79cd3936566a647530167176 60566f2a7d5987dc743979832909c23aa55c68db xujiesen <<EMAIL>> 1749026890 +0800	pull: Fast-forward
60566f2a7d5987dc743979832909c23aa55c68db 8a3d15ae11b4b91da88e9e5364a62d9c6dece0cb xujiesen <<EMAIL>> 1749026936 +0800	revert: Revert "feat(libjtt808-1078/algo): 斑马线功能支持三急，libalgo防止多次启动线程导致卡死"
8a3d15ae11b4b91da88e9e5364a62d9c6dece0cb 60566f2a7d5987dc743979832909c23aa55c68db xujiesen <<EMAIL>> 1749543896 +0800	reset: moving to HEAD^
60566f2a7d5987dc743979832909c23aa55c68db fe47aa2f54d492bec199fd66cc437e8f4053d333 xujiesen <<EMAIL>> 1749815505 +0800	commit: feat(libalgo/prot-jtt808): 优化三急算法，三急添加附件上传
fe47aa2f54d492bec199fd66cc437e8f4053d333 79ca9a2dffc6424e1ed96d669e5a7ccf4e693de3 xujiesen <<EMAIL>> 1750063611 +0800	commit: feat(prot.jtt808-1078): 斑马线/三急根据客户需求修改报警和附件上传逻辑
79ca9a2dffc6424e1ed96d669e5a7ccf4e693de3 b03632788c7258d1ef729186ae4e5d33c8aabb55 xujiesen <<EMAIL>> 1750159494 +0800	cherry-pick: feat(libjtt): 苏标支持setup平台查询连接状态
b03632788c7258d1ef729186ae4e5d33c8aabb55 8c5eb88348c47bf155dbcf4d1cd21df8a9faaac7 xujiesen <<EMAIL>> 1750159509 +0800	pull (finish): refs/heads/master onto 016833ac34edd19349ba09b5a9d00872880b7064
8c5eb88348c47bf155dbcf4d1cd21df8a9faaac7 8bd3a5547e211ed22e924bff46291991b2d8234c xujiesen <<EMAIL>> 1750232759 +0800	pull --tags gitlab master: Fast-forward
8bd3a5547e211ed22e924bff46291991b2d8234c 5fa0da220768a90476d07a28eb349d19b8afa0fc xujiesen <<EMAIL>> 1751011318 +0800	commit: feat(libprot.jtt808-1078): 斑马线不减速报警，修改成55km以上报警
5fa0da220768a90476d07a28eb349d19b8afa0fc babbea82667cf1ecef23776df510b702c072480b xujiesen <<EMAIL>> 1751441550 +0800	pull: Fast-forward
babbea82667cf1ecef23776df510b702c072480b 70f30a2f07748013c9d11e3ebaed78df03c9eb12 xujiesen <<EMAIL>> 1752300299 +0800	pull: Fast-forward
70f30a2f07748013c9d11e3ebaed78df03c9eb12 babbea82667cf1ecef23776df510b702c072480b xujiesen <<EMAIL>> 1752896784 +0800	reset: moving to HEAD^
babbea82667cf1ecef23776df510b702c072480b c360f6aa1990ba6d93c58d8c19b3ec65fe8e90bb xujiesen <<EMAIL>> 1752896793 +0800	merge c360f6aa1990ba6d93c58d8c19b3ec65fe8e90bb: Fast-forward
c360f6aa1990ba6d93c58d8c19b3ec65fe8e90bb c1988a4edee8f687f3acb545171669cd42fa78dc xujiesen <<EMAIL>> 1753774254 +0800	pull --tags gitlab master: Fast-forward
