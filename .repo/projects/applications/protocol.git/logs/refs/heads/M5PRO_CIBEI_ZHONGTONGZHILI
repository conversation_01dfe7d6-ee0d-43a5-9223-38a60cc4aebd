0000000000000000000000000000000000000000 6ca5a2cf41f23ce92219112095f3af18959a973e xujiesen <<EMAIL>> 1748329788 +0800	branch: Created from refs/remotes/gitlab/M5PRO_CIBEI_ZHONGTONGZHILI
6ca5a2cf41f23ce92219112095f3af18959a973e e8f24dbd0887549aacba32da8d78a2b53e6e5a21 xujiesen <<EMAIL>> 1748418563 +0800	commit: feat(libprot.jtt): 苏标bsd适配fbsd
e8f24dbd0887549aacba32da8d78a2b53e6e5a21 e15cb95d6f2e98950648b784ef87153af92c5a91 xujiesen <<EMAIL>> 1748419552 +0800	pull: Fast-forward
e15cb95d6f2e98950648b784ef87153af92c5a91 f9b7b9f121365b63e3030b6eec537d96ace7a00d xujiesen <<EMAIL>> 1748426852 +0800	commit: feat(libprot.jtt): 苏标fbsd映射为左右通道
f9b7b9f121365b63e3030b6eec537d96ace7a00d ff227c237fd05f4b3185ed1bec2847ee947e0cca xujiesen <<EMAIL>> 1748607502 +0800	commit: feat(libprot.jtt): 添加磁北自定义协议：自检、bsd等级
ff227c237fd05f4b3185ed1bec2847ee947e0cca 1cb90b8610643f383bef86010b252331f7548616 xujiesen <<EMAIL>> 1748956843 +0800	cherry-pick: fix(libprot.jtt): 磁北苏标适配bsd
1cb90b8610643f383bef86010b252331f7548616 c59b2890317e9defd0608753e11fce8a09b9f9b0 xujiesen <<EMAIL>> 1750154228 +0800	commit: feat(libalgo): FBSD需求
c59b2890317e9defd0608753e11fce8a09b9f9b0 04e6fcfd030ad4596457a85f0d5bdaaef2c9b059 xujiesen <<EMAIL>> 1750159395 +0800	commit: feat(libjtt): 苏标支持setup平台查询连接状态
04e6fcfd030ad4596457a85f0d5bdaaef2c9b059 153df7450acfa266062501f5eca28abdecd63eaa xujiesen <<EMAIL>> 1751441528 +0800	commit: feat(libalgo): 更新log描述
