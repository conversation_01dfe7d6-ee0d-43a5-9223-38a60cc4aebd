{"core.repositoryformatversion": ["0"], "core.filemode": ["true"], "filter.lfs.smudge": ["git-lfs smudge --skip -- %f"], "filter.lfs.process": ["git-lfs filter-process --skip"], "remote.gitlab.url": ["ssh://********************/hzd/apps/base_core"], "remote.gitlab.projectname": ["base_core"], "remote.gitlab.fetch": ["+refs/heads/*:refs/remotes/gitlab/*"], "branch.master.remote": ["gitlab"], "branch.master.merge": ["refs/heads/master"], "branch.master.vscode-merge-base": ["gitlab/master"]}