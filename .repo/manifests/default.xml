<?xml version="1.0" encoding="UTF-8"?>
<manifest>

    <remote fetch="ssh://********************/hzd/apps" name="gitlab"/>
    <default remote="gitlab" revision="master" sync-j="8" />

    <!-- 编译仓库 -->  
    <project name="build_minieye" path="build/minieye" revision="master">
        <linkfile dest="build.sh" src="build.sh" />
    </project>

    <!-- 通信库 -->
    <!-- ringbuf libmessage socketcmd mcuAgent libevservice libflow dds--> 
    <project name="communication" path="foundation/communication/" revision="master"/>
	
    <!-- 基础库和工具 -->
    <!-- mystd nmea libjsonUtil tools/* --> 
    <project name="base_core" path="foundation/base/core" revision="master"/>
	
    <!-- 第三方库和工具 -->
    <!-- adbd mksh mp4v2 curl--> 
    <project name="third_party" path="third_party" revision="master"/>

    <!-- 基础服务程序 -->
    <!-- idvr.config idvr.mdisks--> 
    <project name="base_service" path="foundation/base/service"/>

    <!-- 多媒体相关库和服务 -->
    <!-- recoder libaud.enc librtp -->
    <project name="multimedia" path="foundation/multimedia"/>

	<!-- 业务相关程序 -->
    <project name="m5pro_apps" path="applications/idvr" revision="master">
        <linkfile dest="product.json" src="product.json" />
    </project>

    <!-- 8838平台的sdk -->
    <!-- lib include -->
    <project name="8838_sdk4" path="applications/vendor/8838_sdk4" revision="master"/>

    <!-- 8838平台的media程序 -->
    <project name="8838_media" path="applications/vendor/8838_media" revision="master"/>

    <!-- 通用协议 -->
    <project name="protocol" path="applications/protocol" revision="master"/>

</manifest>
