#ifndef __PROT_SUBIAO_MSG_H__
#define __PROT_SUBIAO_MSG_H__

#include "mystd.h"
#include "prot.base.h"

#define DEVICE_ID_BRDCST         (0x00)  // 广播
#define DEVICE_ID_ADAS           (0x64)  // ADAS
#define DEVICE_ID_DSM            (0x65)  // DSM
#define DEVICE_ID_TPMS           (0x66)  // TPMS
#define DEVICE_ID_BSD            (0x67)  // BSD

#define CMD_QUERY                (0x2F)  // 查询指令
#define CMD_FACTORY_RESET        (0x30)  // 恢复默认参数指令
#define CMD_SPEED_LBS_INFO       (0x31)  // 实时数据指令
#define CMD_DEVICE_INFO          (0x32)  // 查询外设基本信息
#define CMD_UPGRADE              (0x33)  // 升级外设程序
#define CMD_GET_PARAM            (0x34)  // 查询参数
#define CMD_SET_PARAM            (0x35)  // 设置参数
#define CMD_WARNING_REPORT       (0x36)  // 事件/报警上报
#define CMD_REQ_STATUS           (0x37)  // 外设状态查询
#define CMD_UPLOAD_STATUS        (0x38)  // 外设状态上报
#define CMD_REQ_MM_DATA          (0x50)  // 请求多媒体数据
#define CMD_UPLOAD_MM_DATA       (0x51)  // 上传多媒体数据
#define CMD_SNAP_SHOT            (0x52)  // 立即拍照指令

typedef struct SubiaoProtMsg {
    uint8_t     magicBgn    = PROT_JTT_MAGIC;  // 标识位
    uint8_t     checkSum    = 0;  // 校验码
    uint16_t    seq         = 0;  // 流水号
    uint16_t    vendor      = 0;  // 厂商编号
    uint8_t     devid       = 0;  // 外设编号
    uint8_t     cmd         = 0;  // 功能码
    my::string  payload;          // 数据内容
    uint8_t     magicEnd    = PROT_JTT_MAGIC;  // 标识位

    SubiaoProtMsg(uint8_t command = 0)
    {
        cmd = command;
    }
    int32_t encode(my::string & msg);
    int32_t decode(uint8_t * msg, int32_t len);
    uint8_t checksum(uint8_t * p, uint32_t len);
} SubiaoProtMsg;

struct CarSigStat {
    CarSigStat()
    {
        byte1Resv1  = 0;
        location    = 0;
        byte1Resv2  = 0;

        acc         = 0;
        leftTurn    = 0;
        rightTurn   = 0;
        wipers      = 0;
        brakes      = 0;
        card        = 0;
        byte2Resv1  = 0;
    }
    uint8_t    byte1Resv1   : 2;
    uint8_t    location     : 1;
    uint8_t    byte1Resv2   : 5;

    uint8_t    acc          : 1;
    uint8_t    leftTurn     : 1;
    uint8_t    rightTurn    : 1;
    uint8_t    wipers       : 1;
    uint8_t    brakes       : 1;
    uint8_t    card         : 1;
    uint8_t    byte2Resv1   : 2;
} __attribute__((packed)) ;

struct SBX31 {
    SBX31()
    {
        speed = 0;
        reserve1 = 0;
        mileage = 0;
        reserve2[0] = reserve2[1] = 0;
        altitude = latitude = longitude = 0;
        memset(time, 0, sizeof(time));
    }
    uint8_t   speed;
    uint8_t   reserve1;
    uint32_t  mileage; /*0.1km*/
    uint8_t   reserve2[2];

    uint16_t  altitude;
    uint32_t  latitude;
    uint32_t  longitude;

    uint8_t   time[6];
    uint16_t  CarSigStat;
    int32_t decode(uint8_t * data, int32_t len);
};

struct PERIPHERAL_BASE_INFO {
    uint8_t length;
    std::string content;
};

typedef enum PERIPHERAL_CONTENT_NAME : uint8_t {
    COMPANY_NAME    = 0,
    RPODUCT_NAME    = 1,
    HW_VERSION      = 2,
    SW_VERSION      = 3,
    DEVICE_ID       = 4,
    CUSTOMER_CODE   = 5
} PERIPHERAL_CONTENT_NAME_E;

struct SBX32 {
    std::map<PERIPHERAL_CONTENT_NAME_E, my::string> peripheralInfoMap;
    int32_t encode(my::string & msg);
};

enum UPGRADE_MSG_ID_E : uint8_t {
    START   = 0x1,
    CLEAR   = 0x2,
    DATA    = 0x3,
    EXECUTE = 0x4
};

struct SBX33 {
    uint8_t  upgradeMsgId = 0x0;    // 消息id（1字节）
    uint16_t packTotalNum = 0;      // 总包数（2字节）
    uint16_t packCurIdx   = 0;      // 包序号（2字节）
    uint32_t checkSum = 0x0;        // 接收的校验码
    my::string data;                 // 升级包内容

    int32_t decode(uint8_t * data, int32_t len);
    int32_t encode(my::string & msg, bool verChecksum);
};

struct SBX34_X35_DEVICE_ID_ADAS {   // 0x64
    uint8_t  alarmAnableSpeedThr            = 30;    // 报警使能速度阈值
    uint8_t  alarmPromptVolume              = 6;     // 报警提示音量
    uint8_t  actPhoStrategy                 = 0x00;  // 主动拍照策略
    uint16_t actTimedPhoTimeInt             = 1800;  // 主动定时拍照时间间隔
    uint16_t actFixedDisPhoDisInt           = 100;   // 主动定距拍照距离间隔
    uint8_t  numActPhoTakAtATime            = 3;     // 单次主动拍照张数s
    uint8_t  timeIntBtnSinActPho            = 2;     // 单次主动拍照时间间隔
    uint8_t  photoResolution                = 0x1;   // 拍照分辨率
    uint8_t  vidRecResolution               = 0x1;   // 视频录制分辨率
    uint8_t  reservedFields1[9]             = {0};   // 预留字段
    uint8_t  barrierAlarmDisThr             = 30;    // 障碍物报警距离阈值
    uint8_t  vidRecTimeBAAObsAlarm          = 5;     // 障碍物报警前后视频录制时间
    uint8_t  numObsAlarmPhoTak              = 3;     // 障碍物报警拍照张数
    uint8_t  obsAlarmPhoInt                 = 2;     // 障碍物报警拍照间隔
    uint8_t  freqLaneChaAlarmJudTimePer     = 60;    // 频繁变道报警判断时间段
    uint8_t  freqLaneChaAlarmJudFreq        = 5;     // 频繁变道报警判断次数
    uint8_t  vidRecTimeBAAFreqLaneChaAlarms = 5;     // 频繁变道报警前后视频录制时间
    uint8_t  freqLaneChaAlarmPndNumPhoTak   = 3;     // 频繁变道报警拍照张数
    uint8_t  freqLaneChaAlarmPhoInt         = 2;     // 频繁变道报警拍照间隔
    uint8_t  vidRecTimeBAALaneDepWarn       = 5;     // 车道偏离报警前后视频录制时间
    uint8_t  numLaneDepWarnPhoTak           = 3;     // 车道偏离报警拍照张数
    uint8_t  laneDepWarnCameraInt           = 2;     // 车道偏离报警拍照间隔
    uint8_t  forwardColAlarmTimeThr         = 27;    // 前向碰撞报警时间阈值
    uint8_t  vidRecTimeBAAForwardColWarn    = 5;     // 前向碰撞报警前后视频录制时间
    uint8_t  numForwardColAlarmPhoTak       = 3;     // 前向碰撞报警拍照张数
    uint8_t  forwardColAlarmPhoInt          = 2;     // 前向碰撞报警拍照间隔
    uint8_t  pedColAlarmTimeThr             = 30;    // 行人碰撞报警时间阈值
    uint8_t  vidRecTimeBAAPedColAlarm       = 5;     // 行人碰撞报警前后视频录制时间
    uint8_t  numPedColAlarmPhoTak           = 3;     // 行人碰撞报警拍照张数
    uint8_t  pedColAlarmPhoInt              = 2;     // 行人碰撞报警拍照间隔
    uint8_t  vehicleDisMonAlarmDisThr       = 30;    // 车距监控报警距离阈值
    uint8_t  vidRecTimeAlarmForCloPro       = 5;     // 车距过近报警前后视频录制时间
    uint8_t  numPhoTakCloToTheAlarm         = 3;     // 车距过近报警拍照张数
    uint8_t  cameraIntForWarnCloPro         = 2;     // 车距过近报警拍照间隔
    uint8_t  numPhoTakForRoadSignRec        = 3;     // 道路标识识别拍照张数
    uint8_t  roadSignRecPhoInt              = 2;     // 道路标识识别拍照间隔
    uint8_t  reservedFields2[4]             = {0};   // 保留字段

    int32_t encode(my::string & msg);
    int32_t decode(uint8_t * data, int32_t len);
};
struct SBX34_X35_DEVICE_ID_DSM {   // 0x65
    uint8_t  alarmAnableSpeedThr            = 30;    // 报警使能速度阈值
    uint8_t  alarmPromptVolume              = 6;     // 报警提示音量
    uint8_t  actPhoStrategy                 = 0x00;  // 主动拍照策略
    uint16_t actTimedPhoTimeInt             = 3600;  // 主动定时拍照时间间隔
    uint16_t actFixedDisPhoDisInt           = 200;   // 主动定距拍照距离间隔
    uint8_t  numProPhoTakenEachTime         = 3;     // 每次主动拍照张数
    uint8_t  timeIntActivePhoTake           = 2;     // 每次主动拍照时间间隔
    uint8_t  photoResolution                = 0x1;   // 拍照分辨率
    uint8_t  vidRecResolution               = 0x1;   // 视频录制分辨率
    uint8_t  reservedFields1[10]            = {0};   // 预留字段
    uint16_t smokeAlarmJudTimeInt           = 180;   // 吸烟报警判断时间间隔
    uint16_t alarmJudTimeIntAnsMakeCalls    = 120;   // 接打电话报警判断时间间隔
    uint8_t  videoRecTimeBAAFatDriWarn      = 5;     // 疲劳驾驶报警前后视频录制时间
    uint8_t  numFatDriAlarmPhoTaken         = 3;     // 疲劳驾驶报警拍照张数
    uint8_t  fatDriAlarmPhoIntTime          = 2;     // 疲劳驾驶报警拍照间隔时间
    uint8_t  reservedFields2                = 0;     // 预留字段
    uint8_t  videoRecTimeBAACallPol         = 5;     // 打电话报警前后视频录制时间
    uint8_t  numPhoTakDriFFeaDurPhoCalAla   = 3;     // 接打电话报警拍驾驶员面部特征照片张数
    uint8_t  TimeTakPhoDriFFeaDurPhoCalAla  = 2;     // 接打电话报警拍驾驶员面部特征照片间隔时间
    uint8_t  videoRecTimeBAASmokeAal        = 5;     // 抽烟报警前后视频录制时间
    uint8_t  numComFFeaPhoTakDriSmokeAla    = 3;     // 抽烟报警拍驾驶员完整面部特征照片张数
    uint8_t  TimeTakCompFFeaPhoDrismokeAla  = 2;     // 抽烟报警拍驾驶员完整面部特征照片间隔时间
    uint8_t  videoRecTimeBAAAlarm           = 5;     // 报警前后视频录制时间
    uint8_t  numDisDriAlarmPhoTtaken        = 3;     // 分神驾驶报警拍照张数
    uint8_t  disDriAlarmPhoInt              = 2;     // 分神驾驶报警拍照间隔时间
    uint8_t  recTimeAbnDriVideo             = 5;     // 驾驶异常视频录制时间
    uint8_t  numCapPhoAbnDrive              = 3;     // 驾驶异常抓拍照片张数
    uint8_t  abnAriPhoInt                   = 2;     // 驾驶异常拍照间隔
    uint8_t  reservedFields3[2]             = {0};   // 预留字段

    int32_t encode(my::string & msg);
    int32_t decode(uint8_t * data, int32_t len);
};
struct SBX34_X35_DEVICE_ID_TPMS {   // 0x66
    uint8_t  tireSpecificationsModels[12]  = "900R20";   // 轮胎规格型号
    uint16_t tirePressureUnit              = 0x3;   // 胎压单位
    uint16_t normalTirePressureVal         = 140;   // 正常胎压值
    uint16_t tirePreImbalAlarmThr          = 20;    // 胎压不平衡报警阈值
    uint16_t slowAirLeakageAlarmThr        = 5;     // 慢漏气报警阈值
    uint16_t lowPressureAlarmThr           = 110;   // 低压报警阈值
    uint16_t highVoltageAlarmThr           = 189;   // 高压报警阈值
    uint16_t highTemperatureAlarmThr       = 80;    // 高温报警阈值
    uint16_t voltageAlarmThreshold         = 10;    // 电压报警阈值
    uint16_t timedReportingInt             = 60;    // 定时上报时间间隔
    uint8_t  reservedFields[6]             = {0};   // 预留字段

    int32_t encode(my::string & msg);
    int32_t decode(uint8_t * data, int32_t len);
};
struct SBX34_X35_DEVICE_ID_BSD {   // 0x67
    uint8_t rearApproachAlarmTimeThr       = 2;     // 后方接近报警时间阈值
    uint8_t sideRearApprAlarmTimeThr       = 2;     // 侧后方接近报警时间阈值

    int32_t encode(my::string & msg);
    int32_t decode(uint8_t * data, int32_t len);
};
struct SBX34_X35 {
    SBX34_X35_DEVICE_ID_ADAS adas_query_parameter;
    SBX34_X35_DEVICE_ID_DSM  dsm_query_parameter;
    SBX34_X35_DEVICE_ID_TPMS tpms_query_parameter;
    SBX34_X35_DEVICE_ID_BSD  bsd_query_parameter;
};

/*
    set ACC close = SetCarstatus(currentValue, 0, false);
    set ACC open  = SetCarstatus(currentValue, 0, true);
    set WIPER close = SetCarstatus(currentValue, 3, false);
    set WIPER open  = SetCarstatus(currentValue, 3, true);
    SetBitStatus(当前值，位置，关闭/打开)
*/
template <typename T>
T SetBitStatus(T currentValue, uint8_t pos, bool status)
{
    T value = currentValue;

    if (status) {
        value = ((currentValue | (0x1 << pos)) & 0xFFFF);

    } else {
        value = ((currentValue | (0x1 << pos)) & (~(0x1 << pos)));
    }

    return value;
}

typedef enum MM_TYPE_SUBIAO : uint8_t {
    MM_TYPE_PHOTO    = 0,
    MM_TYPE_AUDIO    = 1,
    MM_TYPE_VIDEO    = 2,
} MM_TYPE_SUBIAO_E;

struct MMInfo {
    uint8_t  type; /*MM_TYPE_SUBIAO_E*/
    uint32_t id;
    int32_t encode(my::string & msg)
    {
        msg << my::hton;
        msg << type << id;
        return msg.length();
    }
} __attribute__((packed));

#define SB_WARN_STATUS_NONE (0x0)
#define SB_WARN_STATUS_BGN  (0x1)
#define SB_WARN_STATUS_END  (0x2)

typedef enum SUBIAO_ADAS_EVT : uint8_t {
    SUBIAO_ADAS_EVT_INVALID = 0x00,
    SUBIAO_ADAS_EVT_FCW,
    SUBIAO_ADAS_EVT_LDW,
    SUBIAO_ADAS_EVT_HMW,
    SUBIAO_ADAS_EVT_PCW,
    SUBIAO_ADAS_EVT_FLW,    /*频繁变道报警*/
    SUBIAO_ADAS_EVT_TSRW    = 0x06,
    SUBIAO_ADAS_EVT_TSR     = 0x10, /*道路标志识别事件*/
    SUBIAO_ADAS_EVT_SNAP    = 0x11, /*主动抓拍事件*/
} SUBIAO_ADAS_EVT_E;

struct SBX36_ADAS { // 0x64
    uint32_t alarmId    = 0;
    uint8_t  beFlag     = 0; /*0x00：不可用;0x01：开始标志;0x02：结束标志*/
    uint8_t  event      = 0; /*SUBIAO_ADAS_EVT_SNAP_E*/
    uint8_t  preCarSpd  = 0; /*前车车速*/
    uint8_t  preObjDist = 0; /*前车/人距离*/
    uint8_t  ldwDirect  = 0; /*0x01：左侧偏离;0x02：右侧偏离;仅报警类型为 0x02 时有效*/
    uint8_t  tsrType    = 0; /*0x01：限速标志;0x02：限高标志;0x03：限重标志;仅报警类型为 0x06 和 0x10 时有效。*/
    uint8_t  tsrValue   = 0; /*tsr数值*/
    uint8_t  speed      = 0;
    uint16_t  altitude   = 0;
    uint32_t latitude = 0;
    uint32_t longitude = 0;
    uint8_t  time[6]    = {0};
    uint16_t carSigStat;
    uint8_t  mmCount    = 0;
    std::vector<MMInfo>  mmTbl;

    int32_t encode(my::string & msg);
    SUBIAO_ADAS_EVT_E evtTrans(int32_t algoEvt);
};

typedef enum SUBIAO_DMS_EVT : uint8_t {
    SUBIAO_DMS_EVT_INVALID = 0,
    SUBIAO_DMS_EVT_DDS     = 0x01,   /*疲劳驾驶*/
    SUBIAO_DMS_EVT_PHONE_CALL,       /*接打电话*/
    SUBIAO_DMS_EVT_SMOKE,            /*抽烟*/
    SUBIAO_DMS_EVT_DISTRACTE,        /*分神驾驶*/
    SUBIAO_DMS_EVT_DRIVER_ABNORMAL,  /*驾驶员异常*/
    SUBIAO_DMS_EVT_RESERVE_6 = 0x6,  /*用户自定义*/
    SUBIAO_DMS_EVT_CAM_OCCLUSION,  /*用户自定义*/
    SUBIAO_DMS_EVT_EYE_OCCLUSION,  /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_9,  /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_A,  /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_B,  /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_C,  /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_D,  /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_E,  /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_F,  /*用户自定义*/
    SUBIAO_DMS_EVT_ACTIVE_CAPTURE = 0x10,    /*主动抓拍*/
    SUBIAO_DMS_EVT_DRIVER_CHANGE,            /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_12      = 0x12,    /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_13,    /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_14,    /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_15,    /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_16,    /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_17,    /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_18,    /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_19,    /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_1A,    /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_1B,    /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_1C,    /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_1D,    /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_1E,    /*用户自定义*/
    SUBIAO_DMS_EVT_RESERVE_1F,    /*用户自定义*/
} SUBIAO_DMS_EVT_E;

struct SBX36_DMS { // 0x65
    uint32_t alarmId     = 0;
    uint8_t  beFlag      = 0;  /*0x00：不可用;0x01：开始标志;0x02：结束标志*/
    uint8_t  event       = 0;  /*SUBIAO_DMS_EVT_E*/
    uint8_t  fatigueLv   = 0;  // 疲劳程度
    uint8_t  reserved[4] = {0};
    uint8_t  speed       = 0;
    uint16_t  altitude   = 0;
    uint32_t latitude = 0;
    uint32_t longitude = 0;
    uint8_t  time[6]    = {0};
    uint16_t carSigStat;
    uint8_t  mmCount    = 0;
    std::vector<MMInfo>  mmTbl;

    int32_t encode(my::string & msg);
    SUBIAO_DMS_EVT_E evtTrans(int32_t algoEvt);
};

struct AlarmEventInfo {
    uint8_t  tirePressurePosition;
    uint16_t type;
    uint16_t  tirePressure;
    uint16_t  tireTemperature;
    uint16_t  batteryLevel;

    int32_t encode(my::string & msg)
    {
        msg << my::hton;
        msg << tirePressurePosition << type;
        msg << tirePressure << tireTemperature;
        msg << batteryLevel;
        return msg.length();
    }
} __attribute__((packed));

struct SBX36_TPMS { // 0x66
    uint32_t alarmId     = 0;
    uint8_t  beFlag      = 0;  /*0x00：不可用;0x01：开始标志;0x02：结束标志*/
    uint16_t  altitude   = 0;
    uint32_t latitude = 0;
    uint32_t longitude = 0;
    uint8_t  time[6]    = {0};
    uint16_t carSigStat;
    uint8_t  mmCount    = 0;
    std::vector<AlarmEventInfo> mTPTb;

    int32_t encode(my::string & msg);
};

typedef enum SUBIAO_BSD_EVT : uint8_t {
    SUBIAO_BSD_EVT_INVALID       = 0,
    SUBIAO_BSD_EVT_APPROACH_REAR = 0x01,   /*后方接近报警*/
    SUBIAO_BSD_EVT_APPROACH_LR,            /*左侧后方接近报警*/
    SUBIAO_BSD_EVT_APPROACH_RR,            /*右侧后方接近报警*/
} SUBIAO_BSD_EVT_E;

struct SBX36_BSD { // 0x67
    uint8_t  reserved[4] = {0};
    uint8_t  beFlag      = 0;  /*0x00：不可用;0x01：开始标志;0x02：结束标志*/
    uint8_t  event;
    uint8_t  speed      = 0;
    uint16_t altitude  = 0;
    uint32_t latitude  = 0;
    uint32_t longitude = 0;
    uint8_t  time[6]    = {0};
    uint16_t carSigStat;

    int32_t encode(my::string & msg);
    SUBIAO_BSD_EVT_E evtTrans(int32_t algoEvt);
};

struct SBX36_BSD_CB_EXT { // 0x67
    uint8_t  bsdLevel = 0;
    uint8_t  reserved[3] = {0};
    uint8_t  beFlag      = 0;  /*0x00：不可用;0x01：开始标志;0x02：结束标志*/
    uint8_t  event;
    uint8_t  speed      = 0;
    uint16_t altitude  = 0;
    uint32_t latitude  = 0;
    uint32_t longitude = 0;
    uint8_t  time[6]    = {0};
    uint16_t carSigStat;
    uint8_t  mmCount    = 0;
    std::vector<MMInfo>  mmTbl;

    int32_t encode(my::string & msg);
    SUBIAO_BSD_EVT_E evtTrans(int32_t algoEvt);
};

struct SBX37 {
    uint8_t  workstatus    = 0x0;
    int32_t  warningstatus = 0x0;

    int32_t encode(my::string & msg);
};

struct SBX37_CB_EXT {
    uint8_t workstatus = 0x0;
<<<<<<<
    uint8_t r1               : 6 = 0;
    uint8_t dmsCamError      : 1 = 0;
    uint8_t adasCamError     : 1 = 0;
    uint8_t leftbsdCamError  : 1 = 0;
    uint8_t rightbsdCamError : 1 = 0;
    int32_t reserve1         : 22 = 0;

    int32_t encode(my::string& msg);
} __attribute__((packed));
=======
    union {
        struct {
            uint32_t r1               : 6;
            uint32_t dmsCamError      : 1;
            uint32_t adasCamError     : 1;
            uint32_t leftbsdCamError  : 1;
            uint32_t rightbsdCamError : 1;
            uint32_t reserve1         : 22;
        } __attribute__((packed)) bits;
        uint32_t i = 0;
    } warnBits;

    int32_t encode(my::string& msg);
};
>>>>>>>

struct SBX50 {
    uint8_t  messageID;
    int32_t  multiMediaID;

    int32_t decode(uint8_t * msg, int32_t len);
};

struct SBX51_RCV {
    uint8_t  messageID;
    int32_t  multiMediaID;
    uint16_t  packTotalNum;
    uint16_t  packCurIdx;
    uint8_t   result;

    int32_t decode(uint8_t * msg, int32_t len);
};

struct SBX51_SEND {
    uint8_t   messageID;
    int32_t   multiMediaID;
    uint16_t  packTotalNum;
    uint16_t  packCurIdx;
    my::string multiMediaData;

    int32_t encode(my::string & msg);
};


typedef union WarnBits {
    uint32_t i = 0;
    struct {
        uint32_t camError       : 1;
        uint32_t mainMemoError  : 1;
        uint32_t auxMemoryError : 1;
        uint32_t infraredError  : 1;
        uint32_t speakerError   : 1;
        uint32_t batteryError   : 1;
        uint32_t reserve1       : 2;

        uint32_t reserve2       : 2;
        uint32_t commModError   : 1;
        uint32_t defineModError : 1;
        uint32_t reserveError   : 20;
    } __attribute__((packed)) b;
} WarnBits;

struct SBX38 {
#define MODULE_STANDBY          0x01
#define MODULE_WORKING          0x02
#define MODULE_MAINTAIN         0x03
#define MODULE_ABNORMAL         0x04

    uint8_t  workStatus = 0;
    WarnBits warnBits;

    SBX38()
    {
        workStatus = 0;
        warnBits.i = 0;
    }
    int32_t encode(my::string & msg);
};
#endif
