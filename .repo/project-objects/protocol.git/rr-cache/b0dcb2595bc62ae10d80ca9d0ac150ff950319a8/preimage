#include "event.h"
#include "prot.subiao.msg.h"
#include <cstring>

uint8_t SubiaoProtMsg::checksum(uint8_t * p, uint32_t len)
{
    uint32_t chksum = 0;
    uint8_t * start = p + sizeof(magicBgn) + sizeof(checkSum) + sizeof(seq);

#define CHKSUM_LEN(len) (len - (2 * sizeof(magicBgn) + sizeof(checkSum) + sizeof(seq)))

    for (uint32_t i = 0; i < CHKSUM_LEN(len); i++) {
        chksum += start[i];
    }

    checkSum = (uint8_t)(chksum & 0xFF);
    return checkSum;
}
int32_t SubiaoProtMsg::encode(my::string & msg)
{
    msg.clear();
    msg << my::hton;
    msg << magicBgn << checkSum << seq << vendor << devid << cmd;
    msg << payload;
    msg << magicEnd;
    msg[1] = checksum((uint8_t *)msg.c_str(), msg.length());
    return msg.length();
}

int32_t SubiaoProtMsg::decode(uint8_t * msg, int32_t len)
{
    if ((msg[0] != PROT_JTT_MAGIC) && (msg[0] != msg[len - 1])) {
        return -1;
    }

    uint8_t cs = checksum(msg, len);

    if (cs != msg[1]) {
        loge("checksum error! 0x%02x != 0x%02x", cs, msg[1]);
        return -2;
    }

    my::constr msgStr((const char *)msg, len);
    msgStr >> my::ntoh;
    msgStr >> magicBgn >> checkSum >> seq >> vendor >> devid >> cmd;
    payload = my::string((const char *)msgStr, msgStr.length() - 1);
    msgStr += payload.length();
    msgStr >> magicEnd;

    return 0;
}

int32_t SBX31::decode(uint8_t * data, int32_t len)
{
    if (!data || len <= 0) {
        return -1;
    }

    my::constr msgStr((const char *)data, len);
    msgStr >> my::ntoh;
    msgStr >> speed >> reserve1 >> mileage >> reserve2[0] >> reserve2[1];
    msgStr >> altitude >> latitude >> longitude;
    msgStr >> time[0] >> time[1] >> time[2] >> time[3] >> time[4] >> time[5] >> CarSigStat;

    return 0;
}

int32_t SBX32::encode(my::string & msg)
{
    msg << my::hton;

    for (auto r : peripheralInfoMap) {
        msg << r.second(1);
    }

    return msg.length();
}

int32_t SBX33::decode(uint8_t * pData, int32_t len)
{
    if (!pData || len <= 0) {
        return -1;
    }

    my::constr msgStr((const char *)pData, len);
    msgStr >> my::ntoh;
    msgStr >> upgradeMsgId;

    if (upgradeMsgId == UPGRADE_MSG_ID_E::DATA) {
        msgStr >> packTotalNum >> packCurIdx;

        if (packCurIdx == 0) {// 首包
            msgStr >> checkSum;

        } else {
            msgStr >> data;
        }
    }

    return 0;
}
int32_t SBX33::encode(my::string & msg, bool succ)
{
    msg << my::hton;
    msg << upgradeMsgId;

    if (upgradeMsgId == UPGRADE_MSG_ID_E::DATA) {
        msg << packTotalNum;
        msg << packCurIdx;
    }

    if (!succ) {
        msg << (uint8_t)0x01;

    } else {
        msg << (uint8_t)0x00;
    }

    return msg.length();
}

int32_t SBX34_X35_DEVICE_ID_ADAS::encode(my::string & msg)
{
    msg << my::hton;
    msg << alarmAnableSpeedThr;
    msg << alarmPromptVolume;
    msg << actPhoStrategy;
    msg << actTimedPhoTimeInt;
    msg << actFixedDisPhoDisInt;
    msg << numActPhoTakAtATime;
    msg << timeIntBtnSinActPho;
    msg << photoResolution;
    msg << vidRecResolution;

    for (int i = 0; i < 9; i++) {
        msg << reservedFields1[i];
    }

    msg << barrierAlarmDisThr;
    msg << vidRecTimeBAAObsAlarm;
    msg << numObsAlarmPhoTak;
    msg << obsAlarmPhoInt;
    msg << freqLaneChaAlarmJudTimePer;
    msg << freqLaneChaAlarmJudFreq;
    msg << vidRecTimeBAAFreqLaneChaAlarms;
    msg << freqLaneChaAlarmPndNumPhoTak;
    msg << freqLaneChaAlarmPhoInt;
    msg << vidRecTimeBAALaneDepWarn;
    msg << numLaneDepWarnPhoTak;
    msg << laneDepWarnCameraInt;
    msg << forwardColAlarmTimeThr;
    msg << vidRecTimeBAAForwardColWarn;
    msg << numForwardColAlarmPhoTak;
    msg << forwardColAlarmPhoInt;
    msg << pedColAlarmTimeThr;
    msg << vidRecTimeBAAPedColAlarm;
    msg << numPedColAlarmPhoTak;
    msg << pedColAlarmPhoInt;
    msg << vehicleDisMonAlarmDisThr;
    msg << vidRecTimeAlarmForCloPro;
    msg << numPhoTakCloToTheAlarm;
    msg << cameraIntForWarnCloPro;
    msg << numPhoTakForRoadSignRec;
    msg << roadSignRecPhoInt;

    for (int i = 0; i < 4; i++) {
        msg << reservedFields2[i];
    }

    return msg.length();
}
int32_t SBX34_X35_DEVICE_ID_ADAS::decode(uint8_t * data, int32_t len)
{
    if (!data || len <= 0) {
        return -1;
    }

    my::constr msgStr((const char *)data, len);
    msgStr >> my::ntoh;

    msgStr >> alarmAnableSpeedThr;
    msgStr >> alarmPromptVolume;
    msgStr >> actPhoStrategy;
    msgStr >> actTimedPhoTimeInt;
    msgStr >> actFixedDisPhoDisInt;
    msgStr >> numActPhoTakAtATime;
    msgStr >> timeIntBtnSinActPho;
    msgStr >> photoResolution;
    msgStr >> vidRecResolution;

    for (int i = 0; i < 9; i++) {
        msgStr >> reservedFields1[i];
    }

    msgStr >> barrierAlarmDisThr;
    msgStr >> vidRecTimeBAAObsAlarm;
    msgStr >> numObsAlarmPhoTak;
    msgStr >> obsAlarmPhoInt;
    msgStr >> freqLaneChaAlarmJudTimePer;
    msgStr >> freqLaneChaAlarmJudFreq;
    msgStr >> vidRecTimeBAAFreqLaneChaAlarms;
    msgStr >> freqLaneChaAlarmPndNumPhoTak;
    msgStr >> freqLaneChaAlarmPhoInt;
    msgStr >> vidRecTimeBAALaneDepWarn;
    msgStr >> numLaneDepWarnPhoTak;
    msgStr >> laneDepWarnCameraInt;
    msgStr >> forwardColAlarmTimeThr;
    msgStr >> vidRecTimeBAAForwardColWarn;
    msgStr >> numForwardColAlarmPhoTak;
    msgStr >> forwardColAlarmPhoInt;
    msgStr >> pedColAlarmTimeThr;
    msgStr >> vidRecTimeBAAPedColAlarm;
    msgStr >> numPedColAlarmPhoTak;
    msgStr >> pedColAlarmPhoInt;
    msgStr >> vehicleDisMonAlarmDisThr;
    msgStr >> vidRecTimeAlarmForCloPro;
    msgStr >> numPhoTakCloToTheAlarm;
    msgStr >> cameraIntForWarnCloPro;
    msgStr >> numPhoTakForRoadSignRec;
    msgStr >> roadSignRecPhoInt;

    for (int i = 0; i < 4; i++) {
        msgStr >> reservedFields2[i];
    }

    return 0;
}
int32_t SBX34_X35_DEVICE_ID_DSM::encode(my::string & msg)
{
    msg << my::hton;
    msg << alarmAnableSpeedThr;
    msg << alarmPromptVolume;
    msg << actPhoStrategy;
    msg << actTimedPhoTimeInt;
    msg << actFixedDisPhoDisInt;
    msg << numProPhoTakenEachTime;
    msg << timeIntActivePhoTake;
    msg << photoResolution;
    msg << vidRecResolution;

    for (int i = 0; i < 10; i++) {
        msg << reservedFields1[i];
    }

    msg << smokeAlarmJudTimeInt;
    msg << alarmJudTimeIntAnsMakeCalls;
    msg << videoRecTimeBAAFatDriWarn;
    msg << numFatDriAlarmPhoTaken;
    msg << fatDriAlarmPhoIntTime;
    msg << reservedFields2;
    msg << videoRecTimeBAACallPol;
    msg << numPhoTakDriFFeaDurPhoCalAla;
    msg << TimeTakPhoDriFFeaDurPhoCalAla;
    msg << videoRecTimeBAASmokeAal;
    msg << numComFFeaPhoTakDriSmokeAla;
    msg << TimeTakCompFFeaPhoDrismokeAla;
    msg << videoRecTimeBAAAlarm;
    msg << numDisDriAlarmPhoTtaken;
    msg << disDriAlarmPhoInt;
    msg << recTimeAbnDriVideo;
    msg << numCapPhoAbnDrive;
    msg << abnAriPhoInt;
    msg << reservedFields3[0] << reservedFields3[1];

    return msg.length();
}
int32_t SBX34_X35_DEVICE_ID_DSM::decode(uint8_t * data, int32_t len)
{
    if (!data || len <= 0) {
        return -1;
    }

    my::constr msgStr((const char *)data, len);
    msgStr >> my::ntoh;

    msgStr >> alarmAnableSpeedThr;
    msgStr >> alarmPromptVolume;
    msgStr >> actPhoStrategy;
    msgStr >> actTimedPhoTimeInt;
    msgStr >> actFixedDisPhoDisInt;
    msgStr >> numProPhoTakenEachTime;
    msgStr >> timeIntActivePhoTake;
    msgStr >> photoResolution;
    msgStr >> vidRecResolution;

    for (int i = 0; i < 10; i++) {
        msgStr >> reservedFields1[i];
    }

    msgStr >> smokeAlarmJudTimeInt;
    msgStr >> alarmJudTimeIntAnsMakeCalls;
    msgStr >> videoRecTimeBAAFatDriWarn;
    msgStr >> numFatDriAlarmPhoTaken;
    msgStr >> fatDriAlarmPhoIntTime;
    msgStr >> reservedFields2;
    msgStr >> videoRecTimeBAACallPol;
    msgStr >> numPhoTakDriFFeaDurPhoCalAla;
    msgStr >> TimeTakPhoDriFFeaDurPhoCalAla;
    msgStr >> videoRecTimeBAASmokeAal;
    msgStr >> numComFFeaPhoTakDriSmokeAla;
    msgStr >> TimeTakCompFFeaPhoDrismokeAla;
    msgStr >> videoRecTimeBAAAlarm;
    msgStr >> numDisDriAlarmPhoTtaken;
    msgStr >> disDriAlarmPhoInt;
    msgStr >> recTimeAbnDriVideo;
    msgStr >> numCapPhoAbnDrive;
    msgStr >> abnAriPhoInt;
    msgStr >> reservedFields3[0] >> reservedFields3[1];

    return 0;
}
int32_t SBX34_X35_DEVICE_ID_TPMS::encode(my::string & msg)
{
    msg << my::hton;

    for (int i = 0; i < 12; i++) {
        msg << tireSpecificationsModels[i];
    }

    msg << tirePressureUnit;
    msg << normalTirePressureVal;
    msg << tirePreImbalAlarmThr;
    msg << slowAirLeakageAlarmThr;
    msg << lowPressureAlarmThr;
    msg << highVoltageAlarmThr;
    msg << highTemperatureAlarmThr;
    msg << voltageAlarmThreshold;
    msg << timedReportingInt;

    for (int i = 0; i < 6; i++) {
        msg << reservedFields[i];
    }

    return msg.length();
}
int32_t SBX34_X35_DEVICE_ID_TPMS::decode(uint8_t * data, int32_t len)
{
    if (!data || len <= 0) {
        return -1;
    }

    my::constr msgStr((const char *)data, len);
    msgStr >> my::ntoh;

    for (int i = 0; i < 12; i++) {
        msgStr >> tireSpecificationsModels[i];
    }

    msgStr >> tirePressureUnit;
    msgStr >> normalTirePressureVal;
    msgStr >> tirePreImbalAlarmThr;
    msgStr >> slowAirLeakageAlarmThr;
    msgStr >> lowPressureAlarmThr;
    msgStr >> highVoltageAlarmThr;
    msgStr >> highTemperatureAlarmThr;
    msgStr >> voltageAlarmThreshold;
    msgStr >> timedReportingInt;

    for (int i = 0; i < 6; i++) {
        msgStr >> reservedFields[i];
    }

    return 0;
}
int32_t SBX34_X35_DEVICE_ID_BSD::encode(my::string & msg)
{
    msg << my::hton;

    msg << rearApproachAlarmTimeThr;
    msg << sideRearApprAlarmTimeThr;

    return msg.length();
}
int32_t SBX34_X35_DEVICE_ID_BSD::decode(uint8_t * data, int32_t len)
{
    if (!data || len <= 0) {
        return -1;
    }

    my::constr msgStr((const char *)data, len);
    msgStr >> my::ntoh;

    msgStr >> rearApproachAlarmTimeThr;
    msgStr >> sideRearApprAlarmTimeThr;


    return 0;
}

int32_t SBX36_ADAS::encode(my::string & msg)
{
    msg << my::hton;
    msg << alarmId;
    msg << beFlag << event << preCarSpd << preObjDist;
    msg << ldwDirect << tsrType << tsrValue << speed;
    msg << altitude;
    msg << latitude;
    msg << longitude;

    for (int i = 0; i < sizeof(time); i++) {
        msg << time[i];
    }

    mmCount = mmTbl.size();
    msg << carSigStat;
    msg << mmCount;

    for (auto r : mmTbl) {
        my::string mmData;
        r.encode(mmData);
        msg << mmData;
    }

    return msg.length();
}
SUBIAO_ADAS_EVT_E SBX36_ADAS::evtTrans(int32_t algoEvt)
{
    switch (algoEvt) {
        case EVT_TYPE_ADAS_LeftLDW:
            return SUBIAO_ADAS_EVT_LDW;

        case EVT_TYPE_ADAS_RightLDW:
            return SUBIAO_ADAS_EVT_LDW;

        case EVT_TYPE_ADAS_FCW:
            return SUBIAO_ADAS_EVT_FCW;

        case EVT_TYPE_ADAS_HW:
            return SUBIAO_ADAS_EVT_HMW;

        case EVT_TYPE_ADAS_PCW:
            return SUBIAO_ADAS_EVT_PCW;

        case EVT_TYPE_ADAS_TSRW:
            return SUBIAO_ADAS_EVT_TSRW;

        case EVT_TYPE_ADAS_TSR:
            return SUBIAO_ADAS_EVT_TSR;

        case EVT_TYPE_ADAS_SNAP:
            return SUBIAO_ADAS_EVT_SNAP;
    }

    return SUBIAO_ADAS_EVT_INVALID;
}

int32_t SBX36_DMS::encode(my::string & msg)
{
    msg << my::hton;
    msg << alarmId;
    msg << beFlag << event << fatigueLv;
    msg << reserved[0] << reserved[1] << reserved[2] << reserved[3];
    msg << speed;
    msg << altitude;
    msg << latitude;
    msg << longitude;

    for (int i = 0; i < sizeof(time); i++) {
        msg << time[i];
    }

    msg << carSigStat;

    mmCount = mmTbl.size();
    msg << mmCount;

    for (auto r : mmTbl) {
        my::string mmData;
        r.encode(mmData);
        msg << mmData;
    }

    return msg.length();
}
SUBIAO_DMS_EVT_E SBX36_DMS::evtTrans(int32_t algoEvt)
{
    switch (algoEvt) {
        case EVT_TYPE_DMS_FATIGUE:
        case EVT_TYPE_DMS_FATIGUE_Eye:
        case EVT_TYPE_DMS_FATIGUE_YAWN:
            return SUBIAO_DMS_EVT_DDS;

        case EVT_TYPE_DMS_LOOK_AROUND:
        case EVT_TYPE_DMS_LOOK_DOWN:
        case EVT_TYPE_DMS_LOOK_UP:
            return SUBIAO_DMS_EVT_DISTRACTE;

        case EVT_TYPE_DMS_PHONE_CALL:
            return SUBIAO_DMS_EVT_PHONE_CALL;

        case EVT_TYPE_DMS_SMOKE:
            return SUBIAO_DMS_EVT_SMOKE;

        case EVT_TYPE_DMS_ABSENCE:
            return SUBIAO_DMS_EVT_DRIVER_ABNORMAL;
        case EVT_TYPE_DMS_CAM_OCCLUSION:
            return SUBIAO_DMS_EVT_CAM_OCCLUSION;
        case EVT_TYPE_DMS_EYE_OCCLUSION:
            return SUBIAO_DMS_EVT_EYE_OCCLUSION;
    }

    return SUBIAO_DMS_EVT_INVALID;
}

int32_t SBX36_TPMS::encode(my::string & msg)
{
    msg << my::hton;
    msg << alarmId;
    msg << beFlag;
    msg << altitude;
    msg << latitude;
    msg << longitude;

    for (int i = 0; i < sizeof(time); i++) {
        msg << time[i];
    }

    msg << carSigStat;
    mmCount = mTPTb.size();
    msg << mmCount;

    for (auto r : mTPTb) {
        my::string mmData;
        r.encode(mmData);
        msg << mmData;
    }

    return msg.length();
}

int32_t SBX36_BSD::encode(my::string & msg)
{
    msg << my::hton;
    msg << reserved[0] << reserved[1] << reserved[2] << reserved[3];
    msg << beFlag;
    msg << event;
    msg << speed;
    msg << altitude;
    msg << latitude;
    msg << longitude;

    for (int i = 0; i < sizeof(time); i++) {
        msg << time[i];
    }

    msg << carSigStat;

    return msg.length();
}
SUBIAO_BSD_EVT_E SBX36_BSD::evtTrans(int32_t algoEvt)
{
    switch (algoEvt) {
        case EVT_TYPE_BSD_Behind:
            return SUBIAO_BSD_EVT_APPROACH_REAR;
        case EVT_TYPE_BSD_Left:
            return SUBIAO_BSD_EVT_APPROACH_LR;
        case EVT_TYPE_BSD_Right:
            return SUBIAO_BSD_EVT_APPROACH_RR;
    }

    return SUBIAO_BSD_EVT_INVALID;
}
int32_t SBX36_BSD_CB_EXT::encode(my::string & msg)
{
    msg << my::hton;
    msg << reserved[0] << reserved[1] << reserved[2] << reserved[3];
    std::memcpy((char *)msg.c_str(), this, 1);
    msg << beFlag;
    msg << event;
    msg << speed;
    msg << altitude;
    msg << latitude;
    msg << longitude;

    for (int i = 0; i < sizeof(time); i++) {
        msg << time[i];
    }

    msg << carSigStat;

    mmCount = mmTbl.size();
    msg << mmCount;

    for (auto r : mmTbl) {
        my::string mmData;
        r.encode(mmData);
        msg << mmData;
    }

    return msg.length();
}
SUBIAO_BSD_EVT_E SBX36_BSD_CB_EXT::evtTrans(int32_t algoEvt)
{
    switch (algoEvt) {
        case EVT_TYPE_BSD_Front:
        case EVT_TYPE_BSD_Left:
            return SUBIAO_BSD_EVT_APPROACH_LR;
        case EVT_TYPE_BSD_Behind:
        case EVT_TYPE_BSD_Right:
            return SUBIAO_BSD_EVT_APPROACH_RR;
    }

    return SUBIAO_BSD_EVT_INVALID;
}
int32_t SBX37::encode(my::string & msg)
{
    msg << my::hton;
    msg << workstatus;
    msg << warningstatus;
    return msg.length();
}
int32_t SBX37_CB_EXT::encode(my::string &msg) {
<<<<<<<
    msg << my::hton;
    msg << workstatus;
    msg << warnBits.i;
=======
    msg.appendSize(5);
    std::memcpy((char *)msg.c_str() + msg.length(), this, sizeof(*this));
>>>>>>>
    return msg.length();
}

int32_t SBX38::encode(my::string & msg)
{
    msg << my::hton;
    msg << workStatus;
    msg << warnBits.i;
    return msg.length();
}
int32_t SBX50::decode(uint8_t * data, int32_t len)
{
    if (!data || len <= 0) {
        return -1;
    }

    my::constr msgStr((const char *)data, len);
    msgStr >> my::ntoh;

    msgStr >> messageID;
    msgStr >> multiMediaID;
    return 0;
}

int32_t SBX51_RCV::decode(uint8_t * data, int32_t len)
{
    if (!data || len <= 0) {
        return -1;
    }

    my::constr msgStr((const char *)data, len);
    msgStr >> my::ntoh;

    msgStr >> messageID;
    msgStr >> multiMediaID;
    msgStr >> packTotalNum;
    msgStr >> packCurIdx;
    msgStr >> result;

    return 0;
}
int32_t SBX51_SEND::encode(my::string & msg)
{
    msg << my::hton;

    msg << messageID;
    msg << multiMediaID;
    msg << packTotalNum;
    msg << packCurIdx;
    msg << multiMediaData;

    return msg.length();
}
