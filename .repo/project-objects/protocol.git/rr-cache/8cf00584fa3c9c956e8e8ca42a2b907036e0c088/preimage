#include "system_properties.h"
#include "prot.subiao.config.h"
#include "prot.subiao.h"
#include "media.mgr.h"
#include "ipc.cmd.h"

#include <set>

using namespace minieye;
#define MM_DATA_PKG_LEN (32 << 10)
#define PKG_VER_PATH "/system/etc/minieye/bsp.version.txt"
string getSwVer(const char * swName = "bsp")
{
    char line[4 << 10];
    string v = swName;
    FILE * fp = fopen(PKG_VER_PATH, "r");

    if (fp) {
        if (nullptr == fgets(line, sizeof(line), fp)) {
            loge("fgets error!");

        } else {
            char * p = line + strlen(line) - 1;

            while (*p && (p > line)) {
                if ((*p != ' ') && (*p != '\t') &&
                        (*p != '\r') && (*p != '\n')) {
                    break;
                }

                *p = 0;
                p--;
            }

            v = line;
        }

        fclose(fp);
    }

    return v;
}

// 返回没插好的摄像头
static std::set<std::string> checkCamWork(std::set<std::string> &func) {
    char propValue[PROP_VALUE_MAX] = {0};
    std::set<std::string> ret;

    int sec, cams;
    my::conf::ini ini;
    my::conf::ini::load(ini, "/data/minieye/idvr/etc/config.ini");
    if (__system_property_get("rw.minieye.cam_status", propValue) == 0) {  // 不存在那就先当做全插好了
        return ret;
    }
    stringstream ss(propValue);
    if (ss >> sec >> cams) {
        for (int i = 1; i < 8; i++) {
            char name[32] = "";
            snprintf(name, sizeof(name), "media.ch%d", i);
            auto funcStr = ini.get(name, "ai.func", "ch");
            auto enableStr = ini.get(name, "ai.enable", "ch");

            if (enableStr == "true" && func.find(funcStr.c_str()) != func.end() && ((cams & 1) == 0)) {
                ret.insert(funcStr.c_str());
            }
            cams >>= 1;
        }
    }
    return ret;
}

static int32_t getChByAlgo(std::string algo) {
    my::conf::ini ini;
    my::conf::ini::load(ini, "/data/minieye/idvr/etc/config.ini");
    my::string al(algo.c_str());

    auto cams = std::stoi(ini.get("media", "cameras", "4").c_str());
    char name[32] = "";

    for (auto i = 1; i <= cams; i++) {
        snprintf(name, sizeof(name), "media.ch%d", i);
        auto ena = ini.get(name, "ai.enable", "false");
        auto func = ini.get(name, "ai.func", "ch");
        if (al == func && ena == "true") {
            return i;
        }
        // logd("ch%d, func:%s, ena:%s", i, func.c_str(), ena.c_str());
    }
    return -1;
}

ProtSubiao::ProtSubiao(bool ignoreSpdThrs, bool associateChs, uint16_t vendor, uint8_t devid)
    : mbIgnoreSpdThrs(ignoreSpdThrs)
    , mbAssociateChs(false)  // 目前没有这个需求，防止其他功能不正常，先写死false
    , mVendor(vendor)
    , mDevId(devid) {
    SuBiaoConfig & config = SuBiaoConfig::getInstance();
    uint32_t w = 1280, h = 720;
    char resl[64];
    IpcCmd ipc;

    char prop[PROP_VALUE_MAX];
    __system_property_get("persist.subiao.subprot", prop);
    mSubProt = prop;
    logi("subiao subprot: %s", mSubProt.c_str());

    config.getAdasResolution(false, &w, &h);
    snprintf(resl, sizeof(resl), "%dx%d", w, h);
    ipc.reset().add("cmd").add("saveconf").add("media.ch2").add("video1.resl").add(resl);

    if (!LogCallProxyCmd::sendReq("config", ipc.cmd().c_str())) {
        loge("sock cmd failed %s", ipc.cmd().c_str());
    }

    config.getDmsResolution(false, &w, &h);
    snprintf(resl, sizeof(resl), "%dx%d", w, h);
    ipc.reset().add("cmd").add("saveconf").add("media.ch1").add("video1.resl").add(resl);

    if (!LogCallProxyCmd::sendReq("config", ipc.cmd().c_str())) {
        loge("sock cmd failed %s", ipc.cmd().c_str());
    }

    MediaMgr & mm = MediaMgr::getInstance();
    mm.addObserver(this, "subiao");
    mm.start();
}

ProtSubiao::~ProtSubiao()
{
}

const char * ProtSubiao::getProtName()
{
    return "SuBiao";
}

std::string ProtSubiao::info(void)
{
    std::string str = "";
    return str;
}
std::string ProtSubiao::dumpConfig(void)
{
    std::string str = "";
    return str;
}
int32_t ProtSubiao::queueMessage(SubiaoProtMsg & msg, uint32_t seq, bool front)
{
    if (seq > 0xFFFF) {
        msg.seq = htons(mSeq.next());

    } else {
        msg.seq = static_cast<uint16_t>(seq);
    }

    my::string data;

    if (!msg.vendor) {
        msg.vendor = mVendor;
    }

    if (!msg.devid) {
        msg.devid  = mDevId;
    }

    msg.encode(data);
    std::vector<uint8_t> v;
    onEscape((uint8_t *)data.c_str(), data.length(), v);
    msgEnque(v.data(), v.size(), front);
    int32_t plen = v.size();
    if ((msg.cmd == CMD_UPLOAD_MM_DATA) && (plen > 32)) {
        plen = 32;
    }
    my::hexdump(my::constr((const char *)v.data(), plen), true);
    return 0;
}
int32_t ProtSubiao::onServerConnected(void)
{
    mTcpConnected = true;
    SubiaoProtMsg msg(CMD_UPLOAD_STATUS);
    SBX38 pl;
    pl.workStatus = MODULE_WORKING;
    pl.encode(msg.payload);
    queueMessage(msg);
    return 0;
}
int32_t ProtSubiao::onServerDisconnected(void)
{
    mTcpConnected = false;
    return 0;
}

bool ProtSubiao::onMessage(uint8_t * p, uint32_t len)
{
    MediaMgr & mm = MediaMgr::getInstance();
    SubiaoProtMsg req;

    if (req.decode(p, len) < 0) {
        return false;
    }

    if (CMD_SPEED_LBS_INFO != req.cmd) {
        logd("cmd 0x%02x, seq 0x%x", req.cmd, req.seq);
        my::hexdump(my::constr((const char *)p, len), true);
    }

    switch (req.devid) { // 外设编号
        case DEVICE_ID_ADAS: {
                break;
            }

        case DEVICE_ID_DSM: {
                break;
            }

        case DEVICE_ID_TPMS: {
                break;
            }

        case DEVICE_ID_BSD: {
                break;
            }

        case DEVICE_ID_BRDCST: {
                if (req.cmd != CMD_QUERY) {
                    return false;
                }

                break;
            }

        default:
            return false;
    }

    SubiaoProtMsg rsp(req.cmd);

    switch (req.cmd) {  // 功能码
        case CMD_QUERY: {          // 查询指令
                if (DEVICE_ID_BRDCST != req.devid) {
                    queueMessage(rsp, req.seq);

                } else {
                    rsp.devid = DEVICE_ID_ADAS;
                    queueMessage(rsp, req.seq);
                    rsp.devid = DEVICE_ID_DSM;
                    queueMessage(rsp, req.seq);
                    rsp.devid = DEVICE_ID_BSD;
                    queueMessage(rsp, req.seq);
                    rsp.devid = DEVICE_ID_TPMS;
                    queueMessage(rsp, req.seq);
                }

                break;
            }

        case CMD_FACTORY_RESET: {  // 恢复默认参数指令
                queueMessage(rsp, req.seq);
                break;
            }

        case CMD_SPEED_LBS_INFO: {  // 实时数据指令
                SBX31 pl;

                if (pl.decode((uint8_t *)req.payload.c_str(), req.payload.length()) < 0) {
                    loge("CMD_SPEED_LBS_INFO decode fail!");
                    break;

                } else {
                    std::lock_guard<std::mutex> _l(mLastSpdLbsMtx);
                    mLastSpdLbsInfo = pl;
                }

                my::string value;
                value.assignf("%d", mLastSpdLbsInfo.speed * 10);

                if (__system_property_set("rw.minieye.fakespeed", value.c_str())) {
                    loge("set speed prop fail!");
                }

                value.assignf("%f, %f", mLastSpdLbsInfo.latitude * 1.0 / 1000000, mLastSpdLbsInfo.longitude * 1.0 / 1000000);

                if (__system_property_set("rw.subiao.location", value.c_str())) {
                    loge("set location prop fail!");
                }

                break;
            }

        case CMD_DEVICE_INFO: {  // 查询外设基本信息
                char sn[PROP_VALUE_MAX] = {0};

                if (__system_property_get("persist.minieye.deviceId", sn) > 0) {
                    if (sn[0]) {
                        logd("devid = %s", sn);
                    }
                }

                char board[PROP_VALUE_MAX] = {0};

                if (__system_property_get("ro.product.board", board) > 0) {
                    if (board[0]) {
                        logd("board = %s", board);
                    }
                }

                SubiaoProtMsg protmsg(CMD_DEVICE_INFO);
                protmsg.devid = req.devid;

                SBX32 pl;

                pl.peripheralInfoMap[PERIPHERAL_CONTENT_NAME::COMPANY_NAME] = "MNEYE";
                pl.peripheralInfoMap[PERIPHERAL_CONTENT_NAME::RPODUCT_NAME] = board;
                pl.peripheralInfoMap[PERIPHERAL_CONTENT_NAME::HW_VERSION]   = "V1.0";
                pl.peripheralInfoMap[PERIPHERAL_CONTENT_NAME::SW_VERSION]   = getSwVer().c_str();
                pl.peripheralInfoMap[PERIPHERAL_CONTENT_NAME::DEVICE_ID]    = sn;
                pl.peripheralInfoMap[PERIPHERAL_CONTENT_NAME::CUSTOMER_CODE] = "CUSTOM";

                pl.encode(protmsg.payload);
                queueMessage(protmsg, req.seq);

                break;
            }

        case CMD_UPGRADE: {  // 升级外设程序
#define UPGRADE_FILE_PATH "/tmp/.upgrade.bin"
                SubiaoProtMsg protmsg(CMD_UPGRADE);
                protmsg.devid = req.devid;

                SBX33 pl;
                pl.decode((uint8_t *)req.payload.c_str(), req.payload.length());

                switch (pl.upgradeMsgId) {
                    case UPGRADE_MSG_ID_E::DATA: {
                            mUpgradeData[pl.packCurIdx] = pl;

                            if (mUpgradeData.size() == (pl.packTotalNum + 1)) {
                                // 计算校验值
                                uint32_t csRcv = -1;
                                uint32_t cs = 0;
                                unlink(UPGRADE_FILE_PATH);
                                my::file f;
                                f.open(UPGRADE_FILE_PATH);

                                for (auto r : mUpgradeData) {
                                    if (r.second.packCurIdx) {
                                        f.puts(r.second.data.c_str(), r.second.data.length());

                                        for (int32_t i = 0; i < r.second.data.length(); i++) {
                                            cs += r.second.data[i];
                                        }

                                    } else {
                                        csRcv = r.second.checkSum;
                                    }
                                }

                                f.close();
                                pl.encode(protmsg.payload, (cs == csRcv));
                                queueMessage(protmsg, req.seq);
                            }

                            break;
                        }

                    case UPGRADE_MSG_ID_E::CLEAR: {
                            mUpgradeData.clear();
                            pl.encode(protmsg.payload, true);
                            queueMessage(protmsg, req.seq);
                            break;
                        }

                    case UPGRADE_MSG_ID_E::START: {
                            // 执行升级操作
                            system("cd /; tar xzvf " UPGRADE_FILE_PATH "; sync;");
                            pl.encode(protmsg.payload, true);
                            queueMessage(protmsg, req.seq);
                            break;
                        }

                    case UPGRADE_MSG_ID_E::EXECUTE: {
                            pl.encode(protmsg.payload, true);
                            queueMessage(protmsg, req.seq);
                            system("sleep 5 && reboot &");
                            break;
                        }

                    default:
                        break;
                }

                break;
            }

        case CMD_GET_PARAM: {  // 查询参数
                std::cout << "查询参数 : " << "cmd=" << std::hex << req.cmd << "  seq=" << std::hex << req.seq << std::endl;

                SubiaoProtMsg protmsg(CMD_GET_PARAM);
                protmsg.devid = req.devid;

                SBX34_X35 pl;

                switch (req.devid) {   // 外设编号
                    case DEVICE_ID_ADAS: {
                            pl.adas_query_parameter = mSafAssDriSysPar.adas_query_parameter;
                            pl.adas_query_parameter.encode(protmsg.payload);
                            queueMessage(protmsg, req.seq);
                            break;
                        }

                    case DEVICE_ID_DSM: {
                            pl.dsm_query_parameter = mSafAssDriSysPar.dsm_query_parameter;
                            pl.dsm_query_parameter.encode(protmsg.payload);
                            queueMessage(protmsg, req.seq);
                            break;
                        }

                    case DEVICE_ID_TPMS: {
                            pl.tpms_query_parameter = mSafAssDriSysPar.tpms_query_parameter;
                            pl.tpms_query_parameter.encode(protmsg.payload);
                            queueMessage(protmsg, req.seq);
                            break;
                        }

                    case DEVICE_ID_BSD: {
                            pl.bsd_query_parameter = mSafAssDriSysPar.bsd_query_parameter;
                            pl.bsd_query_parameter.encode(protmsg.payload);
                            queueMessage(protmsg, req.seq);
                            break;
                        }

                    default:
                        return false;
                }

                break;
            }

        case CMD_SET_PARAM: {  // 设置参数
                std::cout << "设置参数 : " << "cmd=" << std::hex << req.cmd << "  seq=" << std::hex << req.seq << std::endl;

                SubiaoProtMsg protmsg(CMD_SET_PARAM);
                protmsg.devid = req.devid;

                SBX34_X35 pl;

                switch (req.devid) {   // 外设编号
                    case DEVICE_ID_ADAS: {
                            pl.adas_query_parameter.decode((uint8_t *)req.payload.c_str(), req.payload.length());
                            mSafAssDriSysPar.adas_query_parameter = pl.adas_query_parameter;
                            protmsg.payload = 0x1;
                            queueMessage(protmsg, req.seq);
                            break;
                        }

                    case DEVICE_ID_DSM: {
                            pl.dsm_query_parameter.decode((uint8_t *)req.payload.c_str(), req.payload.length());
                            mSafAssDriSysPar.dsm_query_parameter = pl.dsm_query_parameter;
                            protmsg.payload = 0x1;
                            queueMessage(protmsg, req.seq);
                            break;
                        }

                    case DEVICE_ID_TPMS: {
                            pl.tpms_query_parameter.decode((uint8_t *)req.payload.c_str(), req.payload.length());
                            mSafAssDriSysPar.tpms_query_parameter = pl.tpms_query_parameter;
                            protmsg.payload = 0x1;
                            queueMessage(protmsg, req.seq);
                            break;
                        }

                    case DEVICE_ID_BSD: {
                            pl.bsd_query_parameter.decode((uint8_t *)req.payload.c_str(), req.payload.length());
                            mSafAssDriSysPar.bsd_query_parameter = pl.bsd_query_parameter;
                            protmsg.payload = 0x1;
                            queueMessage(protmsg, req.seq);
                            break;
                        }

                    default:
                        protmsg.payload = 0x0;
                        queueMessage(protmsg, req.seq);
                        return false;
                }

                break;
            }

        case CMD_WARNING_REPORT: {  // 事件/报警上报
                std::cout << "事件/报警上报 : " << "cmd=" << std::hex << req.cmd << "  seq=" << std::hex << req.seq << std::endl;
                std::cout << "事件/报警上报 : 此为终端应答" << std::endl;
                break;
            }

        case CMD_REQ_STATUS: {  // 外设状态查询
                std::cout << "外设状态查询 : " << "cmd=" << std::hex << req.cmd << "  seq=" << std::hex << req.seq << std::endl;

                SubiaoProtMsg protmsg(CMD_REQ_STATUS);
                protmsg.devid = req.devid;

                if (mSubProt == "xiamencibei") {
                    SBX37_CB_EXT pl;
                    pl.workstatus = mPeripheralWorkSta.workstatus;
                    std::set<std::string> funcList{"adas", "dms", "bsd", "hod", "fbsd", "objp"};
<<<<<<<

                    auto chkResult = checkCamWork(funcList);
                    pl.warnBits.bits.adasCamError = chkResult.count("adas") ? 1 : 0;
                    pl.warnBits.bits.dmsCamError = chkResult.count("dms") ? 1 : 0;
                    pl.warnBits.bits.leftbsdCamError = chkResult.count("hod") ? 1 : 0;
                    pl.warnBits.bits.rightbsdCamError = (chkResult.count("objp") + chkResult.count("fbsd") + chkResult.count("bsd")) ? 1 : 0;
                    logd("adasCamError %d, dmsCamError %d, leftbsdCamError %d, rightbsdCamError %d",
                         pl.warnBits.bits.adasCamError,
                         pl.warnBits.bits.dmsCamError,
                         pl.warnBits.bits.leftbsdCamError,
                         pl.warnBits.bits.rightbsdCamError);

                    pl.encode(protmsg.payload);
                    my::hexdump(my::constr((const char *)protmsg.payload.c_str(), protmsg.payload.length()), true);
=======
                    auto chkResult = checkCamWork(funcList);
                    pl.adasCamError = chkResult.count("adas") ? 1 : 0;
                    pl.dmsCamError = chkResult.count("dms") ? 1 : 0;
                    pl.leftbsdCamError = chkResult.count("hod") ? 1 : 0;
                    pl.rightbsdCamError =
                        (chkResult.count("objp") + chkResult.count("fbsd") + chkResult.count("bsd")) ? 1 : 0;

                    pl.encode(protmsg.payload);
>>>>>>>
                    queueMessage(protmsg, req.seq);
                } else {
                    SBX37 pl;
                    pl.workstatus = mPeripheralWorkSta.workstatus;
                    pl.warningstatus = mPeripheralWorkSta.warningstatus;

                    pl.encode(protmsg.payload);
                    queueMessage(protmsg, req.seq);
                }

                break;
            }

        case CMD_UPLOAD_STATUS: {  // 外设状态上报
                std::cout << "外设状态上报 : " << "cmd=" << std::hex << req.cmd << "  seq=" << std::hex << req.seq << std::endl;
                std::cout << "外设状态上报 : 此为终端应答" << std::endl;
                break;
            }

        case CMD_REQ_MM_DATA: {  // 请求多媒体数据
                SBX50 pl;
                pl.decode((uint8_t *)req.payload.c_str(), req.payload.length());
                logd("cmd 0x%x seq 0x%x", req.cmd, req.seq);
                logd("msgId 0x%x, mmid %d", pl.messageID, pl.multiMediaID);
                {
                    std::lock_guard<std::mutex> _l(mMMID2CurIdxMapMtx);
                    if (mMMID2CurIdxMap.find(pl.multiMediaID) == mMMID2CurIdxMap.end()) {
                        loge("Not found mmid %d !!!", pl.multiMediaID);
                        break;
                    }
                }
                SubiaoProtMsg protmsg(CMD_REQ_MM_DATA);
                protmsg.devid = req.devid;
                queueMessage(protmsg, req.seq);

                sendMMData(req.devid, pl.messageID, pl.multiMediaID);
                break;
            }

        case CMD_UPLOAD_MM_DATA: {  // 上传多媒体数据
                SBX51_RCV pl;
                pl.decode((uint8_t *)req.payload.c_str(), req.payload.length());
                logd("cmd 0x%x seq 0x%x", req.cmd, req.seq);
                logd("msgId 0x%x, mmid %d, totoal %d, cur %d, res %d", pl.messageID, pl.multiMediaID, pl.packTotalNum, pl.packCurIdx, pl.result);

                if (!pl.result) {
                    do {
                        if (true) {
                            std::lock_guard<std::mutex> _l(mMMID2CurIdxMapMtx);

                            if (mMMID2CurIdxMap.find(pl.multiMediaID) == mMMID2CurIdxMap.end()) {
                                loge("Invalid MMID %d", pl.multiMediaID);
                                break;
                            }

                            if ((pl.packTotalNum - 1) == mMMID2CurIdxMap[pl.multiMediaID]) {
                                mMMID2CurIdxMap.erase(pl.multiMediaID);
                                break;
                            } else {
                                mMMID2CurIdxMap[pl.multiMediaID]++;
                            }
                        }
                        sendMMData(req.devid, pl.messageID, pl.multiMediaID);/*发下一包*/
                    } while (0);
                } else {/*重发*/
                    sendMMData(req.devid, pl.messageID, pl.multiMediaID, pl.packCurIdx);
                }

                break;
            }

        case CMD_SNAP_SHOT: {  // 立即拍照指令
                std::cout << "立即拍照指令 : " << "cmd=" << std::hex << req.cmd << "  seq=" << std::hex << req.seq << std::endl;

                SubiaoProtMsg protmsg(CMD_REQ_MM_DATA);
                protmsg.devid = req.devid;

                protmsg.payload = 0x0;
                queueMessage(protmsg, req.seq);

                break;
            }

        default: {
                loge("Not impl cmd 0x%02x", req.cmd);
                break;
            }
    }

    return true;
}

bool ProtSubiao::onValidate(uint8_t * p, uint32_t len)
{
    if (PROT_JTT_MAGIC != p[0] || PROT_JTT_MAGIC != p[len - 1]) {
        return false;
    }

    SubiaoProtMsg msg;
    uint8_t cs = msg.checksum(p, len);
    return (cs == p[1]);
}

void ProtSubiao::onMessageReceived(const std::shared_ptr<minieye::AMessage> & msg)
{
    MediaMgr & mm = MediaMgr::getInstance();
    SubiaoProtMsg protmsg(CMD_WARNING_REPORT);
    SuBiaoConfig & config = SuBiaoConfig::getInstance();

    EVT_TYPE e = (EVT_TYPE)msg->what();
    if (e >= EVT_TYPE_PROT_CUSTOM_BGN) {
        return;
    }

    minieye::AString algo;
    int32_t vt = 0, pn = 0, threshold = 0, level = 0;
    int64_t ts = 0;
    float speed = 0;
    msg->findString("algo",     &algo);
    msg->findInt64("ts",        &ts);
    msg->findInt32("duration",  &vt);
    msg->findInt32("picNo",     &pn);
    msg->findInt32("spdThres",  &threshold);
    msg->findFloat("speed",     &speed);
    msg->findInt32("level",     &level);

    if (algo == "adas") {
        std::lock_guard<std::mutex> _l(mLastSpdLbsMtx);
        protmsg.devid = DEVICE_ID_ADAS;
        SBX36_ADAS pl;
        pl.alarmId  = mWarningID.next();
        pl.beFlag   = SB_WARN_STATUS_NONE;
        pl.event    = (uint8_t)pl.evtTrans(e);

        if (SUBIAO_ADAS_EVT_INVALID == pl.event) {
            loge("invalid %s event %d", algo.c_str(), e);
            return ;
        }

        pl.preCarSpd = 0;//todo
        pl.speed = (uint8_t)speed;
        pl.altitude = mLastSpdLbsInfo.altitude;
        pl.latitude = mLastSpdLbsInfo.latitude;
        pl.longitude = mLastSpdLbsInfo.longitude;
        my::time2bcd((my::uint)time(nullptr), (char *)pl.time);
        pl.carSigStat = mLastSpdLbsInfo.CarSigStat;

        const char * evtName = value2name(algo.c_str(), e);
        std::vector<struct MMParam> param;

        auto chn = getChByAlgo("adas");
        if (pn > 0) {
            uint32_t w = 1280, h = 720;
            config.getAdasResolution(true, &w, &h);
            struct MMParam m;
            m.channel = chn;
            m.ts = ts;
            m.type = MM_TYPE_PIC;
            m.u.pic.seek = 0;
            m.u.pic.width = w;
            m.u.pic.height = h;
            m.u.pic.count = pn;
            param.push_back(m);
        }

        if (vt > 0) {
            struct MMParam m;
            m.channel = chn;
            m.ts = ts;
            m.type = MM_TYPE_VID;
            m.u.video.seek = -vt;
            m.u.video.duration = vt * 2;
            param.push_back(m);

            if (mbAssociateChs) {
                m.channel = 1;
                param.push_back(m);
            }
        }

        if (param.size() > 0) {
            mm.addTransaction(param, evtName,
            [&pl, this](Transmition * t) -> void {/*生成附件，填充信息*/
                MMInfo mmi;
                const char * path = t->getPath();

                if (strcasestr(path, ".jpg"))
                {
                    mmi.type = MM_TYPE_PIC;

                } else if (strcasestr(path, ".mp4"))
                {
                    mmi.type = MM_TYPE_VID;
                }
                mmi.id = t->getID();
                logd("%d : %s", mmi.id, path);
                std::lock_guard<std::mutex> _l(mMMID2CurIdxMapMtx);
                pl.mmTbl.push_back(mmi);
            });
        }

        pl.encode(protmsg.payload);
        queueMessage(protmsg);

    } else if (algo == "dms") {
        std::lock_guard<std::mutex> _l(mLastSpdLbsMtx);
        SBX36_DMS pl;
        protmsg.devid = DEVICE_ID_DSM;
        pl.event = (uint8_t)pl.evtTrans(e);

        if (SUBIAO_DMS_EVT_INVALID == pl.event) {
            loge("invalid %s event %d", algo.c_str(), e);
            return ;
        }

        pl.speed = speed;
        pl.altitude = mLastSpdLbsInfo.altitude;
        pl.latitude = mLastSpdLbsInfo.latitude;
        pl.longitude = mLastSpdLbsInfo.longitude;
        my::time2bcd((my::uint)time(nullptr), (char *)pl.time);
        pl.carSigStat = mLastSpdLbsInfo.CarSigStat;

        const char * evtName = value2name(algo.c_str(), e);
        std::vector<struct MMParam> param;
        auto chn = getChByAlgo("dms");

        if (pn > 0) {
            uint32_t w = 1280, h = 720;
            config.getDmsResolution(true, &w, &h);
            struct MMParam m;
            m.channel = chn;
            m.ts = ts;
            m.type = MM_TYPE_PIC;
            m.u.pic.seek = 0;
            m.u.pic.width = w;
            m.u.pic.height = h;
            m.u.pic.count = pn;
            param.push_back(m);
        }

        if (vt > 0) {
            struct MMParam m;
            m.channel = chn;
            m.ts = ts;
            m.type = MM_TYPE_VID;
            m.u.video.seek = -vt;
            m.u.video.duration = vt * 2;
            param.push_back(m);

            if (mbAssociateChs) {
                m.channel = 2;
                param.push_back(m);
            }
        }

        if (param.size() > 0) {
            mm.addTransaction(param, evtName,
            [&pl, this](Transmition * t) -> void {/*生成附件，填充信息*/
                MMInfo mmi;
                const char * path = t->getPath();

                if (strcasestr(path, ".jpg"))
                {
                    mmi.type = MM_TYPE_PIC;

                } else if (strcasestr(path, ".mp4"))
                {
                    mmi.type = MM_TYPE_VID;
                }
                mmi.id = t->getID();
                logd("%d : %s", mmi.id, path);
                std::lock_guard<std::mutex> _l(mMMID2CurIdxMapMtx);
                
                pl.mmTbl.push_back(mmi);
            });
        }

        pl.encode(protmsg.payload);
        queueMessage(protmsg);

    } else if (algo == "tpms") {
        SBX36_TPMS pl;
        protmsg.devid = DEVICE_ID_BSD;

        pl.altitude = mLastSpdLbsInfo.altitude;
        pl.latitude = mLastSpdLbsInfo.latitude;
        pl.longitude = mLastSpdLbsInfo.longitude;
        my::time2bcd((my::uint)time(nullptr), (char *)pl.time);
        pl.carSigStat = mLastSpdLbsInfo.CarSigStat;
        pl.encode(protmsg.payload);
        queueMessage(protmsg);

    } else if (algo == "bsd" || algo == "fbsd") {
        if (mSubProt == "xiamencibei") {
            static my::timestamp lastBsdTs = 0;
            if (lastBsdTs.elapsed() < 5000) {
                return;
            }
            SBX36_BSD_CB_EXT pl;
            protmsg.devid = DEVICE_ID_BSD;
            pl.event = (uint8_t)pl.evtTrans(e);

            if (SUBIAO_BSD_EVT_INVALID == pl.event) {
                loge("invalid %s event %d", algo.c_str(), e);
                return;
            }

            pl.bsdLevel = level;
            pl.speed = speed;
            pl.altitude = mLastSpdLbsInfo.altitude;
            pl.latitude = mLastSpdLbsInfo.latitude;
            pl.longitude = mLastSpdLbsInfo.longitude;
            my::time2bcd((my::uint)time(nullptr), (char *)pl.time);
            std::vector<struct MMParam> param;

            auto chn = -1;
            if (pl.event == SUBIAO_BSD_EVT_APPROACH_RR) {
                chn = getChByAlgo("bsd");
                if (chn == -1) {
                    chn = getChByAlgo("fbsd");
                }
            } else if (pl.event == SUBIAO_BSD_EVT_APPROACH_LR) {
                chn = getChByAlgo("hod");
            }
            const char *evtName = value2name(algo.c_str(), e);

            if (pn > 0) {
                uint32_t w = 704, h = 576;  // D1
                struct MMParam m;
                m.channel = chn;
                m.ts = ts;
                m.type = MM_TYPE_PIC;
                m.u.pic.seek = 0;
                m.u.pic.width = w;
                m.u.pic.height = h;
                m.u.pic.count = pn;
                param.push_back(m);
            }

            if (vt > 0) {
                struct MMParam m;
                m.channel = chn;
                m.ts = ts;
                m.type = MM_TYPE_VID;
                m.u.video.seek = -vt;
                m.u.video.duration = vt * 2;
                param.push_back(m);

                if (mbAssociateChs) {
                    m.channel = 2;
                    param.push_back(m);
                }
            }

            if (param.size() > 0) {
                mm.addTransaction(
                    param, evtName, 
                    [&pl, this](Transmition *t) -> void { /*生成附件，填充信息*/
                        MMInfo mmi;
                        const char *path = t->getPath();

                        if (strcasestr(path, ".jpg")) {
                            mmi.type = MM_TYPE_PIC;

                        } else if (strcasestr(path, ".mp4")) {
                            mmi.type = MM_TYPE_VID;
                        }
                        mmi.id = t->getID();
                        logd("%d : %s", mmi.id, path);
                        std::lock_guard<std::mutex> _l(
                            mMMID2CurIdxMapMtx);

                        pl.mmTbl.push_back(mmi);
                    });
            }

            pl.encode(protmsg.payload);
            queueMessage(protmsg);
            lastBsdTs = my::timestamp::now();
        } else {
            SBX36_BSD pl;
            protmsg.devid = DEVICE_ID_BSD;
            pl.event = (uint8_t)pl.evtTrans(e);

            if (SUBIAO_BSD_EVT_INVALID == pl.event) {
                loge("invalid %s event %d", algo.c_str(), e);
                return;
            }

            pl.speed = speed;
            pl.altitude = mLastSpdLbsInfo.altitude;
            pl.latitude = mLastSpdLbsInfo.latitude;
            pl.longitude = mLastSpdLbsInfo.longitude;
            my::time2bcd((my::uint)time(nullptr), (char *)pl.time);
            pl.encode(protmsg.payload);
            queueMessage(protmsg);
        }

    } else {
<<<<<<<
=======
        logd("onMessageReceived incompatible algo %s", algo.c_str());
>>>>>>>
    }
}

bool ProtSubiao::onAlgoEvent(std::shared_ptr<Event> evt)
{
    const SuBiaoConfig::AdasParam * adasParam = SuBiaoConfig::getInstance().getAdasParam();
    const SuBiaoConfig::DmsParam  *  dmsParam = SuBiaoConfig::getInstance().getDmsParam();
    uint8_t vt = 0, pn = 0, threshold = 0;
    if (evt->type() >= EVT_TYPE_PROT_CUSTOM_BGN) {
        return false;
    }
    switch (evt->type()) {
        case EVT_TYPE_DMS_SNAP:
        case EVT_TYPE_DMS_FATIGUE:
        case EVT_TYPE_DMS_FATIGUE_Eye:
        case EVT_TYPE_DMS_FATIGUE_YAWN: {
                vt = dmsParam->VT(Fatigue);
                pn = dmsParam->PN(Fatigue);
                threshold = dmsParam->comm.warnSpeedThreshold;
                break;
            }

        case EVT_TYPE_DMS_LOOK_AROUND:
        case EVT_TYPE_DMS_LOOK_DOWN:
        case EVT_TYPE_DMS_LOOK_UP: {
                vt = dmsParam->VT(Distraction);
                pn = dmsParam->PN(Distraction);
                threshold = dmsParam->comm.warnSpeedThreshold;
                break;
            }

        case EVT_TYPE_DMS_PHONE_CALL: {
                vt = dmsParam->VT(Call);
                pn = dmsParam->PN(Call);
                threshold = dmsParam->comm.warnSpeedThreshold;
                break;
            }

        case EVT_TYPE_DMS_SMOKE: {
                vt = dmsParam->VT(Smoke);
                pn = dmsParam->PN(Smoke);
                threshold = dmsParam->comm.warnSpeedThreshold;
                break;
            }

        case EVT_TYPE_DMS_ABSENCE: {
                vt = dmsParam->VT(Abnormal);
                pn = dmsParam->PN(Abnormal);
                threshold = dmsParam->comm.warnSpeedThreshold;
                break;
            }

        case EVT_TYPE_DMS_CAM_OCCLUSION:
        case EVT_TYPE_DMS_EYE_OCCLUSION: {
                vt = 5;
                pn = 1;
                threshold = dmsParam->comm.warnSpeedThreshold;
                break;
            }

        case EVT_TYPE_DMS_DRIVER_MATCH:
        case EVT_TYPE_DMS_DRIVER_NOT_MATCH:
        case EVT_TYPE_DMS_DRIVER_CHG:

        case EVT_TYPE_DMS_OVER_TIME:
        case EVT_TYPE_DMS_NOT_BELT:
        case EVT_TYPE_DMS_MASK:
        case EVT_TYPE_DMS_DRINK:
        case EVT_TYPE_DMS_PLAY_WIRH_PHONE: {
                return false;
            }

        case EVT_TYPE_ADAS_LeftLDW:
        case EVT_TYPE_ADAS_RightLDW: {
                vt = adasParam->VT(LDW);
                pn = adasParam->PN(LDW);
                threshold = adasParam->comm.warnSpeedThreshold;
                break;
            }

        case EVT_TYPE_ADAS_FCW: {
                vt = adasParam->VT(FCW);
                pn = adasParam->PN(FCW);
                threshold = adasParam->comm.warnSpeedThreshold;
                break;
            }

        case EVT_TYPE_ADAS_HW: {
                vt = adasParam->VT(HW);
                pn = adasParam->PN(HW);
                threshold = adasParam->comm.warnSpeedThreshold;
                break;
            }

        case EVT_TYPE_ADAS_PCW: {
                vt = adasParam->VT(PCW);
                pn = adasParam->PN(PCW);
                threshold = adasParam->comm.warnSpeedThreshold;
                break;
            }

        case EVT_TYPE_ADAS_TSRW: { /*超限*/
                vt = 5;
                pn = 3;
                threshold = adasParam->comm.warnSpeedThreshold;
                break;
            }

        case EVT_TYPE_ADAS_TSR: { /* 识别路牌 */
                vt = 5;
                pn = 0;
                threshold = adasParam->comm.warnSpeedThreshold;
                break;
            }

        case EVT_TYPE_ADAS_CamOcclusion: {
                vt = 5;
                pn = 1;
                threshold = adasParam->comm.warnSpeedThreshold;
                break;
            }

        case EVT_TYPE_BSD_Behind:
        case EVT_TYPE_BSD_Front:
        case EVT_TYPE_BSD_Left:
        case EVT_TYPE_BSD_Right:{
            vt = 5;
            pn = 3;
            threshold = 0;
            break;
        }

        default: {
                break;
            }
    }

    if ((evt->type() == EVT_TYPE_ADAS_PCW) || mbIgnoreSpdThrs || (evt->c.speed >= threshold)) {
        std::string algo = evt->algoName();
        std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
        amsg->setTarget(this->shared_from_this());
        amsg->setWhat(evt->type());
        amsg->setString("algo",     algo.c_str(), algo.length());
        amsg->setFloat("speed",     evt->c.speed);
        amsg->setInt64("ts",        evt->c.ts);
        amsg->setInt32("level",     evt->c.level);
        amsg->setInt32("duration",  vt);
        amsg->setInt32("picNo",     pn);
        amsg->setInt32("spdThres",  threshold);
        amsg->post();
        logd("evt %s spd %f thres %d vt %d pn %d", evt->c.event.c_str(), evt->c.speed, threshold, vt, pn);

    } else {
        loge("evt %s spd %f < thres %d", evt->c.event.c_str(), evt->c.speed, threshold);
    }

    return true;
}

void ProtSubiao::onMMEvent(MM_EVT_E event, Transaction * t)
{
    switch (event) {
        case MM_EVT_TRANSACTION_DESTROY: {
            for (auto r : t->mTransmitions) {
                std::lock_guard<std::mutex> _l(mMMID2CurIdxMapMtx);
                mMMID2CurIdxMap.erase(r.second->getID());
            }

            break;
        }

        case MM_EVT_TRANSACTION_READY: {
            logd("transaction %" FMT_LLD ", task is ready!", t->ID());
            for (auto r : t->mTransmitions) {
                std::lock_guard<std::mutex> _l(mMMID2CurIdxMapMtx);
                logd("mmid %5d : %s", r.second->getID(), r.second->getPath());
                mMMID2CurIdxMap[r.second->getID()] = 0;/*可以上传了*/
            }

            break;
        }

        default: {
            loge("MM Event not impl 0x%x", event);
            break;
        }
    }
}
int32_t ProtSubiao::sendMMData(uint8_t devid, uint8_t type, int32_t mmid, int32_t packIdx)
{
    MediaMgr & mm = MediaMgr::getInstance();
    SubiaoProtMsg uploadMsg(CMD_UPLOAD_MM_DATA);
    uploadMsg.devid = devid;

    SBX51_SEND pl;
    pl.messageID = type;
    pl.multiMediaID = mmid;
    //pl.packTotalNum;
    {
        std::lock_guard<std::mutex> _l(mMMID2CurIdxMapMtx);
        if (packIdx < 0) {
            pl.packCurIdx = mMMID2CurIdxMap[pl.multiMediaID];
        } else {
            pl.packCurIdx = packIdx;
        }
    }
    logd("mmid %d, curIdx %d", pl.multiMediaID, pl.packCurIdx);
    //pl.multiMediaData;
    mm.getMMData(pl.multiMediaID, pl.packCurIdx, MM_DATA_PKG_LEN, pl.packTotalNum, pl.multiMediaData);
    pl.encode(uploadMsg.payload);
    return queueMessage(uploadMsg);
}