.\" DO NOT MODIFY THIS FILE!  It was generated by help2man.
.TH REPO "1" "April 2025" "repo" "Repo Manual"
.SH NAME
repo \- repository management tool built on top of git
.SH SYNOPSIS
.B repo
[\fI\,-p|--paginate|--no-pager\/\fR] \fI\,COMMAND \/\fR[\fI\,ARGS\/\fR]
.SH OPTIONS
.TP
\fB\-h\fR, \fB\-\-help\fR
show this help message and exit
.TP
\fB\-\-help\-all\fR
show this help message with all subcommands and exit
.TP
\fB\-p\fR, \fB\-\-paginate\fR
display command output in the pager
.TP
\fB\-\-no\-pager\fR
disable the pager
.TP
\fB\-\-color\fR=\fI\,COLOR\/\fR
control color usage: auto, always, never
.TP
\fB\-\-trace\fR
trace git command execution (REPO_TRACE=1)
.TP
\fB\-\-trace\-to\-stderr\fR
trace outputs go to stderr in addition to
\&.repo/TRACE_FILE
.TP
\fB\-\-trace\-python\fR
trace python command execution
.TP
\fB\-\-time\fR
time repo command execution
.TP
\fB\-\-version\fR
display this version of repo
.TP
\fB\-\-show\-toplevel\fR
display the path of the top\-level directory of the
repo client checkout
.TP
\fB\-\-event\-log\fR=\fI\,EVENT_LOG\/\fR
filename of event log to append timeline to
.TP
\fB\-\-git\-trace2\-event\-log\fR=\fI\,GIT_TRACE2_EVENT_LOG\/\fR
directory to write git trace2 event log to
.TP
\fB\-\-submanifest\-path\fR=\fI\,REL_PATH\/\fR
submanifest path
.SS "The complete list of recognized repo commands is:"
.TP
abandon
Permanently abandon a development branch
.TP
branch
View current topic branches
.TP
branches
View current topic branches
.TP
checkout
Checkout a branch for development
.TP
cherry\-pick
Cherry\-pick a change.
.TP
diff
Show changes between commit and working tree
.TP
diffmanifests
Manifest diff utility
.TP
download
Download and checkout a change
.TP
forall
Run a shell command in each project
.TP
gc
Cleaning up internal repo and Git state.
.TP
grep
Print lines matching a pattern
.TP
help
Display detailed help on a command
.TP
info
Get info on the manifest branch, current branch or unmerged branches
.TP
init
Initialize a repo client checkout in the current directory
.TP
list
List projects and their associated directories
.TP
manifest
Manifest inspection utility
.TP
overview
Display overview of unmerged project branches
.TP
prune
Prune (delete) already merged topics
.TP
rebase
Rebase local branches on upstream branch
.TP
selfupdate
Update repo to the latest version
.TP
smartsync
Update working tree to the latest known good revision
.TP
stage
Stage file(s) for commit
.TP
start
Start a new branch for development
.TP
status
Show the working tree status
.TP
sync
Update working tree to the latest revision
.TP
upload
Upload changes for code review
.TP
version
Display the version of repo
.PP
See 'repo help <command>' for more information on a specific command.
Bug reports: https://issues.gerritcodereview.com/issues/new?component=1370071
