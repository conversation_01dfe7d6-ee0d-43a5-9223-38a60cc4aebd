# This is a vpython "spec" file.
#
# Read more about `vpython` and how to modify this file here:
#   https://chromium.googlesource.com/infra/infra/+/main/doc/users/vpython.md
# List of available wheels:
#   https://chromium.googlesource.com/infra/infra/+/main/infra/tools/dockerbuild/wheels.md

python_version: "3.11"

wheel: <
  name: "infra/python/wheels/pytest-py3"
  version: "version:8.3.4"
>

# Required by pytest==8.3.4
wheel: <
  name: "infra/python/wheels/py-py2_py3"
  version: "version:1.11.0"
>

# Required by pytest==8.3.4
wheel: <
  name: "infra/python/wheels/iniconfig-py3"
  version: "version:1.1.1"
>

# Required by pytest==8.3.4
wheel: <
  name: "infra/python/wheels/packaging-py3"
  version: "version:23.0"
>

# Required by pytest==8.3.4
wheel: <
  name: "infra/python/wheels/pluggy-py3"
  version: "version:1.5.0"
>

# Required by pytest==8.3.4
wheel: <
  name: "infra/python/wheels/toml-py3"
  version: "version:0.10.1"
>

# Required by pytest==8.3.4
wheel: <
  name: "infra/python/wheels/pyparsing-py3"
  version: "version:3.0.7"
>

# Required by pytest==8.3.4
wheel: <
  name: "infra/python/wheels/attrs-py2_py3"
  version: "version:21.4.0"
>

# NB: Keep in sync with constraints.txt.
wheel: <
  name: "infra/python/wheels/black-py3"
  version: "version:25.1.0"
>

# Required by black==25.1.0
wheel: <
  name: "infra/python/wheels/mypy-extensions-py3"
  version: "version:0.4.3"
>

# Required by black==25.1.0
wheel: <
  name: "infra/python/wheels/tomli-py3"
  version: "version:2.0.1"
>

# Required by black==25.1.0
wheel: <
  name: "infra/python/wheels/platformdirs-py3"
  version: "version:2.5.2"
>

# Required by black==25.1.0
wheel: <
  name: "infra/python/wheels/pathspec-py3"
  version: "version:0.9.0"
>

# Required by black==25.1.0
wheel: <
  name: "infra/python/wheels/typing-extensions-py3"
  version: "version:4.3.0"
>

# Required by black==25.1.0
wheel: <
  name: "infra/python/wheels/click-py3"
  version: "version:8.0.3"
>

wheel: <
  name: "infra/python/wheels/flake8-py2_py3"
  version: "version:6.0.0"
>

# Required by flake8==6.0.0
wheel: <
  name: "infra/python/wheels/mccabe-py2_py3"
  version: "version:0.7.0"
>

# Required by flake8==6.0.0
wheel: <
  name: "infra/python/wheels/pyflakes-py2_py3"
  version: "version:3.0.1"
>

# Required by flake8==6.0.0
wheel: <
  name: "infra/python/wheels/pycodestyle-py2_py3"
  version: "version:2.10.0"
>

wheel: <
  name: "infra/python/wheels/isort-py3"
  version: "version:5.10.1"
>
